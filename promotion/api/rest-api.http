### 获取tab
GET {{api}}api/promotion/getColumns
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMTBkYWNlYTMtMTY2YS00YWU4LWExOWUtNWE5YWFjZWU4YjgzIiwiVXNlcm5hbWUiOiIiLCJJRCI6MTkwLCJBcHBJRCI6MCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcyNDY1NjU4MSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzI0MDUwNzgxfQ.Zc4L_I7JlAtFAxnQJE0UBqmuCrH8HiJAT1rHmKBIUwg
x-User-Id: 1

### 获取分销提成
GET {{api}}api/promotion/getDistributorAwards?page=1&pageSize=10
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取区域分红
GET {{api}}api/promotion/getAreaAgencyAwards?page=1&pageSize=10&order_sn=182002330678
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取招商分红
GET {{api}}api/promotion/getMerchantAwards?page=1&pageSize=10
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取小商店分成
GET {{api}}api/promotion/getSmallShopAwards?page=1&pageSize=1&member=17519512230
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取电影票分红
GET {{api}}api/promotion/getCinemaTicketAwards?page=1&pageSize=10&order_sn=18888888&settle_status=2
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取cps分成
GET {{api}}api/promotion/getCpsAwards?page=1&pageSize=10
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取聚推联盟分成
GET {{api}}api/promotion/getJhCpsAwards?page=1&pageSize=10
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取课程分红
GET {{api}}api/promotion/getCourseAwards?page=1&pageSize=10
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 获取美团分销分成
GET {{api}}api/promotion/getMeituanDistributorAwards?page=1&pageSize=10
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

###
GET {{api}}/promotion/getColumns
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcyNDY1MzY5NSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzI0MDQ3ODk1fQ.zIMS_zlB9A7zwb7Cvzodq_HEN1IzxLOVvHE6JLVJ0SE
x-User-Id:

###
POST {{api}}/promotion/saveColumns
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcyNDY1MzY5NSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzI0MDQ3ODk1fQ.zIMS_zlB9A7zwb7Cvzodq_HEN1IzxLOVvHE6JLVJ0SE
x-User-Id:

{
"data": [
{
"key": 14,
"name": "cps分成",
"url": "promotion/getCpsAwards",
"team_show": false,
"isShow": 0,
"sort": 5
},
{
"key": 23,
"name": "电影票分红",
"url": "promotion/getCinemaTicketAwards",
"team_show": false,
"isShow": 0,
"sort": 4
},
{
"key": 38,
"name": "美团分销分成",
"url": "promotion/getMeituanDistributorAwards",
"team_show": false,
"isShow": 0,
"sort": 3
},
{
"key": 42,
"name": "代理",
"url": "promotion/getInstitutionAwards",
"team_show": true,
"isShow": 0,
"sort": 2
},
{
"key": 17,
"name": "小商店分成",
"url": "promotion/getSmallShopAwards",
"team_show": false,
"isShow": 0,
"sort": 6
},
{
"key": 2,
"name": "分销分成",
"url": "promotion/getDistributorAwards",
"team_show": false,
"isShow": 0,
"sort": 1
},
{
"key": 1,
"name": "区域分红",
"url": "promotion/getAreaAgencyAwards",
"team_show": false,
"isShow": 0,
"sort": 0
},
{
"key": 20,
"name": "聚推联盟分成",
"url": "promotion/getJhCpsAwards",
"team_show": false,
"isShow": 0,
"sort": 0
},
{
"key": 3,
"name": "招商分红",
"url": "promotion/getMerchantAwards",
"team_show": false,
"isShow": 0,
"sort": 0
},
{
"key": 18,
"name": "课程分红",
"url": "promotion/getCourseAwards",
"team_show": false,
"isShow": 0,
"sort": 0
}
]
}


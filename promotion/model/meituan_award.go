package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

type MeituanDistributorOrder struct {
	source.Model
	VerifyDate             string            `json:"verifyDate"`
	VerifyTime             string            `json:"verifyTime"`
	ItemId                 int64             `json:"itemId"`
	UniqueItemId           int               `json:"uniqueItemId" gorm:"primarykey"`
	OrderPayTime           string            `json:"orderPayTime"`
	OrderId                string            `json:"orderId"`
	ActualItemAmount       string            `json:"actualItemAmount"`
	ActualOrderAmount      string            `json:"actualOrderAmount"`
	ShopUuid               string            `json:"shopUuid"`
	ShopName               string            `json:"shopName"`
	BrandName              string            `json:"brandName"`
	CityName               string            `json:"cityName"`
	Cat0Name               string            `json:"cat0Name"`
	Cat1Name               string            `json:"cat1Name"`
	OrderType              string            `json:"orderType"`
	CouponId               string            `json:"couponId"`
	CouponGroupId          int               `json:"couponGroupId"`
	CouponDiscountAmount   string            `json:"couponDiscountAmount"`
	CouponPriceLimit       string            `json:"couponPriceLimit"`
	BalanceAmount          string            `json:"balanceAmount"`
	BalanceCommissionRatio string            `json:"balanceCommissionRatio"`
	OrderUserId            string            `json:"orderUserId"`
	ItemStatus             int               `json:"itemStatus"`
	BalanceStatus          int               `json:"balanceStatus"`
	SettlementType         string            `json:"settlementType"`
	CouponSource           string            `json:"couponSource"`
	OrderPlatform          string            `json:"orderPlatform"`
	UtmSource              int               `json:"utmSource"`
	UtmMedium              string            `json:"utmMedium"`
	ModifyTime             string            `json:"modifyTime"`
	ItemBizStatus          int               `json:"itemBizStatus"`
	BillingDate            string            `json:"billingDate"`
	PromotionId            string            `json:"promotionId"`
	DealId                 int               `json:"dealId"`
	LaunchTag              int               `json:"launchTag"`
	AppID                  uint              `json:"app_id"`
	ThirdUserID            uint              `json:"third_user_id"`
	UserID                 uint              `json:"user_id"`
	Settled                int               `json:"settled" gorm:"default:0;"`              //1 已分成
	LocalSettleAt          *source.LocalTime `json:"local_settle_at" form:"local_settle_at"` //结算时间
	AppCommissionPrice     uint              `json:"app_commission_price"`
	Amount                 uint              `json:"amount"`
	Md5                    string            `json:"md_5"`
	User                   User              `json:"user" gorm:"foreignKey:UserID"`
	SettleName             string            `json:"settle_name" gorm:"-"`
}

func (m *MeituanDistributorOrder) AfterFind(tx *gorm.DB) (err error) {
	if m.Settled == 0 {
		m.SettleName = "未结算"
	} else {
		m.SettleName = "已结算"
	}
	m.Amount = m.AppCommissionPrice
	return
}

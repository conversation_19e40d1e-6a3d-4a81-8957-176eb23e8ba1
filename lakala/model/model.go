package model

import "yz-go/source"

var PayNotifyUrl = "lakala/notify"
var RechargeNotifyUrl = "lakala/recharge"

type LakalaSetting struct {
	Enable     string `json:"enable"`
	AppID      string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`
	SerialNO   string `mapstructure:"serial_no" json:"serial_no" yaml:"serial_no"`
	MerchantNO string `mapstructure:"merchant_no" json:"merchant_no" yaml:"merchant_no"`
	PrivateKey string `mapstructure:"private_key" json:"private_key" yaml:"private_key"`
	Cert       string `mapstructure:"cert" json:"cert" yaml:"cert"`
}

type Notify struct {
}
type ResPayData struct {
	Code     string `json:"code"`
	Msg      string `json:"msg"`
	RespTime string `json:"resp_time"`
	RespData struct {
		MerchantNo         string `json:"merchant_no"`
		ChannelId          string `json:"channel_id"`
		OutOrderNo         string `json:"out_order_no"`
		OrderCreateTime    string `json:"order_create_time"`
		OrderEfficientTime string `json:"order_efficient_time"`
		PayOrderNo         string `json:"pay_order_no"`
		TotalAmount        string `json:"total_amount"`
		CounterUrl         string `json:"counter_url"`
	} `json:"resp_data"`
}

type ReqPayData struct {
	Amount uint   `json:"amount"`
	PaySN  string `json:"pay_sn"`
	Type   uint   `json:"type"` //1 支付  2 充值
	Uid    uint   `json:"uid"`
}

type PayStatus struct {
	PaySN int `json:"pay_sn" form:"pay_sn" `
}

type PayInfo struct {
	source.Model
	Status         PayStatus `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"`                    // 状态（0待支付，1已支付，-1已失效，-2已退款）
	PaySN          uint      `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;index;"`                                   // 编号
	Amount         uint      `json:"amount" form:"amount" gorm:"column:amount;comment:支付总金额(分);"`                                   // 支付总金额
	RefundedAmount uint      `json:"refunded_amount" form:"refunded_amount" gorm:"column:refunded_amount;comment:已退款金额(分);"`        // 已退款金额
	UserID         uint      `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;index;"`                              // 用户id
	PayTypeID      int       `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"` // 订单支付方式
	TransactionId  string    `json:"transaction_id" form:"transaction_id" gorm:"column:transaction_id;comment:微信支付订单号;"`            //微信支付订单号
	PluginId       int       `json:"plugin_id" form:"plugin_id" gorm:"column:plugin_id;comment:插件id;type:smallint;size:3;"`         // 订单支付方式

	ExpireAt *source.LocalTime `json:"expire_at"` // 过期时间
	PaidAt   *source.LocalTime `json:"paid_at"`   // 支付时间
	RefundAt *source.LocalTime `json:"refund_at"` // 退款时间
}
type NotifyData struct {
	ChannelId          string `json:"channel_id"`
	MerchantNo         string `json:"merchant_no"`
	OrderCreateTime    string `json:"order_create_time"`
	OrderEfficientTime string `json:"order_efficient_time"`
	OrderInfo          string `json:"order_info"`
	OrderStatus        string `json:"order_status"`
	OrderTradeInfo     struct {
		AccTradeNo       string `json:"acc_trade_no"`
		AccType          string `json:"acc_type"`
		BankType         string `json:"bank_type"`
		BusiType         string `json:"busi_type"`
		LogNo            string `json:"log_no"`
		PayMode          string `json:"pay_mode"`
		PayerAmount      int    `json:"payer_amount"`
		SettleMerchantNo string `json:"settle_merchant_no"`
		SettleTermNo     string `json:"settle_term_no"`
		TradeAmount      int    `json:"trade_amount"`
		TradeNo          string `json:"trade_no"`
		TradeRemark      string `json:"trade_remark"`
		TradeStatus      string `json:"trade_status"`
		TradeTime        string `json:"trade_time"`
		TradeType        string `json:"trade_type"`
		UserId1          string `json:"user_id1"`
		UserId2          string `json:"user_id2"`
	} `json:"order_trade_info"`
	OutOrderNo      string `json:"out_order_no"`
	PayOrderNo      string `json:"pay_order_no"`
	TermNo          string `json:"term_no"`
	TotalAmount     int    `json:"total_amount"`
	TransMerchantNo string `json:"trans_merchant_no"`
	TransTermNo     string `json:"trans_term_no"`
}

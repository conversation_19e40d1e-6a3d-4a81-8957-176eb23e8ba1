package cron

import (
	aggregatedPaymentSplitSettlementService "aggregated-payment-split-settlement/service"
	"errors"
	incomeModel "finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"small-shop/model"
	"small-shop/service"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushSettleHandle() {
	task := cron.Task{
		Key:  "smallShopSettleAward",
		Name: "小商店店主收益结算",
		// 每半小时执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			Settle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

type SplitShareRecordAmount struct {
	source.Model
	MerchantNo         string `gorm:"column:merchant_no;comment:商户号" json:"merchant_no"`
	PaymentStoreCode   string `json:"payment_store_code" gorm:"column:payment_store_code;comment:聚合支付数据通商户号;"`
	Type               int    `json:"type" form:"type" gorm:"column:type;comment:类型0-商户，1-子商户"`
	SplitShareRecordId uint   `json:"split_share_record_id" gorm:"column:split_share_record_id;comment:分账记录id"`
	SplitAmount        uint   `json:"split_amount" gorm:"column:split_amount;comment:分账金额"`
	SplitStatus        int    `json:"split_status" gorm:"column:split_status;comment:分账状态0待处理1处理中2已受理3处理完成-1处理失败 ;default:0;type:int(11);"`
	SettlementStatus   int    `json:"settlement_status" gorm:"column:settlement_status;comment:结算状态0待结算1已结算 已结算才可以进行请求分账;default:0;type:int(11);"`
	OutSeparateNo      string `json:"out_separate_no"`                                                                  // 商户分账指令流水号 分账请求号
	SplitStatusDesc    string `json:"split_status_desc"`                                                                //分账结果描述第三方返回
	OrderID            uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:商户订单号-小商店订单id;"` //支付编号

	ErrorMsg string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;type:text;"` //错误内容
}

func Settle() {
	//log.Log().Info("小商店收益结算,开始")

	var err error
	var awards []model.SettleAward
	err = source.DB().Model(&model.SettleAward{}).Preload("SmallShopInfo").Where("UNIX_TIMESTAMP(`created_at`) + (IFNULL(`settle_days`, 0) * 86400) <= ?", time.Now().Unix()).Where("status = ? AND order_completed = ?", 0, 1).Find(&awards).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//log.Log().Info("未找到待结算收益结算,返回")
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
	for _, award := range awards {
		if award.SmallShopInfo.ID == 0 {
			//log.Log().Info("小商店不存在,返回")
			return
		}
		// 结算
		err = service.SettleAward(award)
		if err != nil {
			//log.Log().Info("结算失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		// 修改小商店统计
		err = service.UpdateSmallShopBySettle(award.Amount, award.SmallShopInfo)
		if err != nil {
			//log.Log().Info("修改店主统计失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		//如果不是分账则增加收入
		if award.IsSplit == 0 {
			// 增加收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(award.SUID)
			income.IncomeType = incomeModel.SmallShop
			income.Amount = award.Amount
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				//log.Log().Info("增加收入失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return
			}
		}

		if award.IsSplit == 1 {
			err = source.DB().Model(&model.SettleAward{}).Where("id = ?", award.ID).Updates(map[string]interface{}{"split_status": 1, "income_status": 1}).Error
		}
		if err != nil {
			//log.Log().Info("增加收入失败,返回")
			log.Log().Error("小商店分账结算修改信息出现问题", zap.Any("err", err))
			return
		}
		if award.IsSplit == 1 {
			var splitShareRecordAmount SplitShareRecordAmount
			err = source.DB().Model(&SplitShareRecordAmount{}).Where("order_id = ? and type = 1", award.OrderID).First(&splitShareRecordAmount).Error
			if err != nil {
				log.Log().Error("聚合分账：结算获取分账记录失败", zap.Any("err", err), zap.Any("OrderID", award.OrderID))
				return
			}
			err = source.DB().Model(&SplitShareRecordAmount{}).Where("id = ?", splitShareRecordAmount.ID).Updates(map[string]interface{}{"settlement_status": 1}).Error
			if err != nil {
				log.Log().Error("聚合分账：结算修改分账记录状态失败", zap.Any("err", err), zap.Any("OrderID", award.OrderID))
				return
			}
			//进行分账
			_ = aggregatedPaymentSplitSettlementService.RevenueSharing(splitShareRecordAmount.SplitShareRecordId)
			return
		}
		//log.Log().Info("小商店收益结算,成功")
	}
}

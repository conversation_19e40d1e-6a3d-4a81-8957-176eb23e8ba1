package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type Setting struct {
	source.Model
	Key    string `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values Value  `json:"value" gorm:"column:value;comment:值;type:json;"`
}

type Value struct {
	Switch       int        `json:"switch"`         // 开启小商店 0:关闭, 1:开启
	LevelLimit   LevelLimit `json:"level_limit"`    // 等级限制
	OrderPayType int        `json:"order_pay_type"` // 小商店订单支付方式 1:店主金额支付, 2:小商店分账支付
	SettleDays   int        `json:"settle_days"`    // 结算天数
	// 小程序端支付方式
	PayTypes PayTypes `json:"pay_types"`
	// 公众号h5支付方式
	WechatPayTypes PayTypes `json:"wechat_pay_types"`
	Independent    int      `json:"independent"`     // 自主定价 0:关闭, 1:开启
	DefaultShopID  uint     `json:"default_shop_id"` // 小程序默认小商店id
	// 开启免审核 0:关闭, 1:开启
	ApplyAutoPass int `json:"apply_auto_pass"`
	// 开启付费 0:关闭, 1:开启
	OpenPay int `json:"open_pay"`
	// 选品中心数据 0:不限制, 1:选品池
	PickProductLimit int `json:"pick_product_limit"`
	// 专辑数据 0:不限制, 1:专辑池
	AlbumLimit int `json:"album_limit"`
	// 指定选品
	EnableSelectedItems int `json:"enable_selected_items"`
	// 同步会员到中台
	SyncUsers int `json:"sync_users"`
	// 前端显示会员列表 0:不显示, 1:显示
	ShowUserList int `json:"show_user_list"`
}

type LevelLimit []LevelID

type LevelID struct {
	ID uint `json:"id"`
}

type PayTypes []PayType

type PayType struct {
	ID      uint   `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"`     //0正常 30000都是数据通-聚合支付
	PayType string `json:"pay_type"` //数据通-聚合支付 用来区分具体是什么支付 例如微信 支付宝等等
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (Setting) TableName() string {
	return "sys_settings"
}

type ConvergenceSetting struct {
	Merchantno      string `json:"merchantno"`
	Trademerchantno string `json:"trademerchantno"`
	Hmacval         string `json:"hmacval"`
}

type WxSetting struct {
	model.SysSetting
	Value WxValue `json:"value"`
}
type WxValue struct {
	IsOpen                     int    `mapstructure:"isopen" json:"isopen" yaml:"isopen"`                                                                      //状态1开始2关闭
	AppId                      string `mapstructure:"appid" json:"appid" yaml:"appid"`                                                                         //AppID
	AppSecret                  string `mapstructure:"appsecret" json:"appsecret" yaml:"appsecret"`                                                             //AppSecret
	Mchid                      string `mapstructure:"mch_id" json:"mch_id" yaml:"mch_id"`                                                                      //支付商户号
	Miniid                     string `mapstructure:"mini_id" json:"mini_id" yaml:"mini_id"`                                                                   //原始id
	PayKey                     string `mapstructure:"pay_key" json:"pay_key" yaml:"pay_key"`                                                                   //支付密钥
	CertPath                   string `mapstructure:"cert_path" json:"cert_path" yaml:"cert_path"`                                                             //cert证书文件路径
	KeyPath                    string `mapstructure:"key_path" json:"key_path" yaml:"key_path"`                                                                //cert证书文件路径
	MchCertificateSerialNumber string `mapstructure:"mch_certificate_serial_number" json:"mch_certificate_serial_number" yaml:"mch_certificate_serial_number"` //证书编号
	Cert                       string `mapstructure:"cert" json:"cert" yaml:"cert"`                                                                            //微信平台证书 后端下载的不是前端上传的 （暂时微信除了API下载没有任何地方获取这个证书）
	Serial                     string `mapstructure:"serial" json:"serial" yaml:"serial"`                                                                      //微信平台证书序列号
	IsUploadShippingInfo       int    `mapstructure:"is_upload_shipping_info" json:"is_upload_shipping_info" yaml:"is_upload_shipping_info"`                   //是否上传物流信息 1是 0否

}

func (value WxValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *WxValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var WechatMiniSetting *WxValue

func GetSysMiniSetting() (err error, setting WxSetting) {
	err = source.DB().Where("`key` = ?", "small_shop_wx_setting").First(&setting).Error
	return
}
func GetMini() (err error, setting WxValue) {
	if WechatMiniSetting == nil {
		var wxSetting WxSetting
		err, wxSetting = GetSysMiniSetting()
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		WechatMiniSetting = &wxSetting.Value
	}
	return err, *WechatMiniSetting
}

func ResetMini() {
	//重置全局变量 start
	WechatMiniSetting = nil
}

func (WxSetting) TableName() string {
	return "sys_settings"
}

type ProtocolSetting struct {
	source.Model
	Key    string        `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values ProtocolValue `json:"value" gorm:"column:value;comment:值;type:json;"`
}

type ProtocolValue struct {
	// 服务协议标题
	ServiceTitle string `json:"service_title"`
	// 服务协议内容
	ServiceContent string `json:"service_content"`
	// 隐私协议标题
	PrivacyTitle string `json:"privacy_title"`
	// 隐私协议内容
	PrivacyContent string `json:"privacy_content"`
}

func (value ProtocolValue) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *ProtocolValue) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func (ProtocolSetting) TableName() string {
	return "sys_settings"
}

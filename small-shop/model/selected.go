package model

import "yz-go/source"

type SmallShopSelectedProductMigration struct {
	source.Model
	ProductID uint `json:"product_id" from:"product_id" gorm:"column:product_id;default:0;index"`
}

func (SmallShopSelectedProductMigration) TableName() string {
	return "small_shop_selected_products"
}

type SmallShopSelectedProduct struct {
	source.Model
	ProductID uint `json:"product_id" from:"product_id" gorm:"column:product_id;default:0;index"`
}

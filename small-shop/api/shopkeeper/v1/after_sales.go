package v1

import (
	model2 "after-sales/model"
	service2 "after-sales/service"
	ufv1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	afterSales "small-shop/after-sales"
	"small-shop/model"
	"small-shop/request"
	"small-shop/service"
	supplierService "supplier/service"
	v1 "user/api/f/v1"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

func VideoGetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	pageInfo.SmallShopUserID = smallUser.ID
	if err, list, total := afterSales.GetAfterSalesList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func VideoFindAfterSales(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)
	userID := v1.GetUserID(c)
	err, info := afterSales.FindAfterSales(reqId.Id)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err != nil || info.SmallShopUserID != smallUser.ID {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

type ShopAddressReq struct {
	AfterSalesID uint `json:"after_sales_id" form:"after_sales_id" query:"after_sales_id"`
}

func FindShopAddress(c *gin.Context) {
	var addressRequest ShopAddressReq
	err := c.ShouldBindQuery(&addressRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, sales := afterSales.FindAfterSales(addressRequest.AfterSalesID)
	if err != nil {
		yzResponse.FailWithMessage("查询售后失败", c)
		return
	}
	var supplierID uint
	err, supplierID = supplierService.GetSupplierIDBySmallShopOrderID(sales.OrderID)
	if err != nil {
		yzResponse.FailWithMessage("查询供应商失败", c)
		return
	}
	if err, res := supplierService.GetShopAddressBySupplierId(supplierID, 0, "0"); err != nil {
		yzResponse.FailWithMessage("商家暂时没有配置默认退货地址", c)
		return
	} else {
		yzResponse.OkWithData(res, c)
	}
}

type GetReasonsRequest struct {
	AfterSaleType model2.AfterSalesType `json:"after_sale_type" form:"after_sale_type"`
	IsReceived    int                   `json:"is_received" form:"is_received"` //0未收到货 1已
}

func VideoGetReasonList(c *gin.Context) {
	var getReasonsRequest GetReasonsRequest
	err := c.ShouldBindQuery(&getReasonsRequest)
	if err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}

	info := service2.GetReasons(getReasonsRequest.AfterSaleType, getReasonsRequest.IsReceived)
	RefundReason := map[model2.RefundReason]string{}

	for _, value := range info {
		_, name := model2.GetRefundReasonName(value)
		RefundReason[value] = name
	}

	yzResponse.OkWithData(gin.H{"reasons": RefundReason}, c)
}

func VideoUserReceive(c *gin.Context) {
	var as AfterSalesRequest
	err := c.ShouldBindJSON(&as)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = afterSales.UserReceive(as.SmallShopAfterSales, smallUser.ID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type SendRequest struct {
	afterSales.SendRequest
}

func VideoSend(c *gin.Context) {
	var sendRequest SendRequest
	err := c.ShouldBindJSON(&sendRequest)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := v1.GetUserID(c)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = afterSales.SendAfterSales(sendRequest.SendRequest, smallUser.ID)
	if err != nil {
		//log.log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

func VideoSaveAfterSales(c *gin.Context) {
	var r AfterSalesRequest
	err := c.ShouldBindJSON(&r)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = afterSales.SaveAfterSales(r.SmallShopAfterSales)
	if err != nil {
		//log.log().Error("修改售后申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("修改成功，请等待审核", c)
}

func VideoCreateAfterSales(c *gin.Context) {
	var r AfterSalesRequest
	err := c.ShouldBindJSON(&r)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	uid := v1.GetUserID(c)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(uid)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = afterSales.ApplyAfterSales(r.SmallShopAfterSales, smallUser.ID)
	if err != nil {
		//log.log().Error("申请售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("申请成功，请等待审核", c)
}

func VideoClose(c *gin.Context) {
	var as AfterSalesRequest
	err := c.ShouldBindJSON(&as)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	uid := v1.GetUserID(c)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(uid)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = afterSales.CloseAfterSales(as.ID, smallUser.ID)
	if err != nil {
		//log.log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type AfterSalesRequest struct {
	afterSales.SmallShopAfterSales
}

func ApplyVideoAfterSales(c *gin.Context) {
	var r AfterSalesRequest
	err := c.ShouldBindJSON(&r)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	// 查询店主会员
	err, smallUser := model.GetDefaultUserByUid(userId)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = afterSales.ApplyAfterSales(r.SmallShopAfterSales, smallUser.ID)
	if err != nil {
		//log.log().Error("申请售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("申请成功，请等待审核", c)
}

func ApplyShopAfterSales(c *gin.Context) {
	var req afterSales.ShopkeeperAfterSales
	err := c.ShouldBindJSON(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := afterSales.ApplyShopAfterSales(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("提交成功", c)
	}
}

func GetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		//log.log().Error("未绑定小商店", zap.Any("err", err))
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	pageInfo.SmallShopID = smallShop.ID
	if err, list, total := afterSales.GetAfterSalesList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func FindAfterSales(c *gin.Context) {
	var req yzRequest.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, read := afterSales.GetAfterSalesByID(req.Id); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}

type AddressRequest struct {
	OrderID uint `json:"order_id" form:"order_id" query:"order_id"`
}

func GetShopAddressByOrderID(c *gin.Context) {
	var addressRequest AddressRequest
	err := c.ShouldBindQuery(&addressRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var supplierID uint
	err, supplierID = supplierService.GetSupplierIDBySmallShopOrderID(addressRequest.OrderID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, res := supplierService.GetShopAddressBySupplierId(supplierID, 0, "0"); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("商家暂时没有配置默认退货地址", c)
		return
	} else {
		yzResponse.OkWithData(res, c)
	}
}

func FindAfterSalesByOrderItemId(c *gin.Context) {
	var req yzRequest.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, read := afterSales.GetAfterSalesByOrderItemId(req.Id); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"read": read}, c)
	}
}

func Close(c *gin.Context) {
	var req yzRequest.GetById
	err := c.ShouldBindQuery(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		//log.log().Error("未绑定小商店", zap.Any("err", err))
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err = afterSales.Close(req.Id, smallShop.ID); err != nil {
		//log.log().Error("修改OrderItem状态失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("操作成功", c)
	}
}

func GetAfterSalesCount(c *gin.Context) {
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		//log.log().Error("未绑定小商店", zap.Any("err", err))
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err, total := afterSales.GetAfterSalesCount(smallShop.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"total": total}, c)
	}
}

func PassAudit(c *gin.Context) {
	var req yzRequest.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = afterSales.PassAudit(req.Id); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("操作成功", c)
	}
}

func RejectAudit(c *gin.Context) {
	var resAfterSaleAudit model.SmallShopAfterSalesAudit
	err := c.ShouldBindJSON(&resAfterSaleAudit)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	if err = afterSales.RejectAudit(resAfterSaleAudit, userId); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("操作成功", c)
	}
}

package v1

import (
	"github.com/gin-gonic/gin"
	"small-shop/request"
	"small-shop/service"
	yzResponse "yz-go/response"
)

func GetShopkeepersList(c *gin.Context) {
	var pageInfo request.ShopkeeperSearch
	err := c.<PERSON>ind<PERSON>(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetShopkeepersList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

package v1

import (
	"after-sales/model"
	"after-sales/service"
	ufv1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	afterSales "small-shop/after-sales"
	"small-shop/request"
	supplierService "supplier/service"
	v1 "user/api/f/v1"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

type SendRequest struct {
	afterSales.SendRequest
}

// Send
// @Tags 售后
// @Summary 小商店会员售后发货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body SendRequest true "发货快递信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /smallShop/afterSales/send [post]
func Send(c *gin.Context) {
	var sendRequest SendRequest
	err := c.ShouldBindJSON(&sendRequest)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := v1.GetUserID(c)
	err = afterSales.SendAfterSales(sendRequest.SendRequest, userID)
	if err != nil {
		//log.log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// UserReceive
// @Tags 售后
// @Summary 小商店会员售后收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AfterSales true "收货信息"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /afterSales/userReceive [post]
func UserReceive(c *gin.Context) {
	var as AfterSalesRequest
	err := c.ShouldBindJSON(&as)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)
	err = afterSales.UserReceive(as.SmallShopAfterSales, userID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type GetReasonsRequest struct {
	AfterSaleType model.AfterSalesType `json:"after_sale_type" form:"after_sale_type"`
	IsReceived    int                  `json:"is_received" form:"is_received"` //0未收到货 1已
}

// GetReasonList
// @Tags 售后
// @Summary 小商店用户获取退款理由
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} refund.AfterSales
// @Router /api/afterSales/reason/list [get]
func GetReasonList(c *gin.Context) {
	var getReasonsRequest GetReasonsRequest
	err := c.ShouldBindQuery(&getReasonsRequest)
	if err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}

	info := service.GetReasons(getReasonsRequest.AfterSaleType, getReasonsRequest.IsReceived)
	RefundReason := map[model.RefundReason]string{}

	for _, value := range info {
		_, name := model.GetRefundReasonName(value)
		RefundReason[value] = name
	}

	yzResponse.OkWithData(gin.H{"reasons": RefundReason}, c)
}

// FindAfterSales
// @Tags 售后
// @Summary 用id查询售后信息
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询售后信息"
// @Success 200 {object} service.AfterSalesForDetail
// @Router /api/afterSales/get [get]
func FindAfterSales(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)
	userID := v1.GetUserID(c)
	err, info := afterSales.FindAfterSales(reqId.Id)
	if err != nil || info.SmallShopUserID != userID {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

func FindAfterSalesByOrderItemId(c *gin.Context) {
	var reqId yzRequest.GetByOrderItemId
	_ = c.ShouldBindQuery(&reqId)
	userID := v1.GetUserID(c)
	err, info := afterSales.GetAfterSalesByOrderItemId(reqId.OrderItemId)
	if err != nil || info.SmallShopUserID != userID {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"after_sales": info}, c)
}

type AfterSalesRequest struct {
	afterSales.SmallShopAfterSales
}

func Close(c *gin.Context) {
	var as AfterSalesRequest
	err := c.ShouldBindJSON(&as)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	uid := v1.GetUserID(c)
	err = afterSales.CloseAfterSales(as.ID, uid)
	if err != nil {
		//log.log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

// CreateAfterSales
// @Tags 售后
// @Summary 小商店会员创建售后申请
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Ad true "小商店会员创建售后申请"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/afterSales/create [post]
func CreateAfterSales(c *gin.Context) {
	var r AfterSalesRequest
	err := c.ShouldBindJSON(&r)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	uid := v1.GetUserID(c)
	err = afterSales.ApplyAfterSales(r.SmallShopAfterSales, uid)
	if err != nil {
		//log.log().Error("申请售后失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithMessage("申请成功，请等待审核", c)
}

type AddressRequest struct {
	OrderID uint `json:"order_id" form:"order_id" query:"order_id"`
}

type SmallShopOrder struct {
	ID            uint  `json:"id" form:"id" gorm:"primarykey"`
	SupplyOrderID uint  `json:"supply_order_id" form:"supply_order_id" gorm:"column:supply_order_id;comment:供应链订单id;index;"` // 供应链中台订单id
	Order         Order `json:"order" gorm:"foreignKey:SupplyOrderID"`
}

type Order struct {
	ID         uint `json:"id" form:"id" gorm:"primarykey"`
	SupplierID uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"` // 供应商id
}

func FindShopAddressByOrderID(c *gin.Context) {
	var addressRequest AddressRequest
	err := c.ShouldBindQuery(&addressRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var supplierID uint
	err, supplierID = supplierService.GetSupplierIDBySmallShopOrderID(addressRequest.OrderID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, res := supplierService.GetShopAddressBySupplierId(supplierID, 0, "0"); err != nil {
		//log.log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("商家暂时没有配置默认退货地址", c)
		return
	} else {
		yzResponse.OkWithData(res, c)
	}
}

func GetAfterSalesList(c *gin.Context) {
	var pageInfo request.AfterSalesSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.SmallShopUserID = v1.GetUserID(c)
	if err, list, total := afterSales.GetAfterSalesList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

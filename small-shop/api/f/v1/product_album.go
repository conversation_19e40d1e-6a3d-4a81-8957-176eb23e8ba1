package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"small-shop/model"
	"small-shop/request"
	"small-shop/service"
	v1 "user/api/f/v1"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

func GetAlbumList(c *gin.Context) {
	var pageInfo request.AlbumSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.UID = v1.GetUserID(c)
	if err, list, total := service.GetAlbumList(pageInfo); err != nil {
		//log.log().Error("获取中台专辑报错", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetAllTag(c *gin.Context) {
	if err, tags := service.GetAllTag(); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"tags": tags,
		}, "获取成功", c)
	}
}

func GetAllTagByImported(c *gin.Context) {
	userId := v1.GetUserID(c)
	if err, tags := service.GetAllTagByImported(userId); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"tags": tags,
		}, "获取成功", c)
	}
}

func GetSmallShopAlbumListByUid(c *gin.Context) {
	var pageInfo request.SmallShopAlbumSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.UID = v1.GetUserID(c)
	if err, list, total := service.GetSmallShopAlbumListByUid(pageInfo); err != nil {
		//log.log().Error("获取店主专辑报错", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func RemoveAlbum(c *gin.Context) {
	var reqData request.ImportRequest
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err := service.RemoveAlbum(userId, smallShop.ID, reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("移除成功", c)
	}
}

func ImportAlbum(c *gin.Context) {
	var reqData request.ImportRequest
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if len(reqData.AlbumIDs) == 0 {
		yzResponse.FailWithMessage("请选择至少一个专辑", c)
		return
	}
	if reqData.PriceProportion < 10000 {
		yzResponse.FailWithMessage("比例不能小于100%", c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err, importAlbumIDs := service.ImportAlbum(userId, smallShop.ID, reqData); err != nil {
		log.Log().Error("选品导入失败", zap.Any("err", err.Error()))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"album_ids": importAlbumIDs,
		}, "获取成功", c)
	}
}

func ImportAllAlbum(c *gin.Context) {
	var reqData request.Strategy
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if reqData.PriceProportion < 10000 {
		yzResponse.FailWithMessage("比例不能小于100%", c)
		return
	}
	userId := v1.GetUserID(c)
	err, smallShop := service.FindSmallShop(userId)
	if err != nil {
		yzResponse.FailWithMessage("未绑定小商店", c)
		return
	}
	if err := service.ImportAllAlbum(userId, smallShop.ID, reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("导入成功", c)
	}
}

func FindAlbum(c *gin.Context) {
	var reqId yzRequest.GetById
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	var album model.ProductAlbum
	err, album = service.FindProductAlbum(reqId.Id)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(gin.H{
		"album": album,
	}, "获取成功", c)
}

func GetProductListByAlbumID(c *gin.Context) {
	var req request.AlbumProduct
	err := c.ShouldBindQuery(&req)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	if err, products, total := service.GetProductListByShopkeeper(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     products,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

func VerifyAlbumExist(c *gin.Context) {
	var req request.AlbumID
	err := c.ShouldBindJSON(&req)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, exist := service.VerifyExist(req, v1.GetUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{
			"exist": exist,
		}, "获取成功", c)
	}
}

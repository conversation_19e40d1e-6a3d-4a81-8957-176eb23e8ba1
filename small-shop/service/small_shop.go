package service

import (
	"errors"
	"gorm.io/gorm"
	shopOrder "order/model"
	"small-shop/model"
	"small-shop/request"
	"small-shop/shopkeeper_mq"
	"time"
	"yz-go/source"
)

type SmallShop struct {
	model.SmallShop
}

// CreateSmallShop 创建小商店
func CreateSmallShop(req request.CreateSmallShopRequest) (err error) {
	// 查询会员是否已经存在小商店
	err, exist := FindSmallShop(req.Uid)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		err = errors.New("查询小商店sql失败" + err.Error())
		return
	}
	if exist.ID != 0 {
		err = errors.New("该会员已经创建了小商店")
		return
	}
	// 查询会员是否已经申请店主
	var apply model.ShopkeeperApply
	err = source.DB().Model(&model.ShopkeeperApply{}).Where("uid = ? and status = ?", req.U<PERSON>, 0).First(&apply).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		err = errors.New("查询小商店申请sql失败" + err.Error())
		return
	}
	if apply.ID != 0 {
		apply.Status = 1
		err = source.DB().Save(&apply).Error
		if err != nil {
			err = errors.New("修改小商店申请sql失败" + err.Error())
			return
		}
	}
	var shop model.SmallShop
	shop.Title = req.Title
	shop.Uid = req.Uid
	shop.Status = req.Status
	err = source.DB().Create(&shop).Error
	if err != nil {
		err = errors.New("创建小商店失败" + err.Error())
		return
	}
	_ = shopkeeper_mq.PublishMessage(shop.ID)
	return err
}

func FindSmallShop(UserID uint) (err error, SmallShop SmallShop) {
	err = source.DB().Model(SmallShop).Where("uid = ?", UserID).Find(&SmallShop).Error
	return
}

func ShopOpen(id uint) (err error) {
	var shop SmallShop
	err = source.DB().Model(&SmallShop{}).Where("id = ?", id).First(&shop).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if shop.Status == 1 {
		err = errors.New("小商店已经开启, 无需再次开启")
		return
	}
	err = source.DB().Model(&shop).Where("id = ?", id).Updates(map[string]interface{}{"status": 1}).Error
	return
}

func ShopClose(id uint) (err error) {
	var shop SmallShop
	err = source.DB().Model(&SmallShop{}).Where("id = ?", id).First(&shop).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if shop.Status == -1 {
		err = errors.New("小商店已经关闭, 无需再次关闭")
		return
	}
	err = source.DB().Model(&shop).Where("id = ?", id).Updates(map[string]interface{}{"status": 0}).Error
	return
}

func GetAllSmallShop() (err error, shops interface{}) {
	db := source.DB().Model(&model.SmallShopByAll{})
	var allSmallShop []model.SmallShopByAll
	err = db.Order("id desc").Find(&allSmallShop).Error
	return err, allSmallShop
}

type SmallShopByTitle struct {
	ID    uint   `json:"id" form:"id" gorm:"primarykey"`
	Title string `json:"title" form:"title" gorm:"column:title;comment:小商店名称;type:varchar(500);size:500;"`
}

func (SmallShopByTitle) TableName() string {
	return "small_shops"
}

func GetSmallShopListByTitle(info request.SmallShopSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&SmallShopByTitle{})
	var smallShops []SmallShopByTitle
	if info.ShopTitle != "" {
		db.Where("`title` like ?", "%"+info.ShopTitle+"%")
	}
	err = db.Count(&total).Error
	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&smallShops).Error
	return err, smallShops, total
}

func GetSmallShopsList(info request.SmallShopSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.SmallShopByAdminList{})
	var smallShops []model.SmallShopByAdminList

	// 添加查询条件
	if info.SUID != 0 {
		var shopIDs []uint
		err = source.DB().Model(&model.SmallShopUserProfile{}).Distinct("shop_id").Where("uid = ?", info.SUID).Pluck("shop_id", &shopIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`id` in ?", shopIDs)
	}
	if info.SID != 0 {
		db.Where("`id` = ?", info.SID)
	}
	if info.UID != 0 {
		db.Where("`uid` = ?", info.UID)
	}
	if info.Member != "" {
		var userIDs []uint
		err = source.DB().Model(model.User{}).Where("username like ?", "%"+info.Member+"%").Or("mobile like ?", "%"+info.Member+"%").Pluck("id", &userIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", userIDs)
	}
	if info.ShopTitle != "" {
		db.Where("`title` like ?", "%"+info.ShopTitle+"%")
	}
	if info.LevelID != 0 {
		var uids []uint
		err = source.DB().Model(model.User{}).Where("level_id = ?", info.LevelID).Pluck("id", &uids).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`uid` in ?", uids)
	}

	// 查询总数
	err = db.Count(&total).Error
	if err != nil {
		return err, nil, 0
	}

	// 查询店铺列表
	err = db.Preload("UserInfo").Order("id desc").Limit(limit).Offset(offset).Find(&smallShops).Error
	if err != nil {
		return err, nil, 0
	}

	// 批量查询积分总数
	var shopPoints []struct {
		ShopID uint   `gorm:"column:shop_id"`
		Points uint64 `gorm:"column:total_points"`
	}
	err = source.DB().Model(&model.SmallShopUserProfile{}).
		Select("shop_id, COALESCE(SUM(points), 0) as total_points").
		Where("shop_id IN ?", getShopIDs(smallShops)).
		Group("shop_id").
		Scan(&shopPoints).Error
	if err != nil {
		return err, nil, 0
	}
	// 将积分总数映射到店铺列表
	pointsMap := make(map[uint]uint64)
	for _, sp := range shopPoints {
		pointsMap[sp.ShopID] = sp.Points
	}
	// 批量查询店铺用户总数
	var shopUserCounts []struct {
		ShopID    uint  `gorm:"column:shop_id"`
		UserCount int64 `gorm:"column:user_count"`
	}
	err = source.DB().Model(&model.SmallShopUserProfile{}).
		Select("shop_id, COUNT(*) as user_count").
		Where("shop_id IN ?", getShopIDs(smallShops)).
		Group("shop_id").
		Scan(&shopUserCounts).Error
	if err != nil {
		return err, nil, 0
	}
	// 将用户总数映射到店铺列表
	userCountMap := make(map[uint]int64)
	for _, suc := range shopUserCounts {
		userCountMap[suc.ShopID] = suc.UserCount
	}
	for i := range smallShops {
		smallShops[i].TotalUserPoints = pointsMap[smallShops[i].ID]
		smallShops[i].TotalUserCount = userCountMap[smallShops[i].ID]
	}

	return nil, smallShops, total
}

// 辅助函数：获取店铺ID列表
func getShopIDs(smallShops []model.SmallShopByAdminList) []uint {
	ids := make([]uint, len(smallShops))
	for i, shop := range smallShops {
		ids[i] = shop.ID
	}
	return ids
}

func UpdateSmallShopBySettle(amount uint, smallShop model.SmallShop) (err error) {
	smallShop.FinishSettleSum += amount
	smallShop.WaitSettleSum -= amount
	err = source.DB().Model(&smallShop).Updates(map[string]interface{}{"finish_settle_sum": smallShop.FinishSettleSum, "wait_settle_sum": smallShop.WaitSettleSum}).Error
	return err
}

func UpdateStatisticByOrderCreate(OrderID uint) (err error) {
	// 查询小商店订单
	var smallShopOrder model.SmallShopOrder
	err = source.DB().Model(&model.SmallShopOrder{}).Where("id = ?", OrderID).First(&smallShopOrder).Error
	if err != nil {
		return
	}
	// 查询店主
	var smallShop model.SmallShopByStatistic
	err = source.DB().Model(&model.SmallShopByStatistic{}).Where("id = ?", smallShopOrder.SmallShopID).First(&smallShop).Error
	if err != nil {
		return
	}
	// 更新店主统计
	smallShop.OrderPriceSum += smallShopOrder.Amount
	smallShop.OrderCount += 1
	// 更新修改时间
	now := time.Now().Format("2006-01-02 15:04:05")
	err = source.DB().Model(&smallShop).Where("id = ?", smallShop.ID).Updates(map[string]interface{}{"order_price_sum": smallShop.OrderPriceSum, "order_count": smallShop.OrderCount, "updated_at": now}).Error
	return err
}

func UpdateStatisticByOrderClosed(OrderID uint) (err error) {
	// 查询小商店订单
	var smallShopOrder model.SmallShopOrder
	err = source.DB().Model(&model.SmallShopOrder{}).Where("id = ?", OrderID).First(&smallShopOrder).Error
	if err != nil {
		return
	}
	// 查询店主
	var smallShop model.SmallShopByStatistic
	err = source.DB().Model(&model.SmallShopByStatistic{}).Where("id = ?", smallShopOrder.SmallShopID).First(&smallShop).Error
	if err != nil {
		return
	}
	// 更新店主统计
	smallShop.OrderPriceSum -= smallShopOrder.Amount
	smallShop.OrderCount -= 1
	if smallShop.OrderPriceSum < 0 {
		smallShop.OrderPriceSum = 0
	}
	if smallShop.OrderCount < 0 {
		smallShop.OrderCount = 0
	}
	now := time.Now().Format("2006-01-02 15:04:05")
	err = source.DB().Model(&smallShop).Where("id = ?", smallShop.ID).Updates(map[string]interface{}{"order_price_sum": smallShop.OrderPriceSum, "order_count": smallShop.OrderCount, "updated_at": now}).Error
	return err
}

func getOrderByOrderID(OrderID uint) (err error, order shopOrder.Order) {
	err = source.DB().Model(&shopOrder.Order{}).Select("id, amount").Where("id = ?", OrderID).First(&order).Error
	return
}

func getSettleAwardByOrderID(OrderID uint) (err error, award model.SettleAward) {
	err = source.DB().Model(&model.SettleAward{}).Where("shop_order_id = ?", OrderID).First(&award).Error
	return
}

func GetSmallShopByID(SID uint) (err error, smallShop model.SmallShop) {
	err = source.DB().Model(&model.SmallShop{}).Where("id = ?", SID).First(&smallShop).Error
	return
}

func GetSmallShopPreloadByID(SID uint) (err error, smallShop model.SmallShop) {
	err = source.DB().Model(&model.SmallShop{}).Preload("UserInfo").Where("id = ?", SID).First(&smallShop).Error
	return
}

func GetSmallShopByUID(uid uint) (err error, smallShop model.SmallShop) {
	err = source.DB().Model(&model.SmallShop{}).Where("uid = ?", uid).First(&smallShop).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}

func IsShopkeeper(uid, sid uint) (err error, result bool) {
	var shopKeeperUser model.User
	err, shopKeeperUser = getUserInfoBySID(sid)
	if err != nil {
		return
	}
	if shopKeeperUser.WxUnionid == "" {
		return
	}
	// 查询会员信息
	var user *model.SmallShopUser
	err, user = GetUserInfo(uid)
	if err != nil {
		return
	}
	if user.WxUnionid == "" {
		return
	}
	if user.WxUnionid == shopKeeperUser.WxUnionid {
		result = true
	}
	return
}

func getUserInfoBySID(SID uint) (err error, user model.User) {
	var smallShop model.SmallShop
	err, smallShop = GetSmallShopByID(SID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("店主不存在")
		}
		return
	}
	if smallShop.Uid == 0 {
		err = errors.New("店主不存在")
		return
	}
	err = source.DB().Model(&model.User{}).Where("id = ?", smallShop.Uid).First(&user).Error
	return
}

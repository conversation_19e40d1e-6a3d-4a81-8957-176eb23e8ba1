package service

import (
	_ "embed"
	"encoding/json"
	productmodel "product/model"
	"small-shop/model"
	"small-shop/request"
	"strings"
	"sync"
	"yz-go/component/log"
	"yz-go/source"
)

type Category struct {
	source.SoftDel
	ID       uint   `json:"id" form:"id" gorm:"primarykey"`
	Name     string `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(100);size:100;index"`
	ParentID uint   `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"` //父级id
}

func GetCategoryByParentID(sid, parentID uint, level int) (err error, list []Category) {
	var categoryIDs []uint
	err = source.DB().Model(&model.SmallShopCategory{}).Where("small_shop_id = ? and level = ?", sid, level).Pluck("category_id", &categoryIDs).Error
	if err != nil {
		return
	}
	err = source.DB().Model(&Category{}).Where("id in ? and level = ? and parent_id = ? and is_display = ?", categoryIDs, level, parentID, 1).Order("sort desc,id desc").Find(&list).Error
	return
}

func GetCategoryByPID(parentID uint, level int) (err error, list []Category) {
	err = source.DB().Model(&Category{}).Where("level = ? and parent_id = ? and is_display = ?", level, parentID, 1).Order("sort desc,id desc").Find(&list).Error
	return
}

func CategoryList(info request.CategoryListSearch) (err error, category []productmodel.Category) {
	db := source.DB().Model(&model.SmallShopProductSale{}).Joins("left join products on products.id = small_shop_product_sales.product_id")
	//查询导入到小商品的商品
	db.Where("small_shop_product_sales.small_shop_id = ?", info.Sid)
	if info.ParentID != 0 && info.Level != 0 {
		switch info.Level {
		case 1: //2级
			db.Joins("left join categories on categories.id = products.category2_id").Where("products.category1_id = ?", info.ParentID)
			break
		case 2: //3级
			db.Joins("left join categories on categories.id = products.category3_id").Where("products.category2_id = ?", info.ParentID)
			break
		default:
			db.Joins("left join categories on categories.id = products.category2_id").Where("products.category1_id = ?", info.ParentID)
			break
		}
	} else {
		db.Joins("left join categories on categories.id = products.category1_id")
	}

	// 增加只查询启用的分类条件
	db.Where("categories.is_display = ?", 1)

	err = db.Select("categories.*").Group("categories.id").Find(&category).Error
	//没有分类返回空
	if err != nil {
		err = nil
		return
	}
	return
}

//go:embed douyin_category.json
var douyinCategoryJson string

type DouyinCategoryItem struct {
	CateID   string               `json:"cate_id"`
	Name     string               `json:"name"`
	Level    int                  `json:"level"`
	ParentID string               `json:"parent_id"`
	Children []DouyinCategoryItem `json:"children"`
}

var DouyinCategories []DouyinCategoryItem

func GetDouyinCategories() []DouyinCategoryItem {
	if DouyinCategories == nil {
		err := json.Unmarshal([]byte(douyinCategoryJson), &DouyinCategories)
		if err != nil {
			log.Log().Error(err.Error())
		}
	}
	return DouyinCategories
}

//go:embed douyin_city.json
var douyinCityJson string

type DouyinCityItem struct {
	CityID   int    `json:"city_id"`
	CityName string `json:"city_name"`
	Sort     string `json:"sort"`
}

var DouyinCities []DouyinCityItem

var once sync.Once

var cityMap map[string]DouyinCityItem

func GetDyCityList() {
	err := json.Unmarshal([]byte(douyinCityJson), &DouyinCities)
	if err != nil {
		log.Log().Error(err.Error())
		return
	}

	cityMap = make(map[string]DouyinCityItem)
	for _, city := range DouyinCities {
		cityMap[city.CityName] = city
	}
}

func GetDouyinCities(req request.GetCitiesParam) []DouyinCityItem {
	once.Do(func() {
		GetDyCityList()
	})

	if req.CityName == "" {
		return DouyinCities
	}

	// 存放匹配的城市列表
	var matchedCities []DouyinCityItem

	// 遍历 cityMap 查找模糊匹配的城市
	for name, city := range cityMap {
		if strings.Contains(name, req.CityName) {
			matchedCities = append(matchedCities, city)
		}
	}
	// 如果有匹配的城市，返回列表
	if len(matchedCities) > 0 {
		return matchedCities
	}

	/*if city, ok := cityMap[req.CityName]; ok {
		return []DouyinCityItem{city}
	}*/
	return nil
}

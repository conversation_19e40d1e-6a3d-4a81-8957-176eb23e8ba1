package listener

import (
	"fmt"
	"go.uber.org/zap"
	"small-shop/model"
	"small-shop/service"
	"small-shop/small-shop-order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func SmallShopKeeperStatisticListenerHandles() {
	var err error
	mq.PushHandles("SmallShopKeeperStatistic", func(orderMsg mq.OrderMessage) error {
		var smallShopOrder model.SmallShopOrder
		err = source.DB().Model(&model.SmallShopOrder{}).Where("id = ?", orderMsg.OrderID).First(&smallShopOrder).Error
		if err != nil {
			log.Log().Error("查询小商店订单失败：", zap.Any("orderID", orderMsg.OrderID))
			return nil
		}
		// 小商店订单下单
		if orderMsg.MessageType == mq.Created {
			// 增加小商店数据统计
			err = service.UpdateStatisticByOrderCreate(orderMsg.OrderID)
			if err != nil {
				log.Log().Error(fmt.Sprintf("小商店-订单创建监听order_id:%d,修改店主统计失败", orderMsg.OrderID), zap.Any("error", err.Error()))
				return nil
			}
		}
		// 小商店订单关闭
		if orderMsg.MessageType == mq.Closed {
			// 扣除小商店数据统计
			err = service.UpdateStatisticByOrderClosed(orderMsg.OrderID)
			if err != nil {
				log.Log().Error(fmt.Sprintf("小商店-订单关闭监听order_id:%d,修改店主统计失败", orderMsg.OrderID), zap.Any("error", err.Error()))
				return nil
			}
		}
		return nil
	})
}

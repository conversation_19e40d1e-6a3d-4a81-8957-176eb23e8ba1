package request

import (
	"github.com/silenceper/wechat/v2/officialaccount/material"
	"github.com/silenceper/wechat/v2/officialaccount/menu"
	yzRequest "yz-go/request"
)

type Buttons []Button

// 接受的数据
type Button struct {
	menu.Button
}

type PageData struct {
	Type material.PermanentMaterialType
	yzRequest.PageInfo
}

type DelMaterialData struct {
	MediaId string `json:"media_id" form:"media_id" `
}
type WechatNewDatas []WechatNewData
type WechatNewData struct {
	material.Article
}

type WechatMessageId struct {
	Id int `json:"id" form:"id" `
}

type KeywordType struct {
	KeywordType int `json:"keyword_type" form:"keyword_type" gorm:"column:keyword_type;comment:关键字类型;"` // 1 关注回复 2关键词回复 3查找商品关键词回复 4任意消息回复
}

type WechatLogin struct {
	Code        string `json:"code" form:"code"` //微信code
	Url         string `json:"url" form:"url"`   //链接地址用来填写让用户授权获取code的请求链接使用
	ShopID      uint   `json:"shop_id" form:"shop_id"`
	ReferrerUID uint   `json:"referrer_uid"` // 推荐人ID
}

type UpdateMask struct {
	Field string `json:"field"`
	Value string `json:"value"`
}

package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"hbsk/model"
	"hbsk/request"
	"hbsk/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

func GetPurchaseBalanceList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetPurchaseBalanceList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func GetDivideAccounts(c *gin.Context) {
	var pageInfo request.HbskSettlementRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetDivideAccounts(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func PersonHolderList(c *gin.Context) {
	var pageInfo request.PersonHolderRequest
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.PersonHolderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func EnterpriseHolderList(c *gin.Context) {
	var pageInfo request.EnterpriseHolderRequest
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.EnterpriseHolderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func EnterpriseHolder(c *gin.Context) {

	var reqData model.EnterpriseHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		log.Log().Error("err", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.ShareEnterpriseHolder(reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}
}
func ManualEnterpriseHolder(c *gin.Context) {

	var reqData model.ManualEnterpriseHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err = service.ManualShareEnterpriseHolder(reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}
}
func ManualPersonHolder(c *gin.Context) {

	var reqData model.ManualPersonHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err = service.ManualPersonHolderr(reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}
}
func DeleteHolder(c *gin.Context) {

	var reqData model.DeleteHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err = service.DeleteHolder(reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}
}

type Base64Data struct {
	Data     string `json:"data"`
	FileName string `json:"file_name"`
}

func UploadFile(c *gin.Context) {

	var jsonData Base64Data
	err := c.ShouldBindJSON(&jsonData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}

	var data interface{}
	//reqData, _ := c.GetRawData()
	if err, data = service.Upload(string(jsonData.Data), jsonData.FileName); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func GetRegion(c *gin.Context) {

	yzResponse.OkWithData(model.RegionList, c)

}
func Callback(c *gin.Context) {
	var reqData model.ShareApplyData
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.Callback(reqData)

	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData("success", c)

}

type RequestBank struct {
	BankCode string `json:"bank_code"`
}

func GetBranch(c *gin.Context) {
	var reqData RequestBank
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var newBranch []model.Branch
	for _, item := range model.BranchList {
		if item.BankNo == reqData.BankCode {
			newBranch = append(newBranch, item)
		}

	}

	yzResponse.OkWithData(newBranch, c)

}
func GetBank(c *gin.Context) {

	yzResponse.OkWithData(model.BankList, c)

}

func PersonHolder(c *gin.Context) {
	var reqData model.RequestPersonHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.ShareHolder(reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}
}
func SmsVerify(c *gin.Context) {
	var reqData model.SmsData
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SmsVerify(reqData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.Ok(c)
	}
}

func UpdatePersonHolder(c *gin.Context) {
	var reqData model.UpdatePersonHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err = service.UpdateShareHolder(reqData); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
	} else {
		yzResponse.Ok(c)
	}
}

func SupplierDetail(c *gin.Context) {
	var reqData model.Supplier
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	var data interface{}
	if data = service.SupplierDetail(reqData.SupplierID); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func UpdateShareEnterpriseHolder(c *gin.Context) {
	var reqData model.EnterpriseHolder
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err = service.UpdateShareEnterpriseHolder(reqData); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
	} else {
		yzResponse.Ok(c)
	}
}
func ShareHolderQuery(c *gin.Context) {
	var reqData model.RequestHolderDetail
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err, data := service.ShareHolderQuery(reqData.Code); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func SetSetting(c *gin.Context) {

	var reqData model.HolderSetting
	err := c.ShouldBindJSON(&reqData)
	if err != nil {
		yzResponse.FailWithMessage("获取参数失败", c)
		return
	}
	if err = service.SetSetting(reqData); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
	} else {
		yzResponse.Ok(c)
	}
}
func GetSetting(c *gin.Context) {
	if err, data := service.GetSetting(); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
	} else {
		yzResponse.OkWithData(data, c)
	}
}

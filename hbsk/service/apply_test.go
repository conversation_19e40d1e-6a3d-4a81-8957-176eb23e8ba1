package service

import (
	"fmt"
	"hbsk/model"
	"strconv"
	"testing"
	"time"
)

func TestShareApplyQuery(t *testing.T) {

	ShareApplyQuery("SN_1692946441")
	return

	//var mapData = model.Holders{}
	//var mapArr []interface{}
	var mapArr model.ListHolders

	mapArr = append(mapArr, model.Holders{Code: "0", Amount: 0.01})

	mapArr = append(mapArr, model.Holders{Code: "H8665843", Amount: 0.01})
	//mapArr = append(mapArr, model.Holders{Code: "333", Amount: 31.33})

	ssssn := "SN_" + strconv.Itoa(int(time.Now().Unix()))

	fmt.Println("分账号：", ssssn) //1692945865
	ShareApply(mapArr, ssssn)
}

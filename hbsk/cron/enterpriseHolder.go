package cron

import (
	"encoding/json"
	"go.uber.org/zap"
	"hbsk/model"
	"hbsk/service"
	"yz-go/component/log"
	"yz-go/source"
)

func CronEnterpriseHolderStatus() {

	var personHolder []model.EnterpriseHolder
	var personHolderData model.EnterpriseHolder
	err := source.DB().Where("state!=? or state is null", "accept").Where("code!=''").Find(&personHolder).Error

	log.Log().Info("CronEnterpriseHolderStatus personHolder", zap.Any("info", personHolder))

	if err != nil {
		return
	}
	if len(personHolder) == 0 {
		return
	}
	for _, item := range personHolder {
		if item.Code == "" {
			continue
		}

		err, data := service.ShareHolderQuery(item.Code)
		log.Log().Info("CronEnterpriseHolderStatus data", zap.Any("info", data))

		if err != nil {
			log.Log().Error("err", zap.Any("err", err))
		}
		err = json.Unmarshal([]byte(data), &personHolderData)
		log.Log().Info("CronEnterpriseHolderStatus  Unmarshal", zap.Any("err", personHolderData))

		if err != nil {
			log.Log().Error("CronEnterpriseHolderStatus Unmarshal err", zap.Any("err", err))
		}
		if personHolderData.State != "" {
			var update model.EnterpriseHolder

			update.State = personHolderData.State
			update.Reason = personHolderData.Reason
			update.Name = personHolderData.Name
			update.LegalName = personHolderData.LegalName
			update.LegalMobile = personHolderData.LegalMobile
			log.Log().Info("update  EnterpriseHolder ", zap.Any("info", update))

			err = source.DB().Where("id=?", item.ID).Updates(&update).Error
			if err != nil {
				log.Log().Error("cron update query EnterpriseHolder ", zap.Any("err", err))
			}
		}

	}

}

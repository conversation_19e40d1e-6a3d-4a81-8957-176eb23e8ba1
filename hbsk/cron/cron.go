package cron

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"hbsk/model"
	"hbsk/service"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushCronHolderStatus() {
	task := cron.Task{
		Key:  "PushCronHolderStatus",
		Name: "PushCronHolderStatus",
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			InitSetting()
			CronHolderStatus()
			CronEnterpriseHolderStatus()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func InitSetting() {
	_, sysSetting := service.GetSetting()
	json.Unmarshal([]byte(sysSetting.Value), &service.HolderSetting)

	service.Key = service.HolderSetting.Key
	service.Secret = service.HolderSetting.Secret
	service.Code = service.HolderSetting.MerchantID
	service.Open = service.HolderSetting.IsOpen

}

func CronHolderStatus() { //个人开户状态查询

	var personHolder []model.PersonHolder
	var personHolderData model.PersonHolder
	err := source.DB().Where("state!=? or state is null", "accept").Where("code!=''").Find(&personHolder).Error

	if err != nil {
		return
	}
	if len(personHolder) == 0 {
		return
	}
	for _, item := range personHolder {

		err, data := service.ShareHolderQuery(item.Code)
		if err != nil {
			log.Log().Error("err", zap.Any("err", err))
		}
		err = json.Unmarshal([]byte(data), &personHolderData)
		if err != nil {
			log.Log().Error("CronHolderStatus Unmarshal err", zap.Any("err", err))
		}
		if personHolderData.State != "" {

			var personHolderUpdate model.PersonHolder
			personHolderUpdate.State = personHolderData.State
			personHolderUpdate.Reason = personHolderData.Reason
			source.DB().Where("id=?", item.ID).Updates(&personHolderUpdate)
		}

	}

}

func CronShareApplyQuery() {
	var hbskSettlement []model.HbskSettlement
	err := source.DB().Where("status='' or status is null").Find(&hbskSettlement).Error
	if err != nil {
		return
	}
	if len(hbskSettlement) == 0 {
		return
	}
	for _, item := range hbskSettlement {

		_, data := service.ShareApplyQuery(item.ShareApplySN)

		if data.State != "" {
			source.DB().Model(&model.HbskSettlement{}).Where("id=?", item.ID).UpdateColumn("status", data.State)

		}

	}

}

func CronShareApplyPush() { //定时任务push结算数据
	var hbskSettlement []model.HbskSettlement
	err := source.DB().Where("send_push=? and status!=''", 0).Find(&hbskSettlement).Error
	if err != nil {
		return
	}
	if len(hbskSettlement) == 0 {
		return
	}
	for _, item := range hbskSettlement {

		fmt.Print(item)
		//pushData := item

		//requestData, _ := json.Marshal(&pushData)

		//utils.HttpPostJson("3", "url")

		//_, data := service.ShareApplyQuery(item.ShareApplySN)

		if err != nil {
			log.Log().Error("CronShareApplyPush push err", zap.Any("err", err), zap.Any("errdata", item))
			continue
		}
		source.DB().Model(&model.HbskSettlement{}).Where("id=?", item.ID).UpdateColumn("send_push", 1)

	}

}

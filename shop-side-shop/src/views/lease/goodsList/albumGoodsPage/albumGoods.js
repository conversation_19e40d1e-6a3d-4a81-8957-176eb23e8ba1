import {mapGetters} from "vuex";

const Breadcrumb = () => import("../components/breadcrumb")
import zhLang from 'element-ui/lib/locale/lang/zh-CN';
import SortButton from '@/components/sortButton/sortButton.vue'
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue'
export default {
    name: "leaseAlbumGoods",
    components: { 
        Breadcrumb,
        SortButton,
        SortButtonGroup, 
    },
    computed: {
        ...mapGetters("home", ["getFramework"])
    },
    data() {
        return {
            loading: false,
            formData:{
                min_rate:null, // 最低利润率
                max_rate:null, // 最高利润率
                min_price:null, // 最低价格
                max_price:null, // 最高价格
                is_new: 0, // 新品
                is_hot: null, // 热卖
                is_promotion: 0, // 促销
            },
            sortForm:{
                value:'default', //按"默认"排序
                sort:'up', // 升序
            },
            goodsList: [],
            page: 1,
            pageSize: 200,
            total: 0,
        }
    },
    mounted() {
        this.getGoods()
    },
    methods: {
        pagination(val) {
            this.page = val.page
            this.getGoods()
        },
        getGoods() {
            this.loading = true;
            let id = parseInt(this.$route.query.album_key);
            /**
             * 排序查询：
             * formatObj键 => 1:向上up; 2:向下down。
             * formatObj值 => 1:默认降; 6:默认升; 4:销量降; 7:销量升; 2:价格降; 3:价格升; 9:利润率降; 10:利润率升; 5:发布时间降; 8:发布时间升。
             */
            const formatObj = {
                default: { 
                    1: 8,
                    2: 5,
                },
                hot: {
                    1: 7,
                    2: 4,
                },
                price: {
                    1: 3,
                    2: 2,
                },
                profit: {
                    1: 10,
                    2: 9,
                },
            }
            let params = {
                collection_id: id,
                sort_by: formatObj[this.sortForm.value][this.sortForm.sort], //排序
                origin_rate:{ // 利润率
                    from: Number(this.formData.min_rate)||null,
                    to: Number(this.formData.max_rate)||null
                },
                min_price: Number(this.formData.min_price)|| null, //最低价格
                max_price: Number(this.formData.max_price)|| null, //最高价格
                is_new: this.formData.is_new, //新品
                is_hot: this.formData.is_hot||null, //热卖
                is_promotion: this.formData.is_promotion, //促销
                page: this.page,
                pageSize: this.pageSize,
            }
            let apiUrl = this.$ls.getUserId() ? '/product/listByCollectionIdLogin' : '/product/listByCollectionId'

            this.$post(apiUrl, params).then(res => {
                this.loading = false;
                if (res.code === 0) {
                    this.goodsList = res.data.list
                    this.total = res.data.total
                    zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
                }else {
                    this.total = 0
                    this.goodsList = []
                }
            }).catch((res) => {
                this.loading = false
                console.log(res)
            });
        }
    }
};
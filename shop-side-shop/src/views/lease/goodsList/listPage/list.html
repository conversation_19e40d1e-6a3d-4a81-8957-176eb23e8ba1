<div class="inner goodsDetail-box">
  <el-row :gutter="20">
    <!-- 一级分类 start -->
    <el-col :span="5">
      <div class="bg-white p10">
        <div class="classify1-div">
          <el-radio-group          
            v-model="category1_id"   
            v-watch-height="checkFirstExpand"
            class="classify1-radio-group"
            :class="isUnfold ? 'unfold' : ''"
            @change="handleClassify1Change" 
          >
            <el-radio 
              v-for="item in classify_list" 
              :label="item.id"
              :key="item.id"   
            >
              <template v-if="item.name.length > 12" >{{item.name.slice(0, 12) + '...'}}</template>
              <template v-else>{{item.name}}</template>
            </el-radio>
          </el-radio-group>
          <div class="operation-div">
              <a href="javascript:;" v-show="isShowFirstExpand" class="operation-a" @click="isUnfold = !isUnfold">
                {{ isUnfold | unfoldText}} 
                <i :class="isUnfold ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
              </a>
          </div>
        </div>
      </div>
    </el-col>
    <!-- 一级分类 end -->
    <el-col :span="19">
      <div class="con-bread">
        <el-button icon="el-icon-location" type="text" @click="onOpenArea" style="color: #999;">{{selectArea.name}}</el-button>
        <Breadcrumb :data="breadcrumbData"/>
      </div>
      <!-- 二级分类 start -->
      <m-card class="mb20 child-classify-div" v-if="classify2_list.length > 1">
        <div class="box" :class="isUnfold2 ? 'unfold2' : ''"> 
          <div class="classify2-div">   
            <el-radio-group 
              v-model="category2_id" 
              v-watch-height="checkSecondExpand"
              class="classify2-radio-group" 
              @change="handleClassify2Change"
            >
              <template v-for="c2 in classify2_list">
                <el-radio class="item-classify-2" v-if="c2.name==='全部'" @change="handleAllClassiyfClick" :label="c2.id" :key="c2.id">{{c2.name}}</el-radio>
                <el-radio class="item-classify-2" v-else :label="c2.id" :key="c2.id">{{c2.name}}</el-radio>                
              </template>
            </el-radio-group>
          </div> 
        </div>
        <div class="operation-div" v-if="classify2_list">
          <a 
            href="javascript:;" 
            class="operation-a"  
            v-show="isShowSecondExpand"
            @click="isUnfold2 = !isUnfold2"
          >
            {{ isUnfold2 | unfoldText}} 
            <i :class="isUnfold2 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </a>
        </div>
      </m-card>
      <!-- 二级分类 end -->
      <!-- 三级分类 start -->
      <m-card class="mb20 child-classify-div" v-if="classify3_list.length > 1">
        <div class="box" :class="isUnfold3 ? 'unfold3' : ''">
          <div class="f con-classify-3">
            <el-radio-group 
              v-model="category3_id" 
              v-watch-height="checkThirdExpand"
              class="classify3-radio-group" 
              @change="handleClassify3Change"
            >
              <el-radio class="item-classify-3" :label="c3.id" v-for="c3 in classify3_list" :key="c3.id">{{c3.name}}</el-radio>
            </el-radio-group>  
          </div>
        </div>
        <div class="operation-div" v-if="classify2_list">
          <a 
            href="javascript:;" 
            class="operation-a"  
            v-show="isShowSecondExpand"
            @click="isUnfold3 = !isUnfold3"
          >
            {{ isUnfold3 | unfoldText}} 
            <i :class="isUnfold3 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </a>
        </div>
      </m-card>
      <!-- 三级分类 end -->
      <!-- 品牌 start -->
      <m-card class="mb20 child-classify-div" v-if="classify4_list.length > 0">
        <div class="box" :class="isUnfold4 ? 'unfold4' : ''">
          <div class="f con-classify-4">
            <span class="item-classify-4" style="margin-top: -2px;">品牌</span>
            <el-radio-group 
              v-model="category4_id" 
              v-watch-height="checkForthExpand"
              class="classify4-radio-group" 
              @change="handleClassify4Change"
            >
              <el-radio class="item-classify-4" :label="c4.id" v-for="c4 in classify4_list" :key="c4.id">{{c4.name}}</el-radio>
            </el-radio-group>  
          </div>
        </div>
        <div class="operation-div" v-if="classify4_list">
          <a 
            href="javascript:;" 
            class="operation-a"  
            v-show="isShowForthExpand"
            @click="isUnfold4 = !isUnfold4"
          >
            {{ isUnfold4 | unfoldText}} 
            <i :class="isUnfold4 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </a>
        </div>
      </m-card>
      <!-- 品牌 end -->
      <!-- 供应商 start -->
      <m-card class="mb20 child-classify-div" v-if="classify5_list.length > 0">
        <div class="box" :class="isUnfold5 ? 'unfold5' : ''">
          <div class="f con-classify-5">
            <span class="item-classify-5" style="margin-top: -2px;">供应商</span>
            <el-radio-group 
              v-model="category5_id" 
              v-watch-height="checkFifthExpand"
              class="classify5-radio-group" 
              @change="handleClassify5Change"
            >
              <el-radio class="item-classify-5" :label="c5.id" v-for="c5 in classify5_list" :key="c5.id">{{c5.name}}</el-radio>
            </el-radio-group>  
          </div>
        </div>
        <div class="operation-div" v-if="classify5_list">
          <a 
            href="javascript:;" 
            class="operation-a"  
            v-show="isShowFifthExpand"
            @click="isUnfold5 = !isUnfold5"
          >
            {{ isUnfold5 | unfoldText}} 
            <i :class="isUnfold5 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </a>
        </div>
      </m-card>
      <!-- 供应商 end -->
      <!-- 排序与筛选 start -->   
      <!-- <div>
        <el-form class="f" ref="formData" :model="formData" label-width="80px">
          <el-form-item label="利润率">
            <el-col :span="11" class="f">
              <el-input placeholder="请输入数值" v-model="formData.min_rate" size="small" clearable></el-input> %
            </el-col>
            <el-col class="line" :span="1">-</el-col>
            <el-col :span="11" class="f">
              <el-input placeholder="请输入数值" v-model="formData.max_rate" size="small" clearable></el-input> %
            </el-col>
          </el-form-item>
          <el-form-item label="价格">
            <el-col :span="11" class="f">
              <el-input placeholder="请输入数值" v-model="formData.min_price" size="small" clearable></el-input> 元
            </el-col>
            <el-col class="line" :span="1">-</el-col>
            <el-col :span="11" class="f">
              <el-input placeholder="请输入价格" v-model="formData.max_price" size="small" clearable></el-input> 元
            </el-col>
          </el-form-item>
          <div style="height: 20px; margin-top: 4px">
            <el-button type="danger" size="small" @click="fetch">查询</el-button>
          </div>
        </el-form>  
      </div> -->
      <div class="f p-y-10">
        <div class="con-sort">
          <SortButtonGroup v-model="sortForm" @change="fetch">
            <!-- <SortButton value="default">默认</SortButton> -->
            <SortButton value="hot">销量</SortButton>
            <SortButton value="price">价格</SortButton>
            <!-- <SortButton value="profit">利润率</SortButton> -->
          </SortButtonGroup>
        </div> 
        <!-- <div class="con-checkbox">
            <el-checkbox v-model="formData.is_new" :true-label="1" :false-label="0" label="新品" @change="fetch"></el-checkbox>
            <el-checkbox v-model="formData.is_hot" :true-label="1" :false-label="0" label="热卖" @change="fetch"></el-checkbox>
            <el-checkbox v-model="formData.is_promotion" :true-label="1" :false-label="0" label="促销" @change="fetch"></el-checkbox>
        </div>   -->
      </div>
      <!-- 排序与筛选 end -->
      <!-- 商品列表 start -->
      <div v-loading="loading">
        <m-goods v-if="goodsList.length > 0" class="mt10">
            <goods-item 
              v-for="item in goodsList" 
              :key="item.id" 
              :link="`/lease/goodsDetail?goods_id=${item.id}`"
              :page_setup_setting="getFramework.page_setup_setting"
              :url="item.image_url" 
              :price="item.minPrice"
              :title="item.title" 
              :originPrice="item.origin_price"
            >
            </goods-item>
        </m-goods>
        <m-empty v-else></m-empty>
      </div>
      <!-- 商品列表 end -->
      <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next"></Pagination>
      <AreaDialog ref="areaDialog" @submitted="onSelectArea"></AreaDialog>
    </el-col>
  </el-row>
</div>


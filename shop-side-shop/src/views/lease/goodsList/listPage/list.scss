.bg-white {
  background: #fff;
}
.con-bread {
  display: flex;
  justify-content: space-between
}
.classify-box {
  .all-classify {
    font-size: 18px;
    color: #f3535b;
    margin-top: 15px;
    cursor:pointer;
  }
  .el-divider {
    margin: 20px 0;
  }
}
::v-deep .el-radio-group {
  .el-radio {
    margin-bottom: 15px;
    &.is-checked {
      .el-radio__label {
        color: #f42121;
      }

    }
    .el-radio__input {
      display: none;
    }
  }
}

// 一级分类
::v-deep .classify1-radio-group.el-radio-group {
  //max-height: 440px;
  max-height: 970px;
  transition: max-height 0.4s;
  overflow: hidden;
  &.unfold {
    max-height: 1300px;
  }
  .el-radio {
    margin-right: 10px;
    &.is-checked {
      .el-radio__label {
        color: #ffffff;
        background-color: #f3535b;
      }
    }
    .el-radio__label {
      display: inline-block;
      height: 30px;
      line-height: 30px;
      padding: 0 15px;
      background: #f3f3f3;
    }
  }
}
.classify1-div {
  position: relative;
  .operation-div {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .operation-a {
      color: #939393;
    }
  }
}
.m-card-box {
  width: auto;
}
// 子分类
.child-classify-div {
  .box {
    padding-top: 5px;
    max-height: 86px;
    transition: max-height 0.4s;
    overflow: hidden;
    &.unfold2 {
      max-height: 1000px;
    }
    &.unfold3 {
      max-height: 1000px;
    }
    &.unfold4 {
      max-height: 1000px;
    }
    &.unfold5 {
      max-height: 1000px;
    }
  }
  margin-top: 20px;
  background: #fbfbfb;
  // padding: 20px;
  padding: 15px 20px 6px 20px;
  // 二级分类
  .classify2-div {
    display: flex;
    // margin-bottom: 15px;
    .el-radio-group {
      padding-top: 3px;
    }
    .classify2-title {
      min-width: 90px;
      font-weight: bold;
      color: #303030;
    }
  }
  .operation-div {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .operation-a {
      color: #939393;
    }
  }
}
.item-classify-2 {
  margin: 0 20px 0 0;
  font-weight: bold;
}

.con-classify-3 {
  // padding: 10px;
  .item-classify-3 {
    margin: 0 20px 15px 0;
  }
}
.con-classify-4 {
  // padding: 10px;
  .item-classify-4 {
    margin: 0 20px 15px 0;
  }
}
.con-classify-5 {
  // padding: 10px;
  .item-classify-5 {
    margin: 0 20px 15px 0;
  }
}
::v-deep.el-radio__label {
  padding-left: 0;
}

// 以下：排序与筛选
.con-sort {
  padding: 0 30px 0 26px;
  // width: 334px;
  width: 160px;
}
.con-checkbox {
  margin-left: 75px;
  .el-checkbox {
    margin-right: 30px;
  }
}
::v-deep.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $theme-color;
  border-color: $theme-color;
}
::v-deep.el-checkbox__input.is-checked+.el-checkbox__label {
  color: $theme-color;
}
::v-deep.el-form-item {
  width: 45%;
  .el-input {
    margin-right: 7px;
  }
}

//以下：商品列表
.special-offer-box {
  grid-template-columns: repeat(4, 226px) !important;
}
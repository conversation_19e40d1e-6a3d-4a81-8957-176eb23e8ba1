<div class="inner goodsDetail-box">
    <el-row :gutter="20">
        <!-- 一级分类 start -->
        <el-col :span="5">
            <div class="bg-white p10">
                <div class="classify1-div">
                    <el-radio-group v-model="category1_id" v-watch-height="checkFirstExpand"
                        class="classify1-radio-group" :class="isUnfold ? 'unfold' : ''" @change="handleClassify1Change">
                        <el-radio v-for="item in classify_list" :label="item.id" :key="item.id">
                            <template v-if="item.name.length > 12">{{item.name.slice(0, 12) + '...'}}</template>
                            <template v-else>{{item.name}}</template>
                        </el-radio>
                    </el-radio-group>
                    <div class="operation-div">
                        <a href="javascript:;" v-show="isShowFirstExpand" class="operation-a"
                            @click="isUnfold = !isUnfold">
                            {{ isUnfold | unfoldText}}
                            <i :class="isUnfold ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                        </a>
                    </div>
                </div>
            </div>
        </el-col>
        <!-- 一级分类 end -->
        <el-col :span="19">
            <Breadcrumb :data="breadcrumbData" />

            <!-- 搜索 -->
            <div class="search-box bgw">
                <!-- 二级分类 start -->
                <div class="mb15 child-classify-div" v-if="classify2_list.length > 1">
                    <div class="box" :class="isUnfold2 ? 'unfold2' : ''">
                        <div class="classify2-div">
                            <el-radio-group v-model="category2_id" v-watch-height="checkSecondExpand"
                                class="classify2-radio-group" @change="handleClassify2Change">
                                <template v-for="c2 in classify2_list">
                                    <el-radio class="item-classify-2" v-if="c2.name==='全部'"
                                        @change="handleAllClassiyfClick" :label="c2.id"
                                        :key="c2.id">{{c2.name}}</el-radio>
                                    <el-radio class="item-classify-2" v-else :label="c2.id"
                                        :key="c2.id">{{c2.name}}</el-radio>
                                </template>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="operation-div" v-if="classify2_list">
                        <a href="javascript:;" class="operation-a" v-show="isShowSecondExpand"
                            @click="isUnfold2 = !isUnfold2">
                            {{ isUnfold2 | unfoldText}}
                            <i :class="isUnfold2 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                        </a>
                    </div>
                </div>
                <!-- 二级分类 end -->
                <!-- 三级分类 start -->
                <div class="mb15 child-classify-div" v-if="classify3_list.length > 1">
                    <div class="box" :class="isUnfold3 ? 'unfold3' : ''">
                        <div class="f con-classify-3">
                            <el-radio-group v-model="category3_id" v-watch-height="checkThirdExpand"
                                class="classify3-radio-group" @change="handleClassify3Change">
                                <el-radio class="item-classify-3" :label="c3.id" v-for="c3 in classify3_list"
                                    :key="c3.id">{{c3.name}}</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="operation-div" v-if="classify2_list">
                        <a href="javascript:;" class="operation-a" v-show="isShowSecondExpand"
                            @click="isUnfold3 = !isUnfold3">
                            {{ isUnfold3 | unfoldText}}
                            <i :class="isUnfold3 ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                        </a>
                    </div>
                </div>
                <!-- 三级分类 end -->
                <el-form ref="formData" :model="formData" label-width="0px">
                    <el-form-item>
                        <div class="f fac fw" style="width: 1000px;">
                            <div class="f fac merchandise-screen-select-title">
                                <div class="select-name">来源</div>
                                <el-select class="selecl-sty" v-model="formData.source" placeholder="请选择">
                                    <el-option v-for="item in sourceList" :key="item.source_name"
                                        :label="item.source_name"
                                        :value="item.gather_supply_id + '-' + item.type + '-' + item.source_name">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="f fac merchandise-screen-select-title " v-if="formData.grossProfitRate != 5">
                                <div class="select-name">毛利率</div>
                                <el-select class="selecl-sty" v-model="formData.grossProfitRate" placeholder="请选择">
                                    <el-option v-for="item in grossProfitRate" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-else class="f merchandise-screen-select-eles">
                                <div class="select-name">毛利率</div>
                                <el-input class="input-sty" v-model="formData.g1" placeholder="请选择"></el-input>
                                <div class="line">-</div>
                                <div class="input-bin">
                                    <el-input class="input" v-model="formData.g2" placeholder="请选择"></el-input>
                                    <div class="bin">%</div>
                                </div>
                            </div>
                            <div class="f fac merchandise-screen-select-title" v-if="formData.profitRate != 5">
                                <div class="select-name">利润率</div>
                                <el-select class="selecl-sty" v-model="formData.profitRate" placeholder="请选择">
                                    <el-option v-for="item in profitRateOptios" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-else class="f merchandise-screen-select-eles">
                                <div class="select-name">利润率</div>
                                <el-input class="input-sty" v-model="formData.p1" placeholder="请选择"></el-input>
                                <div class="line">-</div>
                                <div class="input-bin">
                                    <el-input class="input" v-model="formData.p2" placeholder="请选择"></el-input>
                                    <div class="bin">%</div>
                                </div>
                            </div>
                            <div class="f fac merchandise-screen-select-title" v-if="formData.agreementPrice != 5">
                                <div class="select-name">协议价</div>
                                <el-select class="selecl-sty" v-model="formData.agreementPrice" placeholder="请选择">
                                    <el-option v-for="item in agreementPriceOptions" :key="item.value"
                                        :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-else class="f merchandise-screen-select-eles">
                                <div class="select-name">协议价</div>
                                <el-input class="input-sty" v-model="formData.a1" placeholder="请选择"></el-input>
                                <div class="line">-</div>
                                <div class="input-bin">
                                    <el-input class="input" v-model="formData.a2" placeholder="请选择"></el-input>
                                    <div class="bin">元</div>
                                </div>
                            </div>
                            <div class="f fac merchandise-screen-select-title">
                                <div class="select-name">指导价</div>
                                <el-select class="selecl-sty" v-model="formData.guidePrice" placeholder="请选择">
                                    <el-option v-for="item in guide_marketing_PriceOptions" :key="item.value"
                                        :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="f fac merchandise-screen-select-title" v-if="formData.discount != 5">
                                <div class="select-name">折扣</div>
                                <el-select class="selecl-sty" v-model="formData.discount" placeholder="请选择">
                                    <el-option v-for="item in discountList" :key="item.value" :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div v-else class="f merchandise-screen-select-eles">
                                <div class="select-name">折扣</div>
                                <el-input class="input-sty" v-model="formData.d1" placeholder="请选择"></el-input>
                                <div class="line">-</div>
                                <div class="input-bin">
                                    <el-input class="input" v-model="formData.d2" placeholder="请选择"></el-input>
                                    <div class="bin">折</div>
                                </div>
                            </div>
                            <div class="f fac merchandise-screen-select-title">
                                <div class="select-name">营销价</div>
                                <el-select class="selecl-sty" v-model="formData.marketingPrice" placeholder="请选择">
                                    <el-option v-for="item in guide_marketing_PriceOptions" :key="item.value"
                                        :label="item.label" :value="item.value">
                                    </el-option>
                                </el-select>
                            </div>
                            <div class="f fac merchandise-screen-select-title">
                                <div class="select-name selectc">是否已导入</div>
                                <el-select class="selecl-sty selectcs" v-model="formData.self_is_import"
                                    placeholder="请选择">
                                    <el-option label="全部" value=""></el-option>
                                    <el-option label="已导入" value="1"></el-option>
                                    <el-option label="未导入" value="0"></el-option>
                                </el-select>
                            </div>
                            <div class="f fac merchandise-screen-select-title">
                                <div class="select-name" style="width: 60px">营销属性</div>
                                <el-select class="selecl-sty" style="width: 182px" v-model="formData.marketing"
                                    placeholder="请选择">
                                    <el-option label="默认" value=""></el-option>
                                    <el-option label="新品" value="is_new"></el-option>
                                    <el-option label="热卖" value="is_hot"></el-option>
                                    <el-option label="促销" value="is_promotion"></el-option>
                                </el-select>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="">
                        <div class="f fac" style="width: 1000px;">
                            <el-button class="but-true" @click="goSearch">搜索</el-button>
                            <el-button @click="resetfun">重置</el-button>
                            <!-- <div class="btn-ai f fac ml10 shou" @click="selectAi">
                    <img style="width: 21px;" src="../../../assets/images/xuanpinai.png">
                    <div class="ml5">选品AI</div>
                  </div> -->
                            <div class="selection-num" @click="toMySel">
                                我的选品库（{{ my_center_total ? my_center_total : 0 }}）
                            </div>
                            <div class="selection-num" @click="addAlbum">加入商品专辑</div>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
            <!-- 排序 start -->
            <div class="check-all f fac fjsb">
                <div class="f">
                    <el-checkbox style="margin-right: 20px" v-model="checked" @change="checkedfun">全选</el-checkbox>
                    <template v-for="item in sort">
                        <div v-if="item.id == 1 && item.id == sortid" :key="item.id"
                            style="cursor: pointer; color: #f42121">
                            {{ item.name }}
                        </div>
                        <div v-else-if="item.id == 1 && item.id != sortid" :key="item.id" @click="newest"
                            style="cursor: pointer">
                            {{ item.name }}
                        </div>
                    </template>
                    <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                        <template v-for="item in sort">
                            <SortButton v-if="item.id != 1" :key="item.id" :value="item.value">
                                <space-between v-if="item.id == sortid" style="color: #f42121">
                                    {{ item.name }}
                                </space-between>
                                <span v-else @click="sortid = item.id">{{
                                    item.name
                                    }}</span>
                            </SortButton>
                        </template>
                    </SortButtonGroup>
                </div>
                <div class="check-all-right">
                    <div @click="tolead(0)">
                        待导入商品（{{ selection_list.length }}）
                    </div>
                    <div @click="tolead(1)">
                        导入全部筛选商品（{{
                        total ? total : 0
                        }}）
                    </div>
                </div>
            </div>
            <!-- 排序 end -->
            <!-- 商品列表 start -->
            <div v-loading="isLoading">
                <!-- <m-goods v-if="goodsList.length > 0" class="mt10">
            <goods-item v-for="item in goodsList" :key="item.id" :link="`/goodsDetail?goods_id=${item.id}`"
                        :product_id="item.id"
                        :page_setup_setting="getFramework.page_setup_setting"
                :url="item.thumb" :price="item.min_price" :title="item.title" :originPrice="item.origin_price">
            </goods-item>
        </m-goods> -->
                <div v-if="goodsList.length > 0" class="commodity">
                    <div class="commodity-card" v-for="item in goodsList" :key="item.id">
                        <div class="commodity-card-true">
                            <img class="commodityimg" :src="item.image_url" alt="" />
                            <div class="commodity-name">{{ item.title }}</div>
                            <div class="price">
                                <div class="price-box fen">
                                    <div class="price-name">供货价</div>
                                    <!-- <div class="price-num">¥{{ item.agreement_price | formatF2Y }}</div> -->
                                    <div class="price-num" v-if="$ls.getUserId()">
                                        ¥{{ item.agreement_price / 100 }}
                                    </div>
                                    <div v-else class="price-num" @click.prevent="$router.push('/login')">登录查看</div>
                                </div>
                                <div class="price-box">
                                    <div class="price-name">利润率</div>
                                    <div class="price-num">
                                        ¥{{ $fn.changeMoneyY2F(item.market_rate) }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="commodity-card-hover">
                            <div style="position: relative">
                                <div class="commodityimg-hover">
                                    <img :src="item.image_url" alt="" />
                                </div>
                                <div class="commodityimg-hover-button">
                                    <div style="border-right: 1px solid #ffffff" @click="goodsDetail(item.id)">
                                        查看详情
                                    </div>
                                    <div v-if="item.is_import == 1">已导入</div>
                                    <div v-else-if="
                                    !selection_list.find(
                                        (val) => val == item.id,
                                    )
                                " @click="addOption(item.id)">
                                        加入选品
                                    </div>
                                    <div v-else @click="delOption(item.id)">
                                        移除选品
                                    </div>
                                </div>
                            </div>
                            <div class="commodity-name">{{ item.title }}</div>
                            <div class="price">
                                <div class="price-box fen">
                                    <div class="price-name">供货价</div>
                                    <div class="price-num" v-if="$ls.getUserId()">
                                        ¥{{ item.agreement_price / 100 }}
                                    </div>
                                    <div v-else class="price-num" @click.prevent="$router.push('/login')">登录查看</div>
                                </div>
                                <div class="price-box">
                                    <div class="price-name">利润率</div>
                                    <div class="price-num">
                                        ¥{{ $fn.changeMoneyY2F(item.market_rate) }}%
                                    </div>
                                </div>
                            </div>
                            <div class="det-price">
                                <div class="det-price-box">
                                    <div class="det-price-name">指导价</div>
                                    <!-- <div class="det-price-num">¥{{ item.guide_price | formatF2Y }}</div> -->
                                    <div class="det-price-num">
                                        ¥{{ item.guide_price / 100 }}
                                    </div>
                                </div>
                                <div class="det-price-box">
                                    <div class="det-price-name">最小利润</div>
                                    <div class="det-price-num">
                                        ¥{{ item.min_profit / 100 }}
                                    </div>
                                </div>
                                <div class="det-price-box">
                                    <div class="det-price-name">折扣</div>
                                    <!-- <div class="det-price-num">{{  item.profit | formatF2Y }}</div> -->
                                    <div class="det-price-num">
                                        {{ item.profit / 100 }}
                                    </div>
                                </div>
                                <div class="det-price-box">
                                    <div class="det-price-name">毛利率</div>
                                    <div class="det-price-num">
                                        {{
                                        $fn.changeMoneyY2F(
                                        item.gross_profit_rate,
                                        )
                                        }}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            <skeletonGoods v-if="hasMore === true && isLoading === false" v-for="item in 10"></skeletonGoods>

                </div>
                <m-empty v-else></m-empty>
            </div>
            <!-- 商品列表 end -->
            <!-- <Pagination :total="total" @pagination="pagination" :page="page" :limit="pageSize"
                layout="total, prev, pager, next">
            </Pagination> -->
        </el-col>
    </el-row>

    <!-- 导入对话框 -->
    <el-dialog title="导入商品" :visible.sync="centerDialogVisible" @close="nolead">
        <div class="title-con" v-if="!centerDialogVisibleID">
            当前待导入商品数量：{{ selection_list.length }}
        </div>
        <div class="title-con" v-else>
            当前待导入商品数量：{{ total }}
        </div>
        <div class="title-text">
            点击导入，将待导入商品加入选品库，通过API对接您的系统，可一键导入您选品库的所有商品!
        </div>
        <div class="title-button">
            <el-button class="ackbutton" @click="acktrue">导入</el-button>
            <el-button @click="nolead">取消</el-button>
        </div>
    </el-dialog>
    <!-- 加入商品专辑 -->
    <albumDialog ref="albumDialog" :visible.sync="dialog.visible" :propRow="dialog.propRow"></albumDialog>
    <selectDialog ref="selectDialog"></selectDialog>
</div>
import rechargeDialog from "../../personalCenter/myWallet/components/rechargeDialog";
import QRCode from 'qrcodejs2'


let polling = null;
export default {
    name: "paymentIndex",
    components: { rechargeDialog },
    data() {
        return {
            payTabs: [],
            code: 0,
            payType: "",
            pay_type: "", // 区分汇聚支付微信 支付宝 快捷方式
            wechat_qr_code: "",
            qr_code: "",
            // 余额不足状态
            funds: false,
            funds2: false,
            // 1 = 扫码支付  2 = 余额支付
            // 余额
            purchasing_balance: "",
            settlement_balance: "",
            pay_status: 2,
            // qr_code: "",
            time: "",
            flag: false,
            // 结束时间
            endTime: 0,
            // 订单编号
            order_number: "",
            table_isShow: false,
            qrcodeImg: "",
            orders: [],
            payments: [],
            orderAmount: "",
            para: null,
            buy_id: 0,
            pay_info_id: null,
            payTabs2: [],
            lakala_sn: null, //拉卡拉订单编号
        };
    },
    destroyed() {
        clearInterval(polling);
    },
    mounted() {
        this.getTabs();
        this.getTabs2();
        console.log(this.$route.query);
        // this.buy_id = parseInt(this.$route.query.buy_id);
    },
    methods: {
        /* getTabs2() {
            this.$get('/finance/getPluginPayType').then(res => {
                if (res.code === 0) {
                    this.payTabs2 = res.data || []
                }
                console.log(res, '????');
            })
        }, */
        getTabs() {
            this.$post("/payment/getPayment", { platform: 'pc' }).then((res) => {
                if (res.code === 0) {
                    /* const payList = [];
                    const regPC = RegExp(/0/);
                    const regAll = RegExp(/all/);
                    const regNewAll = RegExp(/platform_balance/);
                    if (res.data.PaymentList && res.data.PaymentList.length > 0) {
                        res.data.PaymentList.forEach((item) => {
                            if (regPC.test(item.type) || regAll.test(item.type) || regNewAll.test(item.type)) {
                                payList.push({ ...item });
                            }
                        });
                    }
                    this.code = payList && payList.length ? payList[0].code : []; */
                    this.code = res.data.PaymentList[0].code
                    this.payTabs = res.data.PaymentList;
                    this.initPara();
                }
            });
        },
        handleTabsClick(code, type = "", pay_type = "") {
            // 清空之前的二维码
            if (this.$refs.qrCodeALiPayUrl) {
                this.$refs.qrCodeALiPayUrl.innerHTML = '';
            }
            if (this.$refs.qrCodeWeChatUrl) {
                this.$refs.qrCodeWeChatUrl.innerHTML = '';
            }
            this.code = code;
            this.payType = type
            this.pay_type = pay_type
            this.orderOperationPay();
            if (code != 5) {
                clearInterval(polling);
            }
        },
        // 获取微信支付
        getWechat(data) {
            this.$post("/finance/wechatPay", {
                pay_sn: data.pay_info.pay_sn,
                // amount: data.pay_info.amount,
                amount: 1,
                pay_type: this.code,
            }).then((res) => {
                if (res.code === 0) {
                    this.qr_code = res.data.rd_Pic;
                    this.initPayData(data);
                }
            });
        },
        // 打开充值dialog
        openRechargeDialog() {
            this.$refs.rechargeDialog.isShow = true;
        },
        //初始化参数
        initPara() {

            console.log();
            if (typeof this.$route.query.para === 'string') {
                this.para = this.$route.query.para.split(',')
            } else {
                this.para = this.$route.query.para
            }
            console.log(this.para);
            this.orderOperationPay();
            /*if (this.para != null) {
                      this.orderOperationPay();
                  } else {
                      this.orderConfirm();
                  }*/
        },

        //订单提交
        /* orderConfirm() {
                let that = this;
                let para = {
                    "buy_id": parseInt(that.$route.query.buy_id)
                }
                this.$post("/trade/confirm", para).then((res) => {
                    if (res.code === 0) {
                        that.initPayData(res.data);
                    }
                });
            }, */

        //订单支付
        orderOperationPay() {
            let that = this;
            let para = {
                order_ids: [],
            };
            console.log('this.para', this.para);
            if (this.para) {
                if (Array.isArray(this.para)) {
                    this.para.forEach(item => {
                        para.order_ids.push(parseInt(item))
                    })
                } else {
                    para.order_ids.push(parseInt(this.para))
                }
            }
            this.$post("/trade/cashier", para)
                .then(function (res) {
                    if (res.code == 0) {
                        // 扫码支付
                        if (that.code === 9999) {
                            that.getWechat(res.data);
                            // that.initPayData(res.data);
                        } else if (that.code === 13) {
                            that.getWechatPayCode(res.data)
                        } else if (that.code === -3) {
                            let data = res.data
                            that.pay_info_id = data.pay_info.id;
                            that.orders = data.pay_info.orders;
                            that.payments = data.payments;
                            that.orderAmount = data.pay_info.amount;
                        } else if (that.code === 7000) {
                            console.log(res.data.pay_info.pay_sn)
                            that.lakala_sn = res.data.pay_info.pay_sn
                        } else if (that.pay_type === 'ALIPAY') {
                            that.juHeALIPAY(res.data)
                        } else if (that.pay_type === 'WECHAT') {
                            that.juHeWECHAT(res.data)
                        } else {
                            // 余额支付
                            that.getFunds(res.data);
                        }
                        //that.getPayQRCode(res.data)
                    }
                })
                .catch(function (res) {
                    console.log(res);
                });
        },
        // 获取微信扫码支付
        getWechatPayCode(data) {
            let params = {
                pay_type: this.code,
                amount: data.pay_info.amount,
                pay_info_id: data.pay_info.id
            }
            this.$post("/finance/wechatPayment", params).then(res => {
                if (res.code === 0) {
                    this.wechat_qr_code = res.data
                    this.initPayData(data)
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 余额查询
        getFunds(data) {
            this.orders = data.pay_info.orders;
            this.payments = data.payments;
            this.orderAmount = data.pay_info.amount;
            this.pay_info_id = data.pay_info.id;
            // this.timeInit(data.pay_info.expire_at);
            // this.$post("/finance/getUserBalance", {uid: this.$ls.getUserId()}).then(res => {
            this.$post("/finance/getUserBalance").then((res) => {
                if (res.code === 0) {
                    let obj = res.data.find(item => item.type === this.code)
                    this.purchasing_balance = obj.purchasing_balance;
                    this.settlement_balance = obj.purchasing_balance;
                    /*if (this.code === 1 || this.code === 2) {
                        // 结算余额
                        this.purchasing_balance = res.data[0].purchasing_balance;
                        this.settlement_balance = res.data[1].purchasing_balance;
                    }
                    if (this.code === 3) {
                        this.purchasing_balance = res.data[2].purchasing_balance;
                    }*/
                    // 余额不足
                    if (this.purchasing_balance < data.pay_info.amount) {
                        this.funds = true;
                    } else {
                        this.funds = false;
                    }
                    if (this.settlement_balance < data.pay_info.amount) {
                        this.funds2 = true;
                    } else {
                        this.funds2 = false;
                    }
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        // 余额扣款
        fundsDeduct() {
            /* this.funds = true;
            this.funds2 = true; */
            this.$post("/finance/balanceDeduction", {
                pay_type: this.code,
                amount: this.orderAmount,
                // uid: this.$ls.getUserId()
                pay_info_id: this.pay_info_id,
            })
                .then((res) => {
                    if (res.code === 0) {
                        if (res.data.status === 1) {
                            this.$message.success(res.msg);
                            this.$router.push({
                                path: "/personalCenter/myOrder",
                            });
                            /*setTimeout(() => {
                                          this.$router.push({
                                              path: "/personalCenter/myOrder"
                                          })
                                      }, 2000)*/
                        }
                    } else {
                        this.$message.error(res.msg);
                        this.funds = false;
                        this.funds2 = false;
                    }
                })
                .catch(() => {
                    this.funds = false;
                    this.funds2 = false;
                });
        },
        fundsDeducts() {
            this.funds = true;
            this.funds2 = true;
            this.$post("/payment/getPay", {
                pay_type: 7000,
                amount: this.orderAmount,
                pay_sn: this.lakala_sn.toString(),
            }).then(res => {
                window.open(res.data.url)
                this.$router.push({
                    path: "/personalCenter/myOrder",
                });
            })
                .catch(() => {
                    this.funds = false;
                    this.funds2 = false;
                });
        },
        // 聚合支付
        async fundsDeductJuHe() {
            const res = await this.$post("/finance/aggregatePaymentPay", {
                pay_type: parseInt(this.code),
                amount: this.orderAmount,
                pay_info_id: this.pay_info_id,
                type: 'P'
            })
            if (res.code === 0) {
                window.open(res.data.fields.counter_url)
                this.$router.push({
                    path: "/personalCenter/myOrder",
                });
            } else {
                this.$message.error(res.msg)
            }
        },
        // 聚合支付宝
        async juHeALIPAY(data) {
            this.$refs.qrCodeALiPayUrl.innerHTML = '';
            const res = await this.$post("/finance/aggregatePaymentPay", {
                pay_type: parseInt(this.code),
                amount: this.orderAmount,
                pay_info_id: data.pay_info.id,
                type: 'P'
            })
            if (res.code === 0) {
                new QRCode(this.$refs.qrCodeALiPayUrl, {
                    text: res.data.fields.code, // 转换为二维码的内容
                    width: 160,
                    height: 160,
                    colorDark: '#000000',
                    colorLight: '#ffffff',
                    correctLevel: QRCode.CorrectLevel.H
                });
                this.initPayData(data);
            }
        },
        // 聚合微信
        async juHeWECHAT(data) {
            const res = await this.$post('/finance/paySnOrderStatus', {
                pay_info_id: data.pay_info.id
            })
            if (res.code === 0) {
                let origin = window.location.origin
                console.log('qweqwewqe', window.location);
                this.$refs.qrCodeWeChatUrl.innerHTML = '';
                new QRCode(this.$refs.qrCodeWeChatUrl, {
                    text: origin + '/h5/?menu#/packageA/goodsorder/juHeOrderPay/juHeOrderPay?amount=' + this.orderAmount + '&pay_type=' + this.code + '&pay_info_id=' + data.pay_info.id, // 转换为二维码的内容
                    width: 160,
                    height: 160,
                    colorDark: '#000000',
                    colorLight: '#ffffff',
                    correctLevel: QRCode.CorrectLevel.H
                });
                this.initPayData(data);
            } else {
                this.$message.error(res.msg)
            }
        },
        // 获取支付二维码 * 充值
        /*getPayQRCode(data) {
                let params = {
                    pay_sn: data.pay_info.pay_sn,
                    amount: data.pay_info.amount
                }
                this.$post("/finance/userJoinRecharge", params).then(res => {
                    if (res.code === 0) {
                        this.orders = data.pay_info.orders;
                        this.payments = data.payments;
                        this.orderAmount = data.pay_info.amount;
                        this.timeInit(data.pay_info.expire_at);
                        this.qr_code = res.data.rd_Pic

                        polling = setInterval(() => {
                            this.$post("/finance/getPayStatus", { pay_sn: data.pay_info.pay_sn }).then(res => {
                                if (res.code === 0) {
                                    // pay_status 1 = 已支付 0 = 未支付
                                    if (res.data.pay_status === 1) {
                                        clearInterval(polling)
                                        this.$message.success(res.msg)
                                        this.$router.push({
                                            path: "/personalCenter/myOrder"
                                        })
                                    }
                                }
                            })
                        }, 1500)
                    }
                })
            },*/
        //初始化支付参数
        initPayData(data) {
            this.orders = data.pay_info.orders;
            this.payments = data.payments;
            this.orderAmount = data.pay_info.amount;
            // this.timeInit(data.pay_info.expire_at);

            polling = setInterval(() => {
                this.$post("/finance/getPayStatus", {
                    pay_sn: data.pay_info.pay_sn,
                }).then((res) => {
                    if (res.code === 0) {
                        // pay_status 1 = 已支付 0 = 未支付
                        if (res.data.pay_status === 1) {
                            clearInterval(polling);
                            this.$message.success(res.msg);
                            this.$router.push({
                                path: "/personalCenter/myOrder",
                            });
                        }
                    }
                });
            }, 1500);
        },

        timeInit(endTime) {
            this.endTime = endTime;
            let time = setInterval(() => {
                if (this.flag == true) {
                    clearInterval(time);
                }
                this.timeDown();
            }, 1000);
        },

        timeDown() {
            this.endTime--;
            const endTime2 = new Date(this.endTime);
            let m = this.formate(parseInt((endTime2 / 60) % 60));
            let s = this.formate(parseInt(endTime2 % 60));
            if (this.endTime <= 0) {
                this.flag = true;
            }
            this.time = `${m}分${s}秒`;
        },
        formate(time) {
            if (time >= 10) {
                return time;
            } else {
                return `0${time}`;
            }
        },
    },
};

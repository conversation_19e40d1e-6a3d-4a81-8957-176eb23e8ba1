.detailHead {
    position: fixed;
    top: 0;
    width: 100%;
    border-bottom: 1px solid red;
    z-index: 999;
    background-color: white;

    .head-box {
        .left-box {
            width: 235px;

            .shop-top-box {
                .shop-logo-box {
                    width: 50px;
                    height: 50px;
                    margin-right: 15px;
                }

                .shop-ta-box {
                    .shop-title-p {
                        font-weight: 600;
                        font-size: 18px;
                        margin-bottom: 5px;
                    }

                    .shop-addrs-p {
                        font-size: 12px;
                        margin-top: 5px;
                    }
                }
            }
        }

        .right-box {
            position: relative;
            margin-left: 10px;

            .add-shoping-cart-btn {
                position: absolute;
                right: 0;
                top: 8px;
                width: 178px;
                height: 46px;
                border-radius: 0;
                background-color: #f42121;
                border: 1px solid #f42121;
                font-size: 18px;
            }

            /*头部tabs*/
            ::v-deep .el-tabs {
                .el-tabs__header {
                    margin: 0;

                    .el-tabs__nav-wrap::after {
                        background-color: transparent;
                    }

                    .el-tabs__active-bar {
                        height: 4px;
                        background-color: #f42121;
                    }

                    .el-tabs__item {
                        font-size: 20px;
                        padding: 0;
                        margin-bottom: 12px;
                        width: 120px;
                        text-align: center;

                        &:hover {
                            color: #303133;
                        }

                        &.is-active {
                            color: #303133;
                        }
                    }
                }
            }
        }
    }
}

.goodsDetail-box {
    /*商品图片/规格部分*/
    .goods-head-box {
        /*商品缩略图*/
        .m-swiper-box {
            width: 438px;

            .big-img-box {
                height: 436px;
                border: 1px solid #cccccc;
                margin-bottom: 10px;
                background-color: #000000;
            }

            /*缩略图部分*/
            .slt-box {
                width: 100%;
                height: 75px;

                .tab-btn {
                    text-align: center;
                    width: 21px;
                    height: 73px;
                    line-height: 75px;
                    border: 1px solid #dfdfdf;

                    i {
                        font-size: 18px;
                        color: #cccccc;
                    }
                }

                .imgs-box {
                    width: 382px;
                    height: 75px;
                    margin: 0 auto;
                    overflow: hidden;
                    position: relative;

                    & > div {
                        width: 100%;
                        position: absolute;
                        top: 0;
                        left: 0;
                        transition: 0.5s;

                        .img-item {
                            margin-right: 1.5px;
                            width: 75px;
                            height: 75px;
                            flex-shrink: 0;

                            &.imgActive {
                                width: 73px;
                                height: 73px;
                                border: 1px solid #000000;
                            }
                        }
                    }
                }
            }
        }

        /*商品规格部分*/
        .goods-head-right-box {
            margin-left: 32px;

            /*商品标题*/
            .goods-title {
                font-size: 16px;
                font-weight: bold;
                padding-top: 5px;
            }

            /*商品价格部分*/
            .price-box {
                margin: 25px 0 21px 0;
                padding: 15px 10px;
                background-color: #f6f6f6;

                .discounts-p {
                    color: #cf2444;
                    padding: 5px;
                    border: 1px solid #cf2444;
                }

                .money-span {
                    color: $money-color;
                    font-weight: 500;
                    font-size: 24px;

                    .symbol-span {
                        font-size: 16px;
                    }

                    .x-span {
                        display: inline-block;
                        margin: 0 10px;
                        font-size: 20px;
                    }
                }

                .retail-price {
                    margin-left: 20px;
                    color: #979797;
                    text-decoration: line-through;
                }

                .price-right {
                    div:nth-child(1) {
                        padding-right: 22px;
                        margin-right: 22px;
                        border-right: 1px solid #d4d4d4;
                    }

                    & > div {
                        i {
                            font-size: 26px;
                            font-weight: 500;
                            margin-right: 7px;
                        }

                        & > div p:nth-child(2) {
                            color: #f42121;
                        }
                    }

                    p {
                        font-size: 16px;
                    }
                }
            }

            /*组合部分*/
            .goods-group-box {
                padding-left: 10px;

                p.text-j {
                    line-height: 37px;
                }

                ::v-deep .el-radio-group {
                    .el-radio {
                        margin-bottom: 10px;

                        &.is-checked {
                            .el-radio__label {
                                color: #606266;
                                border: 1px solid red;
                            }
                        }

                        .el-radio__label {
                            color: #606266;
                            display: inline-block;
                            // width: 120px;
                            height: 35px;
                            line-height: 35px;
                            text-align: center;
                            // padding: 0;
                            padding: 0 20px;
                            border: 1px solid #dfdfdf;
                            margin-right: 20px;
                        }

                        .el-radio__input {
                            display: none;
                        }
                    }
                }
            }

            // 物流
            .logistics-box {
                padding-left: 10px;

                .logistics-wrapper {
                    .selectAreaSlot {
                    }
                }
            }

            //  规格
            .specification-box {
                padding-left: 10px;

                & > p {
                    padding-top: 5px;
                }

                & > div {
                    width: 100%;

                    .el-row {
                        display: flex;
                        align-items: center;

                        .skus-img-box {
                            width: 30px;
                            height: 30px;
                            min-width: 30px;
                            min-height: 30px;
                            cursor: pointer;
                        }

                        p.sku-p {
                            line-height: 18px;
                            // white-space: nowrap;
                            overflow: hidden;
                            word-break: normal;
                            //width: 9rem;
                            margin-left: -10px;

                            // text-overflow: ellipsis;
                            /*&.place-p{
                span{
                  max-width: 127px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }*/
                        }

                        p.sku-p1 {
                            margin-left: 35px;
                            white-space: nowrap;
                        }

                        p.sku-p2 {
                            margin-left: 20px;
                            //white-space: nowrap;
                        }

                        p.place-p {
                            span {
                                display: inline-block;
                                max-width: 127px;
                                /*white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;*/
                            }

                            .copy-a {
                                margin-left: 10px;
                                color: #f42222;
                            }
                        }

                        .el-input-number {
                            width: 100%;
                        }

                        &:nth-child(1) {
                            padding-top: 0px;
                        }

                        &:last-child {
                            border: 0;
                        }

                        border-bottom: 1px solid #f6f6f6;
                        margin: 0 !important;
                        padding-bottom: 5px;
                        padding-top: 5px;
                    }
                }
            }

            // 提示购买
            .tip {
                position: fixed;
                z-index: 99999;
                left: 45%;
                top: 20%;
                width: 65px;
                height: 30px;
                text-align: center;
                font-size: 12px;
                line-height: 30px;
                border-radius: 3px;
                background-color: rgba(1, 1, 1, 0.6);
                color: #ffffff;
            }

            // 讲师
            .lecturer-box {
                margin-bottom: 60px;
                .lecturer-message {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: pointer;
                    border: 1px solid #f2f2f2;
                }
                .lecturer-msg {
                    display: flex;
                    justify-content: start;
                    align-items: center;
                    .lecturer-img {
                        height: 60px;
                        img {
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            margin-top: 10px;
                            margin-right: 10px;
                            margin-left: 10px;
                        }
                    }
                    .lecturer-describes {
                        .describes-name {
                            font-weight: 600;
                            margin-right: 10px;
                        }
                        .describes-title {
                            .describes-tag {
                                margin-right: 5px;
                            }
                        }
                        .describes-msg {
                            font-size: 12px;
                            color: rgba(1, 1, 1, 0.5);
                        }
                    }
                }
                .lecturer-icon {
                    font-size: 18px;
                    margin-right: 20px;
                    color: rgba(1, 1, 1, 0.5);
                }
                .course-time {
                    margin-top: 10px;
                    color: rgba(1, 1, 1, 0.5);
                }
            }

            // 购买课程
            .purchase {
                margin-top: 20px;
                .purchase-view {
                    margin-top: 10px;
                    color: rgba(1, 1, 1, 0.5);
                }
                .purchase-time {
                    margin-top: 10px;
                    color: rgba(1, 1, 1, 0.5);
                }
                .purchase-detail {
                    margin-top: 20px;
                    color: #f61d22;
                    border-color: #f61d22;
                }
            }

            //  按钮操作部分
            .operation-box {
                .el-button {
                    border-radius: 0;
                    width: 110px;
                    height: 40px;
                    font-size: 16px;
                    padding: 0;
                    border-radius: 5px;
                    font-weight: 400;
                    &.shoping-btn {
                        background-color: #f42121;
                        border: 1px solid #f42121;
                    }
                    &.btn-hollow {
                        border: 1px solid #f42121;
                        &:hover {
                            color: #606266;
                            background: #fff;
                        }
                    }
                    &.shoping-cart-btn {
                        background-color: #ff900b;
                        border: 1px solid #ff900b;
                    }

                    &.album-btn {
                        margin-right: 20px;
                        color: #fff;
                        background-color: #44be95;
                        border: 1px solid #44be95;
                    }
                }

                a {
                    color: #333333;

                    i {
                        font-size: 21px;
                        margin-right: 11px;
                    }

                    &.is_check {
                        color: red;
                    }
                }
            }
        }
    }

    /*相关推荐*/
    .recommend-box {
        margin-top: 60px;
    }

    /*中间部分*/
    .goods-cent-box {
        /*左侧部分*/
        .goods-cent-l-box {
            /*店铺*/
            .shop-box {
                width: 235px;
                border: 1px solid #f7f7f7;

                .shop-top-box {
                    margin: 20px;

                    .shop-logo-box {
                        width: 50px;
                        height: 50px;
                        margin-right: 15px;
                    }

                    .shop-ta-box {
                        .shop-title-p {
                            font-weight: 600;
                            font-size: 18px;
                            margin-bottom: 5px;
                        }

                        .shop-addrs-p {
                            font-size: 12px;
                            margin-top: 5px;
                        }
                    }
                }

                .shop-c-box {
                    margin: 0 15px 20px 15px;

                    p {
                        span:nth-child(1) {
                            display: inline-block;
                            width: 60px;
                        }

                        span:nth-child(2) {
                            color: #f42121;
                            font-size: 16px;
                        }
                    }
                }

                .hx {
                    height: 1px;
                    width: 217px;
                    margin: 0 auto;
                    background-color: #f1f1f1;
                }

                .shop-b-box {
                    margin: 20px 10px 0 10px;

                    .score-box {
                        text-align: center;

                        p:last-child {
                            margin-top: 15px;
                            color: #df3030;
                            margin-bottom: 17px;
                        }
                    }
                }

                .btn-b {
                    margin: 0 10px 25px 10px;

                    .el-button {
                        border-radius: 0;
                        width: 87px;
                        font-weight: 600;
                    }

                    .entrance-btn {
                        background-color: #ffe3e3;
                        border: 1px solid #ff0000;
                        color: #ff0000;
                    }

                    .collect-btn {
                        background-color: white;
                        border: 1px solid #c0c0c0;
                        color: #333333;
                    }
                }
            }

            /*店铺推荐*/
            .shop-recommend-box {
                ::v-deep .goods-item {
                    .special-offer-item {
                        border: 1px solid #f7f7f7 !important;
                    }
                }
            }
        }

        /*右侧部分*/
        .goods-cent-r-box {
            margin-left: 10px;
            // position: relative;

            /*自定义tabs*/
            ::v-deep .el-tabs {
                .el-tabs__header {
                    .el-tabs__active-bar {
                        height: 4px;
                        background-color: #f42121;
                    }

                    .el-tabs__item {
                        font-size: 20px;
                        padding: 0;
                        margin-bottom: 12px;
                        width: 120px;
                        text-align: center;

                        &:hover {
                            color: #303133;
                        }

                        &.is-active {
                            color: #303133;
                        }
                    }
                }
            }
        }
    }
}

.el-popover {
    .area-wrapper {
        .pointer {
            cursor: pointer;
        }

        .row-div {
            &:first-child {
                margin-top: 0;
            }

            .city-div {
                padding: 0px 15px 10px 15px;
                background-color: #fffbeeff;
                border: 1px solid #ffe287ff;

                p {
                    padding: 3px 8px;

                    &.active {
                        background-color: #f4ae4bff;
                        color: #fff;
                    }
                }
            }

            .area-item {
                width: 120px;

                span.active {
                    color: #f8c277ff;
                }
            }
        }

        .bottom-wrapper {
            padding: 10px;
            background-color: #f6f6f6;

            .purchase-wrapper {
                margin-top: 10px;
                .purchase-l {
                }

                .purchase-r {
                    width: 80px;
                    height: 60px;
                    background-color: #fff;
                }
            }
        }
    }
}

.pt20 {
    padding-top: 20px;
}

.pb20 {
    padding-bottom: 20px;
}

.text-j {
    // padding-top: 0 !important;
    min-width: 60px;
    /* display: flex;
  align-items: center; */
    // text-align: justify;
    // text-align-last: justify;
    margin-right: 10px;
}

h2.title-h2 {
    font-weight: bold;
    margin-top: 72px;
    margin-bottom: 27px;
    font-size: 18px;
}

.special-offer-box {
    display: grid;
    grid-template-columns: repeat(5, 230px);
    grid-row-gap: 13px;
    grid-column-gap: 13px;

    .special-offer-item {
        height: 345px;
        background-color: white;
        border: 1px solid #f7f7f7 !important;

        .special-offer-product-img-box {
            height: 230px;
            display: flex;
            justify-content: center;
            align-items: center;

            .el-image {
                width: 150px !important;
                height: 150px !important;
            }
        }

        .special-offer-b-box {
            padding: 10px;
            font-size: 14px;

            p {
                height: 38px;
            }

            span.special-offer-title {
                color: #a5a5a5;
                margin-right: 10px;
            }

            span.special-offer-number {
                color: $theme-color;
            }
        }
    }
}

.special-offer-box.one {
    display: grid;
    grid-template-columns: repeat(1, 230px);
    grid-row-gap: 13px;
    grid-column-gap: 13px;
}
.mtb20 {
    margin-top: 20px;
    margin-bottom: 20px;
}
.mt10 {
    margin-top: 10px;
}
::v-deep .el-tag {
    border-color: #fff7d5;
}

::v-deep .el-radio-group {
  line-height: 30px;

  .el-radio {
    margin: 0 6px;

    &.is-checked {
      .el-radio__label {
        color: $theme-color;
      }
    }

    .el-radio__input {
      display: none;
    }
  }
}

.sort-group {
  display: flex;
  justify-content: flex-start;

  .sort-item {
    margin-right: 20px;

    &:first-child {
      margin-left: 16px;
    }
  }
}

.shop-item-div {
  width: 574px;
  margin-top: 15px;
  background-color: #ffffff;
  border-radius: 5px;
  padding: 8px;

  &:nth-child(even) {
    margin-left: 20px;
  }

  .top-div {
    .statistics {
    }
  }

  .bottom-div {
    background-color: #f7f7f7;
    padding: 5px;

    .goods-item {
      width: 136px;
      margin-right: 6.5px;
      background-color: #ffffff;

      .content-div {
        padding: 0 10px;

        p.title {
          margin-top: 10px;
          height: 40px;
          color: #333333;
        }

        .price-div {
          font-size: 12px;
          span.price-title {
            color: #a5a5a5;
            margin-right: 2px;
          }

          span.price-num {
            color: #db0000;
          }
          .del-price{
            margin-left: 2px;
            color: #b5b5b5;
          }
        }

      }

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
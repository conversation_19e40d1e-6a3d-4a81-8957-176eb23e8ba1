<template>
  <el-dialog
      title="修改密码"
      :visible="isShow"
      width="500px"
      :before-close="handleClose">
    <el-form :model="formData" label-width="120px" ref="form" :rules="rules">
      <el-form-item label="账号/手机号:" prop="username">
        <el-input v-model="formData.username" placeholder="请输入账号/手机号"></el-input>
      </el-form-item>
      <el-form-item label="旧密码:" prop="password">
        <el-input v-model="formData.password" placeholder="请输入旧密码" show-password></el-input>
      </el-form-item>
      <el-form-item label="新密码:" prop="new_password">
        <el-input v-model="formData.new_password" placeholder="请输入新密码" show-password></el-input>
      </el-form-item>
      <el-form-item label="确认密码:" prop="checkPassword">
        <el-input v-model="formData.checkPassword" placeholder="请再次输入密码" show-password></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="confirm" class="confirm-btn">确 定</el-button>
      <el-button @click="handleClose" class="cancel-btn">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "findPasswordDialog",
  data() {
    return {
      isShow: false,
      formData: {
        username: "",
        password: "",
        new_password: "",
        checkPassword: ""
      },
      rules: {
        username: {required: true, message: "请输入账号/手机号", trigger: "blur"},
        password: {required: true, message: "请输入旧密码", trigger: "blur"},
        new_password: {required: true, message: "请输入新密码", trigger: "blur"},
        checkPassword: [
          {
            required: true, trigger: "blur", validator: (rule, value, callback) => {
              if (value !== this.formData.new_password) {
                callback(new Error('两次输入密码不一致!'));
              } else {
                callback();
              }
            }
          },

        ]
      }
    }
  },
  methods: {
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.formData = {
          username: "",
          password: "",
          new_password: ""
        }
      }
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false;
        delete this.formData.checkPassword
        this.$post("/user/changePassword", this.formData).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.handleClose()
          }else{
            this.$message.error(res.msg)
          }
        })
      })
    }
  }
}
</script>

<style scoped>

</style>
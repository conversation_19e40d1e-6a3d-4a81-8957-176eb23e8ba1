<div>
    <login-item>
        <span class="label">已有账号,请</span>
        <a class="re-a" href="javascript:;" @click="jump">登录</a>
        <el-form :model="loginForm">
            <el-form-item prop="username">
                <el-input placeholder="请填写手机号" v-model="loginForm.username">
                    <i class="iconfont icon-pc_line_member" slot="prefix"></i>
                </el-input>
            </el-form-item>
            <el-form-item v-if="settingData.need_password !== 1" prop="password" class="code-box">
                <el-input v-model="loginForm.password" show-password placeholder="请输入密码">
                    <i class="iconfont icon-pc_line_member" slot="prefix"></i>
                </el-input>
            </el-form-item>
            <el-form-item prop="captcha_code" class="code-box">
                <el-input placeholder="请填写验证码" v-model="loginForm.captcha_code">
                    <i class="iconfont icon-pc_line_member" slot="prefix"></i>
                    <a slot="append" href="javascript:;" @click="sendCode" v-if="codeIsShow">发送验证码</a>
                    <p v-else class="countDown-p" slot="append">{{ countDown }}s</p>
                </el-input>
            </el-form-item>
            <el-form-item prop="invite_code" class="yqcode-box">
                <el-input v-model="loginForm.invite_code" :placeholder="settingData.required_invite_code === 1 ? '请填写邀请码' : '请填写邀请码(选填)'">
                    <i class="iconfont icon-pc_line_member" slot="prefix"></i>
                </el-input>
            </el-form-item>
            <!-- 第三部 通过是否显示默认邀请码展示相对应的文字以及邀请码 -->
             <p class="yqcode-p" v-if="settingData.show_default_invite_code === 1">没有邀请码,请填写官方邀请码 {{settingData.default_invite_code}}</p>
            <el-form-item class="login-btn-box">
                <el-button :disabled="!loginForm.captcha_code || !loginForm.captcha_key" @click="$clicks(register)">注册</el-button>
                <!-- <el-button  @click="$clicks(register)">注册</el-button> -->
            </el-form-item>
        </el-form>
    </login-item>
    <el-dialog :visible="isShow" title="邀请人信息确认" width="500px" :close-on-click-modal="false"
        :close-on-press-escape="false" :before-close="handleClose">
        <div class="f">
            <m-acatar :size="60" :src="inviteUserData.avatar"></m-acatar>
            <div class="user-info-box ml20">
                姓名: {{inviteUserData.nickname}} <br>
                手机号: {{inviteUserData.mobile}} <br>
                邀请码: {{inviteUserData.invite_code}}
            </div>
        </div>
        <div slot="footer">
            <el-button class="red-btn" size="small" @click="handleClose">确 认</el-button>
        </div>
    </el-dialog>
</div>
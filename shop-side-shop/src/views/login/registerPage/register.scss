span.label {
  color: #AAAAAA;
}
p.countDown-p{
  text-align: center;
}
a.re-a {
  display: inline-block;
  height: 21px;
  border-bottom: 1px solid #F42121;
  margin-left: 8px;
  color: #F42121;
}

.el-form {
  margin-top: 15px;

  ::v-deep .el-input {
    position: relative;

    .el-input-group__append {
      position: absolute;
      right: 85px;
      top: 0;
      margin: 0;
      padding: 0;
      border: 0;
      height: 40px;
      line-height: 40px;

      a {
        color: #F42121;
      }
    }

    .el-input__inner {
      width: 275px;
      background: #F8F8F8;
      border-radius: 20px;
      padding-left: 61px;
      border: 1px solid #F8F8F8;
    }

    span.el-input__prefix {
      line-height: 40px;
      left: 14px;

      i.iconfont {
        color: #FF625D;
        font-weight: bold;
      }
    }
  }

  .code-box {
    margin-top: 15px;
  }

  .yqcode-box {
    margin-top: 23px;
  }

  .yqcode-p {
    font-size: 12px;
    color: #AAAAAA;
    margin-top: 11px;
  }

  .login-btn-box {
    margin-top: 19px;

    .el-button {
      padding: 0;
      width: 100%;
      height: 37px;
      background: #F42121;
      border-radius: 19px;
      border-color: #F42121;
      color: white;
      font-size: 16px;
      &.is-disabled{
        background: #e7e5e5;
        border-color: #e7e5e5;
        color: #b1b4bb;
      }
    }
  }
}
::v-deep .el-dialog {
  font-size: 16px;
  border-radius: 10px;

  .el-dialog__header {
    border-bottom: 1px solid #F0F0F0;
  }

  .el-dialog__body {
    .user-info-box{
      
    }  
  } 
}
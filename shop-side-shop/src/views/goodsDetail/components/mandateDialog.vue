<template>
    <div>
        <el-dialog width="912px" title="授权" :visible="isShow" @close="onClose">
            <p  class="red">您未授权！请复制以下链接在浏览器打开进行授权</p>
            <p>【授权链接】{{ url }}</p>
            <el-button type="text" class="red" @click="resetURLfun">复制链接</el-button>
        </el-dialog>
    </div>
</template>

<script>
export default {
    data() {
        return {
            isShow: false,
            url: ''
        }
    },
    methods: {
        onClose() {
            this.isShow = false;
        },
        // 复制
        resetURLfun() {
            this.$fn.copyFn(this.url)
        },
        
        info(url) {
            this.isShow = true;
            this.url = url;
        }
    }
}
</script>
<style lang="scss" scoped>
p {
    font-size: 14px;
    color: #666666;
    line-height: 28px;
}

.red {
    color: #f42121;
}

</style>
<template>
    <div>
        <template v-if="materialList.length !== 0">
            <div class="materila-box">
                <div
                    class="materila-item shou"
                    v-for="(item, index) in materialList"
                    @click="goDetail(item.id)"
                    :key="index"
                >
                    <template v-if="item.id">
                        <el-image
                            v-if="item.img_url.length !== 0"
                            style="width: 226px; height: 226px;"
                            :src="item.img_url[0]"
                            fit="fill"
                        ></el-image>
                        <div class="message-box">
                            <div class="message-title">
                                {{ item.title }}
                            </div>
                            <div
                                v-if="item.img_url.length === 0"
                                class="message-content"
                            >
                                {{ item.content }}
                            </div>
                            <div class="message-user">
                                <el-image
                                    class="user-image"
                                    style="width: 24px; height: 24px; border-radius: 50%"
                                    :src="item.user.avatar"
                                    fit="fill"
                                ></el-image>
                                <div class="user-title limit-line1">
                                    {{
                                        item.user.nickname
                                            ? item.user.nickname
                                            : '用户' +
                                              item.user.nickname.slice(-4)
                                    }}
                                </div>
                                <div class="user-view">
                                    <i class="el-icon-view"></i>
                                    {{ item.visit_number }}
                                </div>
                            </div>
                        </div>
                    </template>
                    <div class="materila-items" v-else></div>
                </div>
            </div>
        </template>
        <m-empty v-else></m-empty>
    </div>
</template>

<script>
export default {
    name: 'productMaterial',
    data() {
        return {
            materialList: [],
            page: 1,
            pageSize: 999,
        };
    },
    mounted() {
        this.getMaterialList();
    },
    methods: {
        // 获取素材列表
        async getMaterialList() {
            const data = {
                product_id: parseInt(this.$route.query.goods_id),
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await this.$post('/material/center/list', data);
            if (res.code === 0) {
                res.data.list.forEach((item) => {
                    if (item.img_url) {
                        item.img_url = item.img_url.split(',');
                    } else {
                        item.img_url = [];
                    }
                });
                this.materialList = res.data.list.reverse();
            }
        },
        // 跳转详情
        goDetail(id) {
            this.$_blank('/materialDetail?id=' + id);
        },
    },
};
</script>
<style lang="scss" scoped>
.materila-box {
    width: 936px;
    column-count: 4; // 设置四列
    column-gap: 10px; // 设置列之间的间距
    .materila-item {
        width: 226px;
        max-height: 330px;
        background-color: #f5f5f5;
        margin-bottom: 10px;
        break-inside: avoid; // 确保内容不会被拆分到两列中
        .message-box {
            width: 226px;
            .message-title {
                font-weight: 500;
                font-size: 16px;
                color: #242424;
                margin-left: 12px;
                padding-top: 15px;
                box-sizing: border-box;
            }
            .message-content {
                font-weight: 400;
                font-size: 14px;
                color: #232426;
                margin-left: 12px;
                margin-top: 14px;
            }
            .message-user {
                display: inline-block; // 使用 inline-block 来让其并排显示
                margin-top: 8px;
                margin-bottom: 14px;
                vertical-align: middle;
                .user-image {
                    margin-left: 12px;
                    vertical-align: middle;
                }
                .user-title {
                    display: inline-block;
                    margin-left: 8px;
                    font-weight: bold;
                    font-size: 14px;
                    color: #161616;
                    vertical-align: middle;
                    width: 135px;

                }
                .user-view {
                    display: inline-block;
                    vertical-align: middle;
                    font-weight: 400;
                    font-size: 14px;
                    color: #808080;
                }
            }
        }
    }
    .materila-items {
        background-color: #ffffff;
        height: 330px;
        width: 226px;
    }
}
</style>

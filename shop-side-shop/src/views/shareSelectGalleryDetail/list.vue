<template>
    <div>
        <div class="bgw mt_10">
            <div>
                <div class="f fac fjsb">
                    <div class="gallery-title">{{ stats.name }}</div>
                    <div>{{ shareTime }}共享</div>
                </div>
                <div class="mt_5 gallery-time">
                    最近选品时间：{{ stats.recent_selection_at | formatDate }}
                </div>
            </div>
            <div class="f fac fjsa mt_20 gallery-money">
                <div class="f-column fac">
                    <div class="red">{{ stats.product_count }}</div>
                    <div class="mt5 fs14">商品数量</div>
                </div>
                <div class="f-column fac">
                    <div class="red">{{ stats.order_count }}</div>
                    <div class="mt5 fs14">累计订单数量</div>
                </div>
                <div class="f-column fac">
                    <div class="red">{{ stats.order_amount | formatF2Y }}</div>
                    <div class="mt5 fs14">累计订单金额（元）</div>
                </div>
                <div class="f-column fac">
                    <div class="red">{{ stats.browse_count }}</div>
                    <div class="mt5 fs14">累计访问次数</div>
                </div>
            </div>
        </div>
        <!-- 搜索 -->
        <!-- <div class="search-box bgw">
            <el-form
                ref="formData"
                :model="formData"
                label-width="85px"
                label-position="left"
            >
                <el-form-item label="类目：">
                    <div class="f" style="width: 100%">
                        <div>
                            <el-select
                                class="merchandise-screen-select"
                                v-model="formData.category_1_id"
                                placeholder="请选择一级分类"
                                filterable
                                @change="getCategory(2, formData.category_1_id)"
                            >
                                <el-option
                                    v-for="item in category1"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div>
                            <el-select
                                class="merchandise-screen-select"
                                v-model="formData.category_2_id"
                                placeholder="请选择二级分类"
                                filterable
                                @change="getCategory(3, formData.category_2_id)"
                            >
                                <el-option
                                    v-for="item in category2"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div>
                            <el-select
                                class="merchandise-screen-select"
                                v-model="formData.category_3_id"
                                placeholder="请选择三级分类"
                                filterable
                            >
                                <el-option
                                    v-for="item in category3"
                                    :key="item.id"
                                    :label="item.name"
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="来源：">
                    <el-select
                        class="merchandise-screen-select"
                        v-model="formData.source"
                        placeholder="全部"
                    >
                        <el-option
                            v-for="item in sourceList"
                            :key="item.source_name"
                            :label="item.source_name"
                            :value="
                                item.gather_supply_id +
                                '-' +
                                item.type +
                                '-' +
                                item.source_name
                            "
                        >
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="商品属性：">
                    <div class="f fac fw" style="width: 1100px">
                        <div
                            class="f fac merchandise-screen-select-title"
                            v-if="formData.grossProfitRate != 5"
                        >
                            <div class="select-name">毛利率</div>
                            <el-select
                                class="selecl-sty"
                                v-model="formData.grossProfitRate"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in grossProfitRate"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div v-else class="f merchandise-screen-select-eles">
                            <div class="select-name">毛利率</div>
                            <el-input
                                class="input-sty"
                                v-model="formData.g1"
                                placeholder="请选择"
                            ></el-input>
                            <div class="line">-</div>
                            <div class="input-bin">
                                <el-input
                                    class="input"
                                    v-model="formData.g2"
                                    placeholder="请选择"
                                ></el-input>
                                <div class="bin">%</div>
                            </div>
                        </div>
                        <div
                            class="f fac merchandise-screen-select-title"
                            v-if="formData.profitRate != 5"
                        >
                            <div class="select-name">利润率</div>
                            <el-select
                                class="selecl-sty"
                                v-model="formData.profitRate"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in profitRateOptios"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div v-else class="f merchandise-screen-select-eles">
                            <div class="select-name">利润率</div>
                            <el-input
                                class="input-sty"
                                v-model="formData.p1"
                                placeholder="请选择"
                            ></el-input>
                            <div class="line">-</div>
                            <div class="input-bin">
                                <el-input
                                    class="input"
                                    v-model="formData.p2"
                                    placeholder="请选择"
                                ></el-input>
                                <div class="bin">%</div>
                            </div>
                        </div>
                        <div
                            class="f fac merchandise-screen-select-title"
                            v-if="formData.agreementPrice != 5"
                        >
                            <div class="select-name">协议价</div>
                            <el-select
                                class="selecl-sty"
                                v-model="formData.agreementPrice"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in agreementPriceOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div v-else class="f merchandise-screen-select-eles">
                            <div class="select-name">协议价</div>
                            <el-input
                                class="input-sty"
                                v-model="formData.a1"
                                placeholder="请选择"
                            ></el-input>
                            <div class="line">-</div>
                            <div class="input-bin">
                                <el-input
                                    class="input"
                                    v-model="formData.a2"
                                    placeholder="请选择"
                                ></el-input>
                                <div class="bin">元</div>
                            </div>
                        </div>
                        <div class="f fac merchandise-screen-select-title">
                            <div class="select-name">指导价</div>
                            <el-select
                                class="selecl-sty"
                                v-model="formData.guidePrice"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in guide_marketing_PriceOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div
                            class="f fac merchandise-screen-select-title"
                            v-if="formData.discount != 5"
                        >
                            <div class="select-name">折扣</div>
                            <el-select
                                class="selecl-sty"
                                v-model="formData.discount"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in discountList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div v-else class="f merchandise-screen-select-eles">
                            <div class="select-name">折扣</div>
                            <el-input
                                class="input-sty"
                                v-model="formData.d1"
                                placeholder="请选择"
                            ></el-input>
                            <div class="line">-</div>
                            <div class="input-bin">
                                <el-input
                                    class="input"
                                    v-model="formData.d2"
                                    placeholder="请选择"
                                ></el-input>
                                <div class="bin">折</div>
                            </div>
                        </div>
                        <div class="f fac merchandise-screen-select-title">
                            <div class="select-name">营销价</div>
                            <el-select
                                class="selecl-sty"
                                v-model="formData.marketingPrice"
                                placeholder="请选择"
                            >
                                <el-option
                                    v-for="item in guide_marketing_PriceOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                        </div>
                        <div class="f fac merchandise-screen-select-title">
                            <div class="select-name selectc">是否已导入</div>
                            <el-select
                                class="selecl-sty selectcs"
                                v-model="formData.self_is_import"
                                placeholder="请选择"
                            >
                                <el-option label="全部" value=""></el-option>
                                <el-option label="已导入" value="1"></el-option>
                                <el-option label="未导入" value="0"></el-option>
                            </el-select>
                        </div>
                        <div class="f fac merchandise-screen-select-title">
                            <div class="select-name" style="width: 60px">
                                营销属性
                            </div>
                            <el-select
                                class="selecl-sty"
                                style="width: 182px"
                                v-model="formData.marketing"
                                placeholder="请选择"
                            >
                                <el-option label="默认" value=""></el-option>
                                <el-option
                                    label="新品"
                                    value="is_new"
                                ></el-option>
                                <el-option
                                    label="热卖"
                                    value="is_hot"
                                ></el-option>
                                <el-option
                                    label="促销"
                                    value="is_promotion"
                                ></el-option>
                            </el-select>
                        </div>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="f fac">
                        <el-button class="but-true" @click="goSearch"
                            >搜索</el-button
                        >
                        <el-button @click="resetfun">重置</el-button>
                        <div class="selection-num" @click="toMySel">
                            我的选品库（{{
                                my_center_total ? my_center_total : 0
                            }}）
                        </div>
                        <div class="selection-num" @click="addAlbum">
                            加入商品专辑
                        </div>
                    </div>
                </el-form-item>
            </el-form>
        </div> -->
        <!-- <div class="check-all f fac fjsb">
            <div class="f">
                <el-checkbox
                    style="margin-right: 30px"
                    v-model="checked"
                    @change="checkedfun"
                    >全选</el-checkbox
                >
                <template v-for="item in sort">
                    <div
                        v-if="item.id == 1 && item.id == sortid"
                        :key="item.id"
                        style="cursor: pointer; color: #f42121"
                    >
                        {{ item.name }}
                    </div>
                    <div
                        v-else-if="item.id == 1 && item.id != sortid"
                        :key="item.id"
                        @click="newest"
                        style="cursor: pointer"
                    >
                        {{ item.name }}
                    </div>
                </template>
                <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                    <template v-for="item in sort">
                        <SortButton
                            v-if="item.id != 1"
                            :key="item.id"
                            :value="item.value"
                        >
                            <space-between
                                v-if="item.id == sortid"
                                style="color: #f42121"
                            >
                                {{ item.name }}
                            </space-between>
                            <span v-else @click="sortid = item.id">{{
                                item.name
                            }}</span>
                        </SortButton>
                    </template>
                </SortButtonGroup>
            </div>
            <div class="check-all-right">
                <div @click="tolead(0)">
                    待导入商品（{{ selection_list.length }}）
                </div>
                <div @click="tolead(1)">
                    导入全部筛选商品（{{ total ? total : 0 }}）
                </div>
            </div>
        </div> -->
        <searchList></searchList>
    </div>
</template>

<script>
import { formatTimeToStr } from '@/utils/date';
import SortButton from '@/components/sortButton/sortButton.vue';
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue';
import searchList from './searchList.vue';
export default {
    name: 'list',
    components: { searchList },
    data() {
        return {
            stats: {},
            page: 1,
            pageSize: 24,
            shareTime: '',
            formData: {
                category_1_id: null,
                category_2_id: null,
                category_3_id: null,
                source: null, // 来源
                grossProfitRate: null, // 毛利率
                profitRate: null, // 利润率
                agreementPrice: null, // 协议价
                guidePrice: null, // 指导价
                marketingPrice: null, // 营销价
                discount: null, // 折扣
                self_is_import: null, // 是否已导入
                marketing: null, // 营销属性
                d1: null,
                d2: null,
                a1: null,
                a2: null,
                g1: null,
                g2: null,
                p1: null,
                p2: null,
            },
            category1: [], // 一级类目
            category2: [], // 二级类目
            category3: [], // 三级类目
            sourceList: [], // 来源
            grossProfitRate: [
                // 毛利率
                { label: '0-35%', value: 1 },
                { label: '35%-50%', value: 2 },
                { label: '50%-75%', value: 3 },
                { label: '75%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            profitRateOptios: [
                // 利润率
                { label: '0-50%', value: 1 },
                { label: '50%-150%', value: 2 },
                { label: '150%-300%', value: 3 },
                { label: '300%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            agreementPriceOptions: [
                // 协议价
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            // 指导价/营销价optios
            guide_marketing_PriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
            ],
            // 折扣
            discountList: [
                { label: '0-3折', value: 1 },
                { label: '3-5折', value: 2 },
                { label: '5-8折', value: 3 },
                { label: '8折以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            my_center_total: null, // 选品库数量
        };
    },
    mounted() {
        this.getProductCollectionDetail();
    },
    methods: {
        async getProductCollectionDetail() {
            const data = {
                application_id: parseInt(this.$route.query.app_id),
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await this.$post(
                '/application/productCollection/detail',
                data,
            );
            if (res.code === 0) {
                this.stats = res.data.stats;
                this.shareTime = formatTimeToStr(
                    new Date(res.data.stats.recent_selection_at),
                    'M月dd日',
                );
            }
        },
        // 搜索
        goSearch() {
            this.page = 1;
            this.total = 0;
            this.getProductCollectionDetail();
        },
    },
};
</script>

<style lang="scss" scoped>
.bgw {
    width: 1200px;
    margin: 0 auto;
    padding: 20px 10px;
    box-sizing: border-box;
    border-radius: 5px;
    margin-bottom: 10px;
}
.fjsa {
    justify-content: space-around;
}
.gallery-title {
    font-size: 18px;
    color: #101010;
    font-weight: 600;
}
.gallery-time {
    font-size: 14px;
    color: #101010;
}
.gallery-money {
    font-size: 18px;
    color: #101010;
    .fs14 {
        font-size: 14px;
    }
}
.red {
    color: #f42121;
}
.search-box {
    padding: 16px;
    border-radius: 12px;

    .merchandise-screen-select {
        width: 258px;
        height: 40px;
        border-radius: 8px;
        margin-right: 15px;
    }

    .merchandise-screen-select-title {
        width: 258px;
        height: 40px;
        border-radius: 8px;
        margin-right: 14px;
        border: 1px solid #dee0e5;
        margin-bottom: 16px;

        .selecl-sty {
            width: 192px;
            border: none;
        }

        ::v-deep .el-select .el-input .el-input__inner {
            height: 15px;
        }

        .select-name {
            padding-left: 16px;
            font-size: 14px;
            height: 40px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #606266;
        }

        .selectc {
            width: 70px;
        }

        .selectcs {
            width: 172px;
        }

        ::v-deep .el-input__inner {
            border: 0;
            border-radius: 8px;
        }
    }

    .merchandise-screen-select-eles {
        width: 400px;
        height: 40px;
        margin-right: 15px;
        margin-bottom: 16px;

        .select-name {
            padding: 0 16px;
            font-size: 14px;
            height: 40px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #606266;
        }
        .input-sty {
            width: 140px;
            height: 40px;
        }

        .input-bin {
            width: 140px;
            height: 40px;
            display: flex;
            border: 1px solid #dee0e5;
            border-radius: 8px;

            .input {
                width: 100px;
                height: 40px;
            }

            ::v-deep .el-input__inner {
                border: 0;
                border-radius: 8px;
            }

            .bin {
                width: 40px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 0 8px 8px 0;
                background-color: #f5f7fa;
            }
        }

        .line {
            font-size: 24px;
            padding: 0 5px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .but-true {
        background-color: #f42121;
        color: #fff;
    }

    button {
        width: 100px;
        height: 40px;
        border-radius: 8px;
    }

    .selection-num {
        color: #f42121;
        font-size: 14px;
        margin-left: 12px;
        cursor: pointer;
    }
}
.check-all {
    height: 65px;
    font-size: 14px;

    .f {
        display: flex;
        div {
            margin-right: 30px;
            cursor: pointer;
        }
    }
    .check-all-right {
        display: flex;

        div {
            margin-left: 30px;
            font-weight: 500;
            cursor: pointer;
        }
    }
}
</style>

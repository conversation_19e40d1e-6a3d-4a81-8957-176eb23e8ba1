export default {
    name: "myFavoriteStoreIndex",

    data() {
        return {
            favoriteData:[],
            checkList: [],
            isCheckAll: false
        }
    },

    mounted() {
        this.initFavoriteList();

    },

    methods: {
        //商品收藏列表
        initFavoriteList(){
            let that = this;
            let para = {
                "page":1,
                "type":2
            };
            that.$post("/favorite/getFavoriteList", para).then(function (res) {
                if (res.code == 0) {
                    that.favoriteData=res.data.list;
                }
            }).catch(function (res) {
                console.log(res);
            });
        },

        //删除收藏
        deleteFavoriteDialog(item){
            let that=this;
            this.$confirm('是否取消当前收藏店铺？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                that.deleteItem(item);
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });          
              });
        },

        deleteItem(item){
            let that = this;
            let para = {
                "id":item.id
            };
            that.$post("/favorite/deleteFavorite", para).then(function (res) {
                if (res.code == 0) {
                    that.$message.success(res.msg);
                    that.initFavoriteList();
                }
            }).catch(function (res) {
                console.log(res);
            });
        },


        //删除全部收藏
        deleteAllFavoriteDialog(item){
            let that=this;
            if (that.checkList.length == 0) {
                return;
            }
            this.$confirm('是否取消当前收藏店铺？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                that.deleteAllItem(item);
              }).catch(() => {
                this.$message({
                  type: 'info',
                  message: '已取消'
                });          
              });
        },

        deleteAllItem(){
            let that = this;
            let para = {
                "ids": that.checkList,
            };

            that.$post("favorite/deleteFavoriteByIds", para).then(function (res) {
                if (res.code == 0) {
                    that.$message.success(res.msg);
                    that.initFavoriteList();
                }
            }).catch(function (res) {
                console.log(res);
            });
        },


         //多选框点击事件
         checkItem() {
            let that = this;
            that.isCheckAll = that.checkList.length != that.favoriteData.length ? false : true;
        },

        //全选处理
        checkAllItem(isCheck) {
            let that = this;
            if (isCheck) {
                that.checkList = [];
                for (let i = 0; i < that.favoriteData.length; i++) {
                    that.checkList.unshift(that.favoriteData[i].id);
                }
            } else {
                that.checkList = [];
            }
        }
    }
}

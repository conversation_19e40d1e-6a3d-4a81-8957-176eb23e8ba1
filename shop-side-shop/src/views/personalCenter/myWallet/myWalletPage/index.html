<div>
    <div class="top bgw">
        <p class="title-p">我的钱包</p>
        <!-- 提现部分 -->
        <div class="cont balance-div">
            <!-- <p class="tx-p">可提现金额(元)</p> -->
            <p class="tx-p">采购余额(元)</p> <!-- 结算余额,收入余额 -->
            <!-- <div class="f fac" v-if="balance_settings.join_balance === '1'">
                <div class="f fac">
                    <p class="f fac">汇聚: <span class="num-p">{{ remaining_sum | formatF2Y }}</span></p> -->
                    <!-- <el-button @click="openRechargeDialog" v-if="join_scan_code_recharge === '1'">充值</el-button> -->
                    <!-- <el-button @click="openJoinRecharge" v-if="balance_settings.join_recharge === '1'">充值</el-button>
                </div>
            </div> -->
            <div class="f fac mt20" v-if="balance_settings.station_balance === '1'">
                <p class="f fac">站内: <span class="num-p">{{ remaining_sum2 | formatF2Y }}</span></p>
                <el-button @click="openStationRecharge" v-if="balance_settings.station_recharge === '1'">充值</el-button>
            </div>
            <div class="line"></div>
            <p class="tx-p mt20" v-if="isSupplier">结算余额(元)</p>
            <div class="f fac pb10" v-if="isSupplier">
                <p class="f fac">站内结算余额: <span class="num-p">{{ clearing_sum | formatF2Y }}</span></p>
                <el-button v-if="settlement_btn_isShow"
                    @click="$router.push({path:'/personalCenter/extract',query:{type:'settlement'}})">提现
                </el-button>
                <el-button v-if="settlement_btn_isShow" @click="$router.push('/personalCenter/bindBankCard')">绑定银行卡
                </el-button>
            </div>
            <div class="line" v-if="isSupplier"></div>
            <p class="tx-p mt20">收入余额(元)</p>
            <div class="f fac pb10">
                <p class="f fac">收入余额: <span class="num-p">{{ income_amount | formatF2Y }}</span></p>
                <!-- && clearing_sum > 0 -->
                <el-button v-if="income_btn_isShow"
                    @click="$router.push({path:'/personalCenter/extract',query:{type:'income'}})">提现
                </el-button>
                <el-button v-if="income_btn_isShow" @click="$router.push('/personalCenter/bindBankCard')">绑定银行卡
                </el-button>
            </div>
        </div>
        <p class="hint-p">温馨提示：提现到账时间三到五个工作日，节假日顺延；</p>

        <!-- isSupplier -->
        <!-- <div class="cont clearing-div">
            <p class="tx-p">结算余额(元)</p>
            <div>
                <div class="f fac">
                    <p class="f fac">站内结算余额: <span class="num-p">{{clearing_sum | formatF2Y}}</span></p>
                    <el-button @click="$router.push('/personalCenter/extract')">提现</el-button>
                    <el-button @click="$router.push('/personalCenter/bindBankCard')">绑定银行卡</el-button>
                </div>
            </div>
        </div>
        <div class="cont clearing-div">
            <p class="tx-p">收入余额(元)</p>
            <div>
                <div class="f fac">
                    <p class="f fac">收入余额: <span class="num-p">{{clearing_sum | formatF2Y}}</span></p>
                    <el-button @click="$router.push('/personalCenter/extract')">提现</el-button>
                    <el-button @click="$router.push('/personalCenter/bindBankCard')">绑定银行卡</el-button>
                </div>
            </div>
        </div> -->
        <!-- <p class="hint-p">温馨提示：1、每天只能提现一次；2、每次提现金额不低于100元；3、提现到账时间三到五个工作日，节假日顺延；</p> -->
    </div>
    <div class="cont-box bgw">
        <div class="tabs-search-box f fjsb">
            <div class="tabs-box f">
                <a href="javascript:;" v-for="item in tabsList" :key="item.id" @click="handleClickTabs(item)">
                    <p :class="tabsActive === item.value?'active':''">{{ item.name }}</p>
                </a>
            </div>
            <!-- <el-form :model="searchForm" class="f" size="small">
                <el-form-item>
                    <el-select placeholder="选择资金类型">
                        <el-option label="选择资金类型1" value="1">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-date-picker type="daterange" range-separator="-" start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker>
                </el-form-item>
                <el-form-item>
                    <el-button>确定</el-button>
                </el-form-item>
            </el-form> -->
        </div>
        <!-- 收支明细列表 -->
        <detail-list v-show="tabsActive === 1" ref="detailList"></detail-list>
        <!-- 提现记录列表 -->
        <record-list v-show="tabsActive === 2" ref="recordList"></record-list>
        <!-- 充值记录 -->
        <recharge-list v-show="tabsActive === 3" ref="rechargeList"></recharge-list>
        <!-- 充值dialog -->
        <rechargeDialog  ref="rechargeDialog" :station_balance_recharge="station_balance_recharge"  @reload="getRemaining"></rechargeDialog>
        <!-- 已绑定的银行卡 -->
        <bindBankList v-show="tabsActive === 4" ref="bindBankList"></bindBankList>
        <!-- 余额明细列表 -->
        <balance-list v-show="tabsActive === 5" ref="balanceList"></balance-list>
        <!-- 收入明细列表 -->
        <income-list v-show="tabsActive === 6" ref="incomeList"></income-list>
    </div>
</div>
.bindBankCard-box {
  padding: 15px;

  p.title-p {
    font-size: 16px;
    font-weight: bold;
  }

  .hint-box {
    margin-top: 23px;
    width: 864px;
    height: 58px;
    line-height: 58px;
    font-size: 16px;
    background: rgba(244, 33, 33, 0.08);
    border-radius: 10px;
    color: #F42121;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }

  ::v-deep .el-form {
    padding-left: 48px;
    margin-top: 25px;

    .el-form-item {
      margin-bottom: 27px;

      .bind-btn {
        width: 181px;
        height: 48px;
        background: #F42121;
        border: 1px solid #F42121;
        color: white;
      }

      .reserved-phone-a {
        color: #F42121;
        margin-left: 28px;
      }

      &.bank-box {
        position: relative;

        .dialog-bank-box {
          position: absolute;
          top: 0;
          left: 400px;

          a {
            color: #F42121;
          }
        }
      }

      .hint-p {
        margin-top: 14px;
        color: #AAAAAA;
        line-height: normal;
      }
    }

    .n-mb {
      margin-bottom: 0;
    }

    .el-input__inner {
      border-radius: 0;
    }

    .zj-item {
      width: 116px;
      margin-right: 19px;
    }
  }
}

.w365 {
  width: 365px;
}

.code-box {
  ::v-deep .el-input {
    .el-input__inner {
      border-right: 0;
    }

    .el-input-group__append {
      border-left: 0;
      background-color: white;
      border-radius: 0;

      a {
        color: #F42121;
      }
    }
  }
}

.list-box {
  width: 310px;
  background: #FFFFFF;
  box-shadow: 0px 2px 7px 3px rgba(1, 2, 16, 0.03);
  border-radius: 10px;
  padding: 10px 20px;

  ::v-deep .bank-search-input {
    .el-input__inner {
      border-radius: 10px;
    }
  }

  .bott-box {
    margin-top: 15px;
    display: grid;
    grid-template-columns: repeat(3, 115px);
    grid-row-gap: 13px;
    grid-column-gap: 13px;

    .list-item {
      cursor: pointer;
    }
  }
}

::v-deep .avatar-uploader1 {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 178px;
    height: 178px;
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;

  line-height: 178px;
  text-align: center;
}
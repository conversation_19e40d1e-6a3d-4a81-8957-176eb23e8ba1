import EditBindBankDialog from "../editBindBankDIalog"

export default {
    name: "bindBankList.vue",
    components: {EditBindBankDialog},
    data() {
        return {
            list: []
        }
    },
    methods: {
        // 设置为默认
        handleDefaultClick(row) {
            this.$post("/finance/saveUserBank", {id: row.id, is_default: 1}).then(res => {
                if (res.code === 0) {
                    this.$message.success("设置成功")
                    this.fetch()
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        edit(row) {
            this.$refs.editBindBankDialog.isShow = true
            this.$refs.editBindBankDialog.init(row)
        },
        del(row) {
            this.$confirm("是否确认删除?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                this.$post("/finance/deleteUserBank", {id: row.id}).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg)
                        this.fetch()
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            }).catch(() => {
            })
        },
        fetch() {
            this.$post('/finance/getUserBankList').then(res => {
                if (res.code === 0) {
                    this.list = res.data
                } else {
                    this.$message.error(res.msg)
                }
            })
        }
    }
}
<!-- 修改绑定银行卡 -->
<template>
    <el-dialog
        title="修改绑定银行卡"
        :visible="isShow"
        width="40%"
        :before-close="handleClose"
    >
        <el-form
            :model="formData"
            ref="form"
            :rules="rules"
            label-width="123px"
            label-position="left"
        >
            <el-form-item prop="account_name">
                <span slot="label">{{
                    formData.bank_type === 1 ? '开户名' : '公司名'
                }}</span>
                <el-input
                    v-model="formData.account_name"
                    placeholder="请输入姓名"
                ></el-input>
            </el-form-item>
            <el-form-item class="mt20">
                <span slot="label"
                    ><span class="color-red">* </span>证件号</span
                >
                <div class="f">
                    <el-form-item>
                        <el-select v-model="select">
                            <el-option label="身份证" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item class="f1 ml10" prop="card_id">
                        <el-input v-model="formData.card_id"></el-input>
                    </el-form-item>
                </div>
                <p class="hint-p">请输入在银行办理该卡时使用的证件号</p>
            </el-form-item>
            <el-form-item
                class="mt20"
                label="身份证正面"
                prop="id_card_z"
                v-if="formData.bank_type === 1"
            >
                <el-upload
                    class="avatar-uploader1"
                    :show-file-list="false"
                    :action="path + '/common/upload'"
                    :headers="{ 'x-token': '' }"
                    :on-success="handleWeChatIDCardZImgSuccess"
                    :before-upload="beforeAvatarUpload"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                >
                    <img
                        v-if="formData.id_card_z"
                        :src="formData.id_card_z"
                        class="ewm-img"
                    />
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </el-form-item>
            <el-form-item
                class="mt20"
                label="身份证反面"
                prop="id_card_f"
                v-if="formData.bank_type === 1"
            >
                <el-upload
                    class="avatar-uploader1"
                    :show-file-list="false"
                    :action="path + '/common/upload'"
                    :headers="{ 'x-token': '' }"
                    :on-success="handleWeChatIDCardFImgSuccess"
                    :before-upload="beforeAvatarUpload"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                >
                    <img
                        v-if="formData.id_card_f"
                        :src="formData.id_card_f"
                        class="ewm-img"
                    />
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </el-form-item>
            <el-form-item
                label="营业执照"
                prop="business_license"
                v-if="formData.bank_type === 2"
            >
                <el-upload
                    class="avatar-uploader1"
                    :show-file-list="false"
                    :action="path + '/common/upload'"
                    :headers="{ 'x-token': '' }"
                    :on-success="handleWeChatBusinessImgSuccess"
                    :before-upload="beforeAvatarUpload"
                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                >
                    <img
                        v-if="formData.business_license"
                        :src="formData.business_license"
                        class="ewm-img"
                    />
                    <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
            </el-form-item>
            <el-form-item label="银行卡号" prop="bank_account" class="mt20">
                <el-input
                    v-model="formData.bank_account"
                    placeholder="请输入银行卡号"
                ></el-input>
                <!-- <div class="dialog-bank-box">
            <a href="javascript:;" @click="openBankList">
                <span>支持银行</span>
            </a>
            <div v-if="bankListIsShow" class="list-box">
                <el-input class="bank-search-input" placeholder="搜索支持银行"></el-input>
                <div class="bott-box">
                    <div class="list-item" v-for="item in bankList" :key="item.id" @click="handleClickItem">
                        {{item.name}}
                    </div>
                </div>
            </div>
        </div> -->
            </el-form-item>
            <el-form-item label="所属银行" prop="bank_name" class="mt20">
                <el-select
                    filterable
                    clearable
                    v-model="formData.bank_name"
                    class="w365"
                    placeholder="请选择所属银行"
                >
                    <el-option
                        v-for="item in bank_options"
                        :key="item.id"
                        :label="item.text"
                        :value="item.text"
                    ></el-option>
                </el-select>
                <p class="hint-p">输入银行名即可查询</p>
            </el-form-item>
            <el-form-item :error="areaError" class="pull-box mt20">
                <span slot="label"
                    ><span class="color-red">* </span>所在地区</span
                >
                <select-area
                    ref="selectArea"
                    @selectData="getArea"
                ></select-area>
            </el-form-item>
            <el-form-item
                class="mt20"
                label="银行分行"
                prop="branch"
                v-if="formData.bank_type === 2"
            >
                <el-input
                    v-model="formData.branch"
                    placeholder="请输入银行分行"
                ></el-input>
            </el-form-item>
            <el-form-item class="mt20" label="账户类型" prop="bank_type">
                <el-radio-group
                    class="m-radio-group"
                    v-model="formData.bank_type"
                >
                    <el-radio :label="1">对私</el-radio>
                    <el-radio :label="2">对公</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item class="mt20" label="设置默认" prop="is_default">
                <el-radio-group
                    class="m-radio-group"
                    v-model="formData.is_default"
                >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                class="mt20"
                v-if="formData.bank_type === 2"
                label="联行号"
                prop="bank_channel_no"
                :rules="[{ required: true, message: '请输入联行号' }]"
            >
                <el-input
                    v-model="formData.bank_channel_no"
                    placeholder="请输入联行号"
                ></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" class="confirm-btn" @click="confirm"
                >确 定</el-button
            >
            <el-button class="cancel-btn" @click="handleClose">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import SelectArea from '../components/selectArea';
import verify from '@/utils/verify';
import bankList from '@/json/bank';

export default {
    name: 'editBindBankDIalog',
    components: { SelectArea },
    data() {
        return {
            bank_options: bankList,
            isShow: false,
            areaError: '',
            formData: {
                user_id: this.$ls.getUserId() || null,
                bank_account: '', // 卡号
                card_id: '', // 身份证号
                province_id: '', // 省
                province: '',
                city_id: '', // 市
                city: '',
                county_id: '', // 区
                county: '',
                bank_type: 1, // 1 对私 2 对公
                branch: '', // 分行
                account_name: '', // 姓名
                bank_channel_no: '', // 联行号
                bank_name: '', // 所属银行
                is_default: 0,
                id_card_z: '',
                id_card_f: '',
                business_license: ''
            },
            select: 1,
            rules: {
                account_name: {
                    required: true,
                    validator: (rule, value, callback) => {
                        switch (this.formData.bank_type) {
                            case 1:
                                !value
                                    ? callback(new Error('请输入开户名'))
                                    : callback();
                                break;
                            case 2:
                                !value
                                    ? callback(new Error('请输入公司名'))
                                    : callback();
                                break;
                        }
                    },
                    trigger: 'blur',
                },
                card_id: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (verify.checkCardId(value)) {
                            callback();
                        } else {
                            callback(new Error('身份证格式不正确'));
                        }
                    },
                    trigger: 'blur',
                },
                bank_account: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (verify.checkNumber(value)) {
                            callback();
                        } else {
                            callback('银行卡号格式不正确');
                        }
                        /*switch (this.formData.bank_type) {
              case 1:
                if (verify.checkBankCard(value)) {
                  callback()
                } else {
                  callback(new Error('银行卡号格式不正确'))
                }
                break;
              case 2:
                if (verify.checkBankCard12(value)) {
                  callback()
                } else {
                  callback(new Error('银行卡号格式不正确'))
                }
                break;
            }*/
                    },
                    trigger: 'blur',
                },
                bank_name: {
                    required: true,
                    message: '请选择所属银行',
                    trigger: 'change',
                },
                branch: {
                    required: true,
                    message: '请输入银行分行',
                    trigger: 'blur',
                },
                bank_type: {
                    required: true,
                    message: '请选择账户类型',
                    trigger: 'change',
                },
                id_card_f: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.formData.bank_type === 1) {
                            !value
                                ? callback(new Error('请上传身份证反面'))
                                : callback();
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
                id_card_z: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.formData.bank_type === 1) {
                            !value
                                ? callback(new Error('请上传身份证正面'))
                                : callback();
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur',
                },
                business_license: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (this.formData.bank_type === 2) {
                            !value
                                ? callback(new Error('请上传营业执照'))
                                : callback();
                        } else {
                            callback();
                        }
                    },
                },
            },
            path: this.$path,
        };
    },
    methods: {
        init(row) {
            this.setFrom(row);
            this.$nextTick(() => {
                this.$refs.selectArea.selectData.province_id = row.province_id;
                this.$refs.selectArea.selectData.province = row.province;
                this.$refs.selectArea.selectData.city_id = row.city_id;
                this.$refs.selectArea.selectData.city = row.city;
                this.$refs.selectArea.selectData.county_id = row.county_id;
                this.$refs.selectArea.selectData.county = row.county;
                this.$refs.selectArea.assembleAddress();
                this.$refs.selectArea.getCity(row.province_id);
                this.$refs.selectArea.getCounty(row.city_id);
                /*this.$refs.selectArea.addressStr = row.province +
            " " +
            row.city +
            " " +
            row.county;*/
            });
        },
        confirm() {
            this.checkArea();
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.$post('/finance/saveUserBank', this.formData).then(
                        (res) => {
                            if (res.code === 0) {
                                this.$message.success(res.msg);
                                this.handleClose();
                                this.$emit('reload');
                            } else {
                                this.$message.error(res.msg);
                            }
                        },
                    );
                } else {
                    return false;
                }
            });
        },
        handleClose() {
            this.isShow = false;
            try {
                this.$refs.form.resetFields();
            } catch (e) {}
            this.formData = {
                user_id: null,
                bank_account: '', // 卡号
                card_id: '', // 身份证号
                province_id: '', // 省
                province: '',
                city_id: '', // 市
                city: '',
                county_id: '', // 区
                county: '',
                bank_type: 1, // 1 对私 2 对公
                branch: '', // 分行
                account_name: '', // 姓名
                bank_channel_no: '', // 联行号
                bank_name: '', // 所属银行
                is_default: 0,
                id_card_z: '',
                id_card_f: '',
            };
            this.areaError = '';
            this.select = 1;
        },
        //赋值表单
        setFrom(val) {
            const keys = Object.keys(val);
            const that = this;
            keys.forEach((element) => {
                that.formData[element] = val[element];
            });
        },
        checkArea() {
            this.areaError = this.formData.province_id ? '' : '请选择省份';
            this.areaError = this.formData.city_id ? '' : '请选择城市';
            this.areaError = this.formData.county_id ? '' : '请选择区/县';
        },
        // 获取选中的区域
        getArea(data) {
            console.log(data);
            this.formData.province_id = data.province_id;
            this.formData.province = data.province;
            this.formData.city_id = data.city_id;
            this.formData.city = data.city;
            this.formData.county_id = data.county_id;
            this.formData.county = data.county;
            // this.formData.id_card_z = row.id_card_z
            // this.formData.id_card_f = row.id_card_f
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传头像图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        //身份证正面
        handleWeChatIDCardZImgSuccess(res) {
            this.formData.id_card_z = res.data.file.url;
        },
        //身份证反面
        handleWeChatIDCardFImgSuccess(res) {
            this.formData.id_card_f = res.data.file.url;
        },
    },
};
</script>

<style lang="scss" scoped>
.hint-p {
    margin-top: 14px;
    color: #aaaaaa;
    line-height: normal;
}

::v-deep .avatar-uploader1 {
    width: 270px;
    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 300px;
        height: 170px;
        .ewm-img {
            width: 300px;
            height: 170px;
        }
    }
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;

    line-height: 178px;
    text-align: center;
}
</style>

::v-deep .el-dialog {
  border-radius: 10px;

  .el-dialog__header {
    border-bottom: 1px solid #F0F0F0;
  }

  .el-dialog__body {
    padding-left: 50px;
    padding-right: 50px;

    .el-form {
      .el-form-item {
        margin-bottom: 17px;

        .el-input {
          .el-input__inner {
            border-radius: 0;
            border: 1px solid #F0F0F0;
          }
        }
      }
    }

    .btn-box {
      margin-top: 50px;

      .el-button {
        width: 118px;
        height: 37px;
        border-radius: 3px;
        font-size: 16px;
        padding: 0;
        line-height: 37px;

        &:nth-child(1) {
          border-color: #F42121;
          background-color: #F42121;
          color: white;
          margin-right: 19px;
        }

        &:last-child {
          border-color: #CCCCCC;
          background-color: #CCCCCC;
          color: #333333;
        }
      }
    }
  }
}

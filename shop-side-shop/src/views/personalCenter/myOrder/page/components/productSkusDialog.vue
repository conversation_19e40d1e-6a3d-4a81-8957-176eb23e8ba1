<template>
  <el-dialog
      title="换货商品"
      :visible="isShow"
      width="900px"
      :before-close="handleClose">
    <div>
      <p class="mb_15">{{ sku_select.spec_title }}</p>
      <el-radio-group v-model="goodsSkuGroupRadio" size="medium" class="custom-radio">
        <el-radio v-for="item in sku_select.options" :key="item.id" :label="item.title" border
                  @change="handleSkusChange(item)">{{
            item.title
          }}
        </el-radio>
      </el-radio-group>
      <p class="mb_15 mt_20">{{ sku_select.sub_spec_title }}</p>
      <el-radio-group v-model="formData.barter_sku_id" size="medium" class="custom-radio">
        <el-radio v-for="item in sku_select_options" :key="item.id" :label="item.id" border>{{ item.options[1].spec_item_name || "" }}</el-radio>
      </el-radio-group>
      <div class="f fac mt_30">
        <p class="mr_20">换货数量</p>
        <p class="mr_43">库存数量{{ stock }}</p>
        <el-input-number :disabled="!stock" size="small" class="w120"
                         v-model="formData.barter_num" :min="0" :max="maxProductNum"
                         :precision="0" placeholder="请输入">
        </el-input-number>
      </div>
      <p class="color-gray">最多可换{{ maxProductNum }}个</p>
    </div>
    <div slot="footer">
      <el-button type="primary" class="confirm-btn" @click="confirm">确 定</el-button>
      <el-button @click="handleClose" class="cancel-btn">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "productSkusDialog",
  data() {
    return {
      // 最多可换
      maxProductNum: 0,
      sku_select: {},
      sku_select_options: [],
      product: {},
      isShow: false,
      goodsSkuGroupRadio: "",
      formData: {
        // 规格id
        barter_sku_id: null,
        // 换货数量
        barter_num: 0,
        // 规格名称
        barter_sku_title: ""
      }
    }
  },
  computed: {
    stock() {
      let obj = this.sku_select_options.find(item => item.id === this.formData.barter_sku_id)
      let num = obj && obj.stock > 0 ? obj.stock : 0
      if (obj) {
        this.formData.barter_sku_title = obj.title
      }
      if (!num) {
        this.formData.barter_num = 0
      }
      return num;
    }
  },
  methods: {
    handleSkusChange(item) {
      this.sku_select_options = item.skus
      this.formData.barter_sku_id = item.skus[0].id
    },
    async init(productID, maxProductNum, barterSkuTitle, barterSkuId, barterNum) {
      this.isShow = true
      this.maxProductNum = maxProductNum
      const {code, data, msg} = await this.$get("/product/get", {id: productID})
      if (code === 0) {
        this.product = this.$fn.deepClone(data)
        this.sku_select = this.$fn.deepClone(data.product.sku_select)
        this.goodsSkuGroupRadio = this.sku_select.options[0].title
        this.sku_select_options = this.sku_select.options[0].skus
        this.formData.barter_sku_id = this.sku_select.options[0].skus[0].id
      } else {
        this.$message.error(msg)
      }
      // 换货时回显之前选择的商品信息
      this.formData.barter_sku_title = barterSkuTitle
      this.formData.barter_sku_id = barterSkuId
      this.formData.barter_num = barterNum
    },
    handleClose() {
      this.isShow = false
      this.maxProductNum = 0
      this.sku_select = {}
      this.sku_select_options = []
      this.product = {}
      this.isShow = false
      this.goodsSkuGroupRadio = ""
      this.formData = {
        // 规格id
        barter_sku_id: null,
        // 换货数量
        barter_num: 0,
        // 规格名称
        barter_sku_title: ""
      }
    },
    confirm() {
      if (this.formData.barter_num === 0) {
        this.$message.error('数量不能为0')
        return
      }
      this.$emit("getSkusData", this.formData)
      this.handleClose()
    }
  }
}
</script>

<style scoped>

</style>
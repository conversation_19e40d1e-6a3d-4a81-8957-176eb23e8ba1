import RefundGoods from "../refundGoods"
import RightComm from "../rightComm"
import ProductSkusDialog from "../productSkusDialog";
import {commMixin} from "@/mixin/comm";

export default {
    name: "refundApply",
    components: {RefundGoods, RightComm, ProductSkusDialog},
    mixins:[commMixin],
    data() {
        return {
            fileList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            orderData: {},
            maxAmount: 0,
            maxFreight: 0, // 最大运费
            max_technical_services_fee: 0.26, // 最大技术服务费
            formData: {
                id: null,
                order_item_id: null,
                refund_type: null, // 0=退款 1=退货 2=换货
                is_received: 0, // 货物状态 0未收到 1已收到
                reason_type: null, // 申请原因
                amount: null, // 退款金额
                freight: null, // 运费
                technical_services_fee: 0, // 技术服务费
                description: "", // 退款说明 描述
                detail_images: [], // 图片凭证 *换货图片字段为image_url
                barter_sku_id: null, // 换货的规格id
                barter_num: null, // 换货数量
                barter_sku_title: "", // 换货的规格名称
                barter_title: "", // 选中的商品规格中文文字 *不提交字段
                refund_way: 0,// 退款方式 0自定义金额 1个数
                num: 0, // 退款商品个数
            },
            reasonOptions: [],
            refundTypeList: [], // 服务类型
            rules: {
                refund_type: {
                    required: true,
                    message: "请选择服务类型",
                    trigger: "blur"
                },
                refund_way: {
                    required: true,
                    message: "请选择退款方式",
                    trigger: "blur"
                },
                num: {
                    required: true,
                    message: "请选择输入退款商品个数",
                    trigger: "blur"
                },
                is_received: {
                    required: true,
                    message: "请选择货物状态",
                    trigger: "blur"
                },
                reason_type: {
                    required: true,
                    message: "请选择申请原因",
                    trigger: "change"
                },
                amount: {
                    required: true,
                    message: "请输入退款金额",
                    trigger: "blur"
                },
                barter_title: {
                    required: true,
                    message: "请选择商品规格",
                    trigger: "blur"
                }
            }
        }
    },
    mounted() {
        this.initOption()
        // setTimeout(() => {
        //     console.log(this.orderData, '???');
        // }, 5000)
    },
    methods: {
        getSkusData(data) {
            this.formData.barter_sku_title = data.barter_sku_title
            this.formData.barter_sku_id = data.barter_sku_id
            this.formData.barter_num = data.barter_num
            this.formData.barter_title = `${data.barter_num}${this.orderData.goods_obj.unit}  ${data.barter_sku_title}`
        },
        // 打开选择商品规格dialog
        openProductSkusDialog() {
            this.$refs.productSkusDialog.init(this.orderData.goods_obj.product_id, this.orderData.goods_obj.qty, this.formData.barter_sku_title, this.formData.barter_sku_id, this.formData.barter_num)
        },
        handeleUpdateSuccess(res) {
            if (res.code === 0) {
                this.formData.detail_images.push(res.data.file.url);
            } else {
                this.$message.error(res.msg)
            }
        },
        beforeUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
        handleExceed(files, fileList) {
            this.$message.warning(`当前限制选择 3 张图片，本次选择了 ${files.length} 张图片，超出了 ${files.length + fileList.length - 3} 张图片`);
        },
        beforeRemove(file, fileList) {
            return this.$confirm(`确定移除 ${file.name}？`);
        },
        handleRemove(file, fileList) {
            this.formData.detail_images = []
            fileList.forEach(item => {
                if (item.response) {
                    this.formData.detail_images.push(item.response.data.file.url)
                } else {
                    this.formData.detail_images.push(item.url)
                }
            })
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        // 提交申请
        handleSunmitClick() {
            this.$refs.form.validate((valid) => {
                if (!valid) return false;
                let data = {
                    order_item_id: this.formData.order_item_id,
                    refund_type: this.formData.refund_type,
                    is_received: this.formData.is_received,
                    reason_type: this.formData.reason_type,
                    refund_way: this.formData.refund_way,
                    num: this.formData.num,
                    amount: JSON.parse(JSON.stringify(this.$fn.changeMoneyY2F(this.formData.amount))),
                    freight: JSON.parse(JSON.stringify(this.$fn.changeMoneyY2F(this.formData.freight))),
                    technical_services_fee: JSON.parse(JSON.stringify(this.$fn.changeMoneyY2F(this.formData.technical_services_fee))),
                    description: this.formData.description,
                    detail_images: this.formData.detail_images
                }
                if (this.formData.refund_type === 2) {
                    data.barter_sku_id = this.formData.barter_sku_id
                    data.barter_num = this.formData.barter_num
                    data.barter_sku_title = this.formData.barter_sku_title
                }
                let request_url = "/afterSales/create"
                if (this.formData.id) {
                    data.id = this.formData.id
                    request_url = "/afterSales/save"
                }
                this.$post(request_url, data).then(res => {
                    if (res.code === 0) {
                        this.$message.success(res.msg)
                        this.$emit("success", 2)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            })
        },
        // 转换退款最大金额/运费/技术服务费 type 1: 退款金额 2: 运费 3: 技术服务费
        convertMaxAmount(type, amount) {
            switch (type) {
                case 1:
                    this.maxAmount = parseFloat(this.$fn.changeMoneyF2Y(amount))
                    break;
                case 2:
                    this.maxFreight = parseFloat(this.$fn.changeMoneyF2Y(amount))
                    break;
                case 3:
                    this.max_technical_services_fee = parseFloat(this.$fn.changeMoneyF2Y(amount))
                    this.formData.technical_services_fee = this.$fn.changeMoneyF2Y(this.orderData.goods_obj.technical_services_fee)
                    break;
            }
        },
        initOption() {
            if (!this.$fn.decode(this.$route.query.order_id)) {
                this.$message.error("非法访问")
                return false
            }
            this.getAfterSalesTypeNameMap()
        },
        // 获取服务类型
        async getAfterSalesTypeNameMap() {
            let order_id = parseInt(this.$fn.decode(this.$route.query.order_id))
            const {code, data, msg} = await this.$get("/afterSales/getAfterSalesTypeNameMap", {order_id})
            if (code === 0) {
                this.refundTypeList = []
                for (let i in data) {
                    this.refundTypeList.push({
                        key: parseInt(i),
                        name: data[i]
                    })
                }
                this.formData.refund_type = this.formData.refund_type === null ? this.refundTypeList[0].key : this.formData.refund_type
                this.getAfterSales()
            } else {
                this.$message.error(msg)
            }
        },
        // 切换服务类型
        getRadio(){
            this.formData.reason_type = null;
            console.log(1111);
            this.getAfterSales()
        },
        // 获取申请原因
        async getAfterSales() {
            let params = {
                after_sale_type: this.formData.refund_type,
                is_received: this.formData.is_received
            }
            console.log(params,this.formData.reason_type,222);
            /*switch (this.formData.refund_type) {
                case 0:
                    params.is_received = false
                    break;
                case 1:
                    params.is_received = true
                    break;
            }*/
            this.reasonOptions = []
            let res = await this.$get("/afterSales/reason/list", params)
            if (res.code === 0) {
                this.reasonOptions = this.$fn.objToArray(res.data.reasons)
            } else {
                this.$message.error(res.msg)
            }
        }
    }
}
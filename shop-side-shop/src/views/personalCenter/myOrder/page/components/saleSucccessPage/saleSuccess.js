import HistoryItem from "../historyItem";
import RightComm from "../rightComm";
export default {
    name: "saleSuccess",
    components: {RightComm, HistoryItem},

    data(){
        return {
            infoData:{},
            orderData:{}
        }
    },
    methods:{
        // 获取售后详情
        getSalesInfo(order_item_id) {
            this.$get("/afterSales/getAfterSalesByOrderItemId", {order_item_id}).then(res => {
                if (res.code === 0) {
                    this.infoData = res.data.after_sales
                    /*this.auditStatusAssemble(res.data.after_sales.status, res.data.after_sales.after_sales_audit.status)
                    if (this.infoData.type === 1) {
                        this.getSupplyAddress(this.infoData.order_id)
                    }*/
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
    }
}
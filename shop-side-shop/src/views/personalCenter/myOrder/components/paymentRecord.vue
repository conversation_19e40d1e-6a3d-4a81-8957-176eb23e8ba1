<template>
  <el-dialog
    width="800px"
    title="开通记录"
    :visible="visible"
    @open="onOpen"
    @close="onClose"
    @closed="onClosed"
  >
      <el-table :data="tableData" style="width: 100%">
        <el-table-column label="开通时间" align="center">
            <template slot-scope="scope">
                <span>{{ scope.row.created_at | formatDate }}</span>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header">
                <p>开通前会员等级</p>
                <p>开通后会员等级</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.before_level_name }}</p>
                <p>{{ scope.row.after_level_name }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center" width="170" >
            <template slot="header">
                <p>开通前有效日期</p>
                <p>开通后有效日期</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.before_validity }}</p>
                <p>{{ scope.row.after_validity }}</p>
<!--                <p v-if="scope.row.before_validity_at">{{ scope.row.before_validity_at | formatDate }}</p>-->
<!--                <p v-if="scope.row.after_validity_at">{{ scope.row.after_validity_at | formatDate }}</p>-->
            </template>
        </el-table-column>
        <el-table-column label="开通类型" align="center">
            <template slot-scope="scope">
                {{ scope.row.purchase_type | formatPurchase }}
            </template>
        </el-table-column>
        <el-table-column label="支付金额（元）" align="center">
            <template slot-scope="scope">   
                <p>{{ scope.row.order_amount | formatF2Y }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center">
            <template slot="header">
                <p>支付状态</p>
                <p>支付方式</p>
            </template>
            <template slot-scope="scope">
                <!-- <p>{{ scope.row.order_status | formatOrderStatus }}</p>
                <p>{{ scope.row.order_pay_name }}</p> -->
                <p>{{ scope.row.order_status_name }}</p>
                <p>{{ scope.row.order_pay_name }}</p>
            </template>
        </el-table-column>
      </el-table>
      <el-pagination
          background
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100,200]"
          :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="total, sizes,prev, pager, next, jumper"
          class="mt30">
      </el-pagination>
  </el-dialog>
</template>
<script>
export default {
    name: "paymentRecord",
    data() {
      return {
        tableData:[],
        page: 1,
        pageSize: 10,
        total: 0,
      }
    },
    filters: {
      formatPurchase(v) {
        let s = ''
        switch (v) {
          case 1:
            s = '升级'
            break
          case 2:
            s = '续费'
            break
        }
        return s
      },
      formatOrderStatus(v) {
        let s = ''
        switch (v) {
          case 1:
            s = '已支付'
            break
          case 2:
            s = '已退款'
            break
        }
        return s
      },
    },
    props: {
      // 弹窗显示/隐藏
      visible: {
        type: Boolean,
        default: false
      },
      propRow: {
        type: Object,
        default: () => { return {} }
      },
      rowId: {
        type: Number,
        default: () => {
          return 0
        },
      },
    },
    methods: {
      onOpen () {
        this.getRecord()
        this.$nextTick(() => {
          // this.formData = {...this.propRow}
        })
      },
      onClose () {
        this.$emit('update:visible', false)
      },
      onClosed () {

      },
      getRecord(){
        let params = {
          page: this.page,
          pageSize: this.pageSize,
        }
        this.$get("/user/getPurchaseRecordList",params).then(res => {
          if (res.code === 0) {
              this.tableData = res.data.list
              this.total = res.data.total;
          } else {
              this.$message.error(res.msg)
          }
        })
      },
      handleCurrentChange(page) {
        this.page = page;
        this.getRecord();
      },
      handleSizeChange(size) {
        this.pageSize = size;
        this.getRecord();
      },


    },
}
</script>
<style lang='scss' scoped>
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
    background-color: $theme-color;
}
.mt30 {
  margin-top: 30px;
}
</style>

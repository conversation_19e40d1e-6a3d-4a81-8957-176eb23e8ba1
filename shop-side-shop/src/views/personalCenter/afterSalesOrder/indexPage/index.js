export default {
    name: "afterSalesOrderIndex",

    data() {
        return {
            page: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            searchDate: [],
            orderSearchConditions: [
                {name: "售后编号", value: 0},
                {name: "订单编号", value: 1},
                {name: "商品名称", value: 2},
            ],
            orderSearchTypeConditions: [
                {name: "退款", value: 0},
                {name: "退货", value: 1},
            ],
            orderSearchConditionTag: null,
            placeholderText:"请输入",
            orderSearchConditionType: null,
            orderSearchCondition: "",
        }

    },
    created() {
        this.fetch()
    },
    methods: {
        handleChange(value){
            let s = "";
            switch (value){
                case 0:
                    s = "请输入售后编号"
                    break;
                case 1:
                    s = "请输入订单编号"
                    break;
                case 2:
                    s = "请输入商品名称"
                    break;
            }
            this.placeholderText = s
        },
        jumpOrderDetail(row) {
            this.$_blank("/personalCenter/myOrderDetail", {
                oid: row.order_id,
                status: "n"
            })
        },
        jumpRefund(row){
            this.$_blank("/refund", {
                order_id:this.$fn.encode(row.order_id),
                order_item_id:this.$fn.encode(row.order_item.id)
            })
        },
        handleSearchClick() {
            this.page = 1
            this.fetch()
        },
        pagination(val) {
            this.page = val.page;
            this.fetch()
        },
        async fetch() {
            let params = {
                page: this.page,
                pageSize: this.pageSize,
            }
            if(this.orderSearchConditionTag !== null){
                switch (this.orderSearchConditionTag){
                    case 0:
                        params.after_sale_sn = this.orderSearchCondition
                        break;
                    case 1:
                        params.order_sn = this.orderSearchCondition
                        break;
                    case 2:
                        params.title = this.orderSearchCondition
                        break;
                }
            }
            if(this.orderSearchConditionType){
               params.type = this.orderSearchConditionType
            }
            if(this.searchDate && this.searchDate.length > 0){
                params.start_at = this.searchDate[0]
                params.end_at = this.searchDate[1]
            }
            let res = await this.$get("/afterSales/list", params)
            if (res.code === 0) {
                this.tableData = res.data.list
                this.total = res.data.total
            } else {
                this.$message.error(res.msg)
            }
        }
    }
}
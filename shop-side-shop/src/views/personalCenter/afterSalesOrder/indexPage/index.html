<div class="bgw afterOrder">
    <el-row>
        <el-col :span="2">
            <h3 class="order-title">售后订单</h3>
        </el-col>
        <el-col :span="22">
            <el-row justify="space-between" :gutter="10">
                <el-col :span="3">
                    <el-select size="small" v-model="orderSearchConditionTag" class="w100" @change="handleChange">
                        <el-option :label="item.name" :value="item.value" v-for="item in orderSearchConditions">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="7">
                    <el-input size="small" :placeholder="placeholderText" v-model="orderSearchCondition"
                              clearable></el-input>
                </el-col>
                <el-col :span="3">
                    <el-select size="small" placeholder="售后类型" v-model="orderSearchConditionType" class="w100">
                        <el-option :label="item.name" :value="item.value" v-for="item in orderSearchTypeConditions">
                        </el-option>
                    </el-select>
                </el-col>
                <el-col :span="8">
                    <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="searchDate" class="w100" size="small"
                                    type="daterange" range-separator="-" start-placeholder="开始日期"
                                    end-placeholder="结束日期">
                    </el-date-picker>
                </el-col>
                <el-col :span="3">
                    <el-button size="small" type="info" class="w100 search-btn" @click="handleSearchClick">确定
                    </el-button>
                </el-col>
            </el-row>
        </el-col>
    </el-row>
    <el-table class="orderTable" :data="tableData">
        <el-table-column prop="after_sale_sn" label="售后编号" align="center">
        </el-table-column>
        <el-table-column label="订单编号" align="center">
            <template slot-scope="scope">
                <el-button type="text" class="color-red" @click="jumpOrderDetail(scope.row)">
                    {{ scope.row.order.order_sn }}
                </el-button>
            </template>
        </el-table-column>
        <el-table-column label="商品" width="200">
            <template slot-scope="scope">
                <p class="limit-line2">{{ scope.row.order_item.title }}</p>
            </template>
        </el-table-column>
        <el-table-column prop="refund_type_name" label="售后类型" align="center">
        </el-table-column>
        <el-table-column label="申请时间" align="center">
            <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
            </template>
        </el-table-column>
        <el-table-column label="状态" align="center">
            <template slot-scope="scope">
                <span v-if="scope.row.status === 0">{{ scope.row.after_sales_audit.status_name }}</span>
                <span v-else>{{ scope.row.status_name }}</span>
            </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
            <template slot-scope="scope">
                <el-button type="text" class="color-red" @click="jumpRefund(scope.row)">查看详情</el-button>
            </template>
        </el-table-column>
    </el-table>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next, jumper">
    </Pagination>
</div>
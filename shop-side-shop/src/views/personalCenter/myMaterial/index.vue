<template>
    <div class="bgw myMaterial-box">
        <div class="top-box f fac fjsb">
            <p class="title-p">我的素材</p>
            <el-button class="red" size="small" @click="addMaterial">
                + 发布素材
            </el-button>
        </div>
        <el-table :data="list" class="mt_20">
            <el-table-column
                label="素材标题"
                prop="title"
                width="200px"
                align="center"
            ></el-table-column>
            <el-table-column label="关联商品" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.product.title }}</p>
                    <el-button
                        class="red-text"
                        type="text"
                        @click="openProduct(scope.row.product, scope.row.id)"
                    >
                        更换商品
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="供货商" align="center">
                <template slot-scope="scope">
                    {{ scope.row.product.supplier.name }}
                </template>
            </el-table-column>
            <el-table-column label="品牌" align="center">
                <template slot-scope="scope">
                    {{ scope.row.product.brand.name }}
                </template>
            </el-table-column>
            <el-table-column label="审核状态/审核时间" align="center">
                <template slot-scope="scope">
                    <p v-if="scope.row.status == 1" style="color: #F96D20">
                        审核中
                    </p>
                    <p v-if="scope.row.status == 2" style="color: #323233">
                        已发布
                    </p>
                    <p v-if="scope.row.status == 3" class="red-text">
                        审核不通过
                    </p>
                    <p v-if="scope.row.status !== 1">
                        {{ scope.row.updated_at | formatDate }}
                    </p>
                </template>
            </el-table-column>
            <el-table-column label="不通过理由" align="center">
                <template slot-scope="scope">
                    <p v-html="scope.row.rejected_reason"></p>
                </template>
            </el-table-column>
            <el-table-column label="素材分组" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.group.title }}</p>
                </template>
            </el-table-column>
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        class="red-text"
                        type="text"
                        @click="goDetail(scope.row.id)"
                        >详情</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <selectGoodsDialog
            ref="selectGoodsDialog"
            @handleCloseProduct="handleCloseProduct"
            @getSelectProduct="getSelectProduct"
        ></selectGoodsDialog>
        <Pagination
            :total="total"
            @pagination="pagination"
            :limit="pageSize"
            layout="total, prev, pager, next, jumper"
        >
        </Pagination>
    </div>
</template>

<script>
import selectGoodsDialog from '../shortVideo/components/selectGoodsDialog.vue';
export default {
    name: 'myMaterial',
    components: { selectGoodsDialog },
    data() {
        return {
            list: [],
            page: 1,
            pageSize: 10,
            total: 0,
            material_id: null,
        };
    },
    mounted() {
        this.getMaterialList();
    },
    methods: {
        // 获取素材列表
        async getMaterialList() {
            const data = {
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await this.$post('/material/self/list', data);
            if (res.code === 0) {
                this.list = res.data.list;
                this.total = res.data.total;
            }
        },
        pagination(p) {
            this.page = p.page;
            this.getMaterialList();
        },
        // 发布素材
        addMaterial() {
            this.$router.push({ name: 'addMaterial' });
        },
        // 取消
        handleCloseProduct() {
            this.material_id = null;
        },
        // 更换商品
        async getSelectProduct(selectProduct) {
            let data = {
                id: this.material_id,
                goods_id: parseInt(selectProduct.id),
                user_id: this.$ls.getUserId(),
            };
            let res = await this.$post('material/self/replace', data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.material_id = null;
                this.getMaterialList();
            }
        },
        // 打开商品弹窗
        openProduct(row, id) {
            this.material_id = parseInt(id);
            this.$refs.selectGoodsDialog.info(row);
        },
        // 跳转详情
        goDetail(id) {
            this.$_blank('/materialDetail?page=personcenter&id=' + id);
        },
    },
};
</script>

<style lang="scss" scoped>
.myMaterial-box {
    padding: 15px 12px;

    .top-box {
        width: 100%;

        p.title-p {
            font-size: 16px;
            font-weight: bold;
        }
    }
}

.el-button.red {
    background: #f42121;
    border: 1px solid #f42121;
    color: white;
    margin-right: 19px;
    width: 96px;
    height: 32px;
    font-size: 12px;
    border-radius: 3px;
    padding: 0;

    &:hover {
        background: #f42121;
        border: 1px solid #f42121;
        color: white;
        margin-right: 19px;
        width: 96px;
        height: 32px;
        font-size: 12px;
        border-radius: 3px;
        padding: 0;
    }
}

.el-button.red.is-disabled {
    color: #c0c4cc;
    cursor: not-allowed;
    background-image: none;
    background-color: #fff;
    border-color: #ebeef5;
}
.red-text {
    color: red;
}
</style>

<div class="refund-content-box mt10 bgw">
    <div class="f">
        <div class="f1">
            <el-row>
                <el-form :model="formData" label-width="120px" ref="form" :rules="rules">
                    <el-col :span="24">
                        <el-form-item label="退款商品:">
                            <div class="f mt10">
                                <refund-goods :src="orderData.goods_obj ? orderData.goods_obj.image_url : ''"
                                              :imgSize="['100px','100px']"
                                              :title="orderData.goods_obj ? orderData.goods_obj.title : ''"
                                              :sku="orderData.goods_obj ? orderData.goods_obj.sku_title : ''"
                                              :price="orderData.goods_obj ? orderData.goods_obj.amount : 0"></refund-goods>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="服务类型:" prop="refund_type">
                            <el-radio-group v-model="formData.refund_type" class="m-radio-group"
                                            @change="getAfterSales">
                                <el-radio v-for="item in refundTypeList" :key="item.id" :label="item.key">
                                    {{ item.name }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" v-if="formData.refund_type !== 2">
                        <el-form-item label="货物状态:" prop="is_received">
                            <el-radio-group v-model="formData.is_received" class="m-radio-group"
                                            @change="getAfterSales">
                                <el-radio :label="0">未收到货</el-radio>
                                <el-radio :label="1">已收到货</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type !== 2">
                        <el-form-item label="申请原因:" prop="reason_type">
                            <el-select v-model="formData.reason_type" class="w100" clearable size="small">
                                <el-option v-for="item in reasonOptions" :key="item.id" :label="item.name"
                                           :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type !== 2">
                        <el-form-item label="退款方式:" prop="refund_way">
                            <el-radio-group v-model="formData.refund_way" class="m-radio-group">
                                <el-radio :label="0">手填金额</el-radio>
                                <el-radio :label="1">按商品个数</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type !== 2 && formData.refund_way === 0">
                        <el-form-item label="退款金额:" prop="amount">
                            <el-input-number size="small" class="number-text-left w100" v-model="formData.amount"
                                             :controls="false" :min="0" :precision="2" :max="maxAmount"
                                             placeholder="请输入">
                            </el-input-number>
                            <p class="color-gray">
                                最多可申请￥{{ orderData.goods_obj ? orderData.goods_obj.amount : '' | formatF2Y }}</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type !== 2 && formData.refund_way === 1">
                        <el-form-item label="商品个数:" prop="num">
                            <el-input-number size="small" class="number-text-left w100" v-model="formData.num"
                                             :controls="false" :min="0" :precision="0" :max="orderData.goods_obj.qty"
                                             placeholder="请输入">
                            </el-input-number>
                            <p class="color-gray">
                                最多可申请{{orderData.goods_obj.qty}}个</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type !== 2">
                        <el-form-item label="运费:" prop="freight">
                            <el-input-number size="small" class="number-text-left w100" v-model="formData.freight"
                                             :controls="false" :min="0" :precision="2" :max="maxFreight"
                                             placeholder="请输入">
                            </el-input-number>
                            <p class="color-gray">
                                最多可申请￥{{ orderData.freight | formatF2Y }}</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type !== 2">
                        <el-form-item label="技术服务费:" prop="technical_services_fee">
                            <el-input-number size="small" class="number-text-left w100"
                                             v-model="formData.technical_services_fee"
                                             :controls="false" :min="0" :precision="2" :max="max_technical_services_fee"
                                             placeholder="请输入">
                            </el-input-number>
                            <p class="color-gray">最多可申请￥{{
                                    orderData.goods_obj ? orderData.goods_obj.technical_services_fee : '' | formatF2Y
                                }}</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="13" v-if="formData.refund_type === 2" class="mb_15">
                        <el-form-item label="换货商品:" prop="barter_title">
                            <div @click="openProductSkusDialog">
                                <el-input
                                        readonly
                                        class="custom-input"
                                        size="small"
                                        placeholder="请选择"
                                        suffix-icon="el-icon-arrow-down"
                                        v-model="formData.barter_title">
                                </el-input>
                            </div>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item :label="formData.refund_type === 2 ? '换货说明:' : '退款说明:'" prop="description">
                            <el-input v-model="formData.description" maxlength="200" type="textarea" :rows="8"
                                      :placeholder="formData.refund_type === 2 ? '换货说明' : '退款说明'">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="24" class="mt20">
                        <el-form-item label="上传图片:" prop="detail_images">
                            <el-upload
                                    multiple
                                    :limit="3"
                                    :action="`${$path}/common/upload`"
                                    list-type="picture-card"
                                    :on-preview="handlePictureCardPreview"
                                    :before-remove="beforeRemove"
                                    :on-remove="handleRemove"
                                    :on-exceed="handleExceed"
                                    :on-success="handeleUpdateSuccess"
                                    :before-upload="beforeUpload"
                                    :file-list="fileList"
                                    accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <p class="color-gray">上传凭证(最多3张), 每张最大限制10MB</p>
                            <el-dialog :visible.sync="dialogVisible">
                                <img width="100%" :src="dialogImageUrl" alt="">
                            </el-dialog>
                        </el-form-item>
                    </el-col>
                    <el-col>
                        <el-form-item class="mt20">
                            <el-button class="confirm-btn" @click="$clicks(handleSunmitClick)">提交</el-button>
                        </el-form-item>
                    </el-col>
                </el-form>
            </el-row>
        </div>
        <div class="cross-line"></div>
        <!-- 订单详情 -->
        <right-comm :infoData="orderData"></right-comm>
        <!-- 选择商品规格弹框 -->
        <product-skus-dialog ref="productSkusDialog" @getSkusData="getSkusData"></product-skus-dialog>
    </div>
</div>
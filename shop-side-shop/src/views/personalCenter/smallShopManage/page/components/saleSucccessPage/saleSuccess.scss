.success-content-box {
  padding-bottom: 20px;
  position: relative;
  .left-box {
    padding: 15px;
    .status-title-p {
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .hint-span {
      font-weight: 400;
      color: #202020;
      font-size: 16px;
    }
    .hint-p {
      color: #666666;
    }
    .saleAdd-p{
      font-size: 16px;
      color: #1b1b1b;
    }
    // 协商历史
    .history-box {
      .history-p {
        font-size: 16px;
        font-weight: bold;
        color: #101010;
      }
    }
    .el-divider {
      background-color: #f0f0f0;
      margin: 15px 0;
    }
  }
  .cross-line {
    position: absolute;
    height: 100%;
    width: 1px;
    right: 260px;
    background-color: #f0f0f0;
  }
}
.color-green {
  color: #29ba9c;
}

export default {
    data() {
        return {
            path: this.$path,
            isShow: false,
            //   imgsList: [],
            formData: {
                // 描述相符评分
                des_level: 0,
                // 卖家服务评分
                shop_level: 0,
                // 物流服务评分
                express_level: 0,
                // 评分
                level: 0,
                // 订单id
                orderId: null,
                // 商品id
                productId: null,
                // 会员id
                userId: this.$ls.getUserId(),
                // 昵称
                nickname: this.$ls.getUser().nickname,
                // 头像
                avatar: this.$ls.getUser().avatar,
                // 评论图片
                imageUrls: "",
                // 1评论 2回复
                type: 1,
                // 评论内容
                content: "",
                // 规格
                product_attr: "",
                order_item_id: null
            },
        };
    },
    methods: {
        // 表单赋值
        setForm(data) {
            this.formData.orderId = data.order_id;
            this.formData.productId = data.product_id;
            this.formData.product_attr = data.sku_title;
            this.formData.order_item_id = data.id
        },
        handleClose() {
            this.formData = {
                // 描述相符评分
                des_level: 0,
                // 卖家服务评分
                shop_level: 0,
                // 物流服务评分
                express_level: 0,
                // 评分
                level: 0,
                // 订单id
                orderId: null,
                // 商品id
                productId: null,
                // 会员id
                userId: this.$ls.getUserId(),
                // 昵称
                nickname: this.$ls.getUser().nickname,
                // 头像
                avatar: this.$ls.getUser().avatar,
                // 评论图片
                imageUrls: "",
                // 1评论 2回复
                type: 1,
                // 评论内容
                content: "",
                // 规格
                product_attr: "",
                order_item_id: null
            };
            this.isShow = false;
        },
        beforeImgsUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
        // 确定
        confirm() {
            if (this.formData.level <= 0) {
                this.$message.error("请给商品进行评分");
                return false;
            }
            if (
                this.$refs.imgsUpload.uploadFiles &&
                this.$refs.imgsUpload.uploadFiles.length > 0
            ) {
                let uploadFiles = this.$refs.imgsUpload.uploadFiles;
                let arr = uploadFiles.map((v) => {
                    return v.response.data.file.url;
                });
                this.formData.imageUrls = arr.join(",");
            }
            this.$post("/comment/create", this.formData).then((res) => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.$emit("reload")
                    this.handleClose();
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
    },
};
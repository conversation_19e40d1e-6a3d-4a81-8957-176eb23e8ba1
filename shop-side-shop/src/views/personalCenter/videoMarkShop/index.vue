<template>
    <!-- 视频号小店 -->
    <div class="bgw myOrder-box">
        <el-row class="p-15-12">
            <el-col :span="24" class="but">
                <el-button
                    :class="groupType === 2 ? 'red-btn' : 'color-black'"
                    type="p" 
                    size="small" 
                    @click="shop_overview"
                >
                    小店概况
                </el-button>
                <el-button
                    :class="groupType === 4 ? 'red-btn' : 'color-black'"
                    type="p" 
                    size="small" 
                    @click="good_manage"
                >
                    小店商品
                </el-button>
                <el-button 
                    :class="groupType === 1 ? 'red-btn' : 'color-black'" 
                    size="small" 
                    @click="order_manage"
                >
                    订单管理
                </el-button>
                <el-button
                    :class="groupType === 3 ? 'red-btn' : 'color-black'"
                    type="p" 
                    size="small" 
                    @click="shop_setting"
                >
                    小店设置
                </el-button>
            </el-col>
        </el-row>
        <!-- 小店概况 start -->
        <div v-if="groupType === 2">
            <div class="bg-grey con-overview">
                <el-row :gutter="15">
                    <el-col :span="12">
                        <el-card class="box-card" shadow="never">
                            <div slot="header" class="clearfix">
                                <span class="bold">待处理</span>
                            </div>
                            <div class="item-overview">
                                <div class="pointer" @click="goOrderPage('order0')">
                                    <p class="overview-title">                                    
                                        待付款
                                        <i class="el-icon-arrow-right"></i>
                                    </p>
                                    <p class="overview-num">{{wait_handle.wait_pay}}</p>
                                </div>
                                <div class="pointer" @click="goOrderPage('order1')">
                                    <p class="overview-title">
                                        待发货
                                        <i class="el-icon-arrow-right"></i>
                                    </p>
                                    <p class="overview-num">{{wait_handle.wait_send}}</p>
                                </div>
                                <div class="pointer" @click="goOrderPage('order5')">
                                    <p class="overview-title">
                                        售后
                                        <i class="el-icon-arrow-right"></i>
                                    </p>
                                    <p class="overview-num">{{wait_handle.after_sales}}</p>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="box-card" shadow="never">
                            <div slot="header" class="clearfix">
                                <span class="bold">数据概况</span>
                            </div>
                            <div class="item-overview">
                                <div>
                                    <p class="overview-title">累计订单</p>
                                    <p class="overview-num">{{statistics.order_count}}</p>
                                </div>
                                <div>
                                    <p class="overview-title">累计订单金额</p>
                                    <p class="overview-num">{{$fn.changeMoneyF2Y(statistics.order_price_sum)}}</p>
                                </div>
                                <div>
                                    <p class="overview-title">账户余额</p>
                                    <p class="overview-num">{{$fn.changeMoneyF2Y(statistics.account_balance)}}</p>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="box-card" shadow="never">
                            <div slot="header" class="clearfix">
                                <span class="bold">今日数据</span>
                            </div>
                            <div class="item-overview">
                                <div>
                                    <p class="overview-title">支付金额</p>
                                    <p class="overview-num">{{$fn.changeMoneyF2Y(today_statistics.pay_sum)}}</p>
                                    <p class="overview-yesterday">昨日 {{$fn.changeMoneyF2Y(yesterday_statistics.pay_sum)}}</p>
                                </div>
                                <div>
                                    <p class="overview-title">订单数</p>
                                    <p class="overview-num">{{today_statistics.order_count}}</p>
                                    <p class="overview-yesterday">昨日 {{yesterday_statistics.order_count}}</p>
                                </div>
                                <div>
                                    <p class="overview-title">支付订单数</p>
                                    <p class="overview-num">{{today_statistics.pay_order_count}}</p>
                                    <p class="overview-yesterday">昨日 {{yesterday_statistics.pay_order_count}}</p>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card class="box-card" shadow="never">
                            <div slot="header" class="clearfix">
                                <span class="bold">商品管理</span>
                            </div>
                            <div class="item-overview">
                                <div>
                                    <p class="overview-title">销售中</p>
                                    <p class="overview-num">{{product_admin.sale_count}}</p>
                                </div>
                                <div>
                                    <p class="overview-title">审核中</p>
                                    <p class="overview-num">{{product_admin.audit_count}}</p>
                                </div>
                                <div>
                                    <p class="overview-title">新增商品</p>
                                    <p class="overview-num">
                                        <el-button type="primary" size="mini" icon="el-icon-plus" @click="goProductManage"></el-button>
                                    </p>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </div>
        </div>
        <!-- 小店概况 end -->
        <!-- 商品管理 start -->
        <div v-if="groupType === 4">
            <el-row class="mt20 mb20 p-x-12">
                <el-col :span="24">
                    <el-row justify="space-between" :gutter="20">
                        <el-col :span="6">
                            <el-input size="small" v-model="formData.product_title" placeholder="商品名称" clearable></el-input>
                        </el-col>
                        <el-col :span="6">                       
                            <el-select 
                                v-model="formData.cat_1_id" 
                                placeholder="一级分类"
                                size="small"
                                filterable 
                                clearable 
                                @change="getCategoryServe(2,formData.cat_1_id)"
                            >
                                <el-option 
                                    v-for="item in categoryList1" 
                                    :key="item.id" 
                                    :label="item.name" 
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>                     
                        </el-col>
                        <el-col :span="6">                           
                            <el-select 
                                v-model="formData.cat_2_id" 
                                placeholder="二级分类"
                                filterable 
                                clearable         
                                size="small"
                                @change="getCategoryServe(3,formData.cat_3_id)"
                            >
                                <el-option 
                                    v-for="item in categoryList2" 
                                    :key="item.id" 
                                    :label="item.name" 
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>                           
                        </el-col>
                        <el-col :span="6">                         
                            <el-select 
                                v-model="formData.cat_3_id"
                                placeholder="三级分类"
                                filterable 
                                clearable 
                                size="small"
                            >
                                <el-option 
                                    v-for="item in categoryList3" 
                                    :key="item.id" 
                                    :label="item.name" 
                                    :value="item.id"
                                >
                                </el-option>
                            </el-select>         
                        </el-col>     
                    </el-row>
                    <el-row class="mt15" justify="space-between" :gutter="20">
                        <el-col :span="6">
                            <el-select v-model="formData.status" placeholder="状态" clearable size="small" class="w100">
                                <el-option
                                    v-for="item in options"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>                
                        <el-col :span="6" class="range">
                            <span class="mr10 c-666 font-14">价格</span>
                            <el-input 
                                v-model="formData.min_price"
                                type="number"
                                size="mini"
                            ></el-input>
                            <span class="mx10 c-666"> - </span>
                            <el-input
                                v-model="formData.max_price"
                                type="number"
                                size="mini"
                            ></el-input>
                        </el-col>
                        <!-- <el-col :span="6" class="range">
                            <span class="mr10 c-666 font-14">折扣</span>
                            <el-input
                                v-model="formData.profit_form"
                                type="number"
                                size="mini" 
                            ></el-input>
                            <span class="mx10 c-666"> - </span>
                            <el-input
                                v-model="formData.profit_to"
                                type="number"
                                size="mini"     
                            ></el-input>
                        </el-col> -->
                        <el-col :span="3">
                            <el-button size="small" type="info" class="w100 search-btn" @click="getProductServe">确定</el-button>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row> 
            <div class="bg-grey">
                <div class="d-bf p-y-15-10">
                    <div class="con-sort">
                        <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                            <SortButton value="price">价格</SortButton>
                            <SortButton value="profit">利润</SortButton>
                            <SortButton value="profit_rate">利润率</SortButton>
                            <SortButton value="sales">销量</SortButton>
                        </SortButtonGroup>
                    </div> 
                    <div class="con-btn">
                        <span
                            class="font_size12 c-3163CE d-cf fontw500 pointer"
                            @click="onCheckAll"
                        >
                            全选
                        </span>
                        <!-- <span
                            class="font_size12 c-3163CE d-cf fontw500 ml_30 pointer"
                            @click="onOpenPriceBatch"
                        >
                            批量改价
                        </span> -->
                        <span
                            class="font_size12 c-3163CE d-cf fontw500 ml_30 pointer"
                            @click="onDelete"
                        >
                            批量移除
                        </span>
                    </div>
                </div> 
                <!-- 商品列表 start -->
                <div class="con-list">
                    <el-row :gutter="20">
                        <el-col class="relative radius-5" :span="12" v-for="item in productList" :key="item.id">
                            <!-- <el-checkbox class="box-checkbox" v-model="item.isChecked" @change="onCheckOne(item)"></el-checkbox>    -->
                            <el-checkbox class="box-checkbox" v-model="item.isChecked" @change="(val)=>onCheckOne(val, item)"></el-checkbox> 
                            <div class="tag" v-if="item.status_name">{{ item.status_name }}</div>
                            <el-card class="box-card" shadow="never">                    
                                <el-image
                                    style="width: 110px; height: 110px"
                                    :src="item.product_image"
                                    @click="goProductDetail(item.product_id)"
                                    class="pointer"
                                >
                                </el-image>
                                <div class="ml_15 w70 pt-10">
                                    <div class="d-f">
                                        <div class="pr_20" style="width: 100%">
                                            <p class="fs-2 ell line2 h37 pointer" @click="goProductDetail(item.product_id)">{{ item.product_title }}</p>
                                            <div class="d-bf fs-0-5 c-8a mt_10">
                                                <!-- <p>利润率 {{ $fn.toPercent(item.profit_rate) }}</p>
                                                <p>售价￥{{ $fn.changeMoneyF2Y(item.shop_price) }}</p>
                                                <p>销量 {{ item.sales }}</p> -->
                                                <p>指导价￥{{ $fn.changeMoneyF2Y(item.guide_price) }}</p>
                                                <p>批发价￥{{ $fn.changeMoneyF2Y(item.price) }}</p>
                                                <p>状态{{ item.status_name }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-bf d-cf mt_8">
                                        <div class="c-orange">
                                            <span class="fs-0-5">￥</span>
                                            <span class="fs-2">{{ $fn.changeMoneyF2Y(item.max_price) }}</span>
                                        </div>
                                        <div class="fs-2">
                                            <span
                                                class="c-3163CE fs-0-5 pointer"
                                                @click="onDelete(item.product_id)"
                                            >
                                            移除
                                            </span>
                                            <!-- <span
                                                class="c-3163CE fs-0-5 pointer"
                                                @click="goProductDetail(item.id)"
                                            >
                                            查看
                                            </span> -->
                                        </div>
                                    </div>
                                </div>
                             
                            </el-card>
                        </el-col>
                    </el-row>                    
                    <div class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
                        <template v-if="isLast && productList.length != 0">
                            <el-empty description="暂无更多"></el-empty>
                        </template>
                        <template v-if="productList.length == 0">
                            <el-empty description="暂无数据"></el-empty>
                        </template>
                    </div>
                    <Pagination 
                        :total="totalProduct" 
                        @pagination="paginationProduct" 
                        :limit="pageSizeProduct" 
                        layout="total, prev, pager, next, jumper"
                    >
                    </Pagination>
                </div>
                <!-- 商品列表 end --> 
            </div> 
        </div>
        <!-- 商品管理 end -->
        <!-- 订单管理 start -->
        <div v-if="groupType === 1">
            <el-row class="p-15-12">
                <el-col :span="18">
                    <el-row justify="space-between" :gutter="20">
                        <!-- <el-col :span="7">
                            <el-input size="small" v-model="searchForm.product_title" placeholder="商品名称"></el-input>
                        </el-col> -->
                        <el-col :span="7">
                            <el-input size="small" v-model="searchForm.keyword" placeholder="输入收货人、手机号、订单编号"></el-input>
                        </el-col>
                        <el-col :span="7">
                            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="searchDate" class="w100"
                                            size="small"
                                            type="daterange" range-separator="-" start-placeholder="开始日期"
                                            end-placeholder="结束日期">
                            </el-date-picker>
                        </el-col>
                        <el-col :span="3">
                            <el-button size="small" type="info" class="w100 search-btn" @click="handleSearchClick">确定</el-button>
                        </el-col>
                    </el-row>
                </el-col>
            </el-row>
            <div class="order-tabs f fac fjsb p-15-12">
                <el-tabs v-model="activeName" @tab-click="handleClick" class="f1">
                    <el-tab-pane
                        v-for="item in tabsList"
                        :key="item.id"
                        :label="item.label + `(` + item.num + `)`"
                        :name="item.name"
                    >
                    </el-tab-pane>
                </el-tabs>
                <el-button size="small" @click="exportTable" class="red-btn">导出</el-button>
            </div>
            <!--订单-->
            <div class="order-cont-box p-15-12" v-for="order in orderList" :key="order.id">
                <!-- 订单组 第一层循环 -->
                <div class="order-item-group">
                    <!-- 订单日期  订单号 -->
                    <div class="top f fac">
                        <p>{{ order.created_at |formatDate }}</p>
                        <p class="mr10">小商店订单号: {{ order.small_order_sn }}</p>
                        <p class="mr10">供应链订单号: {{ order.supply_order_id}}</p>
                        <p class="mr10">视频号订单号: {{ order.wechat_order_id }}</p>
                        <p class="ml_20" v-if="order.third_order_sn">第三方订单号: {{ order.third_order_sn }}</p>
                    </div>

                    <!-- 订单内容 -->
                    <div class="order-cont f">
                        <div>
                            <!-- 商品部分 第二层循环 -->
                            <div class="order-goods-item f fac" v-for="(item,index) in order.order_items" :key="item.id"
                                :class="index+1 === order.order_items.length?'no-border':''">
                                <div class="img-box" @click="goGoodsDialog(item)">
                                    <!-- <img :src="item.image_url" class="img100"> -->
                                    <m-image :src="item.thumb_img"></m-image>
                                </div>
                                <div class="f1 line-h23" @click="goGoodsDialog(item)">
                                    <p class="item-title-p">{{ item.title }}</p>
                                    <p class="gg-p limit-line2">{{ item.sku_title }}</p>
                                    <!-- <p class="num-p">数量: {{ item.qty }}</p> -->
                                    <p v-if="order.comment_status === 0 && item.comment_status === 0 && order.status === 3">
                                        <a href="javascript:;" @click.stop="handleOpenCommentClick(item)" class="color-red">点击评价</a>
                                    </p>
                                </div>
                                <div class="f1 unit-price-div">
                                    <p class="onePrice-p">单价: <span class="color-red">￥{{ item.market_price | formatF2Y }}</span>元
                                    </p>
                                    <!-- <a href="javascript:;" v-for="operation in order.operations" :key="operation.id">
                                        <p v-if="operation.code === 'refund' && item.can_refund === 1 && item.after_sales.id === 0"
                                        class="mt10 color-red" :class="operation.code === 'pay' ? 'color-red' : ''"
                                        @click="jumpRefund(order,item.id)">{{ operation.title }}
                                        </p>
                                        <p v-else-if="operation.code === 'refund' && item.can_refund === 1 && item.after_sales.id"
                                        class="mt10 color-red" :class="operation.code === 'pay' ? 'color-red' : ''"
                                        @click="jumpRefund(order,item.id)">{{ item.after_sales.status_name }}
                                        </p>
                                    </a> -->
                                </div>
                            </div>
                        </div>
                        <!-- 买方信息 -->
                        <div class="purchaser-box f fac">
                            <div class="line-h23">
                                <p>{{ order.address_info.user_name }}</p>
                                <p>{{ order.address_info.tel_number }}</p>
                                <p>
                                    {{ order.address_info.province_name }} 
                                    {{ order.address_info.city_name }}
                                    {{ order.address_info.county_name }} 
                                    <!-- {{ order.address_info.town_name }} -->
                                </p>
                            </div>
                        </div>
                        <!-- 操作部分 -->
                        <div class="operation-box f fac fjc f1">
                            <div>
                                <p class="mt10">{{ order.status_name }}</p>
                                <p class="title-3 mt10 color-red" v-if="order.send_status===1">
                                    ({{ order.send_status_name }})
                                </p>
                                <!-- <a href="javascript:;" v-for="operation in order.operations" :key="operation.id">
                                    <p v-if="operation.code === 'close' || operation.code === 'pay' || operation.code === 'receive' || operation.code === 'express_info' || operation.code === 'bill'"
                                    class="mt10" :class="operation.code === 'pay' ? 'color-red' : ''"
                                    @click="orderOperationAction(order.id,operation,order.order_bill.bill_id)">
                                        <span v-if="operation.code === 'bill'"><span
                                                v-if="order.order_bill.bill_id > 0">{{ operation.title }}</span></span>
                                        <span v-else>{{ operation.title }}</span>
                                    </p>
                                </a> -->
                            </div>
                        </div>
                    </div>
                    <div class="bottom f fac fjsb">
                        <p v-if="order.error" class="color-red con-error">{{order.error}}</p>     
                        <p class="order-total-sum">总计: <span class="color-red">￥{{ order.order_price | formatF2Y }}</span>元</p>
                        <a href="javascript:;" @click="jumpOrderDetail(order.id)">订单详情</a>                   
                        <!-- <a href="javascript:;" style="margin-left: 10px;" v-if="order.status === 2"
                            @click="jumpOrderDetail(order,'y')">查看物流</a> -->
                    </div>
                </div>
            </div>
            <el-empty description="暂无订单" v-if="!orderList"></el-empty>
            <Pagination 
                :total="total" 
                @pagination="pagination" 
                :limit="pageSize" 
                layout="total, prev, pager, next, jumper"
            >
            </Pagination>
            <CreateCommentDialog ref="createCommentDialog" @reload="initOrderInfo"></CreateCommentDialog>
        </div>
        <!-- 订单管理 end -->
        <!-- 小店设置 start -->
        <div class="bg-grey apiPro-box" v-if="groupType === 3">
            <div class="bg-white">
                <div class="mb20 p-0-12 w70">
                    <p class="title-p">绑定视频号</p>
                    <el-form :model="formDataSetting" label-width="90px" class="mt20 binding">
                        <el-form-item label="appid:">
                            <el-input v-model="formDataSetting.appid" placeholder="请输入"></el-input>
                        </el-form-item>
                        <el-form-item label="secret:">
                            <el-input v-model="formDataSetting.secret" placeholder="请输入"></el-input>
                            <p class="remarks mt10">小店唯一凭证，即小店ID，可在「视频号小店-服务市场-自研」页中获得。（需要已完成店铺开通，且账号没有异常状态）</p>
                        </el-form-item>
                        <el-form-item label="Token令牌:">
                            <el-input v-model="formDataSetting.token" placeholder="请输入"></el-input>
                        </el-form-item>
                        <el-form-item label="消息密钥:">
                            <el-input v-model="formDataSetting.msg_secret" placeholder="请输入"></el-input>
                        </el-form-item>
                        <el-form-item label="视频号通知回调地址:">
                            <el-input v-model="callback_url" :disabled="true">
                                <template slot="append">
                                    <a href="javascript:;" @click="$fn.copyFn(callback_url)">复制</a>
                                </template>
                            </el-input>
                            <p class="remarks mt10">请前往视频号设置该回调地址</p>
                        </el-form-item>
                    </el-form> 
                    <p class="title-p">更新设置</p>
                    <el-form label-width="90px" class="mt20 binding">
                        <el-form-item label="商品数据:"> 
                            <el-checkbox v-model="formDataSetting.auto_sync_stock" :true-label="1" :false-label="0" label="快速更新商品库存"></el-checkbox>
                            <p class="remarks">默认开启，本地商品库存变动，快速更新库存！</p>
                        </el-form-item>
                    </el-form>   
                    <div class="ml90 pb20">
                        <el-button class="confirm-btn" @click="onSetVideoShop">绑定</el-button>
                    </div>
                                
                
                </div> 
            </div>
        </div>
        <!-- 小店设置 end -->

        <!-- 单规格改价 start -->
        <PricePopup
            ref="pricePopup"
            :pricePopupShow="pricePopupShow"
            :editPopupShow="editPopupShow"
            :price="price"
            :product="product"
            :headTitle="headTitle"
            @onOpenEditPopup="onOpenEditPopup"
            @onConfirmPricePopup="onConfirmPricePopup"
            @onConfirmEditPopup="onConfirmEditPopup"
            @onClosePricePopup="onClosePricePopup"
            @onCloseEditPopup="onCloseEditPopup"
        ></PricePopup>
        <!-- 单规格改价 end -->
        <!-- 批量改价 start -->
        <PriceBatchPopup
            :priceBatchShow="priceBatchShow"
            :checkboxValue="checkboxValue"
            @onOpenPriceBatch="onOpenPriceBatch"
            @onConfirmPriceBatch="onConfirmPriceBatch"
            @onClosePriceBatch="onClosePriceBatch"
        >
        </PriceBatchPopup>
        <!-- 批量改价 end -->
    </div>
</template>
<script>
import {formatTimeToStr} from "@/utils/date"
import CreateCommentDialog from "@/views/personalCenter/smallShopManage/components/createCommentDialog"
import SortButton from '@/components/sortButton/sortButton.vue'
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue'
import PricePopup from '@/views/personalCenter/smallShopManage/components/pricePopup.vue'
import PriceBatchPopup from '@/views/personalCenter/smallShopManage/components/priceBatchPopup.vue'
export default {
    name: 'smallShopManageIndex',
    components: { 
        CreateCommentDialog,
        SortButton,
        SortButtonGroup, 
        PricePopup,
        PriceBatchPopup,
    },
    data() {
        return {
            a:'',
            callback_url:"",
            groupType: 2, // 1-订单管理 4-商品管理 2-小店概况 3-小店设置
            activeName: this.$route.query.status ? this.$route.query.status : 'orderAll',
            tabsList: [
                {label: '全部', name: 'orderAll'},
                {label: '待付款', name: 'order0'},
                {label: '待发货', name: 'order1'},
                {label: '待收货', name: 'order2'},
                {label: '已完成', name: 'order3'},
                {label: '关闭', name: 'order4'},
                {label: '售后订单', name: 'order5'},
            ],
            orderList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            pageProduct: 1,
            pageSizeProduct: 10,
            totalProduct: 0,
            searchDate: [],
            searchForm: {
                // product_title: "",
                // order_sn: "", 
                keyword: "", // 订单编号
                start_at: "", // 开始日期
                end_at: "", // 结束日期
            },
            sortForm: {
                value: 'price', // 价格price, 利润profit, 利润率profit_rate, 销量sales
                sort: '1', // 1为升序，2为降序
            },
            checkboxValue: [],
            ischeckAll: false,
            // 商品列表数据源
            productList: [],
            // 单条商品数据源
            product: {},
            // 类目1
            categoryList1: [],
            categoryList2: [],
            categoryList3: [],
            formData: {
                cat_1_id: null, // 一级分类
                cat_2_id: null, // 二级分类
                cat_3_id: null, // 三级分类
                sort_by: null, // 排序：1综合降2价格降3价格升4销量降5销量升6创建时间降7创建时间升8利润降9利润升
                title: '', // 产品标题
                is_new: null, // 新品
                is_hot: null, // 热卖
                is_promotion: null, // 促销
                min_price: null, // 最低售价（分）
                max_price: null, // 最高售价（分）
                profit_form: null, // 最低折扣
                profit_to: null, // 最高折扣
                is_choose: null, // 只看未选

                value: ''
            },
            options: [{
                    value: 2,
                    label: '审核中'
                }, {
                    value: 5,
                    label: '上架'
                }, {
            }],
            //改价
            pricePopupShow: false,
            editPopupShow: false,
            priceBatchShow: false,
            product: {}, // 单条商品数据源
            price: '', // 单规格销售价
            headTitle: '商品改价', // Popup标题
            // 绑定视频号
            formDataSetting:{
                appid:'',
                secret:'',
                token:'',
                msg_secret:'',
                auto_sync_stock: 1, //0:关闭 1：开启
            },
            // 视频号概况
            wait_handle: { // 待处理
              wait_pay: 0, // 代付款
              wait_send: 0, // 待发货
              after_sales: 0, // 售后
            }, 
            statistics: { // 数据概况
                order_count: 0, // 累计订单
                order_price_sum: 0, // 累计订单金额
                account_balance: 0, // 账户余额
            }, 
            today_statistics: { // 今日数据
                pay_sum: 0, // 支付金额
                order_count: 0, // 订单数
                pay_order_count: 0, // 支付订单数
            }, 
            yesterday_statistics: { // 昨日数据
                pay_sum: 0, // 支付金额
                order_count: 0, // 订单数
                pay_order_count: 0, // 支付订单数
            }, 
            product_admin: { // 商品管理
                sale_count: 0, // 销售中
                audit_count: 0, // 审核中
            }, 
        }
    },
    filters: {
        // 格式化订单状态
        formatStatus: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "未 分 成"
                    break;
                case 1:
                    name = "已 分 成"
                    break;
            }
            return name;
        },
        // optStatus: function (status) {
        //     let name = ""
        //     switch (status) {
        //         case 0: //待付款 
        //             // name = "确认收货"
        //             break;
        //         case 1: // 待发货 
        //             // name = ""
        //             break;
        //         case 2: // 待收货 
        //             name = "确认收货"
        //             break;
        //         case 3: //已完成 
        //             // name = ""
        //             break;
        //         case -1: //关闭
        //             // name = ""
        //             break;
        //         case 4: // 待归还  
        //             name = "申请归还"
        //             break;
        //         case 5: // 申请归还
        //             // name = ""
        //             break;
        //         case 6: // 归还中  
        //             name = "填写归还"
        //             break;
        //         case 7: // 待确认(审核中)
        //             name = "归还详情"
        //             break;
        //         case -2: // 驳回申请归还
        //             // name = ""
        //             break;
        //     }
        //     return name;
        // },      
    },
    mounted() {
        this.initOrderInfo()
        this.getProductServe()
        this.getCategoryServe(1, 0) // 获取一级类目
        this.onGetVideoShop()
        this.getDashboard()
        this.getCallbackUrl()
    },
    methods: {
        getCallbackUrl(){
            this.$get("/smallShopVideo/callback/url").then(res => {
                if (res.code === 0) {
                    this.callback_url = res.data.callback_url
                } else {
                    this.$message.error(res.msg)
                }
            }).catch(function (res) {
                console.log(res);
            });
        },
        // 视频号概况
        getDashboard(){
            this.$get("/smallShopVideo/dashboard/info").then(res => {
                if (res.code === 0) {
                    this.wait_handle = res.data.info.wait_handle
                    this.statistics = res.data.info.statistics
                    this.today_statistics = res.data.info.today_statistics
                    this.yesterday_statistics = res.data.info.yesterday_statistics
                    this.product_admin = res.data.info.product_admin
                } else {
                    this.$message.error(res.msg)
                }
            }).catch(function (res) {
                console.log(res);
            });
        },
        goProductManage() {
            this.$_blank("/personalCenter/smallShopManage")
        },
        goOrderPage(val){
            this.order_manage()
            this.activeName = val
            this.initOrderInfo()
        },
        // 小店设置-绑定
        onSetVideoShop(){
            this.formDataSetting.auto_sync_stock = Number(this.formDataSetting.auto_sync_stock)
            for (const key in this.formDataSetting) {
                if (!this.formDataSetting[key]) {
                    delete this.formDataSetting[key]
                }
            }
            let params = {
                ...this.formDataSetting
            }      
            this.$post("/smallShopVideo/setting/bind", params).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg)
                } else {
                    this.$message.error(res.msg);
                }
            })
        },
        // 小店设置-获取
        onGetVideoShop(){ 
            let params = {
                uid: this.$ls.getUserId()
            }    
            this.$get("/smallShopVideo/setting/find", params).then(res => {
                if (res.code === 0) {
                    this.formDataSetting.id = res.data.info.id
                    this.formDataSetting.appid = res.data.info.appid
                    this.formDataSetting.secret = res.data.info.secret
                    this.formDataSetting.token = res.data.info.token
                    this.formDataSetting.msg_secret = res.data.info.msg_secret
                    this.formDataSetting.auto_sync_stock = res.data.info.auto_sync_stock
                } else {
                    this.$message.error(res.msg);
                }
            })
        },
        // 以下：商品列表
        // 获取类目
        getCategoryServe(level, pid) {
            const api = '/smallShopVideo/category/getCategory'
            let params = {
                level: level,
                f_cat_id: pid,
            }
            this.$get(api, params).then(r => {
                switch (params.level) {
                    case 1:
                        this.categoryList1 = r.data.categories
                        break;
                    case 2:
                        this.categoryList2 = r.data.categories
                        break;
                    case 3:
                        this.categoryList3 = r.data.categories
                        break;
                }
            })
        },
        // 提交选品
        onAddProduct(params) {
            const api = '/smallShop/product/changePrice'
            let newParams = {
                product_id: params.product_ids[0],
                price_proportion: params.price_proportion,
                price_type: params.price_type,
            }
            this.$post(api, newParams, true)
                .then(res => {
                    if (res.code === 0) {
                        this.getProductServe()
                    } else {
                        this.$message.error(res.msg)
                    }
                })
                .catch(Error => {
                    console.log(Error)
                })
        },
        // 查询商品列表
        getProductServe(add = false) {
            this.formData.sort_by = this.getSortStatus(this.sortForm)
      
            // 查询商品列表入参判空
            const temps = { ...this.formData }
            for (const item in temps) {
                if (temps[item] === null) {
                delete temps[item]
                }
            }
            const params = {
                ...temps,
                page: this.pageProduct,
                pageSize: this.pageSizeProduct,
            }
            if (params.min_price) {
                params.min_price = this.$fn.changeMoneyY2F(params.min_price)
            }
            if (params.max_price) {
                params.max_price = this.$fn.changeMoneyY2F(params.max_price)
            }
            const api = '/smallShopVideo/product/list'
            this.$get(api, params, true)
                .then(res => {
                    if (res.code === 0) {
                        // if (add) { 
                        //     this.productList = [...this.productList, ...res.data.list]
                        // } else {
                        //     res.data.list.forEach(element => element.isChecked = false);
                        //     this.productList = res.data.list || []
                        // }
                        if(res.data.list){
                            res.data.list.forEach(element => element.isChecked = false);
                        }
                        
                        this.productList = res.data.list || []
                        this.totalProduct = res.data.total
                        this.isLast = this.productList.length === this.total ? true : false
                    } else {
                        this.$message.error(res.msg)
                    }
                })
                .catch(Error => {
                    console.log(Error)
                })
        },
        search() {
            this.page = 1
            this.getProductServe()
        },
        // 按排序条件查询时的入参
        onChangeSort(val) {
        /**
         * 排序查询：
         * formatObj键 => 1:向上up 升序1; 2:向下down 降序2。
         * formatObj值 => 1综合降 2价格降 3价格升 4销量降5销量升6创建时间降7创建时间升8利润降9利润升10利润率降序11升序
         */
        /* const formatObj = {
            price: {
            1: 3,
            2: 2,
            },
            profit: {
            1: 9,
            2: 8,
            },
            profit_rate: {
            1: 11,
            2: 10,
            },
            sales: {
            1: 5,
            2: 4,
            },
        } */
        this.sortForm = val
        this.search()
        },
        getSortStatus(sortForm) {
            /**
             * 排序查询：
             * formatObj键 => 1:向上up 升序1; 2:向下down 降序2。
             * formatObj值 => 1综合降 2价格降 3价格升 4销量降5销量升6创建时间降7创建时间升8利润降9利润升10利润率降序11升序
             */
            const formatObj = {
                price: {
                    1: 3,
                    2: 2,
                },
                profit: {
                    1: 9,
                    2: 8,
                },
                profit_rate: {
                    1: 11,
                    2: 10,
                },
                sales: {
                    1: 5,
                    2: 4,
                },
            }
            return formatObj[sortForm.value][sortForm.sort]
        },
        onCheckOne(val,item){
            if(val) {
                this.checkboxValue.push(item.product_id)
            } else {
                this.checkboxValue = this.checkboxValue.filter(element => {    
                    return element !== item.product_id 
                })              
            }
            // console.log('this.checkboxValue',this.checkboxValue)
        },
        onCheckAll(){
            if(!this.ischeckAll) { // 全选               
                this.productList.forEach(element => {
                    element.isChecked = true
                })
                this.checkboxValue = this.productList.map(item => item.id)
                this.ischeckAll = true
            } else { // 非全选              
                this.productList.forEach(element => {
                    element.isChecked = false
                })
                this.checkboxValue = []
                this.ischeckAll = false
            }
            // console.log('this.checkboxValue',this.checkboxValue)
        },
        // 删除商品(单条、批量)
        onDelete(id) {
            const api = '/smallShopVideo/product/delete'
            let params = {
                ids: [id],
            }
            if (typeof id === 'number') {
                params.ids[0] = id // 单条移除
                this.getDeleteProduct(api, params)
            } else {             
                if (this.checkboxValue.length === 0) {
                    params.ids = null // 未勾选
                    this.$message.error('请选择需要批量移除的商品')
                }
                if (this.checkboxValue.length !== 0) {
                    // console.log('this.checkboxValue',this.checkboxValue);
                    params.ids = this.checkboxValue // 批量移除
                    this.getDeleteProduct(api, params)
                }
            }
        },
        getDeleteProduct(api, params){
            this.$confirm("是否移除此商品?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            }).then(() => {
                this.$del(api, params, true).then(res => {
                    if (res.code === 0) {
                        this.getProductServe()
                        this.$message.success(res.msg)
                    } else {
                        this.$message.error(res.msg)
                    }
                })
                .catch(Error => {
                    console.log(Error)
                })
            }).catch(Error => {
                console.log(Error)
            });
        },
        // 打开商品管理popup
        onOpenPricePopup(item){
            this.pricePopupShow = true      
            this.product = item
            // this.$refs.pricePopup.price_proportion =
            //     item.small_shop_product_sale.price_proportion / 100
            // this.$refs.pricePopup.price_type = item.small_shop_product_sale.price_type
            if(item?.small_shop_product_sale?.price_proportion){
                this.$refs.pricePopup.price_proportion =
                    item.small_shop_product_sale.price_proportion / 100
            }
            if(item?.small_shop_product_sale?.price_type){
                this.$refs.pricePopup.price_type = item.small_shop_product_sale.price_type
            }
            this.$nextTick(() => {
                this.$refs.pricePopup.computedSellingPrice()
            })
        },
        // 打开批量改价popup（商品管理页面，目前在用）
        onOpenPriceBatch() {
            if(this.checkboxValue.length === 0) {
                this.$message.error('请选择需要批量改价的商品')
            } else {
                this.priceBatchShow = true
            }
        },
        // 确认批量改价popup（商品管理页面，目前在用）
        onConfirmPriceBatch(p) {
            this.onAddProductBatch(p)
            this.onClosePriceBatch()
        },
        // 提交批量改价（商品管理页面，目前在用）
        onAddProductBatch(params) {
            const api = '/smallShop/product/batchChangePrice'
            let newParams = {
                product_ids: params.product_ids,
                price_proportion: params.price_proportion,
                price_type: params.price_type,
            }
            this.$post(api, newParams, true)
                .then(res => {
                    if (res.code === 0) {
                        this.getProductServe()
                    } else {
                        this.$message.error(res.msg)
                    }
                })
                .catch(Error => {
                    console.log(Error)
                })
        },
        // 打开批量改价popup（单规格改价页面，目前屏蔽留以备用）
        onOpenEditPopup() {
            this.editPopupShow = true
        },
        // 确认商品管理popup
        onConfirmPricePopup(p) {
            this.onAddProduct(p)
            this.onClosePricePopup()
        },
        // 确认批量改价popup（单规格改价页面，目前屏蔽留以备用）
        onConfirmEditPopup(p) {
            this.onChangePrice(p)
            this.onCloseEditPopup()
        },
        // 关闭商品管理popup
        onClosePricePopup(flg = '') {
            if (!flg) {
                this.pricePopupShow = false
                this.editPopupShow = false
            } else {
                this[flg] = false
            }
        },
        onClosePriceBatch(flg = '') {
            // if (!flg) {
            //     this.pricePopupShow = false
            //     this.editPopupShow = false
            // } else {
            //     this[flg] = false
            // }
            this.priceBatchShow = false
        },
        // 关闭批量改价popup（单规格改价页面，目前屏蔽留以备用）
        onCloseEditPopup() {
            this.editPopupShow = false
        },
        // 商品详情跳转
        goProductDetail(id) {
            this.$_blank("/goodsDetail", {goods_id: id})
        },
        // 导入全部
        onClickImportAll() {
            const params = {
                album_ids: this.albumsData.map(item => item.id),
            }
            this.getImportServe(this.importAllAlbum, params)
        },
        // 批量导入
        onClickImportSome() {
            const params = {
                album_ids: this.checkboxValue,
            }
            this.getImportServe(this.importAlbum, params)
        },
        // 单独导入
        onClickImportSingle(id) {
            const params = {
                album_ids: [id],
            }
            this.getImportServe(this.importAlbum, params)
        },
        getImportServe(api, params) {
            this.$post(api, params, true)
                .then(res => {
                        this.getInit()
                        this.$message.error(res.msg)
                    })
                    .catch(Error => {
                        console.log(Error)
                    })
        },
        // 以上：商品列表
        // 商品管理
        good_manage(){
            this.groupType = 4
        },
        // 订单管理
        order_manage() {
            this.groupType = 1
        },
        // 小店概况
        shop_overview() {
            this.groupType = 2
        },
        // 小店设置
        shop_setting() {
            this.groupType = 3
        },
        //导出
        exportTable() {
            let param = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchForm
            }
            if (this.searchDate && this.searchDate.length > 0) {
                param.start_at = this.searchDate[0]
                param.end_at = this.searchDate[1]
            } else {
                param.start_at = ""
                param.end_at = ""
            }
            switch (this.activeName) {
                case "orderAll":
                    break;
                case "order0": //待付款
                    param.status = "10";
                    break;
                case "order1": //待发货
                    param.status = "20";
                    break;
                case "order2": //待收货
                    param.status = "30";
                    break;
                case "order3": //已完成
                    param.status = "100";
                    break;
                case "order4": //关闭
                    param.status = "-200";
                    break;
                case "order5": //售后订单
                    param.status = "-300";
                    break;
                default:
                    break;
            }
            this.$post("/smallShopVideo/order/export", param).then(res => {
                if (res.code === 0) {
                    let link = this.$path + '/' + res.data.link
                    this.$fn.exportTable(link)
                } else {
                    this.$message.error(res.msg);
                }
            })
        },
        // 打开创建评论
        handleOpenCommentClick(order) {
            this.$refs.createCommentDialog.isShow = true
            this.$refs.createCommentDialog.setForm(order)
        },
        handleSearchClick() {
            this.page = 1
            this.pageSize = 10
            this.total = 0
            if (this.searchDate && this.searchDate.length > 0) {
                this.searchForm.start_at = this.searchDate[0]
                this.searchForm.end_at = this.searchDate[1]
            } else {
                this.searchForm.start_at = ""
                this.searchForm.end_at = ""
            }
            this.initOrderInfo()
        },
        

        //跳转至发票详情
        jumpBillDetail(bill_id) {
            this.$_blank("/personalCenter/myBillDetail", {
                detail_id: bill_id
            })
        },
        jumpOrderDetail(order, status = 'v') {
            let params = {
                status
            }
            switch (typeof order) {
                case 'number':
                    params.oid = order
                    break;
                case 'object':
                    params.statuCode = order.order_leases.status
                    params.oid = order.id
                    params.end_at = order.order_leases.end_at
                    params.isLease = 1
                    break;           
                default:
                    break;
            }
            this.$_blank("/personalCenter/videoOrderDetail", params)
        },
        // 跳转商品详情
        goGoodsDialog(item) {
            this.$_blank("/goodsDetail", {goods_id: item.product_id})
        },
        //获取商品订单列表
        initOrderInfo() {
            let that = this;
            let para = {
                "page": that.page,
                "pageSize": that.pageSize,
                ...this.searchForm
            }
            //tag栏切换 全部:0或者不传，待付款:10，待发货:20，待收货:30，完成:100，关闭:200，售后订单:300
            switch (that.activeName) {
                case "orderAll":
                    break;
                case "order0": // 待付款
                    para.status = "10";
                    break;
                case "order1": // 待发货
                    para.status = "20";
                    break;
                case "order2": // 待收货
                    para.status = "30";
                    break;
                case "order3": // 已完成
                    para.status = "100";
                    break;
                case "order4": // 关闭
                    para.status = "200";
                    break;
                case "order5": // 售后订单
                    para.status = "300";
                    break;
                default:
                    break;
            }
            let api = '/smallShopVideo/order/list'
            that.$get(api, para).then(function (res) {
                if (res.code == 0) {
                    that.orderList = res.data.list;
                    that.total = res.data.total;                  
                    that.tabsList.forEach(element => {
                        switch (element.name) {
                            case 'orderAll': // 全部
                                element.num = 
                                    (typeof res.data?.WaitPayNum === 'number' ? res.data?.WaitPayNum : 0) +
                                    (typeof res.data?.WaitSendNum === 'number' ? res.data?.WaitSendNum : 0) +
                                    (typeof res.data?.WaitReceiveNum === 'number' ? res.data?.WaitReceiveNum : 0) +
                                    (typeof res.data?.CompletedNum === 'number' ? res.data?.CompletedNum : 0) +
                                    (typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0) +
                                    (typeof res.data?.AfterSaleNum === 'number' ? res.data?.AfterSaleNum : 0)
                                break;
                            case 'order0':
                                element.num = typeof res.data?.WaitPayNum === 'number' ? res.data?.WaitPayNum : 0 // 待付款
                                break;
                            case 'order1':
                                element.num = typeof res.data?.WaitSendNum === 'number' ? res.data?.WaitSendNum : 0 // 待发货
                                break;
                            case 'order2':
                                element.num = typeof res.data?.WaitReceiveNum === 'number' ? res.data?.WaitReceiveNum : 0 // 待收货
                                break;
                            case 'order3':
                                element.num = typeof res.data?.CompletedNum === 'number' ? res.data?.CompletedNum : 0 // 已完成
                                break;
                            case 'order4':
                                element.num = typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0 // 关闭
                                break;
                            case 'order5': 
                                element.num = typeof res.data?.AfterSaleNum === 'number' ? res.data?.AfterSaleNum : 0 //售后订单
                                break;
                            default:
                                break;
                        }
                    });
                }
            }).catch(function (res) {
                console.log(res);
            });
        },

        handleClick(tab) {
            this.page = 1;
            this.orderList = [];
            this.initOrderInfo();
        },

        pagination(val) {
            this.page = val.page;
            this.getProductServe();
        },
        paginationProduct() {
            this.page = val.page;
            this.initOrderInfo();
        },

        //订单操作
        orderOperationAction(id, operationItem, bill_id = 0) {
            if (!operationItem || operationItem.code == "" || operationItem.code == undefined) {
                return;
            }
            switch (operationItem.code) {
                case "close":
                    this.orderOperationCloseDialog(id, operationItem);
                    break;
                case "pay":
                    this.orderOperationPay(id, operationItem);
                    break;
                case "receive":
                    this.orderOperationReceive(id, operationItem)
                    break;
                case "express_info":
                    this.jumpOrderDetail(id, "y")
                    break;
                case "bill":
                    this.jumpBillDetail(bill_id)
                    break;
                default:
                    break;
            }

        },

        // 跳转至申请退款/退货
        jumpRefund(order, item_id) {
            this.$_blank('/refund', {
                order_id: this.$fn.encode(order.id),
                order_item_id: this.$fn.encode(item_id)
            })
        },


        //订单关闭
        orderOperationCloseDialog(id, operationItem) {
            this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.orderOperationClose(id, operationItem);
                }).catch(() => {
            });
        },

        orderOperationClose(id, operationItem) {
            let that = this;
            let para = {
                "order_id": id
            }
            this.$get("/order/close", para).then(function (res) {
                if (res.code == 0) {
                    that.$message.success(res.msg);
                    that.initOrderInfo();
                }
            }).catch(function (res) {
                console.log(res);
            });
        },

        //订单支付
        orderOperationPay(id, operationItem) {
            let that = this;
            that.$router.push("/payment?para=" + id);
            // let para = {
            //     "order_ids": [id]
            // }
            // this.$post("/trade/cashier", para).then(function (res) {
            //     console.log(res);
            //     if (res.code == 0) {
            //         ///goodsDetail?goods_id=${item.id}`
            //         that.$router.push("/payment?para="+id);
            //     }
            // }).catch(function (res) {
            //     console.log(res);
            // });
        },
    }
}
</script>
<style lang="scss" scoped>
.pointer {
    cursor: pointer;
}
.p-x-12 {
    padding: 0 12px;
}
.p-15-12 {
    padding: 15px 12px 0;
}
.myOrder-box {
//   padding: 15px 12px;

  h3.order-title {
    font-weight: bold;
    line-height: 32px;
  }

  .search-btn {
    background-color: #1e1e1e;
    border: 1px solid #1e1e1e;
    color: white;
  }
    .but{
    ::v-deep
    .el-button{
        padding-left: 5px;
        padding-right: 5px;
    }
    ::v-deep
    .el-button.color-black{
        border: none;
        //border-radius: 4px;
        color: black;
        //background-color: #FFFFFF;
        &:hover{
        border: none;
        color:black;
        background-color: #ffffff;
        }
    }
    /*::v-deep
    .el-button--danger:hover{
        background-color: red;
        color: #FFFFFF;
    }
    ::v-deep
    .el-button--danger:focus{
        background-color: red;
        color: #FFFFFF;
    }*/

    //::v-deep
    //.el-button--danger{
    //  background: red;
    //}
    }
    //订单部分
    .order-cont-box {
    // 订单组
    .order-item-group {
        margin-bottom: 20px;
        border: 1px solid #eeeeee;
        // 订单日期  订单号
        .top {
        height: 36px;
        line-height: 36px;
        border-bottom: 1px solid #eeeeee;
        padding: 0 12px;

        p:nth-child(2) {
            margin-left: 19px;
        }
        /* p.order-total-sum {
            p-align: right;
        } */
        }

        // 订单内容
        .order-cont {
        border-bottom: 1px solid #eeeeee;
        //  商品部分
        .order-goods-item {
            width: 538px;
            height: 128px;
            border-bottom: 1px solid #eeeeee;
            color: #000000;

            /* &:last-child {
            border-bottom: 0;
            } */
            &.no-border {
            border-bottom: 0;
            }

            .img-box {
            width: 90px;
            height: 90px;
            border: 1px solid #eeeeee;
            margin: 15px 12px 20px 15px;
            cursor: pointer;
            & + div {
                cursor: pointer;
            }
            }

            div.line-h23 {
            p.gg-p,
            p.num-p {
                color: #818181;
            }
            p.item-title-p {
                overflow: hidden;
                p-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                line-clamp: 2;
                -webkit-box-orient: vertical;
            }
            }

            .unit-price-div {
            height: 100%;
            border-left: 1px solid #eeeeee;
            padding: 0 20px;
            .onePrice-p{
                margin-top: 40px;
            }
            }
        }

        //  买方信息
        .purchaser-box {
            width: 190px;
            padding: 0 15px;
            border-left: 1px solid #eeeeee;
            border-right: 1px solid #eeeeee;
            position: relative;
            color: #000000;
        }

        //  操作部分
        .operation-box {
            width: 100%;
        }
        }
        // 底部 订单详情按钮
        .bottom {
        // height: 36px;
        // line-height: 36px;
        min-height: 36px;
        line-height: 24px;
        padding: 10px 12px;
        p.order-total-sum {
            margin-right: 19px;
        }
        p.order-total-sum2{
            margin-right: 680px;
        }
        }
    }
    }

    //  tabs
    .order-tabs {
    ::v-deep .el-tabs {
        // margin-top: 23px;

        .el-tabs__header {
        margin-bottom: 24px;

        .el-tabs__nav-wrap {
            &::after {
            display: none;
            }

            .el-tabs__item {
            font-size: 15px;
            // padding: 0 10px;
            &:hover {
                color: #303133;
            }

            &.is-active {
                color: #303133;
            }
            }

            .el-tabs__active-bar {
            background-color: $theme-color;
            }
        }
        }
    }
    }

    p {
    word-break: break-all;
    }

    .line-h23 {
    line-height: 23px;
    }
}

// 以下：排序与筛选
.con-sort {
//   padding: 0 30px 0 26px;
  padding: 0 30px 0 5px;
  width: 270px;
}
// .con-checkbox {
//   margin-left: 75px;
//   .el-checkbox {
//     margin-right: 30px;
//   }
// }
.pt-5 {
    margin-top: 5px;
}
.pt-10 {
    margin-top: 10px;
}
.h37 {
    height: 37px;
}
.radius-5 {
    border-radius: 5px;
}
.box-checkbox {
    position: absolute;
    top: 5px;
    right: 20px;
}
.tag {
    position: absolute;
    top: 0;
    left: 10px;
    padding: 5px 10px;
    font-size: 12px;
    color: white;
    border-radius: 5px 0;
    background: #c34b40fa;
    cursor: default;
    z-index: 2; 
}
::v-deep.el-checkbox__input.is-checked .el-checkbox__inner, .el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: $theme-color;
  border-color: $theme-color;
}
::v-deep.el-checkbox__input.is-checked+.el-checkbox__label {
  color: $theme-color;
}
::v-deep.el-form-item {
  width: 45%;
  .el-input {
    margin-right: 7px;
  }
}

// 以下：商品列表
.d-f {
	display: flex;
}
.pr_20 {
    padding-right: 20px;
}
.fs-2 {
	font-size: 14px;
}
.ell {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	/* 定义显示的行数*/
	overflow: hidden;
}
.line2 {
  overflow: hidden;
  height: 80rpx;
  line-height: 40rpx;
}
.relative {
  position: relative;
}
.checkbox {
  position: absolute;
  top: 16rpx;
  right: 0;
}
.d-bf {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.fs-0-5 {
	font-size: 12px;
}
.c-8a {
	color: #8a8a8a;
}
.c-3163CE {
    color: #3163CE;
}
.mt_8 {
    margin-top: 8px;
}
.mt_10 {
    margin-top: 10px;
}
.d-cf {
	display: flex;
	align-items: center;
}
.d-bf {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.c-orange {
	color: #f15353;
}
.ml_15 {
    margin-left: 15px;
}
.ml_40 {
    margin-left: 20px;
}
.mr_40 {
    margin-right: 20px
}
.c-fa {
	color: #ffaa29;
}
.c-66A3E7 {
	color: #66A3E7;
}
.c-666 {
	color: #666;
}
.font-14 {
    font-size: 14px;
}
.w100 {
  width: 100%;
}
.w70 {
  width: 70%;
}
.pointer {
  cursor: pointer;
}
.con-list {
  padding: 5px;
}
.con-btn {
  display: flex;
  justify-content: flex-end;
  margin-right: 10px;
}
.p-y-15-10 {
    padding: 15px 5px 10px 5px;
}
.bg-grey {
    background: #f7f7f7;;
}
.bg-white {
    background: #ffffff;
}

.range {
    display: flex;
    align-items: center;
    height: 32px;
    ::v-deep.el-input {
        width: 35%;
    }
}
.mr10 {margin-right: 10px;}
.mx10 {margin: 0 10px;}
.mt15 {margin-top: 15px;}

//以下：商品列表02
.box-card {
    margin-bottom: 20px;
}
::v-deep.el-card {
    border: none;
}
::v-deep.el-card__body {
    display: flex;
    flex-direction: row;
    padding: 0;  

}
::v-deep.el-image {
    overflow: visible;
}

.con-overview {
    margin-top: 15px;
    padding-top: 15px;
    ::v-deep.el-card__header {
        border-bottom: none;
    }
    ::v-deep.el-card__body {
        display: flex;
        flex-direction: column;
        padding: 10px 20px 20px 20px;
    }
    .item-overview {
        display: flex;
        justify-content: center;
        width: 100%;
        div {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            height: 70px;
        }
        div:not(:last-child) {    
            border-right: 1px solid #EBEEF5;
        }
        .overview-title {
            width: 140px;
            font-size: 14px;
            text-align: center;

        }
        .overview-num {
            width: 140px;
            margin-top: 10px;
            text-align: center;  
            .el-button--primary {
                background-color: $theme-color;
                border-color: $theme-color;
            }
            ::v-deep.el-button--mini, .el-button--mini.is-round {
                padding: 2px;
            }
        }
        .overview-yesterday {
            width: 140px;
            margin-top: 10px;
            font-size: 12px;
            text-align: center;  
            color:#8a8a8a
        }
        
    }
}
.bold {
    font-weight: bold;
}
::v-deep.pagination-container {
    padding: 20px;
}

// 小店设置
.apiPro-box {
  margin-top: 15px;
  padding-top: 15px;

  p.title-p {
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
  }
}
.w70 {
    width: 70%;
}
.ml90 {
    margin-left: 90px;
}
.binding {
    .el-form-item {
        margin-bottom: 20px;
        width: 100%;
    }
}
.pb20 {
    padding-bottom: 20px;
}
.remarks {
    line-height: 20px;
    font-size: 12px;
    color: #8a8a8a
}
.p-0-12 {
    padding: 1px 12px 0 12px;
}
p.con-error {
    // overflow: hidden;
    display: inline-block;
    width: 60%;
    // p-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 1;
    // line-clamp: 1;
    // -webkit-box-orient: vertical;
}
</style>
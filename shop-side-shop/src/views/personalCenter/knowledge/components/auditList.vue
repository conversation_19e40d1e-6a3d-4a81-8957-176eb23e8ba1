<template>
  <div>
    <div class="bgw pt_15 pl_15 pb_15">
      <el-form :model="searchInfo" inline>
        <el-row :gutter="10">
          <template v-if="auth !== 1">
            <el-col :span="5">
              <el-form-item>
                <el-select v-model="stype" placeholder="请选择会员类型" clearable >
                  <el-option label="会员ID" :value="1"></el-option>
                  <el-option label="会员手机号" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <el-input v-model="stypeValue" placeholder="请输入会员号" clearable ></el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="5">
            <el-form-item>
              <el-input v-model="searchInfo.title" placeholder="请输入文章名称" clearable ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item>
              <el-select v-model="searchInfo.status" placeholder="请选择审核状态" clearable >
                <el-option label="审核中" :value="0"></el-option>
                <el-option label="已通过" :value="1"></el-option>
                <el-option label="已驳回" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-form-item>
              <el-button class="search-btn" @click="search">搜索</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table class="mt_10" :data="list">
      <el-table-column label="ID" align="center" prop="id"></el-table-column>
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="会员ID/会员手机号" align="center">
        <template slot-scope="scope">
          <p>{{ scope.row.article.user_id }}</p>
          <p>{{ scope.row.article.user.mobile ? scope.row.article.user.mobile : scope.row.article.user.username }}</p>
        </template>
      </el-table-column>
      <el-table-column label="文章名称" align="center" prop="article.title"></el-table-column>
      <el-table-column label="所属知识库" align="center" prop="article.knowledge_base.name"></el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | status }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button 
            type="text" 
            @click="openInfoDialog(scope.row)">
            查看
          </el-button>
          <el-button 
            v-if="auth === 2 && scope.row.status === 0" 
            @click="handleOperate(scope.row,1)"
            type="text">
            通过
          </el-button>
          <el-button 
            v-if="auth === 2 && scope.row.status === 0" 
            @click="handleOperate(scope.row,2)" 
            type="text"
            class="color-red">
            驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next">
    </Pagination>
    <info-dialog ref="infoDialog" @reload="getTableList"></info-dialog>
  </div>
</template>

<script>
import InfoDialog from "./infoDialog"

export default {
  name: "auditList",
  components: {InfoDialog},
  data() {
    return {
      stype: null,
      stypeValue: "",
      searchInfo: {},
      page: 1,
      pageSize: 10,
      total: 0,
      list: [],
      auth: 0 // 0 没有权限 1 不能审核 2 能审核
    }
  },
  filters: {
    status(v) {
      let s = ""
      switch (v) {
        case 0:
          s = "审核中"
          break;
        case 1:
          s = "已通过"
          break;
        case 2:
          s = "已驳回"
          break;
      }
      return s
    }
  },
  mounted() {
    this.getTableList()
  },
  methods: {
    // 查看
    async openInfoDialog(row) {
      let res = await this.$get("/article/findArticle",{id:row.article.id})
      if(res.code === 0){
        this.$refs.infoDialog.init(res.data)
      }else{
        this.$message.error(res.msg)
      }
    },
    // 通过/驳回操作
    async handleOperate(row, status) {
      let statusStr = ""
      switch (status) {
        case 1:
          statusStr = "确定通过审核吗?"
          break;
        case 2:
          statusStr = "确定驳回审核吗?"
          break;
      }
      this.$confirm(statusStr, "提示", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$put("/article/updateArticleApply", {
          id: row.id,
          article_id: row.article.id,
          user_id: row.article.user_id,
          status,
        }).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getTableList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })
    },
    pagination(page) {
      this.page = page.page
      this.getTableList()
    },
    search() {
      this.page = 1
      switch (this.stype) {
        case 1:
          this.searchInfo.user_id = this.stypeValue
          break;
        case 2:
          this.searchInfo.mobile = this.stypeValue
          break;
        default:
          if (this.searchInfo.user_id) delete this.searchInfo.user_id
          if (this.searchInfo.mobile) delete this.searchInfo.mobile
          break;
      }
      this.getTableList()
    },
    async getTableList() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        //...this.searchInfo
      }
      if(this.searchInfo.status >= 0 && this.searchInfo.status !== '') {
        params.status = this.searchInfo.status
      }
      if(this.searchInfo.title) {
        params.title = this.searchInfo.title
      }
      switch (this.stype) {
        case 1:
          params.user_id = this.searchInfo.user_id
          break;
        case 2:
          params.mobile = this.searchInfo.mobile
          break;
      }
      let res = await this.$get("/article/getArticleApplyList", params)
      if (res.code === 0) {
        // 没有权限返回首页
        if (res.data.auth === 0) {
          this.$router.push("/")
          return false
        }
        this.auth = res.data.auth
        this.list = res.data.list
        this.total = res.data.total
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style scoped>

</style>
<template>
  <div>
    <div class="bgw f fac pt_15 pl_15 pb_15">
      <el-input v-model="searchInfo.title" clearable class="mr_18" style="width: 302px"
                placeholder="请输入文章名称"></el-input>
      <el-button class="red-btn" @click="search">搜索</el-button>
    </div>
    <div v-if="articleList && articleList.length" class="articleList-box">
      <div class="articleList-item bgw mt_10 shou" v-for="item in articleList" :key="item.id" @click="jumpArticle(item)">
        <div class="f fac font_size16 title-box">
          <i class="el-icon-document" style="color: #FF7744"></i>
          <p class="limit-line1 ml_10">{{ item.title }}</p>
        </div>
        <p class="ml_26 mt_10 mb_10" style="color: #9d9d9d">{{ item.desc | formatDesc }}</p>
        <div class="f fac fjsb pl_26">
          <p class="limit-line2">{{ item.knowledge_base ? item.knowledge_base.name : "" }}</p>
          <span style="color: #9d9d9d">{{ item.updated_at | formatDate }}</span>
        </div>
      </div>
    </div>
    <div class="list-box mt_10" v-else>
      <div class="list-item" v-for="item in list" :key="item.id" @click="jumpKnowledgeIndex(item)">
        <div style="height: 175px">
          <m-image :src="item.cover"></m-image>
        </div>
        <div class="item-panel">
          <p class="title limit-line1 bolder">{{ item.name }}</p>
          <p class="introduce limit-line2 grey">
            {{ item.desc }}</p>
        </div>
      </div>
    </div>
    <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next">
    </Pagination>
  </div>
</template>

<script>
export default {
  name: "knowledgeList",
  data() {
    return {
      searchInfo: {
        title: ""
      },
      articleList: [],
      list: [],
      page: 1,
      pageSize: 6,
      total: 0
    }
  },
  filters: {
    formatDesc(str) {
      let reg = /<p(?:(?!<\/p>).|\n)*?<\/p>/gm
      let strs = []
      str.replace(new RegExp(reg), (str) => {
        strs.push(str)
      })
      let s = ""
      if (strs && strs.length) {
        s = strs[0].substring(3, strs[0].length - 4)
      }
      return s
    }
  },
  mounted() {
    // 搜索文章
    if (this.$route.query.keyword) {
      this.searchInfo.title = this.$route.query.keyword
      this.getArticleList()
    } else { // 获取知识库
      this.getTableList()
    }
  },
  methods: {
    jumpArticle(item){
      this.$_blank("/knowledge", {
        kid: item.knowledge_base_id,
        aid: item.id
      })
    },
    jumpKnowledgeIndex(item) {
      this.$_blank("/knowledge", {
        kid: item.id
      })
    },
    search() {
      let query = {}
      if (this.searchInfo.title) query.keyword = this.searchInfo.title
      this.$router.push({
        path: "/personalCenter/knowledge",
        query
      })
      this.page = 1
      this.list = []
      this.articleList = []
      if (this.searchInfo.title) {
        this.getArticleList()
      } else {
        this.getTableList()
      }
    },
    async getArticleList() {
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        title: this.searchInfo.title
      }
      let res = await this.$get("/article/getArticleList", params)
      if (res.code === 0) {
        this.articleList = res.data.list
        this.total = res.data.total
      } else {
        this.$message.error(res.msg)
      }
    },
    pagination(val) {
      if (this.$route.query.keyword) {
        this.getArticleList()
      } else {
        this.getTableList(val.page)
      }
    },
    async getTableList(page = this.page, pageSize = this.pageSize) {
      for (let key in this.searchInfo) {
        if (this.searchInfo[key] === "") {
          delete this.searchInfo[key]
        }
      }
      let res = await this.$get("/knowledge/getKnowledgeBaseList", {
        page, pageSize, ...this.searchInfo
      })
      if (res.code === 0) {
        this.list = res.data.list
        this.total = res.data.total
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bolder {
  font-weight: bolder;
}
.grey {
  color: #a5a5a5;
}
.list-box {
  display: grid;
  grid-template-columns: repeat(3, 310px);
  grid-row-gap: 13px;
  grid-column-gap: 13px;

  .list-item {
    .item-panel {
      background-color: #fff;
      height: 100px;
      .title {
        padding: 15px 15px 10px 15px;
      }

      .introduce {
        padding: 0 15px;
        height: 40px;
      }
    }
  }
}

.articleList-box {
  .articleList-item {
    padding: 20px 40px;

    .title-box {
      font-weight: 400;
    }
  }
}
</style>
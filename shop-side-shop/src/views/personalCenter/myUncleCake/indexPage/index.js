export default {
    name: "myUncleCakeIndex",
    data() {
        return {
            activeName: this.$route.query.status ? this.$route.query.status : 'orderAll',
            tabsList: [
                { label: '全部', name: 'orderAll' },
                { label: '待支付', name: 'order0' },
                { label: '待发货', name: 'order1' },
                { label: '待收货', name: 'order2' },
                { label: '已完成', name: 'order3' },
                // { label: '售后/退货', name: 'order4' },
                { label: '已关闭', name: 'order4' },
                { label: '退换货', name: 'order5' },
                { label: '已退款', name: 'order6' },
            ],
            showLig:false,
            orderList: [],
            page: 1,
            pageSize: 10,
            total: 0,
            searchDate: [],
            searchForm: {
                app_shop_name: "", // 商城名称
                order_sn: "", // 订单编号
                user_name: "", // 收货人姓名
                user_mobile: "", // 手机号
                start_at: "", // 开始日期
                end_at: "", // 结束日期
            },
            Logisticslist:[],//物流信息
        }
    },
    filters: {

        // 格式化订单状态
        formatStatus: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "待付款"
                    break;
                case 1:
                    name = "待发货"
                    break;
                case 2:
                    name = "待收货"
                    break;
                case 3:
                    name = "已完成"
                    break;
                case -1:
                    name = "已关闭"
                    break;
            }
            return name;
        },
    },
    mounted() {
        this.initOrderInfo();
    },

    methods: {
        handleClose(){
            this.showLig=false
        },
        //查看物流信息
        showLogistics(id){
            let that = this;
            that.showLig=true
            that.$post("cake/orderExpress", { order_no:id}).then(function (res) {
                if(res.code===0){
                    that.Logisticslist = res.data.data
                }

            })
        },
        exportTable() {
            let param = {
                page: this.page,
                pageSize: this.pageSize,
                gather_supply_type: 9,
                ...this.searchForm
            }
            if (this.searchDate && this.searchDate.length > 0) {
                param.start_at = this.searchDate[0]
                param.end_at = this.searchDate[1]
            } else {
                param.start_at = ""
                param.end_at = ""
            }
            switch (this.activeName) {
                case "orderAll":
                    break;
                case "order0":
                    param.status = "0";
                    break;
                case "order1":
                    param.status = "1";
                    break;
                case "order2":
                    param.status = "2";
                    break;
                case "order3":
                    param.status = "3";
                    break;
                case "order4":
                    param.status = "-1";
                    break;
                case "order5":
                    param.status = "5";
                    break;
                case "order6":
                    param.status = "6";
                    break;

                default:
                    break;
            }
            this.$get("/order/export", param).then(res => {
                if (res.code === 0) {
                    let link = this.$path + '/' + res.data.link
                    this.$fn.exportTable(link)
                } else {
                    this.$message.error(res.msg);
                }
            })
        },
        // 打开创建评论
        handleOpenCommentClick(order) {
            this.$refs.createCommentDialog.isShow = true
            this.$refs.createCommentDialog.setForm(order)
        },
        //确定按钮
        handleSearchClick() {
            this.page = 1
            this.pageSize = 10
            this.total = 0
            if (this.searchDate && this.searchDate.length > 0) {
                this.searchForm.start_at = this.searchDate[0]
                this.searchForm.end_at = this.searchDate[1]
            } else {
                this.searchForm.start_at = ""
                this.searchForm.end_at = ""
            }
            this.initOrderInfo()
        },
        // //跳转至发票详情
        // jumpBillDetail(bill_id) {
        //     this.$_blank("/personalCenter/myBillDetail", {
        //         detail_id: bill_id
        //     })
        // },
        //订单详情
        jumpOrderDetail(id, status = 'n') {
            this.$router.push({
                path: 'myOrderDetail',
                query: {
                    oid: id,
                    status
                }
            })
        },
        // 跳转商品详情
        goGoodsDialog(item) {
            this.$_blank("/goodsDetail", { goods_id: item.product_id })
        },
        //获取订单信息
        initOrderInfo() {
            let that = this;

            let para = {
                "page": that.page,
                "pageSize": that.pageSize,
                ...this.searchForm
            }

            switch (that.activeName) {
                case "orderAll":
                    break;
                case "order0":
                    para.status = "0";
                    break;
                case "order1":
                    para.status = "1";
                    break;
                case "order2":
                    para.status = "2";
                    break;
                case "order3":
                    para.status = "3";
                    break;
                case "order4":
                    para.status = "-1";
                    break;
                case "order5":
                    para.status = "5";
                    break;
                case "order6":
                    para.status = "6";
                    break;

                default:
                    break;
            }
            that.$get("cake/getLocalOrderList", para).then(function (res) {
                if (res.code == 0) {
                    that.orderList = res.data.list;
                    that.total = res.data.total;
                    that.tabsList.forEach(element => {
                        switch (element.name) {
                            case 'orderAll': // 全部
                                element.num =
                                    (typeof res.data?.WaitPayNum === 'number' ? res.data?.WaitPayNum : 0) +
                                    (typeof res.data?.WaitReceiveNum === 'number' ? res.data?.WaitReceiveNum : 0) + // 待收货
                                    (typeof res.data?.CompletedNum === 'number' ? res.data?.CompletedNum : 0) + // 已完成
                                    (typeof res.data?.BackNum === 'number' ? res.data?.BackNum : 0) + 
                                    (typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0) +
                                    (typeof res.data?.RefundNum === 'number' ? res.data?.RefundNum : 0) +
                                    (typeof res.data?.WaitSendNum === 'number' ? res.data?.WaitSendNum : 0)
                                break;
                            case 'order0':
                                element.num = typeof res.data?.WaitPayNum === 'number' ? res.data?.WaitPayNum : 0 // 待付款
                                break;
                            case 'order1':
                                element.num = typeof res.data?.WaitSendNum === 'number' ? res.data?.WaitSendNum : 0 // 待确认
                                break;
                            case 'order2':
                                element.num = typeof res.data?.WaitReceiveNum === 'number' ? res.data?.WaitReceiveNum : 0 // 已确认
                                break;
                            case 'order3':
                                element.num = typeof res.data?.CompletedNum === 'number' ? res.data?.CompletedNum : 0 // 已完成
                                break;
                            case 'order4':
                                element.num = typeof res.data?.ClosedNum === 'number' ? res.data?.ClosedNum : 0 // 已关闭
                                break;
                            case 'order5':
                                element.num =   typeof res.data?.BackNum === 'number' ? res.data?.BackNum : 0 // 退款中
                                break;
                            case 'order6':
                                element.num = typeof res.data?.RefundNum === 'number' ? res.data?.RefundNum : 0 // 已退款
                                break;
                            default:
                                break;
                        }
                    });
                }
            }).catch(function (res) {
                console.log(res);
            });
        },

        handleClick(tab) {
            this.page = 1;
            this.orderList = [];
            this.initOrderInfo();
        },

        pagination(val) {
            this.page = val.page;
            this.initOrderInfo();
        },


        //订单操作
        orderOperationDialog(id, operationItem) {
            if (!operationItem || operationItem.url == "" || operationItem.url == undefined) {
                return;
            }
            if (operationItem.url == "order/receive") {
                this.orderOperationReceive(id, operationItem);
                return;
            }
            if (operationItem.url == "order/close") {
                this.orderOperationCloseDialog(id, operationItem);
                return;
            }
            if (operationItem.url == "order/orderRefund") {
                this.orderFullRefund(id, operationItem);
                return;
            }
            if (operationItem.url == "order/send") {
                this.orderSend(id, operationItem);
                return;
            }
            if (operationItem.url == "order/pay") {
                this.orderPay(id, operationItem);
                return;
            }
        },
        // 跳转至申请退款/退货
        jumpRefund(order,item_id) {
            this.$router.push({
                path: '/refund',
                query: {
                    order_id:this.$fn.encode(order.id),
                    order_item_id:this.$fn.encode(item_id)
                }
            })
        },
        //确认付款
        orderPay(id, operationItem){
            this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.$post("order/pay", { order_id: id }).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.initOrderInfo()
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }).catch(() => {
            });
        },
        //确认发货
        orderSend(id, operationItem){
            this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.$post("order/send", { order_id: id }).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.initOrderInfo()
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }).catch(() => {
            });
        },
        // 确认收货
        orderOperationReceive(id, operationItem) {
            this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.$get("/order/receive", { order_id: id }).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.initOrderInfo()
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }).catch(() => {
            });
        },
        //全额退款
        orderFullRefund(id, operationItem){
            this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.$post("order/orderRefund", { order_id: id }).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.initOrderInfo()
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }).catch(() => {
            });
        },
        //订单关闭
        orderOperationCloseDialog(id, operationItem) {
            this.$confirm("是否【" + operationItem.title + "】?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.orderOperationClose(id, operationItem);
                }).catch(() => {
            });
        },

        orderOperationClose(id, operationItem) {
            let that = this;
            let para = {
                "order_id": id
            }
            this.$get("/order/close", para).then(function (res) {
                if (res.code == 0) {
                    console.log(res);
                    that.$message.success(res.msg);
                    that.initOrderInfo();
                }
            }).catch(function (res) {

            });
        },

        //订单支付
        orderOperationPay(id, operationItem) {
            let that = this;
            that.$router.push("/payment?para=" + id);
            // let para = {
            //     "order_ids": [id]
            // }
            // this.$post("/trade/cashier", para).then(function (res) {
            //     console.log(res);
            //     if (res.code == 0) {
            //         ///goodsDetail?goods_id=${item.id}`
            //         that.$router.push("/payment?para="+id);
            //     }
            // }).catch(function (res) {
            //     console.log(res);
            // });
        },
    }

}
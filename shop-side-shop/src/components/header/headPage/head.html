<div>
    <!-- 顶部 -->
    <div class="top-box minw1200" v-if="flg === 1 && isShow">
        <div class=" f fjsb fac inner">
            <div class="f">
                <span>{{ welcome }}</span>
                <div v-if="isLogin" class="out-box">
                    <span @click="$_blank('/personalCenter')">{{ userName }}</span>
                    <el-divider direction="vertical"></el-divider>
                    <router-link to="/login">退出登录</router-link>
                </div>
            </div>
            <div class="f fac">
                <div class="link-box" @click="$_blank('/personalCenter/generalize')">
                    <i class="iconfont icon-pc_line_member"></i>
                    <a href="javascript:;">我的</a>
                </div>
                <el-divider direction="vertical"></el-divider>
                <div class="link-box" @click="$_blank('/buyerCart')">
                    <i class="iconfont icon-pc_line_goodcar"></i>
                    <a href="javascript:;">购物车</a>
                </div>
                <el-divider direction="vertical"></el-divider>
                <div class="link-box" @click="$_blank('/personalCenter/myFavorite')">
                    <i class="iconfont icon-pc_line_like"></i>
                    <a href="javascript:;">我的收藏</a>
                </div>
                <!-- <a href="javascript:;" class="supplier-link" @click="junpSupplier">供货商入口</a> -->
                <a v-if="is_open_supplier_entry !== 2" href="javascript:;" class="supplier-link" @click="jumpSupplier">供货商入口</a>
            </div>
        </div>
    </div>
    <!-- 头部 -->
    <div class="bgw minw1200" v-if="isShow">
        <div class="inner">
            <div class="f" :class="flg === 2?'style2':''">
                <div class="logo-box">
                    <router-link to="/">
                        <!-- <img class="img100" :src="logo"> -->
                        <m-image :src="logo"></m-image>
                    </router-link>
                </div>
                <div class="search-box" :class="flg === 2?'f':''">
                    <el-input class="search-input" @keyup.enter.native="handleSearchClick" size="small" :placeholder="placeHolder"
                              v-model="keyword">
                        <!--                        <i slot="prefix" class="iconfont icon-pc_line_search"></i>-->
                        <el-select v-model="searchType" popper-class="search-select" :popper-append-to-body="false" slot="prepend">
                            <el-option v-for="item in searchList" :key="item.id" :label="item.title"
                                       :value="item.value"></el-option>
                        </el-select>
                        <el-button slot="append" @click="handleSearchClick">搜索</el-button>
                    </el-input>
<!--                    <el-badge :value="3" v-if="flg === 2">-->
<!--                        <el-button @click="$router.push('/buyerCart')" class="gwc-btn" size="small"-->
<!--                            icon="iconfont icon-pc_solid_goodcar">-->
<!--                            <span>购物车</span>-->
<!--                        </el-button>-->
<!--                    </el-badge>-->
                    <div class="hot-search-box" v-if="flg === 1">
                        <span class="left-title">热门搜索: </span>
                        <span class="right-item" v-for="word in hotWords" :key="word.id"
                              @click="handleKeywordClick(word)">
                            <a href="javascript:;">{{ word }}</a>
                        </span>
                    </div>
                </div>
                <div class="data-statistics">

                    <div class="data-statisticsLeft" v-if="is_show_product_count == '1'">
                        <scroll-number :value="goodNum"></scroll-number>
                        <span class="goodText">商品总量(sku)</span>
                    </div>
                    <div class="data-statisticsRight" v-if="is_show_sale_count == '1'">
                        <scroll-number :value="productNum"></scroll-number>
                        <span class="goodText">销售总量(件)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
.top-box {
  height: 40px;
  line-height: 40px;
  background: #f1f1f1;
  font-size: 14px;
  color: #666666;

  .out-box {
    margin-left: 44px;

    & > span {
      cursor: pointer;
    }

    span,
    a {
      color: #f42121;
    }

    .el-divider {
      margin: 0 17px;
    }
  }

  .link-box {
    cursor: pointer;

    i {
      margin-right: 10px;
      font-weight: bold;
    }
  }

  .el-divider {
    margin: 0 23px;
    height: 18px;
  }

  .supplier-link {
    margin-left: 20px;
    color: #f42121;
    font-weight: 500;
  }
}
.style2 {
  .logo-box {
    width: 271px;
    height: 100px;
    align-items: center;
  }

  .search-box {
    margin-left: 34px;
    position: relative;
    height: 40px;

    ::v-deep .el-badge {
      position: absolute;
      left: 521px;
      top: 0;

      .el-badge__content.is-fixed {
        right: 42px;
        top: 19px;
      }

      .el-button.gwc-btn {
        margin: 0;
        padding: 0;
        text-align: left;
        width: 159px;
        height: 40px !important;
        border: 1px solid #e2e2e2;
        border-radius: 0;

        i {
          margin-left: 24px;
        }

        span:nth-child(1) {
          margin-left: 12px;
          font-size: 16px;
        }
      }
    }
  }
}

.logo-box {
  width: 321px;
  height: 115px;
}

.search-box {
  margin-top: 22px;
  margin-left: 19px;
  width: 490px;

  ::v-deep .el-input.search-input {
    height: 40px;
    border: 2px solid #f42121;

    .el-select {
      input {
        width: 70px;
        padding-left: 10px;
      }

      i {
        color: #ffffff;
      }

      .search-select {
        .el-select-dropdown__item.selected {
          color: red !important;
        }
      }
    }

    input:nth-child(2) {
      height: 44px;

      &:focus {
        border-color: #DCDFE6
      }
    }

    .el-input-group__prepend, .el-input-group__append {
      background-color: #f42121;
      border-color: #f42121;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      color: #ffffff;
    }

    .el-input-group__append {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      width: 90px;
      text-align: center;
      font-size: 16px;
    }
  }

  .hot-search-box {
    font-size: 14px;
    margin-top: 8px;

    .left-title {
      margin-right: 8px;
      color: #494949;
    }

    .right-item {
      margin-right: 9px;
      color: #b0acac;
    }
  }
}
/*
数据统计
*/
.data-statistics{
  //padding: 0 20px 0 20px;
  //border: 1px solid red;
  margin-left: 60px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  .data-statisticsLeft{
    display: flex;
    flex-direction: column;
    align-items: center;

    .goodNum{
      color:red;
      font-size:20px
    }
    .goodText{
      font-size:14px;
    }

  }
  .data-statisticsRight{
    margin-left: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .goodNum{
      color:red;
      font-size:20px
    }
    .goodText{
      font-size:14px;
    }
  }
}

package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"merchant/model"
	"merchant/service"
	yzResponse "yz-go/response"
)

// 查询Setting
func FindSetting(c *gin.Context) {
	err, setting := service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

// 更新Setting
func UpdateSetting (c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = service.SaveSetting(setting)
	if err == nil {
		yzResponse.OkWithMessage("修改成功", c)
	} else {
		yzResponse.FailWithMessage("修改失败", c)
	}
}

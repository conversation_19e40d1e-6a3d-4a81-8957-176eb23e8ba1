package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"merchant/model"
	"merchant/request"
	"merchant/service"
	yzResponse "yz-go/response"
)

// 新建招商等级
func CreateLevel(c *gin.Context) {
	var level model.MerchantLevel
	err := c.ShouldBindJSON(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateLevel(level); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// 更新招商等级
func UpdateLevel(c *gin.Context) {
	var level model.MerchantLevel
	err := c.Should<PERSON>ind<PERSON>(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateLevel(level); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// 删除招商等级
func DeleteLevel(c *gin.Context) {
	var level model.MerchantLevel
	err := c.ShouldBindJSON(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteLevel(level.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// 根据ID获取招商等级
func FindLevel(c *gin.Context) {
	var level model.MerchantLevel
	err := c.ShouldBindJSON(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, level := service.FindLevel(level.ID); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"level": level}, c)
	}
}

// 分页获取招商等级列表
func GetLevelList(c *gin.Context) {
	var pageInfo request.LevelSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetLevelList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// 获取全部招商等级无分页
func GetAllLevel(c *gin.Context) {
	if err, levels := service.GetAllLevel(); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"levels": levels}, "获取成功", c)
	}
}

package distributor

import "yz-go/source"

type Distributor struct {
	source.Model
	Uid                       uint      `json:"uid" gorm:"column:uid;comment:会员id;"`
	LevelID                   uint      `json:"level_id" gorm:"column:level_id;comment:等级id;"`
}

// 通过等级id获取会员ids
func GetUserIdsByLevelId(levelId uint) (err error, userIds []uint) {
	err = source.DB().Model(&Distributor{}).Where("level_id = ?", levelId).Pluck("id", &userIds).Error
	return
}

// 通过分销商等级获取不是招商的会员ids
func GetUserIdsByLevelIdAndNotByMerchantUserIds(levelId uint, merchantUserIds []uint) (err error, userIds []uint) {
	err = source.DB().Model(&Distributor{}).Where("level_id = ? AND id NOT IN ?", levelId, merchantUserIds).Pluck("id", &userIds).Error
	return
}
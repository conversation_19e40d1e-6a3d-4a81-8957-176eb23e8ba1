package upgrade

import (
	"merchant/model"
	"merchant/service"
	"merchant/supplier"
	"merchant/user"
	orderModel "order/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

// 通过订单升级
func OrderHandle(userId uint) (err error) {
	//log.Log().Info("MERCHANT:OrderHandle(" + strconv.Itoa(int(userId)) + ")")
	// 查询招商
	var merchant model.Merchant
	err, merchant = service.GetMerchantByUserId(userId)
	if err != nil {
		//log.Log().Info("MERCHANT:查询招商出错,返回")
		return
	}
	var levels []model.MerchantLevel
	// 获取等级[]
	if merchant.ID == 0 {
		//log.Log().Info("MERCHANT:获取等级,条件[" + strconv.Itoa(0) + "]")
		err, levels = getLevelsByWeight(0)
	} else {
		//log.Log().Info("MERCHANT:获取等级,条件[" + strconv.Itoa(merchant.LevelInfo.Weight) + "]")
		err, levels = getLevelsByWeight(merchant.LevelInfo.Weight)
	}
	if err != nil {
		//log.Log().Info("MERCHANT:查询招商等级出错,返回")
		return
	}
	if len(levels) == 0 {
		//log.Log().Info("MERCHANT:查询招商结果为空,返回")
		return
	}
	// 要升级的等级id
	var updateLevelId uint
	// 是否可以升级
	var res bool
	for _, level := range levels {
		//log.Log().Info("MERCHANT:等级验证ID[" + strconv.Itoa(int(level.ID)) + "]")
		if level.UpgradeCode == model.UpgradeCodeOR {
			//log.Log().Info("MERCHANT:升级条件为:或")
			// 升级条件为:或
			err, res = or(userId, level)
		} else {
			//log.Log().Info("MERCHANT:升级条件为:与")
			// 升级条件为:与
			err, res = with(userId, level)
		}
		if err != nil {
			//log.Log().Info("MERCHANT:等级验证出错,返回")
			return
		}
		if res == true {
			//log.Log().Info("MERCHANT:当前等级满足升级条件,break")
			updateLevelId = level.ID
			break
		} else {
			//log.Log().Info("MERCHANT:当前等级不满足升级条件,continue")
			continue
		}
	}
	if updateLevelId == 0 {
		//log.Log().Info("MERCHANT:没有可升级的等级,返回")
		return
	}
	if merchant.ID == 0 {
		//log.Log().Info("MERCHANT:成为招商,success")
		// 成为
		merchant.Uid = userId
		merchant.LevelID = updateLevelId
		err = source.DB().Create(&merchant).Error
	} else {
		//log.Log().Info("MERCHANT:招商升级,success")
		// 升级
		err = updateMerchantLevelIdById(merchant.ID, updateLevelId)
	}
	if err != nil {
		//log.Log().Info("MERCHANT:升级程序失败,返回")
		return
	}
	return
}

// 通过供应商升级
func SupplierHandle(userId uint) (err error) {
	//log.Log().Info("MERCHANT:SupplierHandle(" + strconv.Itoa(int(userId)) + ")")
	var parentId uint
	err, parentId = user.GetParentIdByUserId(userId)
	if err != nil {
		//log.Log().Info("MERCHANT:查询上级失败,返回")
		return
	}
	// 查询招商
	var merchant model.Merchant
	err, merchant = service.GetMerchantByUserId(parentId)
	if err != nil {
		//log.Log().Info("MERCHANT:查询招商出错,返回")
		return
	}
	var levels []model.MerchantLevel
	// 获取等级[]
	if merchant.ID == 0 {
		//log.Log().Info("MERCHANT:获取等级,条件[" + strconv.Itoa(0) + "]")
		err, levels = getLevelsByWeight(0)
	} else {
		//log.Log().Info("MERCHANT:获取等级,条件[" + strconv.Itoa(merchant.LevelInfo.Weight) + "]")
		err, levels = getLevelsByWeight(merchant.LevelInfo.Weight)
	}
	if err != nil {
		//log.Log().Info("MERCHANT:查询招商等级出错,返回")
		return
	}
	if len(levels) == 0 {
		//log.Log().Info("MERCHANT:查询等级为空,返回")
		return
	}
	// 要升级的等级id
	var updateLevelId uint
	// 是否可以升级
	var res bool
	for _, level := range levels {
		//log.Log().Info("MERCHANT:等级验证ID[" + strconv.Itoa(int(level.ID)) + "]")
		if level.UpgradeCode == model.UpgradeCodeOR {
			//log.Log().Info("MERCHANT:升级条件为:或")
			// 升级条件为:或
			err, res = or(parentId, level)
		} else {
			//log.Log().Info("MERCHANT:升级条件为:与")
			// 升级条件为:与
			err, res = with(parentId, level)
		}
		if err != nil {
			//log.Log().Info("MERCHANT:等级验证出错,返回")
			return
		}
		if res == true {
			//log.Log().Info("MERCHANT:当前等级满足升级条件,break")
			updateLevelId = level.ID
			break
		} else {
			//log.Log().Info("MERCHANT:当前等级不满足升级条件,continue")
			continue
		}
	}
	if merchant.ID == 0 {
		//log.Log().Info("MERCHANT:成为招商,success")
		// 成为
		merchant.Uid = parentId
		merchant.LevelID = updateLevelId
		err = source.DB().Create(&merchant).Error
	} else {
		//log.Log().Info("MERCHANT:招商升级,success")
		// 升级
		err = updateMerchantLevelIdById(merchant.ID, updateLevelId)
	}
	if err != nil {
		//log.Log().Info("MERCHANT:升级程序失败,返回")
		return
	}
	return
}

// 升级方式:与
func with(userId uint, level model.MerchantLevel) (err error, res bool) {
	// 等级没有勾选升级条件,不能升级
	if level.OrderAmountSwitch != 1 && level.OrderCountSwitch != 1 && level.RecommendSupplierSwitch != 1 && level.RecommendOrderAmountSwitch != 1 && level.RecommendOrderCountSwitch != 1 && level.BuyProductSwitch != 1 {
		res = false
		return
	}
	err, res = oneselfOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = oneselfOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = oneselfProductAmong(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = recommendUserTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = recommendOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = recommendOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	return
}

// 升级方式:或
func or(userId uint, level model.MerchantLevel) (err error, res bool) {
	err, res = oneselfOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = oneselfOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = oneselfProductAmong(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = recommendUserTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = recommendOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = recommendOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	return
}

// 自己购买订单数量
func oneselfOrderCountTotal(userId uint, level model.MerchantLevel) (err error, res bool) {
	//log.Log().Info("MERCHANT:自己购买订单数量|oneselfOrderCountTotal()")
	// 默认等于假
	res = false
	if level.OrderCountSwitch == 1 {
		//log.Log().Info("MERCHANT:oneselfOrderCountTotal()条件开启")
		var resTotal int64
		err, resTotal = getOrderCountTotalByUserId(userId)
		if err != nil {
			//log.Log().Info("MERCHANT:oneselfOrderCountTotal()查询条件出错")
			return
		}
		//log.Log().Info("MERCHANT:oneselfOrderCountTotal()查询结果[" + strconv.FormatInt(resTotal, 10) + "]条件[" + strconv.FormatInt(int64(level.OrderCountTotal), 10) + "]")
		if resTotal >= int64(level.OrderCountTotal) {
			//log.Log().Info("MERCHANT:oneselfOrderCountTotal()满足条件")
			res = true
		}
	}
	return
}

// 自己购买订单总额
func oneselfOrderAmountTotal(userId uint, level model.MerchantLevel) (err error, res bool) {
	//log.Log().Info("MERCHANT:自己购买订单总额|oneselfOrderAmountTotal()")
	// 默认等于假
	res = false
	if level.OrderAmountSwitch == 1 {
		//log.Log().Info("MERCHANT:oneselfOrderAmountTotal()条件开启")
		var resTotal int
		err, resTotal = getOrderAmountTotalByUserId(userId)
		if err != nil {
			//log.Log().Info("MERCHANT:oneselfOrderAmountTotal()查询条件出错")
			return
		}
		//log.Log().Info("MERCHANT:oneselfOrderAmountTotal()查询结果[" + strconv.FormatInt(int64(resTotal), 10) + "]条件[" + strconv.FormatInt(int64(level.OrderAmountTotal), 10) + "]")
		if resTotal >= level.OrderAmountTotal {
			//log.Log().Info("MERCHANT:oneselfOrderAmountTotal()满足条件")
			res = true
		}
	}
	return
}

// 自己购买指定商品之一
func oneselfProductAmong(userId uint, level model.MerchantLevel) (err error, res bool) {
	//log.Log().Info("MERCHANT:自己购买指定商品之一|oneselfProductAmong()")
	// 默认等于假
	res = false
	if level.BuyProductSwitch == 1 {
		//log.Log().Info("MERCHANT:oneselfProductAmong()条件开启")
		var productIds []uint
		err, productIds = getLevelUpdateProductIds(level.ID)
		if err != nil {
			//log.Log().Info("MERCHANT:oneselfProductAmong()查询等级条件出错")
			return
		}
		err, res = isBoughtProduct(productIds, userId)
		if err != nil {
			//log.Log().Info("MERCHANT:oneselfProductAmong()查询满足条件出错")
			return
		}
	}
	return
}

// 推荐供应商数量
func recommendUserTotal(userId uint, level model.MerchantLevel) (err error, res bool) {
	//log.Log().Info("MERCHANT:推荐供应商数量|recommendUserTotal()")
	// 默认等于假
	res = false
	if level.RecommendSupplierSwitch == 1 {
		//log.Log().Info("MERCHANT:recommendUserTotal()条件开启")
		var childIds []uint
		err, childIds = user.GetChildIdsByUserId(userId)
		if err != nil {
			//log.Log().Info("MERCHANT:recommendUserTotal()查询childIds出错")
			return
		}
		var total int64
		err, total = supplier.GetTotalByUserIds(childIds)
		if err != nil {
			//log.Log().Info("MERCHANT:recommendUserTotal()查询供应商总数出错")
			return
		}
		//log.Log().Info("MERCHANT:recommendUserTotal()查询结果[" + strconv.FormatInt(total, 10) + "]条件[" + strconv.FormatInt(int64(level.RecommendSupplierTotal), 10) + "]")
		if int(total) >= level.RecommendSupplierTotal {
			//log.Log().Info("MERCHANT:recommendUserTotal()满足条件")
			res = true
		}
	}
	return
}

// 推荐供应商订单数量
func recommendOrderCountTotal(userId uint, level model.MerchantLevel) (err error, res bool) {
	//log.Log().Info("MERCHANT:推荐供应商订单数量|recommendOrderCountTotal()")
	// 默认等于假
	res = false
	if level.RecommendOrderCountSwitch == 1 {
		//log.Log().Info("MERCHANT:recommendOrderCountTotal()条件开启")
		var childIds, sids []uint
		err, childIds = user.GetChildIdsByUserId(userId)
		if err != nil {
			//log.Log().Info("MERCHANT:recommendOrderCountTotal()查询childIds出错")
			return
		}
		err, sids = supplier.GetSupplierIdsByUserIds(childIds)
		if err != nil {
			//log.Log().Info("MERCHANT:recommendOrderCountTotal()查询供应商总数出错")
			return
		}
		var total int64
		err, total = getOrderCountTotalBySids(sids)
		if err != nil {
			//log.Log().Info("MERCHANT:recommendOrderCountTotal()查询供应商订单总数出错")
			return
		}
		//log.Log().Info("MERCHANT:recommendOrderCountTotal()查询结果[" + strconv.FormatInt(total, 10) + "]条件[" + strconv.FormatInt(int64(level.RecommendOrderCountTotal), 10) + "]")
		if int(total) >= level.RecommendOrderCountTotal {
			//log.Log().Info("MERCHANT:recommendOrderCountTotal()满足条件")
			res = true
		}
	}
	return
}

// 推荐供应商订单总额
func recommendOrderAmountTotal(userId uint, level model.MerchantLevel) (err error, res bool) {
	//log.Log().Info("MERCHANT:推荐供应商订单总额|recommendOrderAmountTotal()")
	// 默认等于假
	res = false
	if level.RecommendOrderAmountSwitch == 1 {
		//log.Log().Info("MERCHANT:recommendOrderAmountTotal()条件开启")
		var childIds, sids []uint
		err, childIds = user.GetChildIdsByUserId(userId)
		if err != nil {
			log.Log().Info("MERCHANT:recommendOrderAmountTotal()查询childIds出错")
			return
		}
		err, sids = supplier.GetSupplierIdsByUserIds(childIds)
		if err != nil {
			log.Log().Info("MERCHANT:recommendOrderAmountTotal()查询sids出错")
			return
		}
		var total int
		err, total = getOrderAmountTotalBySids(sids)
		if err != nil {
			log.Log().Info("MERCHANT:recommendOrderAmountTotal()查询供应商订单总额出错")
		}
		log.Log().Info("MERCHANT:recommendOrderAmountTotal()查询结果[" + strconv.FormatInt(int64(total), 10) + "]条件[" + strconv.FormatInt(int64(level.RecommendOrderAmountTotal), 10) + "]")
		if total >= level.RecommendOrderAmountTotal {
			log.Log().Info("MERCHANT:recommendOrderAmountTotal()满足条件")
			res = true
		}
	}
	return
}

type Order struct {
	source.Model
	CreatedAt            *source.LocalTime      `json:"created_at" gorm:"index;"`
	UpdatedAt            *source.LocalTime      `json:"updated_at" gorm:"index;"`
	OrderSN              uint                   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`                                                 // 编号
	ThirdOrderSN         string                 `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`                               // 编号
	Key                  string                 `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(255);size:255;"`                                     // 标识
	Title                string                 `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`                               // 标题
	Status               orderModel.OrderStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`                        // 订单状态
	Amount               uint                   `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`                                                 // 订单总金额
	ItemAmount           uint                   `json:"item_amount" form:"item_amount" gorm:"column:item_amount;comment:商品市场价(分);"`                                  // 商品市场价
	SupplyAmount         uint                   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`                             // 供货金额
	CostAmount           uint                   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(分);"`                                   // 成本金额
	Freight              uint                   `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                 // 运费(单位:分)
	ServiceFee           uint                   `json:"service_fee" form:"service_fee" gorm:"column:service_fee;comment:服务费(分);"`                                    // 服务费
	GoodsCount           uint                   `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品总数;"`                            // 商品总数
	TechnicalServicesFee uint                   `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"` // 技术服务费
	UserID               uint                   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`
	ApplicationID        uint                   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;index;"`
	OrderItems           OrderItems             `json:"order_items"`
}

type OrderItems []OrderItem
type OrderItem struct {
	source.Model
	Key             string `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(255);size:255;"`                               // 标识
	Title           string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`                         // 名
	SkuTitle        string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(255);size:255;"`          // 规格名
	Unit            string `json:"unit" form:"unit" gorm:"column:unit;comment:单位;type:varchar(20);size:20;"`                              // 单位
	Qty             uint   `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                                                        // 商品数量
	Amount          uint   `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`                                                 // 总价(元)
	PaymentAmount   uint   `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:均摊支付金额(元);"`                  // 均摊支付金额(元)
	DiscountAmount  uint   `json:"discount_amount" form:"discount_amount" gorm:"column:discount_amount;comment:优惠金额(元);"`                 // 优惠金额(元)
	DeductionAmount uint   `json:"deduction_amount" form:"deduction_amount" gorm:"column:deduction_amount;comment:抵扣金额(元);"`              // 抵扣金额(元)
	CostAmount      uint   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(元);"`                             // 成本金额(元)
	SupplyAmount    uint   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(元);"`                       // 供货金额(元)
	ImageUrl        string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`           // 图片地址
	SendStatus      int    `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态;type:smallint;size:3;"` // 发货状态

	TradeID    uint `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`           // 交易id
	SupplierID uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"` // 供应商id
	UserID     uint `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`              // 用户id
	OrderID    uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`           // 订单id
	ProductID  uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;"`     // 产品id
	SkuID      uint `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku id;"`               // sku id
}

// 通过订单id获取订单
func GetOrderById(id uint) (err error, order Order) {
	err = source.DB().Where("id = ?", id).First(&order).Error
	return
}

// 通过会员id获取订单总数
func getOrderCountTotalByUserId(userId uint) (err error, total int64) {
	err = source.DB().Model(&Order{}).Where("user_id = ? AND status >= ?", userId, orderModel.WaitSend).Count(&total).Error
	return
}

// 通过供应商ids获取订单总数
func getOrderCountTotalBySids(sids []uint) (err error, total int64) {
	err = source.DB().Model(&Order{}).Where("supplier_id IN ? AND status >= ?", sids, orderModel.WaitSend).Count(&total).Error
	return
}

// 通过会员id获取订单总额
func getOrderAmountTotalByUserId(userId uint) (err error, total int) {
	err = source.DB().Model(&Order{}).Where("user_id = ? AND status >= ?", userId, orderModel.WaitSend).Pluck("COALESCE(SUM(amount), 0) as amount1", &total).Error
	return
}

// 通过会员ids获取订单总额
func getOrderAmountTotalBySids(sids []uint) (err error, total int) {
	err = source.DB().Model(&Order{}).Where("supplier_id IN ? AND status >= ?", sids, orderModel.WaitSend).Pluck("COALESCE(SUM(amount), 0) as amount1", &total).Error
	return
}

// 通过等级id获取升级需要购买的商品ids
func getLevelUpdateProductIds(levelId uint) (err error, productIds []uint) {
	err = source.DB().Model(&model.MerchantLevelUpgradeProduct{}).Where("merchant_level_id = ?", levelId).Pluck("product_id", &productIds).Error
	return
}

// 通过等级权重获取等级[]
func getLevelsByWeight(weight int) (err error, levels []model.MerchantLevel) {
	err = source.DB().Where("weight > ?", weight).Order("weight DESC").Find(&levels).Error
	return
}

// 验证是否购买过可以升级的商品
func isBoughtProduct(productIds []uint, userId uint) (err error, res bool) {
	var orderIds []uint
	err = source.DB().Model(&Order{}).Where("user_id = ? AND status >= ?", userId, orderModel.WaitSend).Pluck("id", &orderIds).Error
	if err != nil {
		return
	}
	var total int64
	err = source.DB().Model(&OrderItem{}).Where("order_id IN ? AND product_id IN ?", orderIds, productIds).Count(&total).Error
	if total > 0 {
		res = true
	}
	return
}

// 通过招商id修改招商等级
func updateMerchantLevelIdById(merchantId uint, levelId uint) (err error) {
	err = source.DB().Model(&model.Merchant{}).Where("id = ?", merchantId).Update("level_id", levelId).Error
	return
}

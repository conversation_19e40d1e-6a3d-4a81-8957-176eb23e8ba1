package model

import (
	"gorm.io/gorm"
	"merchant/supplier"
	"merchant/user"
	"yz-go/source"
)

type MerchantMigration struct {
	source.Model
	Uid                       uint  `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	LevelID                   uint  `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	RecommendOrderCountTotal  int   `json:"recommend_order_count_total" gorm:"column:recommend_order_count_total;comment:累计推荐订单总数;"`
	RecommendOrderAmountTotal uint  `json:"recommend_order_amount_total" gorm:"column:recommend_order_amount_total;comment:累计推荐订单总额;"`
	SupplierTotal             int64 `json:"supplier_total" gorm:"-"`
	SettleAmountTotal         uint  `json:"settle_amount_total" gorm:"-"`
	FinishSettleAmount        uint  `json:"finish_settle_amount" gorm:"-"`
	WaitSettleAmount          uint  `json:"wait_settle_amount" gorm:"-"`
}

func (MerchantMigration) TableName() string {
	return "merchants"
}

type Merchant struct {
	source.Model
	Uid                       uint      `json:"uid" gorm:"column:uid;comment:会员id;index;"`
	LevelID                   uint      `json:"level_id" gorm:"column:level_id;comment:等级id;"`
	RecommendOrderCountTotal  int       `json:"recommend_order_count_total" gorm:"column:recommend_order_count_total;comment:累计推荐订单总数;"`
	RecommendOrderAmountTotal uint      `json:"recommend_order_amount_total" gorm:"column:recommend_order_amount_total;comment:累计推荐订单总额;"`
	SupplierTotal             int64     `json:"supplier_total" gorm:"-"`
	SettleAmountTotal         uint      `json:"settle_amount_total" gorm:"-"`
	FinishSettleAmount        uint      `json:"finish_settle_amount" gorm:"-"`
	WaitSettleAmount          uint      `json:"wait_settle_amount" gorm:"-"`
	UserInfo                  user.User `json:"user_info" gorm:"foreignKey:Uid"`
	LevelInfo                 Level     `json:"level_info" gorm:"foreignKey:LevelID"`
}

func (d *Merchant) AfterFind(tx *gorm.DB) (err error) {
	err, d.SettleAmountTotal = getAllAmountTotalByUid(tx, d.Uid)
	if err != nil {
		return
	}
	err, d.FinishSettleAmount = getAmountTotalByUidAndStatus(tx, d.Uid, Settled)
	if err != nil {
		return
	}
	err, d.WaitSettleAmount = getAmountTotalByUidAndStatus(tx, d.Uid, Wait)
	if err != nil {
		return
	}

	var childIds []uint
	err, childIds = user.GetChildIdsByUserId(d.Uid)
	if err != nil {
		return
	}
	err, d.SupplierTotal = supplier.GetSupplierTotalByUserIdsAndTx(tx, childIds)
	return
}

// 通过会员id和分成状态获取分成总额
func getAmountTotalByUidAndStatus(tx *gorm.DB, uid uint, status int) (err error, amountTotal uint) {
	err = tx.Model(&MerchantAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ? AND status = ?", uid, status).First(&amountTotal).Error
	return
}

// 通过会员id获取全部分成总额
func getAllAmountTotalByUid(tx *gorm.DB, uid uint) (err error, amountTotal uint) {
	err = tx.Model(&MerchantAward{}).Select("COALESCE(SUM(amount), 0)").Where("uid = ?", uid).First(&amountTotal).Error
	return
}

type Level struct {
	source.Model
	Name               string             `json:"name" gorm:"name;comment:等级名称;type:varchar(50);size:50;"`
	Weight             int                `json:"weight" gorm:"column:weight;comment:等级权重;"`
	SupplierSettleInfo SupplierSettleInfo `json:"supplier_settle_info" gorm:"column:supplier_settle_info;comment:供应商订单结算详情;type:json;"`
}

func (Level) TableName() string {
	return "merchant_levels"
}

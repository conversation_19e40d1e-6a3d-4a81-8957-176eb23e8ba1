package order

import (
	orderModel "order/model"
	"yz-go/source"
)

type Order struct {
	source.Model
	Status     orderModel.OrderStatus `json:"status"`
	Amount     uint                   `json:"amount"`
	UserID     uint                   `json:"user_id"`
	SupplierID uint                   `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"`
}

// 获取已支付订单的会员ids
func GetUserIdsByPaid() (err error, userIds []uint) {
	err = source.DB().Model(&Order{}).Distinct("user_id").Where("status > ?", orderModel.WaitPay).Pluck("user_id", &userIds).Error
	return
}

// 获取不是招商的会员ids
func GetUserIdsNotByMerchantUserIds(merchantUserIds []uint) (err error, userIds []uint) {
	err = source.DB().Model(&Order{}).Distinct("user_id").Where("status > ? AND user_id NOT IN ?", orderModel.WaitPay, merchantUserIds).Pluck("user_id", &userIds).Error
	return
}

// 通过供应商ids获取订单总额
func GetAmountTotalBySids(sids []uint, status int8) (err error, total int64) {
	err = source.DB().Model(&Order{}).Select("COALESCE(SUM(amount), 0)").Where("supplier_id IN ? AND status = ?", sids, status).First(&total).Error
	return
}

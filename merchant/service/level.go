package service

import (
	"errors"
	"gorm.io/gorm"
	"merchant/model"
	"merchant/request"
	"strconv"

	"yz-go/source"
)

// 创建等级
func CreateLevel(level model.MerchantLevel) (err error) {
	var verify model.MerchantLevel
	if !errors.Is(source.DB().Where("weight = ?", level.Weight).First(&verify).Error, gorm.ErrRecordNotFound) {
		return errors.New("等级权重已存在")
	}
	err = source.DB().Create(&level).Error
	return
}

// 删除等级
func DeleteLevel(levelId uint) (err error) {
	var total int64
	err, total = getMerchantCountByLevelId(levelId)
	if err != nil {
		return
	}
	if total > 0 {
		return errors.New("该等级下有[" + strconv.FormatInt(total, 10) + "]个招商商,不能删除")
	}
	// 根据主键删除
	err = source.DB().Delete(&model.MerchantLevel{}, levelId).Error
	return
}

// 修改等级
func UpdateLevel(level model.MerchantLevel) (err error) {
	var verify model.MerchantLevel
	if !errors.Is(source.DB().Where("weight = ? AND id != ?", level.Weight, level.ID).First(&verify).Error, gorm.ErrRecordNotFound) {
		return errors.New("等级权重已存在")
	}
	return source.DB().Transaction(func(tx *gorm.DB) (err error) {
		var upgradeProducts []model.MerchantLevelUpgradeProduct
		err = tx.Where("merchant_level_id = ?", level.ID).Delete(&upgradeProducts).Error
		if err != nil {
			return
		}
		err = tx.Select("*").Session(&gorm.Session{FullSaveAssociations: true}).Updates(&level).Error
		if err != nil {
			return
		}
		return err
	})
}

// 查询等级
func FindLevel(id uint) (err error, level model.MerchantLevel) {
	err = source.DB().Where("id = ?", id).Preload("UpgradeProducts").First(&level).Error
	return
}

// 分页获取等级列表
func GetLevelList(info request.LevelSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.MerchantLevel{})
	var levels []model.MerchantLevel
	err = db.Count(&total).Error
	err = db.Preload("UpgradeProducts").Order("weight desc").Order("created_at desc").Limit(limit).Offset(offset).Find(&levels).Error
	return err, levels, total
}

// 获取全部等级
func GetAllLevel() (err error, levels []model.MerchantLevel) {
	err = source.DB().Find(&levels).Error
	return
}

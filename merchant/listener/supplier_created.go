package listener

import (
	"go.uber.org/zap"
	"merchant/upgrade"
	"supplier/mq"
	"yz-go/component/log"
)

func PushSupplierCreatedHandles() {
	mq.PushHandles("supplierCreatedMerchantUpdate", func(supplierMsg mq.SupplierMessage) (err error) {
		//log.Log().Info("招商升级监听执行(供应商创建)会员id[" + strconv.Itoa(int(supplierMsg.UserID)) + "]")
		// 新增供应商会员升级
		err = upgrade.SupplierHandle(supplierMsg.UserID)
		if err != nil {
			//log.Log().Info("招商升级失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		return nil
	})
}

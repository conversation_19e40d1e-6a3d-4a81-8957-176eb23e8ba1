package goods

import (
	"bytes"
	catemodel "category/model"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"github.com/gogf/gf/frame/g"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	log2 "log"
	"math"
	"mime/multipart"
	url2 "net/url"
	pmodel "product/model"
	pservice "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	common2 "public-supply/common"
	"public-supply/model"
	"public-supply/request"
	"public-supply/service"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"sync"
	wmodel "tianma-supply/model"
	request2 "tianma-supply/request"
	"tianma-supply/response"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type TianMa struct {
	dat      *model.SupplySetting
	Setting  *model.SupplySetting
	SupplyID uint
	Http     string
}

func (y *TianMa) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (y *TianMa) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

func (y *TianMa) ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common.GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var result []string
	var field string
	field = "source_goods_id"
	if info.Key == "tianma" {
		field = "md5"
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &result).Error
		if err != nil {
			log2.Println("查询供应链商品id错误", err)
			return
		}
	}
	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}
	var idsArr []string
	idsArr = GetSnArr(info.List)
	difference := collection.Collect(idsArr).Diff(result).All()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	goodsRecord.Source = strconv.Itoa(common.TIANMA_SOURCE)

	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord)

	var goodsList []model.Goods
	for _, v := range difference {
		for _, item := range info.List {
			md5 := utils.MD5V([]byte(item.ShopName + item.SN))
			if md5 == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for _, item := range arrList {
		wg.Add(1)
		go y.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")
	return
}

func (y *TianMa) RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product

	var recordError []model.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = y.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (y *TianMa) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {

	return
}

func (y *TianMa) DeleteGoods(id uint) (err error) {
	//TODO implement me
	return
}

func (y *TianMa) GetToken() (err error) {

	return
}

func (y *TianMa) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {

	url := y.Http + "openapi/queryAccount.do"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("username", y.dat.BaseInfo.UserName)
	_ = writer.WriteField("password", y.dat.BaseInfo.PassWord)
	err = writer.Close()
	if err != nil {
		return
	}
	//reqData, _ := json.Marshal(&reqValue)
	resData, _ := utils.PostFormData(url, payload, writer.FormDataContentType())
	//balance = string(resData)
	//字符串转浮点数，并且*100之后变为整数
	balance1, _ := strconv.ParseFloat(string(resData), 64)
	balance = balance1 * 100
	if err != nil {
		return
	}

	return
}

func (y *TianMa) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	return
}

//func (y *TianMa) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product) {
//	//panic("implement me")
//}

func (y *TianMa) InitGoods() (err error) {
	return
}

func (y *TianMa) CommodityAssembly(list []model.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product, recordErrors []model.SupplyGoodsImportRecordErrors) {

	log.Log().Info("天马 CommodityAssembly 导入数据", zap.Any("", len(list)))
	if len(list) == 0 {
		log.Log().Error("选择可导商品数量为空", zap.Any("err", err))
		return
	}

	for _, item := range list {
		var reqData request2.InventoryListByGroup
		reqData.GatherSupplyID = y.SupplyID
		reqData.Articleno = item.SN
		reqData.WareHouseName = item.ShopName
		reqData.Sizes = item.Unit
		data, _ := y.GetInventoryListByGroup(reqData)
		if len(data.Rows) == 0 {
			continue
		}
		var goodsDetail []*pmodel.Product
		err, goodsDetail = y.CommodityAssemblyLocal(data.Rows[0], cateId1, cateId2, cateId3, item.ExpressDelivery)
		if err != nil {
			return
		}
		listGoods = append(listGoods, goodsDetail...)
	}
	log.Log().Info("天马 CommodityAssembly 导入数据", zap.Any("", len(listGoods)))

	return
}

func (y *TianMa) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	//panic("implement me")

	return
}

func (y *TianMa) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	y.Setting = y.dat
	if y.dat.BaseInfo.UserName == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	y.Http = y.dat.BaseInfo.ApiUrl + "/api/"
	y.dat.BaseInfo.AppSecret = y.dat.BaseInfo.AppSecret
	y.dat.BaseInfo.UserName = y.dat.BaseInfo.UserName
	y.dat.BaseInfo.PassWord = y.dat.BaseInfo.PassWord
	//
	//y.GetToken()

	if y.Http == "" {
		y.Http = "http://253.open.test.tmyd.tianmagroup.com/api/"
	}

	//y.dat.BaseInfo.Channel = "GOUSHIHUI"
	//y.dat.BaseInfo.AppSecret = "a7d1f3d8e1f541b7bde9736e86b4305b"

	return
}

func (y *TianMa) GetStock(pid []string) (goodsStock interface{}, err error) {

	return
}

func (y *TianMa) GetWareHouseNameInfo(wareHouse request2.WareHouse) (WareHouseName wmodel.WareHouseName, err error) {
	url := y.Http + "/openapi/getWareHouseNameInfo.do"
	payload := &bytes.Buffer{}
	var resData []byte

	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("page", wareHouse.Page)
	_ = writer.WriteField("rows", wareHouse.Rows)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &WareHouseName)

	if err != nil {
		return
	}
	return
}

func (y *TianMa) GetGoodsDetail(reqData wmodel.Article) (goodsDetail wmodel.GoodsDetail, err error) {
	url := y.Http + "/openapi/queryProductInfo.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("goods_no", reqData.ArticleNO)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &goodsDetail)

	if err != nil {
		return
	}
	return
}

func (y *TianMa) GetDeliveryInfo(reqData request2.WareHouse) (deliveryInfo wmodel.DeliveryInfo, err error) {
	url := y.Http + "/openapi/getDeliveryInfo.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("wareHouseName", reqData.WareHouseName)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &deliveryInfo)

	if err != nil {
		return
	}
	return
}

func (y *TianMa) GetDeliveryTimeList(reqData wmodel.DeliveryTimeData) (deliveryTimeList wmodel.DeliveryTimeList, err error) {
	url := y.Http + "/openapi/getDeliveryTimeList.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	_ = writer.WriteField("page", reqData.Page)
	_ = writer.WriteField("rows", reqData.Rows)
	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &deliveryTimeList)

	if err != nil {
		return
	}
	return
}

func (y *TianMa) GetInventoryListByGroup(reqData request2.InventoryListByGroup) (inventoryListByGroupGoods response.InventoryListByGroupGoods, err error) {
	url := y.Http + "/openapi/getInventoryListByGroup.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", reqData.Rows)
	}
	if reqData.BrandNames != "" {
		_ = writer.WriteField("brand_names", reqData.BrandNames)
	}
	if reqData.Sizes != "" {
		_ = writer.WriteField("sizes", reqData.Sizes)
	}
	if reqData.Sexs != "" {
		_ = writer.WriteField("sexs", reqData.Sexs)
	}
	if reqData.Divisions != "" {
		_ = writer.WriteField("divisions", reqData.Divisions)
	}
	if reqData.Quarters != "" {
		_ = writer.WriteField("quarters", reqData.Quarters)
	}
	if reqData.Articleno != "" {
		_ = writer.WriteField("articleno", reqData.Articleno)
	}
	if reqData.MaxMarketPrice > 0 && reqData.MinMarketPrice > 0 {
		MaxMarketPrice := strconv.FormatFloat(float64(reqData.MaxMarketPrice), 'f', -1, 32)
		MinMarketPrice := strconv.FormatFloat(float64(reqData.MinMarketPrice), 'f', -1, 32)
		_ = writer.WriteField("maxMarketPrice", MaxMarketPrice)
		_ = writer.WriteField("minMarketPrice", MinMarketPrice)
	}
	if reqData.MaxDiscount > 0 && reqData.MinDiscount > 0 {
		MaxDiscount := strconv.FormatFloat(float64(reqData.MaxDiscount), 'f', -1, 32)
		MinDiscount := strconv.FormatFloat(float64(reqData.MinDiscount), 'f', -1, 32)
		_ = writer.WriteField("maxDiscount", MaxDiscount)
		_ = writer.WriteField("minDiscount", MinDiscount)
	}
	if reqData.WareHouseName != "" {
		_ = writer.WriteField("wareHouseNames", reqData.WareHouseName)
	}
	err = writer.Close()
	if err != nil {
		return
	}
	formDataf := writer.FormDataContentType()
	if err != nil {
		panic(err)
	}

	data := map[string]interface{}{
		"content-type": formDataf,
		// 这里还可以根据需求添加更多字段
	}

	resultJson, err := json.MarshalIndent(data, "", "\t")
	if err != nil {
		panic(err)
	}
	fmt.Println("请求数据：", string(resultJson))

	// 打印转换后的JSON数据
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &inventoryListByGroupGoods)

	if err != nil {
		return
	}
	return
}

func (y *TianMa) GetStockListByGoodsNo(reqData request2.StockListByGoodsNo) (StockListByGoodsNoGoods response.GetStockListByGoodsNoGoods, err error) {
	url := y.Http + "/openapi/getStockListByGoodsNo.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)

	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", "300")
	}
	if reqData.Articleno != "" {
		_ = writer.WriteField("articleno", reqData.Articleno)
	}

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("info", zap.Any("info", string(resData)))
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &StockListByGoodsNoGoods)

	if err != nil {
		return
	}

	var list []response.GetStockListByGoodsNoGood
	for _, item := range StockListByGoodsNoGoods.Rows {

		InnerNum, _ := strconv.Atoi(item.InnerNum)
		if item.WareHouseName == reqData.WareHouseName && InnerNum > 0 {
			list = append(list, item)
		}

	}

	StockListByGoodsNoGoods.Rows = list
	return
}
func (y *TianMa) GetWarehouseDetail(reqData request2.WareHouse) (StockListByGoodsNoGoods response.GetWarehouseDetail, err error) {
	url := y.Http + "/openapi/warehouseDetail.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)

	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", reqData.Rows)
	}
	if reqData.WareHouseName != "" {
		_ = writer.WriteField("wareHouseName", reqData.WareHouseName)
	}

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	log.Log().Info("获取天马仓库", zap.Any("info", resData))
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &StockListByGoodsNoGoods)

	if err != nil {
		err = errors.New("warehouseDetail接口：" + string(resData))
		log.Log().Error("天马返回数据", zap.Any("resData", string(resData)))
		return
	}
	if StockListByGoodsNoGoods.ErrorCode == "2" {
		err = errors.New(StockListByGoodsNoGoods.ErrorInfo)
		return
	}
	return
}

func (y *TianMa) GetCommodityList(reqData request2.StockListByGoodsNo) (commodityGoods response.CommodityGoods, err error) {
	url := y.Http + "/openapi/getCommodityList.do"
	var resData []byte
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)

	if reqData.Page != "" {
		_ = writer.WriteField("page", reqData.Page)
	}
	if reqData.Rows != "" {
		_ = writer.WriteField("rows", reqData.Rows)
	}
	if reqData.Articleno != "" {
		_ = writer.WriteField("articleno", reqData.Articleno)
	}

	err = writer.Close()
	if err != nil {
		return
	}
	resData, err = utils.PostFormData(url, payload, writer.FormDataContentType())
	if err != nil {
		return
	}
	err = json.Unmarshal(resData, &commodityGoods)

	if err != nil {
		return
	}
	return
}

func (y *TianMa) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	//仓库名称为空时直接返回，如果不选择仓库请求第三方会报错
	if info.WareHouseName == "" {
		//err = errors.New("仓库名称不能为空")
		return
	}
	var list []model.Goods
	url := y.Http + "/openapi/getInventoryListByGroup.do"
	payload := &bytes.Buffer{}
	writer := multipart.NewWriter(payload)
	_ = writer.WriteField("sign", y.dat.BaseInfo.AppSecret)
	page := strconv.Itoa(info.Page)
	rows := strconv.Itoa(50)
	if info.WareHouseName != "" {
		_ = writer.WriteField("wareHouseNames", info.WareHouseName)

	}
	if info.BrandNames != "" {
		_ = writer.WriteField("brandNames", info.BrandNames)
	}
	if info.Articleno != "" {
		_ = writer.WriteField("articleno", info.Articleno)
	}
	if info.Sexs != "" {
		_ = writer.WriteField("sexs", info.Sexs)

	}
	if info.Quarters != "" {
		_ = writer.WriteField("quarters", info.Quarters)

	}
	//_ = writer.WriteField("divisions", "鞋,服,配")
	_ = writer.WriteField("minNum", "1")

	if info.Divisions != "" {
		_ = writer.WriteField("divisions", info.Divisions)

	}

	if info.MaxMarketPrice != "" && info.MinMarketPrice != "" {
		_ = writer.WriteField("maxMarketPrice", info.MaxMarketPrice)
		_ = writer.WriteField("minMarketPrice", info.MinMarketPrice)

	}
	if info.MaxDiscount != "" && info.MinDiscount != "" {
		_ = writer.WriteField("maxDiscount", info.MaxDiscount)
		_ = writer.WriteField("minDiscount", info.MinDiscount)

	}
	_ = writer.WriteField("page", page)
	_ = writer.WriteField("rows", rows)
	//_ = writer.WriteField("return_type", "1")
	err = writer.Close()
	if err != nil {
		return
	}
	//reqData, _ := json.Marshal(&reqValue)
	resData, _ := utils.PostFormData(url, payload, writer.FormDataContentType())
	//fmt.Println(balance)
	if err != nil {
		return
	}

	var resGoodsData wmodel.GoodsData
	err = json.Unmarshal(resData, &resGoodsData)
	fmt.Println(len(resGoodsData.Rows))
	//log.Log().Info("wps resData", zap.Any("info", string(resData)))

	if resGoodsData.ErrorInfo != "" && resGoodsData.ErrorCode == "1" {
		err = errors.New(resGoodsData.ErrorInfo)
		return
	}

	if resGoodsData.Rows == nil {
		err = errors.New("未查询到相关条件商品")
		return
	}
	if resGoodsData.Total == 0 {
		return
	}

	var ids []string
	err = source.DB().Model(pmodel.Product{}).Where("source=?", common2.TIANMA_SOURCE).Pluck("source_goods_id_string", &ids).Error
	//排除掉重复的商品
	var ArticlenoList []string
	for _, item := range resGoodsData.Rows {
		isImport := 0
		for _, pitem := range ids {
			if pitem == item.Articleno {
				isImport = 1
			}
		}
		if collection.Collect(ArticlenoList).Contains(item.Articleno) == true {
			continue
		}
		ArticlenoList = append(ArticlenoList, item.Articleno)

		COriginalPrice := item.Marketprice

		int_COriginalPrice := COriginalPrice * 100

		Stock, _ := strconv.Atoi(item.InnerNum)

		discount, _ := strconv.ParseFloat(item.Discount, 64)
		agreementPrice := item.Marketprice * 100 * float64(discount/10)

		ActivityRate := utils.Decimal((float64(int_COriginalPrice) - float64(agreementPrice)) / float64(int_COriginalPrice) * 100)

		list = append(list, model.Goods{
			Title:             item.Size + item.WareHouseName,
			MarketPrice:       uint(int_COriginalPrice),
			Rate:              float64(discount),
			ActivityRate:      float64(ActivityRate),
			ThirdBrandName:    item.BrandName,
			ThirdCategoryName: item.Division,
			Cover:             item.PicUrl,
			AgreementPrice:    uint(agreementPrice),
			Unit:              item.Size,
			Color:             item.Color,
			Weight:            item.Weight,
			ActivityPrice:     uint(int_COriginalPrice),
			SalePrice:         uint(int_COriginalPrice),
			//ID:             int(item.Articleno),
			SN:       item.Articleno,
			IsImport: uint(isImport),
			//Sale:     uint(item.CGoodsStockStart),
			Stock:    uint(Stock),
			ShopName: item.WareHouseName,
		})
	}

	//
	//}
	//fmt.Println("查询商品返回count:", count, info.Page, info.Limit)
	data = list
	total = int64(resGoodsData.Total)
	return
}

var syncwg sync.WaitGroup

func (y *TianMa) RunUpdateGoodsRun(page interface{}) (err error) {
	//defer syncwg.Done()
	//url := string(y.Http + "open/api/goods/queryGoodsList")
	//var resData []byte
	//headerData := make(map[string]interface{})
	//
	//headerData["accessToken"] = y.dat.BaseInfo.Token
	//headerData["pageNum"] = page
	//
	//reqData, _ := json.Marshal(headerData)
	//
	//resData = utils.HttpPostJson(reqData, url)
	//if err != nil {
	//	return err
	//}
	////jsonData := string(resData)
	////fmt.Println("返回数据：", jsonData)
	//var modelData model.GoodsList
	//err = json.Unmarshal(resData, &modelData)
	//if err != nil {
	//	return err
	//}
	//
	//var lists []int64
	//
	//for _, item := range modelData.Result.GoodsSkuList {
	//	skuCode, _ := strconv.Atoi(item.GoodsSkuCode)
	//	lists = append(lists, int64(skuCode))
	//}
	//fmt.Println("pageDD:", page, len(lists))
	//
	//if len(lists) == 0 {
	//	return
	//}
	//
	//y.SynergeticProcess(lists)

	return
}

func (y *TianMa) UpdateGoodsRun() (err error) {
	//source.DB().Unscoped().Delete(model.YzhProductTemp{}, "id >=1")
	//
	//url := string(y.Http + "open/api/goods/queryGoodsList")
	//var resData []byte
	//headerData := make(map[string]string)
	//
	//headerData["accessToken"] = y.dat.BaseInfo.Token
	//
	//reqData, _ := json.Marshal(headerData)
	//
	//resData = utils.HttpPostJson(reqData, url)
	//if err != nil {
	//	return err
	//}
	//
	//var modelData model.GoodsList
	//err = json.Unmarshal(resData, &modelData)
	//if err != nil {
	//	return err
	//}
	//
	//var count = modelData.Result.TotalCount
	//orderPN := utils.GetOrderNo()
	//goodsRecord := model.SupplyGoodsImportRecord{
	//	Batch:             orderPN,
	//	EstimatedQuantity: count,
	//	Status:            1,
	//	SearchCriteria:    "all",
	//}
	//
	//source.DB().Omit("goods_arr").Create(&goodsRecord)
	//
	//for i := 1; i <= modelData.Result.TotalPage; i++ {
	//	//syncwg.Add(1)
	//	y.RunUpdateGoodsRun(i)
	//}
	//
	////syncwg.Wait()
	//
	////source.DB().CreateInBatches(&yzhProduct, 1000)
	//
	////fmt.Println("全部结束")
	////return
	//
	//err = SetImportRecordCompletion(orderPN)
	//if err != nil {
	//	fmt.Println("变更导入记录状态错误", err)
	//}
	//fmt.Println("导入供应链商品全部完成")
	//time.Sleep(time.Second * 5)
	////source.DB().Exec("DROP TABLE IF EXISTS `yzh_product_goods`;")
	////source.DB().Exec("CREATE TABLE yzh_product_goods  select * from yzh_product_temps  ;")
	//log.Log().Info("批量同步yzh商品到本地全部完成！！！")
	return

}

func (y *TianMa) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {
	//var list []model.Goods

	url := string(y.Http + "api/openapi/getInventoryListByGroup.do")
	var resData []byte
	headerData := make(map[string]string)
	reqValue := url2.Values{}
	page := strconv.Itoa(info.Page)
	headerData["pageNo"] = page

	headerData["pageSize"] = limit
	if info.Category1ID > 0 {
		Category1ID := strconv.Itoa(info.Category1ID)
		headerData["classId"] = Category1ID
		reqValue.Add("classId", Category1ID)
	}
	if info.Category2ID > 0 {
		Category2ID := strconv.Itoa(info.Category2ID)

		headerData["class2Id"] = Category2ID
		reqValue.Add("class2Id", Category2ID)
	}
	if info.Category3ID > 0 {
		Category3ID := strconv.Itoa(info.Category3ID)

		headerData["class3Id"] = Category3ID
		reqValue.Add("class3Id", Category3ID)
	}
	if info.SearchWords != "" {
		headerData["goodName"] = info.SearchWords
		reqValue.Add("goodName", info.SearchWords)
	}
	if info.IsSort == "desc" {
		headerData["is_sort"] = "1"
		reqValue.Add("is_sort", "1")
	}
	if info.RangeType != "" {
		headerData["profitSpace"] = info.RangeType
		reqValue.Add("profitSpace", info.RangeType)
	}
	reqValue.Add("pageNo", page)
	reqValue.Add("pageSize", limit)

	//reqData, _ := json.Marshal(headerData)
	var header = make(map[string]string)

	header["channelType"] = y.dat.BaseInfo.Channel
	key := header["channelType"] + y.dat.BaseInfo.AppSecret
	header["md5"] = strings.ToLower(Sign(headerData, key))
	err, resData = utils.PostForm(url, reqValue, header)
	if err != nil {
		return
	}

	var resGoodsData wmodel.GoodsData
	err = json.Unmarshal(resData, &resGoodsData)
	if resGoodsData.Rows == nil {
		err = errors.New("未查询到相关条件商品")
		return
	}
	if resGoodsData.Total == 0 {
		return
	}

	var count = resGoodsData.Total
	orderPN := utils.GetOrderNo()
	GlobalOrderSN = orderPN
	goodsRecord := model.SupplyGoodsImportRecord{
		Batch:             orderPN,
		EstimatedQuantity: count,
		Status:            1,
		SearchCriteria:    "all",
		Source:            strconv.Itoa(common2.TIANMA_SOURCE),
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	var wg sync.WaitGroup
	for _, item := range resGoodsData.Rows {
		wg.Add(1)
		y.ImportLocalGoods(&wg, item, orderPN, info.Categorys)
	}

	var limitA = pageSize
	var forCount = float64(count / limitA)
	var counts = int(math.Ceil(forCount))

	for i := 1; i <= counts; i++ {
		info.Page = info.Page + 1
		y.RunImport(info, orderPN)
	}

	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return

}

var limit = "50"
var pageSize = 50

func (y *TianMa) RunImport(info request.GetGoodsSearch, orderPN string) (err error) {
	//var list []model.Goods

	//url := string(y.Http + "mcang/Mcang/getGoodsList")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//page := strconv.Itoa(info.Page)
	//headerData["pageNo"] = page
	//
	//headerData["pageSize"] = limit
	//if info.Category1ID > 0 {
	//	Category1ID := strconv.Itoa(info.Category1ID)
	//	headerData["classId"] = Category1ID
	//	reqValue.Add("classId", Category1ID)
	//}
	//if info.Category2ID > 0 {
	//	Category2ID := strconv.Itoa(info.Category2ID)
	//
	//	headerData["class2Id"] = Category2ID
	//	reqValue.Add("class2Id", Category2ID)
	//}
	//if info.Category3ID > 0 {
	//	Category3ID := strconv.Itoa(info.Category3ID)
	//
	//	headerData["class3Id"] = Category3ID
	//	reqValue.Add("class3Id", Category3ID)
	//}
	//if info.SearchWords != "" {
	//	headerData["goodName"] = info.SearchWords
	//	reqValue.Add("goodName", info.SearchWords)
	//}
	//if info.IsSort == "desc" {
	//	headerData["is_sort"] = "1"
	//	reqValue.Add("is_sort", "1")
	//}
	//if info.RangeType != "" {
	//	headerData["profitSpace"] = info.RangeType
	//	reqValue.Add("profitSpace", info.RangeType)
	//}
	//reqValue.Add("pageNo", page)
	//reqValue.Add("pageSize", limit)
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}

	//var resGoodsData wmodel.ResData
	//err = json.Unmarshal(resData, &resGoodsData)
	//if resGoodsData.Data.List == nil {
	//	err = errors.New("未查询到相关条件商品")
	//	return
	//}
	//if resGoodsData.Data.DataCount == 0 {
	//	return
	//}
	//
	//var wg sync.WaitGroup
	//for _, item := range resGoodsData.Data.List {
	//	wg.Add(1)
	//	y.ImportLocalGoods(&wg, item, orderPN, info.Categorys)
	//}

	return

}

func (y *TianMa) ImportLocalGoods(wg *sync.WaitGroup, detail wmodel.Goods, orderPN string, caters string) {
	defer wg.Done()
	//ids := strings.Split(caters, ",")
	//var cate1, cate2, cate3 int
	//if len(ids) >= 3 {
	//	cate1, _ = strconv.Atoi(ids[0])
	//	cate2, _ = strconv.Atoi(ids[1])
	//	cate3, _ = strconv.Atoi(ids[2])
	//}
	//var detail model.YzhGoodsDetail
	//detail.RESULTDATA.PRODUCTDATA = yzhList
	//detail.CategoryNames = yzhList.CateNames

	//err, listGoods := y.CommodityAssemblyLocal(detail, cate1, cate2, cate3)
	//if err != nil {
	//	log.Log().Error("CommodityAssemblyLocal错误", zap.Any("err", err))
	//}
	//
	//if len(listGoods) > 0 {
	//	FinalProcessing(listGoods, orderPN)
	//}

	return
}

func (y *TianMa) SynergeticProcess(item []int64) (err error) {
	//url := string(y.Http + "/open/api/goods/queryGoodsDetail")
	//var resData []byte
	//headerData := make(map[string]interface{})
	//
	//headerData["accessToken"] = y.dat.BaseInfo.Token
	//headerData["goodsSkuCode"] = item
	//
	//reqData, _ := json.Marshal(headerData)
	//
	//resData = utils.HttpPostJson(reqData, url)
	//if err != nil {
	//	return err
	//}
	////var imgList = map[string][]model3.ImageList{}
	//imgList := y.QueryGoodsImageList(item)
	//
	////fmt.Println(imgList)
	//var yzhTempGoods model.YzhGoods
	//
	//err = json.Unmarshal(resData, &yzhTempGoods)
	//if err != nil {
	//	panic(err)
	//	return
	//}
	//var yzhProduct []model.YzhProductTemp
	//
	////yzhProduct = yzhTempGoods.Result
	//
	//for _, goodsItem := range yzhTempGoods.Result {
	//	var galleryList pmodel.Gallery
	//	var galleryItem pmodel.GalleryItem
	//	//if goodsItem.ShelvesStatus != 1001 {
	//	//	continue
	//	//}
	//	for _, imgItem := range imgList[goodsItem.GoodsSkuCode] {
	//		if imgItem.ImgMain == 1 {
	//			goodsItem.ImgMain = imgItem.ImgUrl
	//		} else {
	//			galleryItem.Type = 1
	//			galleryItem.Src = imgItem.ImgUrl
	//			galleryList = append(galleryList, galleryItem)
	//		}
	//
	//	}
	//
	//	SellPrice, _ := strconv.ParseFloat(goodsItem.SellPrice, 64)
	//	MarketPrice, _ := strconv.ParseFloat(goodsItem.MarketPrice, 64)
	//
	//	goodsItem.Gallery = galleryList
	//	goodsItem.Detail = goodsItem.DescDetail.GoodsDescribe
	//	strs := float64(SellPrice) * 100
	//	strm := float64(MarketPrice) * 100
	//	goodsItem.SellPrice = strconv.Itoa(int(strs))
	//	goodsItem.MarketPrice = strconv.Itoa(int(strm))
	//	yzhProduct = append(yzhProduct, goodsItem)
	//}
	//
	//source.DB().Create(&yzhProduct)
	////if err != nil {
	////	panic(err)
	////}
	//fmt.Println("准备创建数量", len(yzhProduct))
	//fmt.Println("总数", len(yzhProduct))

	return
}

var wg sync.WaitGroup

func (y *TianMa) RunGoods(maps []int64, orderPN string) {

	//for index, item := range maps {
	//
	//	wg.Add(1)
	//if index%10 == 0 {
	//	time.Sleep(time.Second * 1)
	//}
	//go y.SynergeticProcess(item)
	//if index >1000 {
	//	return
	//}
	//yzhProduct.ProductImage=goodsDetail.RESULTDATA.PRODUCTIMAGE

	//var resultArr int
	//err = source.DB().Select("id").Model(model.SupplyGoods{}).Where("gather_supply_id=? and supply_goods_id=?", y.SupplyID,item).Pluck("supply_goods_id",&resultArr).Error
	//if err != nil {
	//	log.Log().Error("查询错误",zap.Any("err",err))
	//	continue
	//}

	//var listGoods []*pmodel.Product
	//
	//err, listGoods = y.CommodityAssemblyA(goodsDetail)
	//
	//if len(listGoods) > 0 {
	//	FinalProcessing(listGoods, orderPN)
	//}

	//
	//difference := collection.Collect(resultArr).WhereIn(goodsDetail.RESULTDATA.PRODUCTDATA)

	//}
	//wg.Wait()

}

// 获取分类数据
func (y *TianMa) GetCategory(infof request.GetCategorySearch) (err error, data interface{}) {

	var info request.GetCategoryChild
	err, Listdata := y.GetImportCategoryChild("", info)

	fmt.Println(Listdata)
	if err != nil {
		fmt.Println("获取以及分类错误")
	}
	for _, item := range Listdata {

		IsDisplay := 1

		var cate1 catemodel.Category
		cate1.Name = item.Title
		cate1.Source = int(common2.TIANMA_SOURCE)
		cate1.Level = 1
		cate1.ParentID = 0
		cate1.IsDisplay = &IsDisplay
		source.DB().Where("level=1 and name =?", item.Title).FirstOrCreate(&cate1)
		err, cdata := y.GetImportCategoryChild(item.Code, info)
		if err != nil {
			fmt.Println("获取二级分类错误")
		}
		clist := cdata
		for _, citem := range clist {
			var cate2 catemodel.Category
			cate2.Name = citem.Title
			cate2.Source = common2.TIANMA_SOURCE
			cate2.Level = 2
			cate2.ParentID = cate1.ID
			cate2.IsDisplay = &IsDisplay
			source.DB().Where("level=2 and name =?", citem.Title).FirstOrCreate(&cate2)

			time.Sleep(time.Microsecond * 100000)
			err, ccdata := y.GetImportCategoryChild(citem.Code, info)
			if err != nil {
				fmt.Println("获取二级分类错误")
			}
			cclist := ccdata

			for _, csitem := range cclist {
				var cate3 catemodel.Category
				cate3.Name = csitem.Title
				cate3.Source = common2.TIANMA_SOURCE
				cate3.Level = 3
				cate3.ParentID = cate2.ID
				cate3.IsDisplay = &IsDisplay
				source.DB().Where("level=3 and name =?", csitem.Title).FirstOrCreate(&cate3)

			}

		}

	}

	return
}

func (y *TianMa) GetGroup() (err error, data interface{}) {

	return

}

var AllCateData []model.RESULTDATA

func (y *TianMa) GetYzhCategoryListAll() {
	AllCateData = nil
	var info request.GetCategoryChild
	err, data := y.GetCategoryChildSub(0, info)
	if err != nil {
		fmt.Println("获取以及分类错误")
	}
	AllCateData = data.([]model.RESULTDATA)
	for _, item := range AllCateData {

		err, cdata := y.GetCategoryChildSub(int(item.Code), info)
		if err != nil {
			fmt.Println("获取二级分类错误")
		}
		clist := cdata.([]model.RESULTDATA)
		AllCateData = append(AllCateData, clist...)
		for _, citem := range clist {

			time.Sleep(time.Microsecond * 100000)
			err, ccdata := y.GetCategoryChildSub(int(citem.Code), info)
			if err != nil {
				fmt.Println("获取二级分类错误")
			}
			cclist := ccdata.([]model.RESULTDATA)
			AllCateData = append(AllCateData, cclist...)
		}

	}

	fmt.Println(AllCateData)
}

func (y *TianMa) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	//timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//url := string(y.Http + "mcang/Mcang/getGoodsClassify")
	//var resData []byte
	//headerData := make(map[string]string)

	//headerData["c_level"] = ""
	//headerData["c_parent_code"] = ""

	//url := string(y.Http + "mcang/Mcang/getGoodsClassify")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//code := strconv.Itoa(pid)
	//
	//headerData["c_parent_code"] = code
	//
	//reqValue.Add("c_parent_code", code)
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}

	//var resCategory wmodel.ResCategory
	//err = json.Unmarshal(resData, &resCategory)
	//if err != nil {
	//	return
	//}
	////var yzhCategory []model.YzhCategory
	//var yzhCategory []model.YzhNewCategory
	//for _, cateItem := range resCategory.Data {
	//
	//	//code, _ := strconv.Atoi(cateItem.CategoryCode)
	//	//pidCode, _ := strconv.Atoi(cateItem.ParentCategoryCode)
	//	yzhCategory = append(yzhCategory, model.YzhNewCategory{
	//		//Id:    cateItem.CId,
	//		Title: cateItem.CName,
	//		Level: cateItem.CLevel,
	//		Pid:   cateItem.CParentCode,
	//		Code:  cateItem.CCode,
	//	})
	//}
	//data = yzhCategory
	return

}

func (y *TianMa) GetImportCategoryChild(pid string, info request.GetCategoryChild) (err error, data []model.YzhNewCategory) {
	//
	//url := string(y.Http + "mcang/Mcang/getGoodsClassify")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//code := pid
	//
	//headerData["c_parent_code"] = code
	//
	//reqValue.Add("c_parent_code", code)
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}

	//var resCategory wmodel.ResCategory
	//err = json.Unmarshal(resData, &resCategory)
	//if err != nil {
	//	return
	//}
	////var yzhCategory []model.YzhCategory
	//var yzhCategory []model.YzhNewCategory
	//for _, cateItem := range resCategory.Data {
	//
	//	//code, _ := strconv.Atoi(cateItem.CategoryCode)
	//	//pidCode, _ := strconv.Atoi(cateItem.ParentCategoryCode)
	//	yzhCategory = append(yzhCategory, model.YzhNewCategory{
	//		//Id:    cateItem.CId,
	//		Title: cateItem.CName,
	//		Level: cateItem.CLevel,
	//		Pid:   cateItem.CParentCode,
	//		Code:  cateItem.CCode,
	//	})
	//}
	//data = yzhCategory
	return

}

func (y *TianMa) GetBrand(page string) (err error, data interface{}) {
	//y.Http = "https://uat.api.weipinshang.net/"
	////timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	//url := string(y.Http + "mcang/Mcang/goodBrand")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//headerData["pageNo"] = page
	//headerData["pageSize"] = "20"
	//reqValue.Add("pageNo", page)
	//reqValue.Add("pageSize", "20")
	//
	////reqData, _ := json.Marshal(headerData)
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}
	//
	//var yzhCateGory model.YzhNewCate
	//err = json.Unmarshal(resData, &yzhCateGory)
	//if err != nil {
	//	return
	//}
	////var yzhCategory []model.YzhCategory
	//var yzhCategory []model.YzhNewCategory
	//for _, cateItem := range yzhCateGory.Result {
	//
	//	//code, _ := strconv.Atoi(cateItem.CategoryCode)
	//	//pidCode, _ := strconv.Atoi(cateItem.ParentCategoryCode)
	//	yzhCategory = append(yzhCategory, model.YzhNewCategory{
	//		Id:    cateItem.CategoryCode,
	//		Title: cateItem.CategoryName,
	//		Level: cateItem.CategoryLevel,
	//		Pid:   cateItem.ParentCategoryCode,
	//		Code:  cateItem.CategoryCode,
	//	})
	//}
	//data = yzhCategory
	return

}

func (y *TianMa) GetCategoryChildSub(pid int, info request.GetCategoryChild) (err error, data interface{}) {

	return

}

func (y *TianMa) GetCategoryDetail(id uint) (err error, cate uint) {

	return

}

// 商品组装
func (y *TianMa) CommodityAssemblyLocal(detail response.InventoryListByGroupGood, cateId1, cateId2, cateId3 int, ExpressDelivery string) (err error, listGoods []*pmodel.Product) {
	//
	sourceCode := common2.TIANMA_SOURCE
	goods := new(pmodel.Product)

	product := pmodel.Product{}
	md5 := detail.WareHouseName + detail.Articleno
	md5Str := utils.MD5V([]byte(md5))
	err = source.DB().Where("shop_name =? and source_goods_id_string =  ? and source=?", detail.WareHouseName, detail.Articleno, sourceCode).Preload("Skus").First(&product).Error
	if product.ID > 0 {

		source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", GlobalOrderSN).Updates(map[string]interface{}{
			"repeat_quantity": gorm.Expr("repeat_quantity + ?", 1),
		})
		return
	}
	var reqGroupData request2.InventoryListByGroup
	reqGroupData.Page = "1"
	reqGroupData.Rows = "300"
	reqGroupData.Articleno = detail.Articleno
	reqGroupData.WareHouseName = detail.WareHouseName
	var inventoryListByGroupGoods response.InventoryListByGroupGoods
	inventoryListByGroupGoods, err = y.GetInventoryListByGroup(reqGroupData)
	if err != nil {
		log.Log().Error("tianma GetInventoryListByGroup err", zap.Any("err", err))
		return
	}
	if len(inventoryListByGroupGoods.Rows) == 0 {
		log.Log().Error("tianma GetInventoryListByGroup Rows 0", zap.Any("err", err))

		return
	}

	GoodsSkuCode := detail.Articleno
	goods.SourceGoodsIDString = GoodsSkuCode
	var brand = new(catemodel.Brand)
	if detail.BrandName != "" {
		brand.Name = detail.BrandName
		brand.Source = sourceCode
		err = source.DB().Where(brand).FirstOrCreate(&brand).Error
		goods.BrandID = brand.ID
	}
	var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	var elem model.Goods
	//SellPrice, _ := strconv.ParseFloat(detail.Marketprice, 64)
	MarketPrice := detail.Marketprice
	discount, _ := strconv.ParseFloat(detail.Discount, 64)
	CostPrice := detail.Marketprice * (discount / 10)
	//OriginalPrice, _ := strconv.ParseFloat(detail.COriginalPrice, 64)

	elem.AgreementPrice = uint(CostPrice * 100)  //协议价
	elem.MarketPrice = uint(MarketPrice * 100)   //吊牌价/市场价
	elem.ActivityPrice = uint(MarketPrice * 100) //吊牌价/市场价
	//elem.ActivityPrice = uint(MarketPrice * 100) //建议零售价
	elem.Source = sourceCode
	//
	err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
	//
	////stock, stockErr := y.GetStock(strSkuCode)
	////if stockErr != nil {
	////	return
	////}
	//
	var reqData wmodel.Article
	reqData.ArticleNO = detail.Articleno
	goodsDetail, _ := y.GetGoodsDetail(reqData)
	goods.Title = detail.Size + "/" + goodsDetail.Data.Title
	InnerNum, _ := strconv.Atoi(detail.InnerNum)
	goods.Stock = uint(InnerNum)
	goods.OriginPrice = originPrice
	goods.Price = salePrice
	goods.CostPrice = costPrice
	goods.ActivityPrice = activityPrice
	goods.GuidePrice = guidePrice
	goods.MD5 = md5Str
	//if len(stock.Result) > 0 {
	//	goods.Stock = uint(stock.Result[0].StockNum)
	//}
	goods.DetailImages = goodsDetail.Data.Describe

	goods.IsDisplay = 1
	//
	////goods.TaxRate = int(detail.RESULTDATA.PRODUCTDATA.Tax)
	goods.ImageUrl = detail.PicUrl
	//
	////goods.Unit = detail.GoodsUnit
	//if goods.Unit == "" {
	goods.Unit = "件"
	goods.ShopName = detail.WareHouseName
	goods.ExpressDelivery = ExpressDelivery
	//goods.Wide
	//}
	goods.Source = sourceCode
	//goods.DetailImages = detail.CGoodsContent
	//
	//cateList := strings.Split(detail.CategoryNames, ",")
	var cate1, cate2, cate3 catemodel.Category
	display := 1
	//
	var dat model.SupplySetting
	err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}

	if cateId1 > 0 {
		goods.Category1ID = uint(cateId1)
	}
	if cateId2 > 0 {
		goods.Category2ID = uint(cateId2)
	}
	if cateId3 > 0 {
		goods.Category3ID = uint(cateId3)
	}
	//
	if dat.UpdateInfo.CateGory == 1 {
		if detail.Division != "" {
			cate1.IsDisplay = &display
			cate1.ParentID = 0
			cate1.Level = 1
			cate1.Name = detail.Division
			source.DB().Where("name=? and level=? and parent_id=?", detail.Division, 1, 0).FirstOrCreate(&cate1)
		}
		if detail.Division != "" {
			cate2.IsDisplay = &display
			cate2.ParentID = cate1.ID
			cate2.Level = 2
			cate2.Name = detail.Division
			source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, 2, cate1.ID).FirstOrCreate(&cate2)
		}

		if detail.Division != "" {
			cate3.IsDisplay = &display
			cate3.ParentID = cate2.ID
			cate3.Level = 3
			cate3.Name = detail.Division
			source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, 3, cate2.ID).FirstOrCreate(&cate3)
		}

		goods.Category1ID = cate1.ID
		goods.Category2ID = cate2.ID
		goods.Category3ID = cate3.ID
	}
	if cate1.ID != 0 {
		goods.Category1ID = cate1.ID

	}
	if cate2.ID != 0 {
		goods.Category2ID = cate2.ID

	}
	if cate3.ID != 0 {
		goods.Category3ID = cate3.ID

	}

	goods.FreightType = 2
	goods.GatherSupplyID = y.SupplyID
	//
	var sku = pmodel.Sku{}
	var skuList []pmodel.Sku
	//
	var Gallery pmodel.Gallery

	//if len(goods.Gallery) <= 0 {

	//bannerImg := strings.Split(detail.CBannerImages, ";")

	Gallery = append(Gallery, pmodel.GalleryItem{Type: 1, Src: detail.PicUrl})

	goods.Gallery = Gallery

	//}
	///**
	//处理轮播图结束
	//*/
	//
	goods.MinPrice = goods.Price
	goods.MaxPrice = goods.Price
	//
	var minProfitRate float64
	for _, skuItem := range inventoryListByGroupGoods.Rows {
		innerNum, _ := strconv.Atoi(skuItem.InnerNum)
		if innerNum <= 0 {
			continue
		}

		var options pmodel.Options

		skuTitle := skuItem.Size + "-" + skuItem.Colour

		if skuItem.Colour != "" {
			options = append(options, pmodel.Option{SpecName: "颜色", SpecItemName: skuItem.Colour})
		}
		if skuItem.Size != "" {
			options = append(options, pmodel.Option{SpecName: "尺码", SpecItemName: skuItem.Size})
		}
		if skuItem.Sex != "" {
			options = append(options, pmodel.Option{SpecName: "性别", SpecItemName: skuItem.Sex})
		}

		var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
		var elem model.Goods
		//SellPrice, _ := strconv.ParseFloat(detail.Marketprice, 64)
		MarketPrice := skuItem.Marketprice
		discount, _ := strconv.ParseFloat(skuItem.Discount, 64)
		CostPrice := skuItem.Marketprice * (discount / 10)
		//OriginalPrice, _ := strconv.ParseFloat(detail.COriginalPrice, 64)

		elem.AgreementPrice = uint(CostPrice * 100)  //协议价
		elem.MarketPrice = uint(MarketPrice * 100)   //吊牌价/市场价
		elem.ActivityPrice = uint(MarketPrice * 100) //吊牌价/市场价
		//elem.ActivityPrice = uint(MarketPrice * 100) //建议零售价
		elem.Source = sourceCode
		//
		err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
		//
		sku.Title = skuTitle
		sku.Options = options
		//sku.Weight = skuItem.Weight
		sku.CostPrice = costPrice
		InnerNum, _ := strconv.Atoi(skuItem.InnerNum)
		sku.Stock = int(InnerNum)
		sku.IsDisplay = 1
		sku.Price = salePrice
		sku.OriginPrice = originPrice
		sku.GuidePrice = guidePrice
		sku.ActivityPrice = activityPrice
		sku.Describe = skuItem.Size

		md5 := skuItem.WareHouseName + skuItem.Articleno + skuItem.Size + skuItem.Colour
		md5Str := utils.MD5V([]byte(md5))
		sku.SpecId = md5Str
		if sku.GuidePrice > 0 {
			sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
		} else {
			sku.ProfitRate = 0
		}
		if sku.ProfitRate <= minProfitRate {
			minProfitRate = sku.ProfitRate
		}
		//if skuItem.OldGoodsID > 0 && goods.Source == 2 {
		//	_, skuDetail := st.GetGoodsDetails(skuItem.OldGoodsID)
		//	sku.Describe = skuDetail.Description
		//}

		skuList = append(skuList, sku)

	}
	goods.Skus = skuList

	if len(goods.Skus) == 0 {
		var options pmodel.Options
		var option pmodel.Option
		option.SpecName = "尺码"
		option.SpecItemName = detail.Size
		options = append(options, option)
		//if len(goods.Skus) > 0 {
		//	sku.ID = goods.Skus[0].ID
		//}

		//sku.Weight = int(math.Float64bits(detail.Weight))
		sku.Title = detail.Size + detail.Colour
		sku.Options = options
		//sku.Weight = int(detail.GrossWeight)
		sku.CostPrice = goods.CostPrice
		sku.Describe = detail.Size
		sku.Stock = int(goods.Stock)
		//sku.IsDisplay = goods.IsDisplay
		sku.Price = goods.Price
		sku.OriginPrice = goods.OriginPrice
		sku.GuidePrice = goods.GuidePrice
		sku.ActivityPrice = goods.ActivityPrice
		sku.OriginalSkuID = int64(goods.SourceGoodsID)
		md5 := detail.WareHouseName + detail.Articleno + detail.Size + detail.Colour
		md5Str := utils.MD5V([]byte(md5))
		sku.SpecId = md5Str
		if sku.GuidePrice > 0 {
			sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
		} else {
			sku.ProfitRate = 0
		}
		minProfitRate = sku.ProfitRate

		skuList = append(skuList, sku)
		goods.Skus = skuList
		goods.SingleOption = 1

	}
	//
	if len(goods.Skus) > 0 {
		goods.ProfitRate = minProfitRate
	}
	////---------处理属性json数组结束
	////goods.Desc=detail.Description
	//
	if len(goods.Skus) > 0 {
		listGoods = append(listGoods, goods)

	}

	return
}

// 商品组装
func (y *TianMa) CommodityAssemblyLocalUpdate(detail model.YzhProductTemp, goods pservice.ProductForUpdate, cateId1, cateId2, cateId3 int) (err error, listGoods []*pmodel.Product) {
	var skuIds []int64
	skuCode, _ := strconv.Atoi(detail.GoodsSkuCode)
	skuIds = append(skuIds, int64(skuCode))
	//imgList := y.QueryGoodsImageList(skuIds)

	sourceCode := int(common2.YZH_NEW)
	//var goods pservice.ProductForUpdate
	//err = source.DB().Where("source_goods_id =  ? and source=?", detail.GoodsSkuCode, sourceCode).Preload("Skus").First(&goods).Error

	var strSkuCode []string
	strSkuCode = append(strSkuCode, detail.GoodsSkuCode)
	GoodsSkuCode, _ := strconv.Atoi(detail.GoodsSkuCode)
	goods.SourceGoodsID = uint(GoodsSkuCode)
	//var brand = new(catemodel.Brand)
	//if detail.BrandName != "" {
	//	brand.Name = detail.BrandName
	//	brand.Source = sourceCode
	//	err = source.DB().Where(brand).FirstOrCreate(&brand).Error
	//	goods.BrandID = brand.ID
	//}
	var costPrice, salePrice, originPrice, activityPrice, guidePrice uint
	var elem model.Goods

	SellPrice, spriceErr := strconv.ParseFloat(detail.SellPrice, 64)
	if spriceErr != nil {
		fmt.Println("价格错误", spriceErr)
	}
	MarketPrice, mpriceErr := strconv.ParseFloat(detail.MarketPrice, 64)

	MarketPriceUint := uint(MarketPrice * 100)
	SellPriceUint := uint(SellPrice * 100)

	if MarketPriceUint <= 0 || SellPriceUint <= 0 {
		return
	}

	if mpriceErr != nil {
		fmt.Println("价格错误", mpriceErr)
	}
	elem.AgreementPrice = uint(SellPriceUint)
	elem.GuidePrice = uint(MarketPriceUint)
	elem.ActivityPrice = uint(MarketPriceUint)
	elem.Source = sourceCode

	err, costPrice, salePrice, originPrice, activityPrice, guidePrice = GetPricingPrice(elem, "gatherSupply"+strconv.Itoa(int(y.SupplyID)))
	//var dat model.SupplySetting
	//err, setting := gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(y.SupplyID)))
	//if err != nil {
	//	fmt.Println("获取供应链key设置失败")
	//	return
	//}
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//stock, _ := y.GetStock(strSkuCode)

	//if stock.RESPONSESTATUS != "true" || stock.RESULTDATA.StockStatus != true {
	//	err = errors.New("库存无效")
	//	return
	//}

	goods.Title = detail.GoodsSkuName
	goods.OriginPrice = originPrice
	//if dat.UpdateInfo.CurrentPrice == 1 {
	goods.Price = salePrice
	//}

	//if dat.UpdateInfo.CostPrice == 1 {
	goods.CostPrice = costPrice
	//}

	goods.ActivityPrice = activityPrice
	goods.GuidePrice = guidePrice
	//if len(stock.Result) > 0 {
	//	if stock.Result[0].StockNum > 0 {
	//		goods.Stock = uint(stock.Result[0].StockNum)
	//	}
	//}

	goods.SingleOption = 1
	//goods.Sales = elem.Sale
	//status, _ := strconv.Atoi(detail.RESULTDATA.PRODUCTDATA.Status)
	if detail.ShelvesStatus == 1001 {
		goods.IsDisplay = 1
	} else {
		goods.IsDisplay = 0
	}

	goods.Unit = detail.GoodsUnit
	//goods.SourceGoodsID = uint(detail.RESULTDATA.PRODUCTDATA.ProductId)
	goods.Source = sourceCode
	//goods.DetailImages = detail.Detail

	//cateList := strings.Split(detail.CategoryNames, ",")
	//var cate1, cate2, cate3 catemodel.Category
	//display := 1
	//
	//err = json.Unmarshal([]byte(setting.Value), &dat)
	//if err != nil {
	//	return
	//}
	//
	//if dat.UpdateInfo.CateGory == 1 {
	//
	//	//if len(cateList) > 0 && cateList[0] != "" {
	//
	//	cate1.IsDisplay = &display
	//	cate1.ParentID = 0
	//	cate1.Level = 1
	//	cate1.Name = detail.FirstCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.FirstCategoryName, 1, 0).FirstOrCreate(&cate1)
	//	//	}
	//
	//	//if len(cateList) > 1 && cateList[1] != "" {
	//	cate2.IsDisplay = &display
	//	cate2.ParentID = cate1.ID
	//	cate2.Level = 2
	//	cate2.Name = detail.SecondCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.SecondCategoryName, 2, cate1.ID).FirstOrCreate(&cate2)
	//	//}
	//
	//	//if len(cateList) > 2 && cateList[2] != "" {
	//	cate3.IsDisplay = &display
	//	cate3.ParentID = cate2.ID
	//	cate3.Level = 3
	//	cate3.Name = detail.LastCategoryName
	//	source.DB().Where("name=? and level=? and parent_id=?", detail.LastCategoryName, 3, cate2.ID).FirstOrCreate(&cate3)
	//	//}
	//	//
	//
	//	goods.Category1ID = cate1.ID
	//	goods.Category2ID = cate2.ID
	//	goods.Category3ID = cate3.ID
	//}
	//
	//if cateId1 > 0 {
	//	goods.Category1ID = uint(cateId1)
	//}
	//if cateId2 > 0 {
	//	goods.Category2ID = uint(cateId2)
	//}
	//if cateId3 > 0 {
	//	goods.Category3ID = uint(cateId3)
	//}

	//}
	goods.Gallery = nil
	goods.FreightType = 2
	goods.GatherSupplyID = y.SupplyID

	var sku = pservice.Sku{}
	var skuList []pservice.Sku

	var galleryList pmodel.Gallery
	//var galleryItem pmodel.GalleryItem
	//if goodsItem.ShelvesStatus != 1001 {
	//	continue
	//}
	//for _, imgItem := range imgList[detail.GoodsSkuCode] {
	//	if imgItem.ImgMain == 1 {
	//		detail.ImgMain = imgItem.ImgUrl
	//	} else {
	//		galleryItem.Type = 1
	//		//galleryItem.Src = imgItem.ImgUrl
	//		galleryList = append(galleryList, galleryItem)
	//	}
	//
	//}

	if len(galleryList) > 0 {
		goods.Gallery = galleryList
	}

	//if len(detail.Gallery) > 0 {
	//	goods.Gallery = detail.Gallery
	//}
	//

	if detail.ImgMain != "" {
		goods.ImageUrl = detail.ImgMain
	}
	if len(goods.Gallery) == 0 && detail.ImgMain != "" {
		var item pmodel.GalleryItem
		var gallery pmodel.Gallery
		item.Src = detail.ImgMain
		item.Type = 1
		gallery = append(gallery, item)
		goods.Gallery = gallery
	}

	/**
	处理轮播图结束
	*/

	goods.MinPrice = goods.Price
	goods.MaxPrice = goods.Price

	//if len(goods.Skus) == 0 {
	var options pmodel.Options
	var option pmodel.Option
	option.SpecName = "规格"
	option.SpecItemName = "默认"
	options = append(options, option)
	if len(goods.Skus) > 0 {
		sku.ID = goods.Skus[0].ID
	}

	if len(goods.Skus) > 0 {

		sku.ID = goods.Skus[0].ID
	}

	sku.Title = "默认"
	sku.Options = options
	sku.Weight = 0
	sku.CostPrice = goods.CostPrice
	sku.Stock = int(goods.Stock)
	//sku.IsDisplay = goods.IsDisplay
	sku.Price = goods.Price
	sku.OriginPrice = goods.OriginPrice
	sku.GuidePrice = goods.GuidePrice
	sku.ActivityPrice = goods.ActivityPrice
	sku.OriginalSkuID = int(goods.SourceGoodsID)
	skuList = append(skuList, sku)

	//}

	goods.Skus = skuList
	goods.ProfitRate = pservice.Decimal((float64(goods.GuidePrice) - float64(goods.Price)) / float64(goods.GuidePrice) * 100)
	//---------处理属性json数组结束
	//goods.Desc=detail.Description
	if goods.ID > 0 {
		log.Log().Debug("yunzhonghe修改商品", zap.Any("id", goods.ID))

		err = pservice.UpdateProduct(goods)
		if err != nil {
			log.Log().Error("更新商品错误 update", zap.Any("err", err))
			return
		}
		//source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", GlobalOrderSN).Updates(map[string]interface{}{
		//	"repeat_quantity": gorm.Expr("repeat_quantity + ?", 1),
		//})
	}

	return
}

var GlobalOrderSN string

// 批量获取商品详情
func (y *TianMa) BatchGetGoodsDetails(ids string) (err error, data []wmodel.Goods) {
	//
	//url := string(y.Http + "mcang/Mcang/getDetailsGoods")
	//var resData []byte
	//headerData := make(map[string]string)
	//reqValue := url2.Values{}
	//headerData["father_id"] = ids
	//
	//reqValue.Add("father_id", ids)
	//
	//var header = make(map[string]string)
	//
	//header["channelType"] = y.dat.BaseInfo.Channel
	//key := header["channelType"] + y.dat.BaseInfo.AppSecret
	//header["md5"] = strings.ToLower(Sign(headerData, key))
	//err, resData = utils.PostForm(url, reqValue, header)
	//if err != nil {
	//	return
	//}
	//
	////var mapData = make(map[string]wmodel.GoodsSkuDetail)
	//var resGoodsData wmodel.GoodsDetail
	//err = json.Unmarshal(resData, &resGoodsData)
	//
	//if len(resGoodsData.Data) > 0 {
	//
	//	data = resGoodsData.Data
	//}

	return
}

func (*TianMa) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {

	defer wg.Done()
	var result *stbz.APIResult
	result, err = stbz.API(
		1,
		"/v2/Category/Lists",
		map[string]string{},
		g.Map{"page": i, "limit": info.Limit, "source": info.Source},
	)
	fmt.Println("循环：", i, info.Limit, info.Source)
	if result.Data != nil {
		datas := result.Data.(map[string]interface{})
		var cateItem []model.Category
		cateJson := datas["data"]
		mJson, _ := json.Marshal(cateJson)
		stringJson := string(mJson)
		err = json.Unmarshal([]byte(stringJson), &cateItem)
		mutex.Lock()
		if len(cateItem) > 0 {
			category = append(category, cateItem...)
		}
		mutex.Unlock()

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

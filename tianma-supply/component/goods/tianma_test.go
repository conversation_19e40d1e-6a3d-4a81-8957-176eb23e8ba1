package goods

import (
	"encoding/json"
	"fmt"
	"public-supply/request"
	"testing"
	"tianma-supply/model"
	request2 "tianma-supply/request"
	"tianma-supply/response"
)

func TestYzh_ImportGoodsRun(t *testing.T) {

	aaa := "{\"rows\":[{\"brands\":\"耐克,阿迪达斯,卡帕,匡威,安踏,彪马,锐步,阿迪三叶草,阿迪生活,鬼冢虎,斯伯丁,诺诗兰,狼爪,奥索卡\",\"star\":\"五星\",\"warehouse_remark\":\"鞋子发出有加固盒、货品发出无防掉包扣、系统运费按重计费、发货时效24H、支持取消订单、支持追件、配货中支持改地址、单仓发货、支持发得物地址、发出商品不会剪标及剪吊牌、专票、京东无界面单、拼多多电子面单、抖音电子面单、菜鸟电子面单、快手电子面单\",\"express\":\"普通快递、申通、韵达、圆通、中通、邮政小包、顺丰陆运寄付、顺丰陆运到付、顺丰空运寄付、顺丰空运到付、德邦到付\",\"updateTime\":\"2024-07-01 18:26:59\",\"product_quality\":\"0.00%\",\"delivery_time\":\"9h\",\"deadLine\":\"16:30\",\"des\":\"7月3日正常发货, 截单时间：16:30\",\"warehouseExplainSetting\":\"\",\"first_weight\":1.2,\"pickingRate\":99.00,\"return_remark\":\"支持零散退货（团购5件及以上不允许退货）\",\"postage_type\":false,\"wareHouseName\":\"天马连云港仓\",\"pickingDate\":\"周一至周六\",\"after_sale_finish_time\":\"76h\"}]}"

	var StockListByGoodsNoGoods response.GetWarehouseDetail

	err := json.Unmarshal([]byte(aaa), &StockListByGoodsNoGoods)

	fmt.Println(err)

	return

	var tm TianMa
	tm.InitSetting(1)
	tm.GetSupplyBalance(57)
	return

	//aaacc := "143.79"
	//SellPrice, _ := strconv.ParseFloat(aaacc, 64)
	//
	//fmt.Println(SellPrice)
	//
	//return

	//fmt.Println(runtime.NumGoroutine())
	//return

	//source.DB().Exec("DROP TABLE IF EXISTS `yzh_products`;")
	//source.DB().Exec("CREATE TABLE yzh_products  select * from yzh_temp_products  ;")

	//source.DB().Exec("alter table yzh_temp_products rename yzh_products;")

	//source.DB().Exec("DROP TABLE IF EXISTS `yzh_products`;")
	//source.DB().Exec("CREATE TABLE yzh_products  select * from yzh_temp_products  ;")
	//
	////source.DB().Exec("alter table yzh_temp_products rename yzh_products;")
	//
	//return

}

func TestTianMa_GetGoods(t *testing.T) {
	var tm TianMa
	tm.InitSetting(1)
	var info1 request.GetGoodsSearch
	info1.Page = 1
	tm.GetGoods(info1)
	return

}

func TestTianMa_GetWareHouseNameInfo(t *testing.T) {

	var tm TianMa
	tm.InitSetting(1)
	var info1 request.GetGoodsSearch
	info1.Page = 1
	//tm.GetWareHouseNameInfo()
	return
}

func TestTianMa_GetGoodsDetail(t *testing.T) {

	var tm TianMa
	tm.InitSetting(1)
	var a model.Article
	a.ArticleNO = "88888374-WGD"
	tm.GetGoodsDetail(a)
	return

}

func TestTianMa_GetDeliveryInfo(t *testing.T) {
	var tm TianMa
	tm.InitSetting(1)
	var a request2.WareHouse
	a.WareHouseName = "天马淮安仓"
	tm.GetDeliveryInfo(a)
	return
}

func TestTianMa_GetDeliveryTimeList(t *testing.T) {

	var tm TianMa
	tm.InitSetting(1)
	var a model.DeliveryTimeData
	a.Page = "1"
	a.Rows = "10"
	tm.GetDeliveryTimeList(a)
	return
}

func TestTianMa_GetInventoryListByGroup(t *testing.T) {
	var tm TianMa
	tm.InitSetting(1)
	var a request2.InventoryListByGroup
	//a.WareHouseName = "天马淮安仓"
	a.Articleno = "88888374-WGD"
	a.Page = "1"
	a.Rows = "100"
	a.MinMarketPrice = 0.01
	a.MaxMarketPrice = 1000.00

	tm.GetInventoryListByGroup(a)
	return

}

func TestTianMa_GetStockListByGoodsNo(t *testing.T) {
	var tm TianMa
	tm.InitSetting(1)
	var a request2.StockListByGoodsNo
	//a.WareHouseName = "天马淮安仓"
	a.Articleno = "88888374-WGD"
	a.Page = "1"
	a.Rows = "100"

	tm.GetStockListByGoodsNo(a)
	return
}

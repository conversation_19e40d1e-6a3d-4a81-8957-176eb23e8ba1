package order

import (
	"encoding/json"
	"fmt"
	"public-supply/request"
	"testing"
)

type ATT struct {
	price string
}
type AAA struct {
	TradeId string `json:"trade_id"`
	Orders  []struct {
		GoodsNo string  `json:"goods_no"`
		OrderId int     `json:"order_id"`
		PostFee float64 `json:"post_fee"`
		Price   float64 `json:"price"`
		Size1   string  `json:"size1"`
		Size2   string  `json:"size2"`
	} `json:"orders"`
	PushOrderSn string `json:"push_order_sn"`
	OrderSn     string `json:"order_sn"`
	Status      string `json:"status"`
	Info        string `json:"info"`
}

func TestYzh_ImportGoodsRun(t *testing.T) {

	resData := "[{\"trade_id\":\"976501797\",\"orders\":[{\"goods_no\":\"AFDN068-1\",\"order_id\":989482976,\"post_fee\":8.0,\"price\":244.758,\"size1\":\"M\",\"size2\":\"M\"}],\"push_order_sn\":\"\",\"order_sn\":\"187999310852\",\"status\":\"0\",\"info\":\"下单成功\"}]"
	var orderInfo []AAA
	err := json.Unmarshal([]byte(resData), &orderInfo)

	fmt.Print(err)
	//74b5c82503dce2fe0ea312938cdb94d1

	//12312312344444
	y := &TianMa{}
	y.InitSetting(1)

	//y.AfsApply("mc20231108160301203380")
	//return

	var requestd request.RequestSaleBeforeCheck
	requestd.Address.Province = "黑龙江省"
	requestd.Address.City = "哈尔滨市"
	requestd.Address.Area = "南岗区"
	requestd.Address.Description = "学府经典"

	var RequestConfirmOrder request.RequestConfirmOrder
	RequestConfirmOrder.Address.Consignee = "测试人"
	RequestConfirmOrder.Address.Phone = "18686794695"
	RequestConfirmOrder.Address.Province = "黑龙江省"
	RequestConfirmOrder.Address.City = "哈尔滨市"
	RequestConfirmOrder.Address.Area = "南岗区"
	RequestConfirmOrder.Address.Description = "学府经典"
	RequestConfirmOrder.OrderSn.OrderSn = "3333333444441111666"
	//y.DeliverGoods("186937393763")
	//return
	y.ConfirmOrder(RequestConfirmOrder)

	return

	return

	y.OrderBeforeCheck(requestd)

}

//
//func dd() {
//	var goodInfos []model5.GoodInfo
//	//for _, item := range request.Skus {
//	//	var goodInfo model5.GoodInfo
//	//	goodInfo.GoodsId = "33"
//	//	goodInfo.GoodSpecId = "33"
//	//	goodInfo.Num = 1
//	//	fmt.Println(item.Sku.Sku)
//	//	goodInfos = append(goodInfos, goodInfo)
//	//}
//
//	var goodInfo model5.GoodInfo
//	goodInfo.GoodsId = "WPS6_1221120042400388"
//	goodInfo.GoodSpecId = "WPS6_01061319208840628"
//	goodInfo.Num = 1
//	goodInfos = append(goodInfos, goodInfo)
//
//	url := y.Http + "mcang/Order/preOrder"
//
//	var resData []byte
//	headerData := make(map[string]interface{})
//	singData := make(map[string]string)
//	//reqValue := url2.Values{}
//
//	goodsInfo, _ := json.Marshal(&goodInfos)
//
//	headerData["goodsInfo"] = string(goodsInfo)
//	singData["goodsInfo"] = string(goodsInfo)
//	//reqValue.Add("goodsInfo", headerData["goodsInfo"])
//
//	headerData["lockCode"] = "12312312344444"
//	singData["lockCode"] = "12312312344444"
//	//reqValue.Add("lockCode", headerData["lockCode"])
//
//	headerData["consigneeContacts"] = request.Address.Consignee
//	singData["consigneeContacts"] = request.Address.Consignee
//	//reqValue.Add("consigneeContacts", request.Address.Consignee)
//
//	headerData["consigneePhone"] = request.Address.Phone
//	singData["consigneePhone"] = request.Address.Phone
//	//reqValue.Add("consigneePhone", request.Address.Phone)
//
//	headerData["province"] = request.Address.Province
//	singData["province"] = request.Address.Province
//	//reqValue.Add("province", headerData["province"])
//
//	headerData["city"] = request.Address.City
//	singData["city"] = request.Address.City
//	//reqValue.Add("city", headerData["city"])
//
//	headerData["area"] = request.Address.Area
//	singData["area"] = request.Address.Area
//	//reqValue.Add("area", headerData["area"])
//
//	headerData["address"] = request.Address.Street + request.Address.Description
//	singData["address"] = request.Address.Street + request.Address.Description
//	//reqValue.Add("address", headerData["address"])
//
//	headerData["source"] = "GOUSHIHUI"
//	singData["source"] = "GOUSHIHUI"
//	//reqValue.Add("source", headerData["source"])
//
//	//reqData, _ := json.Marshal(headerData)
//	var header = make(map[string]string)
//
//	header["channelType"] = y.dat.BaseInfo.Channel
//	key := header["channelType"] + y.dat.BaseInfo.AppSecret
//	header["md5"] = strings.ToLower(Sign(singData, key))
//	err, resData = Post(url, headerData, header)
//
//	//var resData []byte
//	//headerData := make(map[string]interface{})
//	//headerData["lockCode"] = time.Now()
//	//
//	//headerData["consigneeContacts"] = request.Address.Consignee
//	//headerData["consigneePhone"] = request.Address.Phone
//	//headerData["province"] = request.Address.Province
//	//headerData["city"] = request.Address.City
//	//headerData["area"] = request.Address.Area
//	//headerData["address"] = request.Address.Street + request.Address.Description
//	//headerData["goodInfo"] = goodInfos
//	//headerData["source"] = "GOUSHIHUI"
//	//
//	//reqData, _ := json.Marshal(headerData)
//	//log.Log().Info("wps 供应链商品准备下单请求所有参数", zap.Any("info", string(reqData)))
//	//var header map[string]string
//	//err, resData = utils.Post(url, reqData, header)
//	if err != nil {
//		log.Log().Error("wps 供应链下单post err", zap.Any("info", err))
//
//		return
//	}
//	log.Log().Info("wps  HttpPostJson 返回结果", zap.Any("err", string(resData)))
//
//	var PreOrder model5.PreOrder
//	err = json.Unmarshal(resData, &PreOrder)
//	if err != nil {
//		log.Log().Error(" ResOrder 解析失败", zap.Any("err", string(resData)))
//		return
//	}
//	if PreOrder.Code == 0 {
//		//res.Data.Available=3
//	} else {
//
//	}
//
//	return
//}

func TestTianMa_ApplyCancel(t *testing.T) {
	var orderClass = &TianMa{}
	orderClass.InitSetting(59)
	orderClass.ApplyCancel("188468363966")

}

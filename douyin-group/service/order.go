package service

import (
	"bytes"
	"douyin-group/common"
	"douyin-group/model"
	"douyin-group/mq"
	"douyin-group/request"
	"douyin-group/response"
	"douyin-group/setting"
	"encoding/json"
	"errors"
	financeModel "finance/model"
	incomeModel "finance/model"
	incomeService "finance/service"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io/ioutil"
	"net/http"
	"os"
	pluginModel "plugin/model"
	"strconv"
	"strings"
	"time"
	userModel "user/model"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func SyncDouYinGroupOrder() {
	//初始化
	err, rd := common.Initial()
	if err != nil {
		return
	}
	var orderListSearch request.OrderListRequest
	var newOrders []model.DouyinGroupOrderModel
	var oldOrdersSave []model.DouyinGroupOrderModel

	var userData []financeModel.User
	err = source.DB().Preload("UserLevelInfo").Find(&userData).Error
	if err != nil {
		return
	}
	var userModelMap = make(map[uint]financeModel.User)
	for _, user := range userData {
		userModelMap[user.ID] = user
	}
	var cursor = ""
	for {
		var info = orderListSearch
		info.Count = 50
		info.Cursor = cursor
		//更新时间查询订单 -- 往前推1分钟
		now := time.Now()
		info.UpdateTimeEnd = now.Unix()
		//info.UpdateTimeBegin = now.AddDate(0, 0, -40).Unix()
		// 往前推十分钟
		info.UpdateTimeBegin = now.Add(-6 * time.Minute).Unix()
		var orderListResponse response.OrderListResponse
		err, orderListResponse = rd.OrderList(info)
		if err != nil {
			log.Log().Error("抖音团购获取订单失败", zap.Any("err", err), zap.Any("info", info))
			return
		}
		if len(orderListResponse.OrderList) == 0 {
			return
		}
		//拼接需要查询的抖音团购订单id
		var douYinOrderIds []string
		for _, r := range orderListResponse.OrderList {
			douYinOrderIds = append(douYinOrderIds, r.OrderId)
		}
		//查询是否同步过
		var oldOrders []model.DouyinGroupOrderModel
		err = source.DB().Select("id,order_id,user_id,application_id,update_time,app_shop_id,small_shop_id").Where("order_id in ?", douYinOrderIds).Find(&oldOrders).Error
		//非空报错直接返回
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("抖音团购获取旧订单失败", zap.Any("err", err), zap.Any("info", info))
			return
		}
		//处理数据方便下面使用
		var oldOrdersMap = make(map[string]model.DouyinGroupOrderModel)
		for _, oOrder := range oldOrders {
			oldOrdersMap[oOrder.OrderId] = oOrder
		}
		for _, rOrder := range orderListResponse.OrderList {
			//结算时间使用第一个
			if len(rOrder.SettleOrders) > 0 {
				rOrder.SettleTime = rOrder.SettleOrders[0].SettleTime
			}
			if _, ok := oldOrdersMap[rOrder.OrderId]; !ok {
				var externalInfo []string
				//rOrder.CommandExternalInfo = "6_287_1" //测试
				// todo rOrder.CommandExternalInfo = "S_1_9" //小商店测试
				externalInfo = strings.Split(rOrder.CommandExternalInfo, "_")
				if len(externalInfo) < 3 {
					log.Log().Info("抖音团购订单同步错误1：转化external", zap.Any("err", rOrder))
					continue
				}
				var userID, appID, ShopUserID, AppShopID, smallShopID int
				if externalInfo[0] == "S" {
					smallShopID, err = strconv.Atoi(externalInfo[1])
					userID, err = strconv.Atoi(externalInfo[2])
					rOrder.PluginID = pluginModel.SmallShopPluginID
				} else {
					userID, err = strconv.Atoi(externalInfo[1])
					if err != nil {
						log.Log().Info("抖音团购订单同步错误2：转化external", zap.Any("err", rOrder))
						continue
					}
					appID, err = strconv.Atoi(externalInfo[0])
					if err != nil {
						log.Log().Info("抖音团购订单同步错误3：转化external", zap.Any("err", rOrder))
						continue
					}
					ShopUserID, err = strconv.Atoi(externalInfo[2])
					if err != nil {
						log.Log().Info("抖音团购订单同步错误4：转化external", zap.Any("err", rOrder))
						continue
					}
					if len(externalInfo) > 3 {
						AppShopID, err = strconv.Atoi(externalInfo[3])
						if err != nil {
							log.Log().Info("抖音团购订单同步错误5：转化external", zap.Any("err", rOrder))
						}
					}
				}

				rOrder.UserID = uint(userID)
				rOrder.ApplicationID = uint(appID)
				rOrder.ShopUserID = uint(ShopUserID)
				rOrder.AppShopID = uint(AppShopID)
				rOrder.SmallShopID = uint(smallShopID)
				if _, uok := userModelMap[rOrder.UserID]; !uok {
					log.Log().Info("抖音团购订单同步错误5：用户不存在", zap.Any("err", rOrder))
					continue
				}
				rOrder.AwardRatio, rOrder.AwardBase, rOrder.AwardAmount = GetAward(userModelMap[rOrder.UserID].UserLevelInfo, rOrder)
				newOrders = append(newOrders, rOrder)
			} else {
				//更新时间改变进行更新
				if rOrder.UpdateTime != oldOrdersMap[rOrder.OrderId].UpdateTime {
					rOrder.UserID = oldOrdersMap[rOrder.OrderId].UserID
					rOrder.ApplicationID = oldOrdersMap[rOrder.OrderId].ApplicationID
					rOrder.AppShopID = oldOrdersMap[rOrder.OrderId].AppShopID
					rOrder.AwardRatio, rOrder.AwardBase, rOrder.AwardAmount = GetAward(userModelMap[rOrder.UserID].UserLevelInfo, rOrder)
					rOrder.ID = oldOrdersMap[rOrder.OrderId].ID
					oldOrdersSave = append(oldOrdersSave, rOrder)
				}
			}
		}

		if orderListResponse.HasMore == true {
			cursor = orderListResponse.Cursor
		} else {
			break
		}
	}
	if len(newOrders) > 0 {
		err = source.DB().Create(&newOrders).Error
		if err != nil {
			return
		}
		_ = NotifyDouyinGroupOrders(newOrders, "douyinGroupOrder.create")
	}
	if len(oldOrdersSave) > 0 {
		log.Log().Error("抖音团购测试1", zap.Any("oldOrdersSave", oldOrdersSave))
		var mqOrders []model.DouyinGroupOrderModel
		for _, item := range oldOrdersSave {
			_ = SaveDouyinGroupOrder(item)
			if item.PluginID == 0 {
				mqOrders = append(mqOrders, item)
			}
		}
		_ = NotifyDouyinGroupOrders(mqOrders, "douyinGroupOrder.update")
	}
	return
}

// 结算
func PercentageHandle() {
	var waitPercentOrders []response.DouyinGroupOrder
	//查询待分成，状态不是已退款，结算金额不为0的
	err := source.DB().Preload("User.UserLevelInfo").Where("supply_status = 0").Where("status in (1,3,4,5)").Where("settle_amount > 0").Limit(1000).Find(&waitPercentOrders).Error
	if err != nil {
		return
	}

	for _, order := range waitPercentOrders {
		//没有用户id，没有用户等级，没有结算单，跳过
		if order.User.ID == 0 || order.User.UserLevelInfo.ID == 0 || len(order.SettleOrders) == 0 {
			continue
		}
		var isSettle = 1
		//只要有一条没有分账完成的就不进行结算
		for _, settleOrder := range order.SettleOrders {
			if settleOrder.Status != 3 {
				isSettle = 0
				break
			}
		}
		//有没有结算的奖励暂不能结算
		if isSettle == 0 {
			continue
		}
		log.Log().Info("抖音团购分成结算,奖励id[" + strconv.Itoa(int(order.ID)) + "]")
		err = source.DB().Transaction(func(tx *gorm.DB) error {
			// 奖励结算
			err = SettleAwardByTx(tx, order)
			if err != nil {
				log.Log().Error("抖音团购分成结算失败"+err.Error(), zap.Any("err", err), zap.Any("order", order))
				return err
			}

			// 增加收入
			var income incomeModel.UserIncomeDetails
			income.UserID = int(order.UserID)
			income.OrderSn = int(order.OrderSn)
			income.IncomeType = incomeModel.DouyinGroup
			income.Amount = uint(order.AwardAmount)
			// 小商店店主分成
			if order.PluginID == pluginModel.SmallShopPluginID {
				income.IncomeType = incomeModel.SmallShop
			}
			err = incomeService.IncreaseIncome(income)
			if err != nil {
				log.Log().Info("增加收入失败,返回")
				log.Log().Error(err.Error(), zap.Any("err", err))
				return err
			}
			err = mq.PublishMessage(order.ID, mq.Settle, 0)
			if err != nil {
				log.Log().Info("抖音团购分销失败,返回")
				return err
			}
			return err
		})
		if err != nil {
			log.Log().Error("抖音团购分销失败"+err.Error(), zap.Any("err", err))
			return
		}

		log.Log().Info("抖音团购分成结算,成功")
	}
	return
}

// 计算分成 基数 金额
func GetAward(userLevelInfo financeModel.UserLevel, rOrder model.DouyinGroupOrderModel) (AwardRatio, AwardBase, AwardAmount int64) {
	AwardRatio = userLevelInfo.DouyinGroupRatio
	if rOrder.SettleAmount > 0 {
		rOrder.CommissionAmount = rOrder.SettleAmount
	}
	//* (100 - ServerRatio) / 100
	AwardBase = rOrder.CommissionAmount

	AwardAmount = AwardBase * AwardRatio / 10000
	return
}

type DouyinGroupOrderMessage struct {
	OperationType string   `json:"message_type"`
	Ids           []string `json:"ids"`
	MemberSign    string   `json:"member_sign"`
}

// 抖音团购使用供应链回调地址
type Application struct {
	source.Model
	MemberId     int    `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	CallBackLink string `json:"callBackLink" form:"callBackLink" gorm:"column:call_back_link;comment:;type:varchar(255);size:255;"`
	AppSecret    string `json:"appSecret" gorm:"column:app_secret;comment:;type:varchar(255);"`
}
type ApplicationShop struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:;type:int;size:10;"`
	ShopName      string `json:"shop_name" form:"shop_name"`
	CallbackLink  string `json:"callback_link" form:"callback_link"`
	AppSecret     string `json:"app_secret" form:"app_secret"`
}

func (Application) TableName() string {
	return "application"
}
func NotifyDouyinGroupOrders(orders []model.DouyinGroupOrderModel, operationType string) (err error) {
	status := setting.IsAppOrderApi()
	if status == 0 {
		//yzResponse.FailWithMessage("已关闭采购端同步订单", c)
		return
	}
	header := map[string]string{
		"Content-Type": "application/json",
	}
	var orderIdsByAppID = make(map[uint][]string)
	var orderIdsByAppShopID = make(map[uint][]string)

	for _, order := range orders {
		if order.AppShopID == 0 {
			orderIdsByAppID[order.ApplicationID] = append(orderIdsByAppID[order.ApplicationID], order.OrderId)
		} else {
			orderIdsByAppShopID[order.AppShopID] = append(orderIdsByAppShopID[order.AppShopID], order.OrderId)

		}
	}
	var message DouyinGroupOrderMessage
	for app, ids := range orderIdsByAppID {
		message = DouyinGroupOrderMessage{}
		message.OperationType = operationType
		message.Ids = ids
		var application Application
		err = source.DB().First(&application, app).Error
		if err != nil {
			continue
		}
		message.MemberSign = application.AppSecret
		if application.CallBackLink != "" {
			log.Log().Info("开始通知下游", zap.Any("application", message))
			err, _ = post(application.CallBackLink, message, header)
			if err != nil {
				log.Log().Info("通知下游失败", zap.Any("application", application))
				continue
			}
		}
	}
	for app, ids := range orderIdsByAppShopID {
		message = DouyinGroupOrderMessage{}
		message.OperationType = operationType
		message.Ids = ids
		var application ApplicationShop
		err = source.DB().First(&application, app).Error
		if err != nil {
			continue
		}
		message.MemberSign = application.AppSecret
		if application.CallbackLink != "" {
			log.Log().Info("开始通知下游", zap.Any("application", message))
			err, _ = post(application.CallbackLink, message, header)
			if err != nil {
				log.Log().Info("通知下游失败", zap.Any("application", application))
				continue
			}
		}
	}

	return
}

type Resp struct {
	Code   int    `json:"code"`
	Result int    `json:"result"`
	Msg    string `json:"msg"`
}

func post(url string, data interface{}, header map[string]string) (error, Resp) {
	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	var respon Resp
	err = json.Unmarshal(result, &respon)
	return err, respon
}

func GetOrderList(info request.OrderListSearch) (err error, total int64, list interface{}) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	var db = source.DB().Model(&response.DouyinGroupOrder{}).Preload("User").Where("plugin_id = ?", 0)
	var data []response.DouyinGroupOrder

	db = OrderListSearch(db, info)
	if info.KeyWord != "" {
		userIds := []uint{}
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.KeyWord+"%").Or("nick_name like ?", "%"+info.KeyWord+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) > 0 {
			db = db.Where("user_id in ?", userIds)
		} else {
			return
		}
	}
	if err != nil {
		return
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&data).Error
	return err, total, data
}

func GetOrderDetailList(info request.GetOrderDetailListSearch) (err error, list []response.DouyinGroupOrder) {

	db := source.DB().Model(&response.DouyinGroupOrder{}).Preload("User").Where("plugin_id = ?", 0)

	if len(info.OrderId) > 0 {
		db.Where("order_id in ?", info.OrderId)
	}
	if info.ApplicationID > 0 {
		db.Where("application_id = ?", info.ApplicationID)
	}
	if info.AppShopID > 0 {
		db.Where("app_shop_id = ?", info.AppShopID)
	}

	err = db.Order("created_at desc").Find(&list).Error
	return
}
func OrderListSearch(db *gorm.DB, info request.OrderListSearch) *gorm.DB {
	if info.ApplicationID > 0 {
		db = db.Where("application_id = ?", info.ApplicationID)
	}

	if info.AppShopID > 0 {
		db = db.Where("app_shop_id = ?", info.AppShopID)
	}

	if info.Status > 0 {
		db = db.Where("status = ?", info.Status)
	}

	if info.OrderSn > 0 {
		db = db.Where("order_sn = ?", info.OrderSn)

	}
	if info.RootOrderId != "" {
		db = db.Where("root_order_id = ?", info.RootOrderId)
	}

	if info.UserID > 0 {
		db = db.Where("user_id = ?", info.UserID)

	}

	var timeValue = ""
	switch info.TimeType {
	case 1:
		timeValue = "pay_time"
		break
	case 2:
		timeValue = "settle_time"
		break
	case 3:
		timeValue = "update_time"
		break
	}
	//如果查询已退款
	if info.TimeType == 3 {
		db = db.Where("refund_amount > 0")
	}
	if timeValue != "" {
		if info.StartAT != "" {
			db.Where("`"+timeValue+"` >= ?", info.StartAT)
		}
		if info.EndAT != "" {
			db = db.Where("`"+timeValue+"` <= ?", info.EndAT)
		}
	}
	return db
}
func SettleAwardByTx(tx *gorm.DB, award response.DouyinGroupOrder) (err error) {
	award.SupplyStatus = 1
	award.LocalSettleAt = &source.LocalTime{Time: time.Now()}
	err = tx.Updates(&award).Error
	return
}

func SaveDouyinGroupOrder(data model.DouyinGroupOrderModel) (err error) {
	err = source.DB().Omit("created_at", "user_id", "shop_user_id", "application_id", "order_sn", "supply_status", "app_shop_id", "local_settle_at").Model(&model.DouyinGroupOrderModel{}).Where("id = ?", data.ID).Save(&data).Error
	return
}

func ExportOrderList(info request.OrderListSearch) (err error, link string) {
	var db = source.DB().Model(&response.DouyinGroupOrder{}).Preload("User")
	var data []response.DouyinGroupOrder
	db = OrderListSearch(db, info)
	if info.KeyWord != "" {
		userIds := []uint{}
		err = source.DB().Model(userModel.User{}).Where("username like ?", "%"+info.KeyWord+"%").Or("nick_name like ?", "%"+info.KeyWord+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("没有需要导出的数据" + err.Error())
			return
		}
		if len(userIds) > 0 {
			db = db.Where("user_id in ?", userIds)
		} else {
			err = errors.New("没有需要导出的数据")
			return
		}
	}
	err = db.Order("created_at desc").Find(&data).Error

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "抖音团购订单号")
	f.SetCellValue("Sheet1", "B1", "抖音团购_appid")
	f.SetCellValue("Sheet1", "C1", "会员id")
	f.SetCellValue("Sheet1", "D1", "中台应用id")
	f.SetCellValue("Sheet1", "E1", "抖音团购商品id")
	f.SetCellValue("Sheet1", "F1", "抖音团购商品名称")
	f.SetCellValue("Sheet1", "G1", "抖音团购商品图片")
	f.SetCellValue("Sheet1", "H1", "总支付金额")
	f.SetCellValue("Sheet1", "I1", "支付时间")
	f.SetCellValue("Sheet1", "J1", "退款时间")
	f.SetCellValue("Sheet1", "K1", "预估结算金额")
	f.SetCellValue("Sheet1", "L1", "预估佣金")
	f.SetCellValue("Sheet1", "M1", "订单状态")
	f.SetCellValue("Sheet1", "N1", "结算时间")
	f.SetCellValue("Sheet1", "O1", "推广费率")
	f.SetCellValue("Sheet1", "P1", "0未分成 1已分成")
	f.SetCellValue("Sheet1", "Q1", "中台分成时间")
	f.SetCellValue("Sheet1", "R1", "分成金额")
	f.SetCellValue("Sheet1", "S1", "分成比例")
	f.SetCellValue("Sheet1", "T1", "分成基数")

	i := 2
	for _, v := range data {
		var refundTime int64 = 0
		if v.UpdateTime > 0 {
			refundTime = v.UpdateTime
		}

		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.OrderId)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.AppId)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.UserID)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.ApplicationID)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), fmt.Sprintf("%d", v.ProductList[0].ProductID))
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.ProductList[0].Name)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.ProductList[0].Img)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), Fen2Yuan(uint(v.PayAmount)))
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), GetDateTimeFormat(v.PayTime))
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), GetDateTimeFormat(refundTime))
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), Fen2Yuan(uint(v.PayAmount)))
		f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), Fen2Yuan(uint(v.CommissionAmount)))
		f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.Status)
		f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), GetDateTimeFormat(v.SettleTime))
		f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), v.CommissionRate)
		f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), v.SupplyStatus)
		if v.LocalSettleAt != nil {
			f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), v.LocalSettleAt.Format("2006-01-02 15:04:05"))
		}
		f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), Fen2Yuan(uint(v.AwardAmount)))
		f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), v.AwardRatio)
		f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), v.AwardBase)

		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_douyin_group_order"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "订单导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link
}

func Fen2Yuan(price uint) float64 {
	d := decimal.New(1, 2) //分除以100得到元

	result := decimal.NewFromInt(int64(price)).DivRound(d, 2).String()
	//result := decimal.NewFromInt(int64(price)).String()

	floatvar, _ := strconv.ParseFloat(result, 64)

	return floatvar
}
func GetDateTimeFormat(timestamp int64) string {
	// 将时间戳转换为时间
	t := time.Unix(timestamp, 0)

	// 输出格式化时间
	return t.Format("2006-01-02 15:04:05")
}

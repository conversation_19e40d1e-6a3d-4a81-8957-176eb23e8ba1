package request

import (
	"material-distribute/model"
	"yz-go/request"
)

type CategoryListSearch struct {
	Category1ID    uint   `json:"category1_id" form:"category1_id" gorm:"column:category1_id;comment:;type:int(11);"`
	Sid   uint `json:"sid" form:"sid" ` //小商店店主id
	Level int `json:"level" form:"level"` //层级
}


type GetMaterialDistributeByProductId struct {
	ProductId    uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:;type:int(11);"`

}


type ListSearch struct {
	model.Material
	request.PageInfo

	Category1ID    uint   `json:"category1_id" form:"category1_id" gorm:"column:category1_id;comment:;type:int(11);"`
	Category2ID    uint   `json:"category2_id" form:"category2_id" gorm:"column:category2_id;comment:;type:int(11);"`
	Category3ID    uint   `json:"category3_id" form:"category3_id" gorm:"column:category3_id;comment:;type:int(11);"`
	ProductTitle   string `json:"product_title"`
	SupplierID     uint   `json:"supplier_id"`
	GatherSupplyId uint   `json:"gather_supply_id"`
	BrandID        uint   `json:"brand_id"`
	IsImport       string `json:"is_import"`
	AppId          uint   `json:"app_id"`

	Auth        string `json:"auth"`
	Type        int    `json:"type"`
	CompanyName string `json:"company_name"`
	AppName     string `json:"app_name"`
	NickName    string `json:"nick_name"`
	UserName    string `json:"user_name"`
	Sid   uint `json:"sid" form:"sid" ` //小商店店主id
	ProductId    uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:;type:int(11);"`

}

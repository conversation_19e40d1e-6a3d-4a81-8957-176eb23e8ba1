package v1

import (
	"github.com/gin-gonic/gin"
	"material-distribute/service"
	yzResponse "yz-go/response"
)

func AuditList(c *gin.Context) {
	var search service.AuditListSearch
	if err := c.ShouldBindJSON(&search); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data, total := service.AuditList(search); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
		}, "获取成功", c)
	}
}

func AuditOperate(c *gin.Context) {
	var params service.AuditOperateParams
	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}

	if err := service.AuditOperate(params); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	}

	yzResponse.Ok(c)
}

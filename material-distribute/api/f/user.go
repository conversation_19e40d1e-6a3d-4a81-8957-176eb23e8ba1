package f

import (
	"github.com/gin-gonic/gin"
	"material-distribute/model"
	"material-distribute/service"
	ufv1 "user/api/f/v1"
	yzResponse "yz-go/response"
)

func SelfList(c *gin.Context) {
	var search service.SelfListSearch
	if err := c.ShouldBindJSON(&search); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	search.UserID = ufv1.GetUserID(c)

	if err, total, auditTotal, adoptTotal, rejectTotal, list := service.SelfList(search); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(map[string]interface{}{
			"list":        list,
			"total":       total,
			"page":        search.Page,
			"pageSize":    search.PageSize,
			"adoptTotal":  adoptTotal,
			"auditTotal":  auditTotal,
			"rejectTotal": rejectTotal,
		}, "获取成功", c)
	}
}

func SelfCreate(c *gin.Context) {
	var record model.MaterialAudit
	if err := c.ShouldBindJSON(&record); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	record.UserID = ufv1.GetUserID(c)

	if err := service.SelfCreate(record); err != nil {
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func SelfDetail(c *gin.Context) {
	var p service.SelfDetailParams
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	p.UserId = ufv1.GetUserID(c)

	if err, data := service.SelfDetail(p); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func SelfReplace(c *gin.Context) {
	var p service.SelfReplaceParams
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	p.UserId = ufv1.GetUserID(c)

	if err := service.SelfReplace(p); err != nil {
		yzResponse.FailWithMessage("操作失败", c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

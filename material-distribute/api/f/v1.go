package f

import (
	"github.com/gin-gonic/gin"
	"material-distribute/request"
	"material-distribute/service"
	yzResponse "yz-go/response"
)
//通过商品id获取关联的素材
func GetMaterialDistributeByProductId(c *gin.Context) {
	var requestData request.GetMaterialDistributeByProductId
	err := c.ShouldBindQuery(&requestData)
	if err != nil {
		yzResponse.FailWithMessage("参数获取失败"+err.Error(), c)
		return
	}
	if requestData.ProductId == 0 {
		yzResponse.FailWithMessage("商品id不可为空", c)
		return
	}

	if err, data := service.GetMaterialDistributeByProductId(requestData); err != nil {
		yzResponse.FailWithMessage("获取失败"+err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)

	}
}
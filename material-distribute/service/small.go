package service

import (
	"errors"
	"gorm.io/gorm"
	"material-distribute/model"
	"material-distribute/request"
	productmodel "product/model"
	"yz-go/source"
)
//查询有商品素材的分类
func CategoryList(info request.CategoryListSearch)(err error,category []productmodel.Category) {
	db := source.DB().Model(&model.Material{}).Joins("left join products on products.id = materials.product_id")
	//查询分组是无限制的素材的
	db.Joins("left join material_groups on material_groups.id = materials.group_id").Where("material_groups.type = 4")
    //查询导入到小商品的商品的素材
	db.Joins("left join small_shop_product_sales as ssps on ssps.product_id = materials.product_id").Where("ssps.small_shop_id = ?",info.Sid)

	if info.Category1ID == 0 {
		db.Joins("left join categories on categories.id = products.category1_id")
	}else{
		db.Joins("left join categories on categories.id = products.category2_id").Where("products.category1_id = ?",info.Category1ID)
	}
	err = db.Select("categories.*").Group("categories.id").Find(&category).Error
	//没有分类返回空
	if err != nil {
		err = nil
		return
	}
	return
}
type MiniProductList struct {
	ID        uint   `json:"id"`
	Title     string `json:"title"`
	ImageUrl  string `json:"image_url"`
	Sales     int    `json:"sales"`
	ShopPrice uint   `json:"shop_price"`
}
type Material struct {
	model.ListMaterial
	MiniProductList MiniProductList `json:"mini_product_list" gorm:"-"`
	Price uint  `json:"price" ` //小商店商品金额

}
//获取素材-- 分组无限制。已导入到小商店商品的
func SmallFrontList(info request.ListSearch) (err error, video []Material, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&video).Preload("Product.Supplier").Preload("Product.GatherSupply").Preload("Group").Where("materials.is_display = 1")
	//只查询已导入的
	db.Joins("left join small_shop_product_sales as ssps on ssps.product_id = materials.product_id").Where("ssps.small_shop_id = ?",info.Sid).Where("ssps.deleted_at is null")
	//只 查询无限制的
	db.Joins("left join material_groups on material_groups.id = materials.group_id").Where("material_groups.type = 4")

	if info.ProductId != 0  {
		db.Where("materials.product_id = ?",info.ProductId)
	}

	if info.Title != "" {
		db.Where("materials.title like ?", "%"+info.Title+"%")
	}

	if info.Category1ID > 0 {
		var productIds []int64
		err = source.DB().Model(productmodel.Product{}).Where("category1_id = ?", info.Category1ID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("materials.product_id in (?)", productIds)
		} else {
			db.Where("materials.product_id in (?)", "")

		}
	}
	if info.Category2ID > 0 {
		var productIds []int64
		err = source.DB().Model(productmodel.Product{}).Where("category2_id = ?", info.Category2ID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("materials.product_id in (?)", productIds)
		} else {
			db.Where("materials.product_id in (?)", "")

		}
	}
	if info.Category3ID > 0 {
		var productIds []int64
		err = source.DB().Model(productmodel.Product{}).Where("category3_id = ?", info.Category3ID).Pluck("id", &productIds).Error
		if len(productIds) > 0 && err == nil {
			db.Where("materials.product_id in (?)", productIds)
		} else {
			db.Where("materials.product_id in (?)", "")

		}
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Select("materials.*,ssps.price").Preload("Product").Preload("Product.Brand").Order("materials.id desc").Find(&video).Error
	for key, item := range video {
		video[key].MiniProductList.ID = item.ProductID
		video[key].MiniProductList.ImageUrl = item.Product.ImageUrl
		video[key].MiniProductList.Title = item.Product.Title
		video[key].MiniProductList.Sales = int(item.Product.Sales)
		video[key].MiniProductList.ShopPrice = item.Price
	}
	return
}


//获取素材-- 分组无限制。已导入到小商店商品的
func FrontSmallMaterialDetail(info request.ListSearch) (err error, video Material) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := source.DB().Model(&video).Preload("Product.Supplier").Preload("Product.GatherSupply").Preload("Group")
	//只查询已导入的
	db.Joins("left join small_shop_product_sales as ssps on ssps.product_id = materials.product_id").Where("ssps.small_shop_id = ?",info.Sid)
	//只 查询无限制的
	db.Joins("left join material_groups on material_groups.id = materials.group_id").Where("material_groups.type = 4")

	db.Where("materials.id = ?",info.ID)
	err = db.Limit(limit).Offset(offset).Select("materials.*,ssps.price").Preload("Product").Preload("Product.Brand").Order("materials.id desc").First(&video).Error

	video.MiniProductList.ID = video.ProductID
	video.MiniProductList.ImageUrl = video.Product.ImageUrl
	video.MiniProductList.Title = video.Product.Title
	video.MiniProductList.Sales = int(video.Product.Sales)
	video.MiniProductList.ShopPrice = video.Price

	return
}
//通过商品id获取关联的素材
func GetMaterialDistributeByProductId(info request.GetMaterialDistributeByProductId)(err error,data []Material)  {
	db := source.DB().Model(&Material{}).Where("product_id = ?",info.ProductId).Where("materials.is_display = 1")

	err = db.Find(&data).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
	   err = errors.New("获取素材失败"+err.Error())
	   return
	}

	err = nil

	return
}
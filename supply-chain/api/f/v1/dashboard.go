package v1

import (
	systemService "gin-vue-admin/admin/service"
	"gin-vue-admin/cmd/gva"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"order/service"
	"supply-chain/request"
	supplyChainService "supply-chain/service"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// 获取仪表盘数据
func GetDashboardIndex(c *gin.Context) {
	gva.DomainName = c.Request.Host
	if err, data := service.GetTodayOrderTotal(0, 0); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}

	//yzResponse.OkWithData(dashboardIndex, c)
}

// 获取仪表盘数据
func GetUserDashboardIndex(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	if userID == 0 {
		yzResponse.FailWithMessage("未登录", c)
		return
	}
	if err, data := service.GetTodayOrderTotal(0, userID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}

	//yzResponse.OkWithData(dashboardIndex, c)
}

// 获取订单30天热销商品前30个
func GetHotSellingProductsOrders(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	if userID == 0 {
		yzResponse.FailWithMessage("未登录", c)
		return
	}
	if err, data := service.GetHotSellingProductsOrders(userID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}

	//yzResponse.OkWithData(dashboardIndex, c)
}

// 订单数量
func GetUserOrderLineChart(c *gin.Context) {
	var rankingRequest request.LineChartRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	if userID == 0 {
		yzResponse.FailWithMessage("未登录", c)
		return
	}
	if err, data := service.GetOrderLineChart(rankingRequest, 0, userID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}

}

// 订单数量
func GetOrderLineChart(c *gin.Context) {
	var rankingRequest request.LineChartRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.GetOrderLineChart(rankingRequest, 0, 0); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}

}

// 新增会员折线图
func GetAddUserLineChart(c *gin.Context) {
	var rankingRequest request.LineChartRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.GetAddUserLineChart(rankingRequest); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}
}

// 站内余额充值折线图
func GetPurchasingBalanceLineChart(c *gin.Context) {
	var rankingRequest request.LineChartRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.GetPurchasingBalanceLineChart(rankingRequest); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}
}

// 新增采购端数量折线图
func GetApplicationLineChart(c *gin.Context) {
	var rankingRequest request.LineChartRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.GetApplicationLineChart(rankingRequest); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}
}

// 新增商品数量折线图
func GetProductLineChart(c *gin.Context) {
	var rankingRequest request.LineChartRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.GetProductLineChart(rankingRequest); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}
}

// 获取仪表盘数据
func GetServerStatus(c *gin.Context) {
	if server, err := systemService.GetServerInfo(); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"server": server}, "获取成功", c)
	}

}

// 获取获取会员统计，商品统计，供应商统计,小程序二维码
func GetTotal(c *gin.Context) {
	if err, data := service.GetTotal(); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		_, shopSetting := supplyChainService.GetSysShopSetting()
		//小程序二维码
		yzResponse.OkWithDetailed(gin.H{"data": data, "shop_app_code": shopSetting.Value.ShopAppCode}, "获取成功", c)
	}

}

// 商品上架饼图 这个方法只有今日与昨日
func ProductStatisticsData(c *gin.Context) {
	var rankingRequest request.RankingRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	data := service.ProductStatisticsData(rankingRequest)

	yzResponse.OkWithDetailed(gin.H{"data": data}, "获取成功", c)

}

// 记录昨日的总数以及饼图
func ProductOldCount(c *gin.Context) {
	var rankingRequest request.RankingRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	service.ProductOldCount()

	yzResponse.Ok(c)

}

// 采购排行
func UserProcurementRanking(c *gin.Context) {
	var rankingRequest request.RankingRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.UserProcurementRanking(rankingRequest); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"data": data}, "获取成功", c)
	}

}

// 商品销量排行
func ProductProcurementRanking(c *gin.Context) {
	var rankingRequest request.RankingRequest
	err := c.ShouldBindQuery(&rankingRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数接收错误"+err.Error(), c)
		return
	}
	if err, data := service.ProductProcurementRanking(rankingRequest); err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"data": data}, "获取成功", c)
	}

}

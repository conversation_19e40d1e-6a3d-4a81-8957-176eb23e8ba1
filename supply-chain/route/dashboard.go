package route

import (
	"github.com/gin-gonic/gin"
	v1 "supply-chain/api/f/v1"
)

func InitDashboardRouter(Router *gin.RouterGroup) {
	DashboardRouter := Router.Group("dashboard")
	{
		DashboardRouter.GET("getDashboardIndex", v1.GetDashboardIndex)                         // 获取仪表盘数据
		DashboardRouter.GET("getOrderLineChart", v1.GetOrderLineChart)                         // 订单数量,订单金额 折线图
		DashboardRouter.GET("getAddUserLineChart", v1.GetAddUserLineChart)                     //新增会员 折线图
		DashboardRouter.GET("getPurchasingBalanceLineChart", v1.GetPurchasingBalanceLineChart) //充值站内余额折线图
		DashboardRouter.GET("getApplicationLineChart", v1.GetApplicationLineChart)             //新增采购端数量折线图
		DashboardRouter.GET("getProductLineChart", v1.GetProductLineChart)                     //新增商品数量折线图
		DashboardRouter.GET("productStatisticsData", v1.ProductStatisticsData)
		//商品上架饼图 这个方法只有今日与昨日  0全部1今日4昨日
		DashboardRouter.GET("productOldCount", v1.ProductOldCount) //商品上架饼图 这个方法只有今日与昨日  0全部1今日4昨日

		DashboardRouter.GET("getServerStatus", v1.GetServerStatus)                     // 获取服务器状态数据
		DashboardRouter.GET("getTotal", v1.GetTotal)                                   //获取获取会员统计，商品统计，供应商统计，
		DashboardRouter.GET("userProcurementRanking", v1.UserProcurementRanking)       //采购排行
		DashboardRouter.GET("productProcurementRanking", v1.ProductProcurementRanking) //商品销量排行

	}
}

func InitUserDashboardRouter(Router *gin.RouterGroup) {
	DashboardRouter := Router.Group("dashboard")
	{
		DashboardRouter.GET("getUserDashboardIndex", v1.GetUserDashboardIndex)             // 获取仪表盘数据
		DashboardRouter.GET("getUserOrderLineChart", v1.GetUserOrderLineChart)             // 订单数量,订单金额 折线图
		DashboardRouter.GET("getHotSellingProductsOrders", v1.GetHotSellingProductsOrders) // 获取订单30天热销商品前30个

	}
}

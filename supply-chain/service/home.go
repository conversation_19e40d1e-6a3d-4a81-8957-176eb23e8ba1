package service

import (
	service2 "after-sales/service"
	service4 "application/service"
	_ "embed"
	"finance/service"
	service3 "product/service"
	service5 "supplier/service"
	service6 "user/service"
	"yz-go/model"
	"yz-go/source"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateApplicationLevel
//@description: 更新ApplicationLevel记录
//@param: applicationLevel *model.ApplicationLevel
//@return: err error

func UpdateHomeIndex(setting model.SysSetting) (err error) {
	err = source.DB().Save(&setting).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateApplicationLevel
//@description: 更新ApplicationLevel记录
//@param: applicationLevel *model.ApplicationLevel
//@return: err error

func CreateHomeIndex(setting model.SysSetting) (err error) {
	err = source.DB().Create(&setting).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationLevel
//@description: 根据id获取ApplicationLevel记录
//@param: id uint
//@return: err error, applicationLevel model.ApplicationLevel

func GetHomeIndex() (err error, setting model.SysSetting) {
	key := "homeIndex"
	err = source.DB().Where("key = ?", key).First(&setting).Error
	return
}

func GetNotifyCount() (err error, data map[string]int64) {

	var countMap = make(map[string]int64)
	countMap["financeCount"], err = service.WithdrawUnCompleteCount()
	if err != nil {
		return
	}
	countMap["orderCount"] = service2.GetAfterSalesCount(0)
	countMap["productCount"], err = service3.GetProductVerifyCount()
	if err != nil {
		return
	}
	countMap["applicationCount"], err = service4.GetApplicationApplyCount()
	if err != nil {
		return
	}
	countMap["supplierCount"], err = service5.GetSupplierApplyCount()
	if err != nil {
		return
	}
	countMap["userMap"], err = service6.GetUserVerifyCount()
	return err, countMap

}

FROM alpine:latest
WORKDIR /data/goSupply
COPY supply /data/goSupply/
COPY resource /data/goSupply/resource
COPY conf /data/goSupply/conf

RUN adduser -DH -h /data/goSupply -u 8888 supply \
    && chmod -R 775 /data/goSupply \
    && chown -R supply:supply /data/goSupply
# set locale to utf-8
ENV LANG='en_US.UTF-8' LANGUAGE='en_US:en' LC_ALL='en_US.UTF-8' TZ='Asia/Shanghai'
EXPOSE 8888
ENTRYPOINT ["/data/goSupply/supply"]
# CMD ["-c", "/data/goSupply/"]

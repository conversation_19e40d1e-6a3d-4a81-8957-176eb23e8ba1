package model

import (
	_ "embed"
	"github.com/gookit/color"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	// color.Success.Println("\n[supplier->migration] --> 执行")
	err = source.DB().AutoMigrate(
		WechatResource{},
		WechatKeyword{},
		WechatKeywordContext{},
	)
	if err != nil {
		// color.Danger.Println("\n[supplier->创建表失败] --> 执行")
		return
	}
	color.Success.Println("\n[wechatofficial->成功1]")

	return
}

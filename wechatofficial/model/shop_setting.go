package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type SysSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}

type Value struct {
	ShopName string `json:"shop_name"`
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func GetShopName() (shopName string) {
	shopName = "中台"
	var err error
	var sysSetting SysSetting
	err = source.DB().Where("`key` = ?", "shop_setting").First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	if sysSetting.Value.ShopName != "" {
		shopName = sysSetting.Value.ShopName
	}
	return
}

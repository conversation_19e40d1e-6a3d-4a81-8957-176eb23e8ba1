package model

import "yz-go/source"

type ServerUser struct {
	Domain string `json:"domain" form:"domain"`
	Status bool   `json:"status" form:"status"`
}
type Plugin struct {
	source.Model
	Name   string       `json:"name" form:"name" gorm:"column:name;comment:;type:varchar(255);size:255;"` //名称
	Desc   string       `json:"desc" form:"desc" gorm:"column:desc;comment:;type:text;"`                  //描述
	Logo   string       `json:"logo" form:"logo" gorm:"column:logo;comment:;type:varchar(255);size:255;"` //logo
	Path   string       `json:"path" form:"path" gorm:"column:path;comment:;type:varchar(255);size:255;"` //path
	Status bool         `json:"status" form:"status" gorm:"-"`                                            //true 默认所有人可见   ， false  默认对所有人不可见
	Auth   []ServerUser `json:"auth" form:"auth" gorm:"-"`                                                //授权列表
}

const (
	SmallShopPluginID = 17
)

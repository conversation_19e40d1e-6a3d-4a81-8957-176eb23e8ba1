package cron

import (
	"fmt"
	"go.uber.org/zap"
	setting2 "jushuitan/setting"
	"product/model"
	"public-supply/common"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushJushuitanCheckStatusHandle() {
	task := cron.Task{
		Key:  "handleJushuitanCheckStatusProduct",
		Name: "检查聚水潭商品上下架状态",
		Spec: "15 */20 * * * *",
		//Spec: "12 21 3 * * *",
		Handle: func(task cron.Task) {
			JushuitanCheckStatusHandle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

var IsRunUploadCheck *int

func JushuitanCheckStatusHandle() {
	if IsRunUploadCheck != nil {
		fmt.Println("已经执行中")
		return
	}
	var result []model.Product
	err := source.DB().Preload("Skus").Where("source = ?", common.JUSHUITAN_SOURCE).Find(&result).Error
	if err != nil {
		IsRunUploadCheck = nil
		return
	}
	if len(result) == 0 {
		IsRunUploadCheck = nil
		return
	}
	var stockUpdate []map[string]interface{}

	err, JstSetting = setting2.GetJstSetting()
	if err != nil {
		log.Log().Error("聚水潭商品检查失败:配置获取失败", zap.Any("err", err.Error()))
		IsRunUploadCheck = nil
		return
	}
	for _, product := range result {
		var totalStock int
		var isDisplay = 0
		for _, sku := range product.Skus {
			totalStock += sku.Stock
		}
		if totalStock > 0 {
			isDisplay = 1
		}
		if isDisplay != product.IsDisplay && product.StatusLock == 0 {
			stockUpdateItem := make(map[string]interface{})
			stockUpdateItem["id"] = product.ID
			stockUpdateItem["is_display"] = isDisplay
			stockUpdateItem["updated_at"] = time.Now().Format("2006-01-02 15:04:05")
			stockUpdate = append(stockUpdate, stockUpdateItem)
		}

	}
	err = source.BatchUpdate(stockUpdate, "products", "id")
	if err != nil {
		log.Log().Error("聚水潭更新商品状态出错", zap.Any("err", err.Error()))
		return
	}
	IsRunUploadCheck = nil
	return
}

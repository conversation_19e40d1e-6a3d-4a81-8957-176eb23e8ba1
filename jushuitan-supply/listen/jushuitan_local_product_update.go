package listener

import (
	"jushuitan-supply/component/goods"
	mq "jushuitan-supply/mq_goods_update"
	ProductModel "product/model"
	callback2 "public-supply/callback"
	"yz-go/source"
)

func JushuitanGoodsLocalUpdateHandles() {

	mq.PushHandles("jushuitanGoodsLocalUpdate", func(data mq.JushuitanGoodsUpdateMessage) (err error) {
		var gatherSupplyID uint

		var callback callback2.GoodsCallBack

		callback.Data.GoodsIds = data.IDs

		var products []ProductModel.Product
		err = source.DB().Where("id in ?", callback.Data.GoodsIds).Find(&products).Error
		if err != nil {
			return
		}

		for k, product := range products {
			if k == 0 {
				gatherSupplyID = product.GatherSupplyID
			}
		}
		if gatherSupplyID == 0 {
			return
		}
		callback.GatherSupplyID = int(gatherSupplyID)
		var jushuitan = goods.Jushuitan{}
		err = jushuitan.InitSetting(gatherSupplyID)
		if err != nil {
			return
		}
		err = jushuitan.GoodsPriceAlert(callback)
		return
	})
	return
}

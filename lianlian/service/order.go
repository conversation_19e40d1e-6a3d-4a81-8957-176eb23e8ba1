package service

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/forgoer/openssl"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"io/ioutil"
	"lianlian/model"
	"net/http"
	orderModel "order/model"
	requestorder "order/request"
	"order/response"
	model4 "payment/model"
	model2 "public-supply/model"
	"shipping/address"
	"shipping/method"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	model3 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Operation struct {
	Title string `json:"title"`
	Code  string `json:"code"`
}

func (o *Order) AfterCreate(tx *gorm.DB) (err error) {
	//timestamp := uint(o.CreatedAt.Unix())
	//orderSn := o.ID + timestamp*110
	//err = tx.Model(&o).Update("order_sn", orderSn).Error
	return
}

type User struct {
	ID       uint   `json:"id"`
	NickName string `json:"nickname"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}

type Supplier struct {
	ID     uint   `json:"id"`
	Name   string `json:"name"`
	Mobile string `json:"supplier"`
}
type GatherSupply struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Logo       string `json:"logo"` //logo
	CategoryID uint   `json:"category_id"`
}
type Application struct {
	CallBackLink string `json:"callBackLink" form:"callBackLink" gorm:"column:call_back_link;comment:;type:varchar(255);size:255;"`

	ID        uint   `json:"id"`
	AppName   string `json:"app_name"`
	AppSecret string `json:"app_secret"`
}

func (Application) TableName() string {
	return "application"
}

type PayInfo struct {
	ID     uint   `json:"id"`
	PaySn  string `json:"pay_sn"`
	Status string `json:"status"`
}
type CloudOrder struct {
	ID           uint   `json:"id"`
	CloudOrderSn string `json:"cloud_order_sn"` //云仓订单号
	OrderId      uint   `json:"order_id"`
	CloudOrderId string `json:"cloud_order_id"` //云仓订单号
}

type Order struct {
	orderModel.Order
	User            User                    `json:"user"`
	ShippingAddress address.ShippingAddress `json:"shipping_address" gorm:"foreignKey:ShippingAddressID"`
	ShippingMethod  method.ShippingMethod   `json:"shipping_method" gorm:"foreignKey:ShippingAddressID"`
	Supplier        Supplier                `json:"supplier" gorm:"foreignKey:SupplierID"`
	GatherSupply    GatherSupply            `json:"gather_supply" gorm:"foreignKey:GatherSupplyID"`
	PayInfo         PayInfo                 `json:"pay_info" gorm:"foreignKey:PayInfoID"`
	Button          interface{}             `json:"button" gorm:"-"`
	ShopName        string                  `json:"shop_name"`
	PayType         string                  `json:"pay_type"`
	Application     Application             `json:"application"`
	CloudOrder      CloudOrder              `json:"cloud_order" gorm:"foreignKey:third_order_sn;references:cloud_order_sn"` //增加返回云仓记录 用于判断是否是云仓订单
	LianOrder       []model.LianOrder       `json:"lian_order" gorm:"foreignKey:order_sn;references:order_sn"`              //联联订单详情
	LianMainOrder   []model.LianMainOrder   `json:"lian_main_order" gorm:"foreignKey:order_sn;references:order_sn"`         //联联订单详情
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {

	b.Button = orderModel.GetButton(b.Status)
	if b.SupplierID != 0 {
		b.ShopName = "供应商:" + b.Supplier.Name
	} else if b.GatherSupplyID != 0 {
		b.ShopName = "供应链:" + b.GatherSupply.Name
	} else {
		b.ShopName = "平台自营"
	}

	b.PayType = model4.GetPayTypeName(b.PayTypeID)

	return
}

func GetOrderInfoList(info requestorder.OrderAdminSearch) (err error, list interface{}, total int64, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	order := Order{}
	db := source.DB().Model(&order).Preload(clause.Associations)
	var ads []Order

	var gatherSupply GatherSupply
	var LianIds []int
	source.DB().Model(&gatherSupply).Where("category_id=?", 8).Pluck("id", &LianIds)

	db.Where("gather_supply_id in (?)", LianIds)
	// 如果有条件搜索 下方会自动创建搜索语句
	var WaitPayNumDb = source.DB().Model(&order).Where("gather_supply_type=8")
	var WaitSendNumDb = source.DB().Model(&order).Where("gather_supply_type=8")
	var WaitReceiveNumDb = source.DB().Model(&order).Where("gather_supply_type=8")
	var CompletedNumDb = source.DB().Model(&order).Where("gather_supply_type=8")
	var ClosedNumDb = source.DB().Model(&order).Where("gather_supply_type=8")
	var BackNumDb = source.DB().Model(&order).Where("gather_supply_type=8")
	var RefundNumDb = source.DB().Model(&order).Where("gather_supply_type=8")

	//if info.SendCodeStatus != nil {
	//	fmt.Println("333")
	//
	//}
	if info.SendCodeStatus != nil && info.WriteStatus != nil {
		var whereIDS []uint
		source.DB().Model(model.LianMainOrder{}).Where("status=? and channel_status=?", info.SendCodeStatus, info.WriteStatus).Pluck("order_sn", &whereIDS)

		db.Where("order_sn in (?)", whereIDS)

		WaitPayNumDb.Where("order_sn in (?)", whereIDS)
		WaitSendNumDb.Where("order_sn in (?)", whereIDS)
		WaitReceiveNumDb.Where("order_sn in (?)", whereIDS)
		CompletedNumDb.Where("order_sn in (?)", whereIDS)
		ClosedNumDb.Where("order_sn in (?)", whereIDS)
		BackNumDb.Where("order_sn in (?)", whereIDS)
		RefundNumDb.Where("order_sn in (?)", whereIDS)
	}
	//
	//if info.SendCodeStatus == 1 && info.WriteStatus == 1 {
	//	var whereIDS []uint
	//	source.DB().Model(model.LianMainOrder{}).Where("status=? and channel_status=?", 1, 3).Pluck("order_sn", &whereIDS)
	//
	//	db.Where("order_sn in (?)", whereIDS)
	//
	//	WaitPayNumDb.Where("order_sn in (?)", whereIDS)
	//	WaitSendNumDb.Where("order_sn in (?)", whereIDS)
	//	WaitReceiveNumDb.Where("order_sn in (?)", whereIDS)
	//	CompletedNumDb.Where("order_sn in (?)", whereIDS)
	//	ClosedNumDb.Where("order_sn in (?)", whereIDS)
	//	BackNumDb.Where("order_sn in (?)", whereIDS)
	//	RefundNumDb.Where("order_sn in (?)", whereIDS)
	//}
	//if info.SendCodeStatus == 0 && info.WriteStatus == 1 {
	//	var whereIDS []uint
	//	source.DB().Model(model.LianMainOrder{}).Where("status=? and channel_status=?", 0, 3).Pluck("order_sn", &whereIDS)
	//	if len(whereIDS) > 0 {
	//		db.Where("order_sn in (?)", whereIDS)
	//
	//	}
	//
	//	WaitPayNumDb.Where("order_sn in (?)", whereIDS)
	//	WaitSendNumDb.Where("order_sn in (?)", whereIDS)
	//	WaitReceiveNumDb.Where("order_sn in (?)", whereIDS)
	//	CompletedNumDb.Where("order_sn in (?)", whereIDS)
	//	ClosedNumDb.Where("order_sn in (?)", whereIDS)
	//	BackNumDb.Where("order_sn in (?)", whereIDS)
	//	RefundNumDb.Where("order_sn in (?)", whereIDS)
	//}

	//if info.WriteStatus == 3 {
	//	db.Joins("lian_main_orders", "orders.order_sn=lian_main_orders.order_sn").Where("lian_main_orders.status=?", 3)
	//
	//}
	//
	//if info.WriteStatus == 0 {
	//	db.Joins("lian_main_orders", "orders.order_sn=lian_main_orders.order_sn").Where("lian_main_orders.status=?", 0)
	//
	//}
	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			db.Where("`supplier_id` > 0")
			WaitPayNumDb.Where("`supplier_id` > 0")
			WaitSendNumDb.Where("`supplier_id` > 0")
			WaitReceiveNumDb.Where("`supplier_id` > 0")
			CompletedNumDb.Where("`supplier_id` > 0")
			ClosedNumDb.Where("`supplier_id` > 0")
			BackNumDb.Where("`supplier_id` > 0")
			RefundNumDb.Where("`supplier_id` > 0")
		} else {
			db.Where("`supplier_id` = ?", &info.SupplierID)
			WaitPayNumDb.Where("`supplier_id` = ?", &info.SupplierID)
			WaitSendNumDb.Where("`supplier_id` = ?", &info.SupplierID)
			WaitReceiveNumDb.Where("`supplier_id` = ?", &info.SupplierID)
			CompletedNumDb.Where("`supplier_id` = ?", &info.SupplierID)
			ClosedNumDb.Where("`supplier_id` = ?", &info.SupplierID)
			BackNumDb.Where("`supplier_id` = ?", &info.SupplierID)
			RefundNumDb.Where("`supplier_id` = ?", &info.SupplierID)
		}
	}

	if info.ApplicationID > 0 {
		db.Where("`application_id` = ?", info.ApplicationID)
		WaitPayNumDb.Where("`application_id` = ?", info.ApplicationID)
		WaitSendNumDb.Where("`application_id` = ?", info.ApplicationID)
		WaitReceiveNumDb.Where("`application_id` = ?", info.ApplicationID)
		CompletedNumDb.Where("`application_id` = ?", info.ApplicationID)
		ClosedNumDb.Where("`application_id` = ?", info.ApplicationID)
		BackNumDb.Where("`application_id` = ?", info.ApplicationID)
		RefundNumDb.Where("`application_id` = ?", info.ApplicationID)
	}

	// 福禄供应链
	var supply GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}
	// 指定供应链
	if info.GatherSupplierID != nil {
		db.Where("gather_supply_id = ?", info.GatherSupplierID)
		WaitPayNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
		WaitSendNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
		WaitReceiveNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
		CompletedNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
		ClosedNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
		BackNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
		RefundNumDb.Where("gather_supply_id = ?", info.GatherSupplierID)
	} else {
		if supply.ID != 0 {
			db.Where("gather_supply_id != ?", supply.ID)
			WaitPayNumDb.Where("gather_supply_id != ?", supply.ID)
			WaitSendNumDb.Where("gather_supply_id != ?", supply.ID)
			WaitReceiveNumDb.Where("gather_supply_id != ?", supply.ID)
			CompletedNumDb.Where("gather_supply_id != ?", supply.ID)
			ClosedNumDb.Where("gather_supply_id != ?", supply.ID)
			BackNumDb.Where("gather_supply_id != ?", supply.ID)
			RefundNumDb.Where("gather_supply_id != ?", supply.ID)
		}
	}

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`pay_info_id` in ?", payInfoIds)
		WaitPayNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitSendNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitReceiveNumDb.Where("`pay_info_id` in ?", payInfoIds)
		CompletedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		ClosedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		BackNumDb.Where("`pay_info_id` in ?", payInfoIds)
		RefundNumDb.Where("`pay_info_id` in ?", payInfoIds)
	}
	if info.PayTypeID != 0 {
		db.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitPayNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitSendNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitReceiveNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		CompletedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		ClosedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		BackNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		RefundNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
	}
	if info.UserID > 0 {
		db.Where("`user_id` = ?", info.UserID)
		WaitPayNumDb.Where("`user_id` = ?", info.UserID)
		WaitSendNumDb.Where("`user_id` = ?", info.UserID)
		WaitReceiveNumDb.Where("`user_id` = ?", info.UserID)
		CompletedNumDb.Where("`user_id` = ?", info.UserID)
		ClosedNumDb.Where("`user_id` = ?", info.UserID)
		BackNumDb.Where("`user_id` = ?", info.UserID)
		RefundNumDb.Where("`user_id` = ?", info.UserID)
	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		db.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitPayNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitSendNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitReceiveNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		CompletedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		ClosedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		BackNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		RefundNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
	}
	if info.Status != nil {
		if *info.Status == 5 {
			db.Where("`refund_status` = ?", orderModel.Refunding)
			WaitPayNumDb.Where("`refund_status` = ?", orderModel.Refunding)
			WaitSendNumDb.Where("`refund_status` = ?", orderModel.Refunding)
			WaitReceiveNumDb.Where("`refund_status` = ?", orderModel.Refunding)
			CompletedNumDb.Where("`refund_status` = ?", orderModel.Refunding)
			ClosedNumDb.Where("`refund_status` = ?", orderModel.Refunding)
			BackNumDb.Where("`refund_status` = ?", orderModel.Refunding)
			RefundNumDb.Where("`refund_status` = ?", orderModel.Refunding)
		} else if *info.Status == 6 {
			db.Where("`refund_status` = ?", orderModel.RefundComplete)
			WaitPayNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
			WaitSendNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
			WaitReceiveNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
			CompletedNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
			ClosedNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
			BackNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
			RefundNumDb.Where("`refund_status` = ?", orderModel.RefundComplete)
		} else {
			db.Where("`status` = ?", info.Status)

		}
	}
	if info.RefundStatus != nil {
		db.Where("`refund_status` = ?", info.RefundStatus)
		WaitPayNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitSendNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitReceiveNumDb.Where("`refund_status` = ?", info.RefundStatus)
		CompletedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		ClosedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		BackNumDb.Where("`refund_status` = ?", info.RefundStatus)
		RefundNumDb.Where("`refund_status` = ?", info.RefundStatus)
	}
	if info.OrderSN != "" {
		db.Where("`order_sn` = ?", info.OrderSN)
		WaitPayNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitSendNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitReceiveNumDb.Where("`order_sn` = ?", info.OrderSN)
		CompletedNumDb.Where("`order_sn` = ?", info.OrderSN)
		ClosedNumDb.Where("`order_sn` = ?", info.OrderSN)
		BackNumDb.Where("`order_sn` = ?", info.OrderSN)
		RefundNumDb.Where("`order_sn` = ?", info.OrderSN)
	}
	if info.SupplySN != "" {
		db.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		WaitPayNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		WaitSendNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		WaitReceiveNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		CompletedNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		ClosedNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		BackNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		RefundNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
	}
	if info.ThirdOrderSN != "" {
		db.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		WaitPayNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		WaitSendNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		WaitReceiveNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		CompletedNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		ClosedNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		BackNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		RefundNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
	}
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitPayNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitSendNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitReceiveNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		CompletedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		ClosedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		BackNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		RefundNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitPayNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitSendNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitReceiveNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		CompletedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		ClosedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		BackNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		RefundNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(orderModel.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)

	}

	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`user_id` in ?", userIds)
		WaitPayNumDb.Where("`user_id` in ?", userIds)
		WaitSendNumDb.Where("`user_id` in ?", userIds)
		WaitReceiveNumDb.Where("`user_id` in ?", userIds)
		CompletedNumDb.Where("`user_id` in ?", userIds)
		ClosedNumDb.Where("`user_id` in ?", userIds)
		BackNumDb.Where("`user_id` in ?", userIds)
		RefundNumDb.Where("`user_id` in ?", userIds)

	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(orderModel.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`shipping_address_id` in ?", shippingIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", shippingIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		BackNumDb.Where("`shipping_address_id` in ?", shippingIds)
		RefundNumDb.Where("`shipping_address_id` in ?", shippingIds)

	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(orderModel.ShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`shipping_address_id` in ?", userIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", userIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", userIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", userIds)
		BackNumDb.Where("`shipping_address_id` in ?", userIds)
		RefundNumDb.Where("`shipping_address_id` in ?", userIds)

	}

	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(orderModel.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)

	}

	if info.CloudOrderId != 0 {
		var orderIds []uint
		source.DB().Model(&CloudOrder{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds)
		db.Where("id in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}

	err = db.Count(&total).Error
	if info.Status != nil && *info.Status == orderModel.WaitReceive {
		db.Order("sent_at DESC")
	} else {
		db.Order("created_at DESC")
	}
	err = db.Limit(limit).Offset(offset).Find(&ads).Error
	err = WaitPayNumDb.Where("status = ?", orderModel.WaitPay).Count(&WaitPayNum).Error
	err = WaitSendNumDb.Where("status = ?", orderModel.WaitSend).Count(&WaitSendNum).Error
	err = WaitReceiveNumDb.Where("status = ?", orderModel.WaitReceive).Count(&WaitReceiveNum).Error
	err = CompletedNumDb.Where("status = ?", orderModel.Completed).Count(&CompletedNum).Error
	err = ClosedNumDb.Where("status = ?", orderModel.Closed).Count(&ClosedNum).Error
	err = BackNumDb.Where("refund_status = ?", orderModel.Refunding).Count(&BackNum).Error
	err = RefundNumDb.Where("refund_status = ?", orderModel.RefundComplete).Count(&RefundNum).Error
	return err, ads, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum
}

func GetKey() string {

	return "tFvBhsRCekJR3wyZuHKpWA=="
}

var supplySetting *model2.SupplySetting
var setting model3.SysSetting

func InitSetting() {
	var err error

	err = source.DB().Where("value like  ?", "%LianChannelId%").First(&setting).Error

	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	//err = json.Unmarshal([]byte(setting.Value), &supplySetting)
	//if err != nil {
	//
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	return
	//}

}
func SendCode(data model.ResJson) (err error) {
	var l = new(ModelData)
	l.InitSetting()
	log.Log().Info("lianlian 发码订单回调接受数据-原始,", zap.Any("err", data))

	key, _ := base64.StdEncoding.DecodeString(l.Key)
	src1, _ := base64.StdEncoding.DecodeString(data.Data.EncryptedData)

	decodeData, _ := openssl.AesECBDecrypt(src1, key, openssl.PKCS5_PADDING)
	log.Log().Info("lianlian 发码订单回调接受数据,", zap.Any("err", string(decodeData)))

	var orderDetail model.SendCodeDetail
	err = json.Unmarshal([]byte(decodeData), &orderDetail)
	log.Log().Info("lianlian解析订单详情,", zap.Any("err", orderDetail))
	if err != nil {
		log.Log().Error("lianlian解析订单详情失败,", zap.Any("err", string(decodeData)))
		return
	}

	log.Log().Info("lianlian 发码回调完整数据", zap.Any("data", orderDetail))
	orderSN := orderDetail.ChannelOrderId
	var order []model.LianOrder
	source.DB().Where("channel_order_id=? and status =?", orderSN, 0).Find(&order)
	if len(order) == 0 {
		log.Log().Error("lianlian 发码全部完成，跳过操作", zap.Any("data", orderDetail))

		return
	}

	for _, item := range orderDetail.CodeList {
		var lianOrder model.LianOrder
		lianOrder.Code = item.Code
		lianOrder.QrCodeUrl = item.QrCodeUrl
		lianOrder.DetailUrl = orderDetail.BatchCode.DetailUrl
		lianOrder.BookingUrl = orderDetail.BatchCode.BookingUrl
		//lianOrder.ValidBeginDate = item.ValidBeginDate
		lianOrder.Status = 1
		//lianOrder.ValidEndDate = item.ValidEndDate
		log.Log().Info("lianlian 发码回调", zap.Any("sendcode", item))
		err = source.DB().Where("order_id=?", item.OrderId).Updates(&lianOrder).Error
		if err != nil {
			log.Log().Info("lianlian 发码回调更新处理失败", zap.Any("err", err))
		}

	}

	UpdateOrderSend(orderSN)

	Notify(orderDetail.ThirdOrderId, "order.send_code")

	return
}

func Notify(orderSN string, MsgType string) (err error) {
	var orderStatus Order
	FirstErr := source.DB().Preload("Application").Where("third_order_sn=?", orderSN).First(&orderStatus).Error

	if FirstErr != nil {
		log.Log().Info("lianlian 发码回调通知商城查询错误", zap.Any("err", err))
		return

	}
	var r OrderRequest

	r.MessageType = MsgType
	r.OrderSn = orderStatus.ThirdOrderSN

	r.MemberSign = orderStatus.Application.AppSecret
	var messageId string
	messageId = "order" + r.OrderSn + string(r.MessageType) + strconv.Itoa(int(time.Now().Unix()))
	r.MessageID = "self" + base64.StdEncoding.EncodeToString([]byte(messageId))
	header := map[string]string{
		"Content-Type": "application/json",
	}
	log.Log().Info("lianlian 发码回调通知商城post ", zap.Any("err", orderStatus.Application.CallBackLink))

	err, _ = post(orderStatus.Application.CallBackLink, r, header)
	if err != nil {
		log.Log().Info("lianlian 发码回调通知商城post错误", zap.Any("err", err))

	}

	return
}

type OrderRequest struct {
	OrderSn     string `json:"order_sn"`
	MessageType string `json:"message_type"`
	MemberSign  string `json:"member_sign"`
	MessageID   string `json:"message_id"`
}

func UpdateOrderSend(orderSN string) {

	var lianMainOrder []model.LianOrder
	source.DB().Find(&lianMainOrder, "channel_order_id=? and status =?", orderSN, 0)
	if len(lianMainOrder) == 0 {
		err := source.DB().Where("channel_order_id=?", orderSN).Model(model.LianMainOrder{}).UpdateColumn("status", 1).Error
		if err != nil {
			log.Log().Error("lianlian发码后更新主订单失败,", zap.Any("err", err))

		}
	}

}

func UpdateOrderCancelAfterVerification(orderSN string) {

	var lianMainOrder []model.LianOrder
	source.DB().Find(&lianMainOrder, "order_sn=? and channel_status!=?", orderSN, 3)
	if len(lianMainOrder) == 0 {
		err := source.DB().Where("order_sn=?", orderSN).Model(model.LianMainOrder{}).UpdateColumn("channel_status", 3).Error
		if err != nil {
			log.Log().Error("lianlian核销后更新主订单失败,", zap.Any("err", err))

		}
	}

}

func OrderExpress(orderSN string) {
	var l ModelData

	l.InitSetting()
	//var order Order
	//
	//source.DB().Where("third_order_sn=?", orderSN).First(&order)
	//if order.ID <= 0 {
	//	//log.Log().Error("lianlian未查询到商城订单", zap.Any("err", string(orderSN)))
	//
	//	return
	//}

	url := l.Domain + "/ll/channel/order/getExpress"
	var reqData model.RequestDataModel
	var reqMap = make(map[string]interface{})

	reqMap["orderId"] = orderSN
	reqMap["channelId"] = l.ChannelId

	reqJsonMap, _ := json.Marshal(reqMap)

	key, _ := base64.StdEncoding.DecodeString(l.Key)

	dst, err := openssl.AesECBEncrypt(reqJsonMap, key, openssl.PKCS5_PADDING)

	aseData := base64.StdEncoding.EncodeToString(dst)

	reqData.EncryptedData = aseData
	reqData.Timestamp = strconv.Itoa(int(time.Now().Unix() * 1000))
	reqData.ChannelId = l.ChannelId

	signStr := reqData.EncryptedData + reqData.ChannelId + reqData.Timestamp
	sign := utils.MD5V([]byte(signStr))
	reqData.Sign = sign

	reqJsonData, err := json.Marshal(reqData)

	resData := utils.HttpPostJson(reqJsonData, url)

	var resOrder model.ResOrder

	json.Unmarshal(resData, &resOrder)

	src1, _ := base64.StdEncoding.DecodeString(resOrder.Data.EncryptedData)

	decodeData, _ := openssl.AesECBDecrypt(src1, key, openssl.PKCS5_PADDING)

	var orderExpressDetail model.ExpressDetail
	err = json.Unmarshal(decodeData, &orderExpressDetail)
	if err != nil {
		log.Log().Error("lianlian解析物流详情详情失败,", zap.Any("err", string(decodeData)))
		return
	}
}

func CancelAfterVerification(data model.ResJson) (err error) {

	var l ModelData
	l.InitSetting()

	key, _ := base64.StdEncoding.DecodeString(l.Key)

	src1, _ := base64.StdEncoding.DecodeString(data.Data.EncryptedData)

	decodeData, _ := openssl.AesECBDecrypt(src1, key, openssl.PKCS5_PADDING)

	//decodeData = []byte("{\"channelId\":10070,\"channelOrderId\":\"C240104000214010070\",\"thirdOrderId\":\"ZBY2401041718F728F8\",\"orderId\":\"240104000270\",\"corderId\":\"S240104000218\",\"completeDate\":\"2024-01-04 16:44:22\"}")

	log.Log().Info("lianlian接到核销回调数据,", zap.Any("err", string(decodeData)))

	var orderDetail model.CancelAfterVerification
	err = json.Unmarshal(decodeData, &orderDetail)
	if err != nil {
		log.Log().Error("lianlian解析核销详情失败,", zap.Any("err", string(decodeData)))
		return
	}

	log.Log().Info("lianlian 核销回调完整数据", zap.Any("data", orderDetail))
	var lianOrder model.LianOrder
	source.DB().First(&lianOrder, "order_id=?", orderDetail.OrderId)
	if lianOrder.Count <= 0 {
		log.Log().Info("lianlian 当前订单已经核销完毕", zap.Any("data", orderDetail))
		return

	}
	source.DB().Create(&orderDetail)

	log.Log().Info("lianlian 当前核销完后数量", zap.Any("data", lianOrder.Count))

	lianOrder.Count = lianOrder.Count - 1
	log.Log().Info("lianlian 当前核销完后数量1", zap.Any("data", orderDetail.Num))

	if lianOrder.Count == 0 {
		err = source.DB().Where("third_party_order_no=?", orderDetail.ThirdOrderId).Model(model.LianMainOrder{}).UpdateColumn("channel_status", 3).Error
		if err != nil {
			log.Log().Error("lianlian发码后更新主订单失败,", zap.Any("err", err))
		}
	}

	lianOrder.ChannelStatus = "3"
	source.DB().Save(&lianOrder)
	orderSN := orderDetail.ThirdOrderId

	Notify(orderSN, "order.cancel_after_verification")
	//UpdateOrderCancelAfterVerification(orderDetail.ThirdPartyOrderNo)

	return
}

func GetOrderDetail(orderSN string) (err error, orderListDetail model.OrderListItemDetail) {
	var l ModelData
	//InitSetting()

	//id := strings.ReplaceAll(setting.Key, "gatherSupply", "")
	//gatherSupplyID, _ := strconv.Atoi(id)
	//gatherSupplyID := 27

	l.InitSetting()
	var order Order

	source.DB().Where("third_order_sn=?", orderSN).First(&order)
	if order.ID <= 0 {
		//log.Log().Error("lianlian未查询到商城订单", zap.Any("err", string(orderSN)))

		return
	}

	url := l.Domain + "/ll/channel/order/getOrderInfo"
	var reqData model.RequestDataModel
	var reqMap = make(map[string]interface{})

	reqMap["thirdOrderId"] = orderSN
	reqMap["channelId"] = l.ChannelId

	reqJsonMap, _ := json.Marshal(reqMap)

	key, _ := base64.StdEncoding.DecodeString(l.Key)

	dst, err := openssl.AesECBEncrypt(reqJsonMap, key, openssl.PKCS5_PADDING)

	aseData := base64.StdEncoding.EncodeToString(dst)

	reqData.EncryptedData = aseData
	reqData.Timestamp = strconv.Itoa(int(time.Now().Unix() * 1000))
	reqData.ChannelId = l.ChannelId

	signStr := reqData.EncryptedData + reqData.ChannelId + reqData.Timestamp
	sign := utils.MD5V([]byte(signStr))
	reqData.Sign = sign

	reqJsonData, err := json.Marshal(reqData)

	resData := utils.HttpPostJson(reqJsonData, url)

	var resOrder model.ResOrder

	json.Unmarshal(resData, &resOrder)

	src1, _ := base64.StdEncoding.DecodeString(resOrder.Data.EncryptedData)

	decodeData, _ := openssl.AesECBDecrypt(src1, key, openssl.PKCS5_PADDING)

	err = json.Unmarshal(decodeData, &orderListDetail)
	if err != nil {
		log.Log().Error("lianlian解析订单详情失败,", zap.Any("err", string(decodeData)))
		return
	}

	return

}

//
//func GetOrderTongbuDetail(orderSN string) (err error, data interface{}) {
//	err, orderListDetail := GetOrderDetail(orderSN)
//	if err != nil {
//		fmt.Println("错误")
//		return
//	}
//
//	for _, item := range orderListDetail.OrderDetailList {
//		var lianOrder model.LianOrder
//		if item.Status == "1" {
//			lianOrder.Code = item.Code
//			lianOrder.QrCodeUrl = item.QrCodeUrl
//			lianOrder.DetailUrl = item.DetailUrl
//			lianOrder.BookingUrl = item.BookingUrl
//			lianOrder.ChannelStatus = strconv.Itoa(item.ChannelStatus)
//			lianOrder.Status = 1
//			log.Log().Info("lianlian 发码回调", zap.Any("sendcode", item))
//			err = source.DB().Where("order_id=?", item.OrderId).Updates(&lianOrder).Error
//			if err != nil {
//				log.Log().Info("lianlian 发码回调处理失败", zap.Any("err", err))
//			}
//		}
//	}
//
//	UpdateOrderSend(orderSN)
//	UpdateOrderCancelAfterVerification(orderSN)
//
//	return
//}

// 发送POST请求
// url：         请求地址
// data：        POST请求提交的数据
// contentType： 请求体格式，如：application/json
// content：     请求放回的内容
func post(url string, data interface{}, header map[string]string) (error, Resp) {

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	var respon Resp
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	err = json.Unmarshal(result, &respon)
	if respon.Code != 0 && respon.Result != 1 {
		err = errors.New("请求成功，但商城返回值为失败")
	}
	return err, respon
}

type Resp struct {
	Code   int `json:"code"`
	Result int `json:"result"`
}

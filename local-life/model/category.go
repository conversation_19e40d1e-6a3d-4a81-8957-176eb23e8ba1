package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

type LocalLifeCategoryMigration struct {
	source.Model
	Name      string `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"`         //分类名称
	Sort      int    `json:"sort" form:"sort" gorm:"column:sort;default:0;comment:排序;"`                                                   //排序
	Level     int    `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`                 //等级
	Desc      string `json:"desc" form:"desc" gorm:"column:desc;comment:简介;type:varchar(255);size:255;"`                                  //描述
	IsDisplay *int   `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;default:0;type:tinyint;size:1;index;"` //是否显示
	Image     string `json:"image" form:"image" gorm:"column:image;comment:图片;"`                                                          //分类图片
	Icon      string `json:"icon" form:"icon" gorm:"column:icon;comment:icon;"`                                                           //分类图片
	Url       string `json:"url"`
	Source    uint   `json:"source" form:"source" gorm:"column:source"`
	SourceID  uint   `json:"source_id" form:"source_id" gorm:"column:source_id"`
	ParentID  uint   `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"`
	Ratio     uint   `json:"ratio" form:"ratio" gorm:"column:ratio;default:0;comment:平台抽成比例"`
}

func (LocalLifeCategoryMigration) TableName() string {
	return "local_life_categories"
}

type LocalLifeCategory struct {
	source.Model
	Name       string                   `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"`         //分类名称
	Sort       int                      `json:"sort" form:"sort" gorm:"column:sort;default:0;comment:排序;"`                                                   //排序
	Level      int                      `json:"level" validate:"required" form:"level" gorm:"column:level;comment:层级;type:smallint;size:1;"`                 //等级
	Desc       string                   `json:"desc" form:"desc" gorm:"column:desc;comment:简介;type:varchar(255);size:255;"`                                  //描述
	IsDisplay  *int                     `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;default:0;type:tinyint;size:1;index;"` //是否显示
	Image      string                   `json:"image" form:"image" gorm:"column:image;comment:图片;"`                                                          //分类图片
	Icon       string                   `json:"icon" form:"icon" gorm:"column:icon;comment:icon;"`                                                           //分类图片
	Url        string                   `json:"url"`
	Source     uint                     `json:"source" form:"source" gorm:"column:source"`
	SourceID   uint                     `json:"source_id" form:"source_id" gorm:"column:source_id"`
	ParentID   uint                     `json:"parent_id" form:"parent_id" gorm:"column:parent_id;default:0;comment:上级id;index"`
	Ratio      uint                     `json:"ratio" form:"ratio" gorm:"column:ratio;default:0;comment:平台抽成比例"`
	SourceName string                   `json:"source_name" gorm:"-"`
	SupplyInfo LocalLifeSyncSupplyChain `json:"supply_info" gorm:"foreignKey:Source;references:ID"`
}

func (l *LocalLifeCategory) AfterFind(tx *gorm.DB) (err error) {
	if l.Source == 0 {
		l.SourceName = "自营"
	} else {
		l.SourceName = l.SupplyInfo.Name
	}
	return
}

type LocalLifeAllCategory struct {
	source.Model
	Name      string `json:"name" validate:"required" form:"name" gorm:"column:name;comment:名称;type:varchar(255);size:255;index"` //分类名称
	Image     string `json:"image" form:"image" gorm:"column:image;comment:图片;"`
	Icon      string `json:"icon" form:"icon" gorm:"column:icon;comment:icon;"`
	IsDisplay *int   `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;default:0;type:tinyint;size:1;index;"`
}

func (LocalLifeAllCategory) TableName() string {
	return "local_life_categories"
}

type LocalLifeCategoryByRelation struct {
	ID   uint   `json:"id" form:"id"`
	Name string `json:"name" form:"name"`
}

func (LocalLifeCategoryByRelation) TableName() string {
	return "local_life_categories"
}

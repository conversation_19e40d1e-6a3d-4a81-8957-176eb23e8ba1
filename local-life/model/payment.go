package model

import (
	"gorm.io/gorm"
	"time"
	"yz-go/source"
)

type PayStatus int

const (
	NotPay PayStatus = iota
	Paid
	InvalidPay   = -1
	Refunded     = -2
	PartRefunded = -3
)

// LocalLifePayInfo 支付信息
type LocalLifePayInfo struct {
	source.Model
	Status         PayStatus `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"`                    // 状态（0待支付，1已支付，-1已失效，-2已退款）
	PaySN          uint      `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"`                                         // 编号
	Amount         uint      `json:"amount" form:"amount" gorm:"column:amount;comment:支付总金额(分);"`                                   // 支付总金额
	RefundedAmount uint      `json:"refunded_amount" form:"refunded_amount" gorm:"column:refunded_amount;comment:已退款金额(分);"`        // 已退款金额
	UserID         uint      `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`                                    // 用户id
	PayTypeID      int       `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"` // 订单支付方式
	TransactionId  string    `json:"transaction_id" form:"transaction_id" gorm:"column:transaction_id;comment:微信支付订单号;"`            //微信支付订单号

	ExpireAt *source.LocalTime `json:"expire_at"` // 过期时间
	PaidAt   *source.LocalTime `json:"paid_at"`   // 支付时间
	RefundAt *source.LocalTime `json:"refund_at"` // 退款时间
}

func (p *LocalLifePayInfo) AfterCreate(tx *gorm.DB) (err error) {
	p.ExpireAt = &source.LocalTime{Time: time.Now().Add(time.Hour)}

	timestamp := uint(p.CreatedAt.Unix())
	paySn := p.ID + timestamp*110
	err = tx.Model(&p).Update("pay_sn", paySn).Error
	return
}

type UpdateBalance struct {
	Uid           uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	PetSupplierID uint   `json:"pet_supplier_id"`
	Amount        uint   `json:"amount" form:"amount" gorm:"column:amount;comment:金额;type:int;"`
	Type          int    `json:"type" form:"type" gorm:"column:type;comment:余额类型(1汇聚2平台);type:int;"`
	PayType       int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type"`
	OperationType int    `json:"operation_type" form:"operation_type" gorm:"column:operation_type;comment:1采购余额，2结算余额;type:int;"` //1采购余额，2结算余额
	Action        int    `json:"action" form:"action" gorm:"column:action;comment:操作(1增/2减);type:int;"`
	PayInfoID     int    `json:"pay_info_id" form:"pay_info_id"`
	Remarks       string `json:"remarks" form:"remarks"`
	PaySn         uint   `json:"pay_sn" form:"pay_sn"`
}

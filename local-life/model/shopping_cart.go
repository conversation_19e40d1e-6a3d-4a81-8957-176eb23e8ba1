package model

import (
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
)

type LocalLifeShoppingCart struct {
	source.Model
	// 品牌ID
	BrandID           uint   `json:"brand_id" form:"brand_id" gorm:"index"`
	UserID            uint   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;type:bigint;index;"`
	ProductID         uint   `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;type:bigint;index;"`
	ApplicationID     uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;type:bigint;"`
	ApplicationShopID uint   `json:"application_shop_id" form:"application_shop_id" gorm:"column:application_shop_id;comment:应用商城id;type:bigint;"`
	SkuID             uint   `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku id;type:bigint;index;"`
	AddressID         uint   `json:"address_id" form:"address_id" gorm:"column:address_id;comment:address id;type:bigint;"`
	ShippingMethodID  int    `json:"shipping_method_id" form:"shipping_method_id" gorm:"column:shipping_method_id;comment:配送方式id;type:bigint;"`
	BuyID             uint   `json:"buy_id" form:"buy_id" gorm:"column:buy_id;comment:购买id;default:0;index;"`
	Status            *int   `json:"status" form:"status" gorm:"column:status;comment:状态（-1已失效0正常）;default:0;type:smallint;size:1;"`
	Checked           *int   `json:"checked" form:"checked" gorm:"column:checked;comment:选中（1是0否）;default:0;type:tinyint;size:1;"`
	Qty               uint   `json:"qty" form:"qty" gorm:"column:qty;comment:数量;type:int;default:0;"`
	IsExpired         *int   `json:"is_expired" form:"is_expired" gorm:"column:is_expired;comment:是否过期（1是0否）;default:0;type:tinyint;size:1;"` //是否失效 1是0否
	ExpiredMessage    string `json:"expired_message" form:"expired_message" gorm:"column:expired_message;comment:失效原因;type:varchar(255);"`    //失效原因
	ThirdOrderSN      string `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:第三方订单编号;"`
	// 顾客信息设置：1需要手机号2不需要手机号
	NeedMobile int    `json:"need_mobile" form:"need_mobile" gorm:"column:need_mobile;comment:顾客信息设置：1需要手机号2不需要手机号;type:int;size:1;"`
	UserMobile string `json:"user_mobile" form:"user_mobile" gorm:"column:user_mobile;comment:手机号;"`
}

func (sc *LocalLifeShoppingCart) BeforeCreate(tx *gorm.DB) (err error) {
	err = fullData(sc)
	return
}

type Sku struct {
	source.Model
	// 商品ID
	ProductID uint `json:"product_id" form:"product_id" gorm:"index"`
	// 规格名称
	Title string `json:"title" form:"title" gorm:"column:title;comment:规格名称;type:varchar(255);size:255;"`
	// 可使用次数
	Usage uint `json:"usage" form:"usage" gorm:"column:usage;comment:可使用次数;"`
	// 供货价
	Price uint `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`
	// 零售价
	OriginPrice uint `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:零售价(单位:分);"`
	// 成本价
	CostPrice uint `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`
	// 指导价
	GuidePrice uint             `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:指导价(单位:分);"`
	Product    LocalLifeProduct `json:"product" gorm:"foreignKey:ProductID;references:ID"`
}

func (Sku) TableName() string {
	return "local_life_product_skus"
}

func fullData(sc *LocalLifeShoppingCart) (err error) {
	sku := Sku{}
	if sc.SkuID == 0 {
		return errors.New("商品信息不存在")
	}
	err = source.DB().Preload("Product").Where("id = ?", sc.SkuID).First(&sku).Error
	if err != nil {
		return errors.New("商品信息不存在")
	}
	if sku.ProductID == 0 || sku.Product.ID == 0 {
		return errors.New("商品信息不存在")
	}
	sc.ProductID = sku.ProductID
	return
}

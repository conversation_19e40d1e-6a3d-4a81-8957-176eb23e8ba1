package listener

import (
	"go.uber.org/zap"
	"local-life/mq"
	"local-life/mq_product"
	"local-life/mq_store"
	"local-life/service"

	"yz-go/component/log"
)

// PushLocalLifeBrandStatisticByStoreMqHandles 品牌统计-门店
func PushLocalLifeBrandStatisticByStoreMqHandles() {
	mq_store.PushHandles("localLifeBrandStatisticToStoreListeners", func(storeMsg mq_store.StoreMessage) (err error) {
		if storeMsg.MessageType == mq_store.Create {
			err = service.BrandStatisticByStoreMqCreate(storeMsg.BrandID)
			if err != nil {
				log.Log().Error("localLifeBrandStatisticToStoreListeners-Create", zap.Any("err", err), zap.Any("storeMsg", storeMsg))
				return nil
			}
		}
		if storeMsg.MessageType == mq_store.Delete {
			err = service.BrandStatisticByStoreMqDelete(storeMsg.BrandID)
			if err != nil {
				log.Log().Error("localLifeBrandStatisticToStoreListeners-Delete", zap.Any("err", err), zap.Any("storeMsg", storeMsg))
				return nil
			}
			err = service.DeleteVerificationByStoreId(storeMsg.StoreID)
			if err != nil {
				log.Log().Error("DeleteVerificationByStoreId-Delete", zap.Any("err", err), zap.Any("storeMsg", storeMsg))
				return nil
			}
		}
		return nil
	})
}

// PushLocalLifeBrandStatisticByProductMqHandles 品牌统计-商品
func PushLocalLifeBrandStatisticByProductMqHandles() {
	mq_product.PushHandles("localLifeBrandStatisticToProductListeners", 1, func(productMsg mq_product.ProductMessage) (err error) {
		if productMsg.MessageType == mq_product.Create {
			err = service.BrandStatisticByProductMqCreate(productMsg.ProductID)
			if err != nil {
				log.Log().Error("localLifeBrandStatisticToProductListeners-Create", zap.Any("err", err), zap.Any("productMsg", productMsg))
				return nil
			}
		}
		if productMsg.MessageType == mq_product.Delete {
			err = service.BrandStatisticByProductMqDelete(productMsg.ProductID)
			if err != nil {
				log.Log().Error("localLifeBrandStatisticToProductListeners-Delete", zap.Any("err", err), zap.Any("productMsg", productMsg))
				return nil
			}
		}
		return nil
	})
}

// PushLocalLifeBrandStatisticByOrderMqHandles 品牌统计-订单
func PushLocalLifeBrandStatisticByOrderMqHandles() {
	mq.PushHandles("localLifeBrandStatisticToOrderListeners", func(orderMsg mq.OrderMessage) (err error) {
		if orderMsg.MessageType == mq.Created {
			err = service.BrandStatisticByOrderMqCreate(orderMsg.OrderID)
			if err != nil {
				log.Log().Error("localLifeBrandStatisticToOrderListeners-Created", zap.Any("err", err), zap.Any("orderMsg", orderMsg))
				return nil
			}
		}
		if orderMsg.MessageType == mq.PartUse || orderMsg.MessageType == mq.Received {
			err = service.BrandStatisticByOrderMqUse(orderMsg.OrderID)
			if err != nil {
				log.Log().Error("localLifeBrandStatisticToOrderListeners-PartUse-Received", zap.Any("err", err), zap.Any("orderMsg", orderMsg))
				return nil
			}

		}
		return nil
	})
}

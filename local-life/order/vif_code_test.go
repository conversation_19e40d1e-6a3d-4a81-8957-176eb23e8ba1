package order

import (
	"fmt"
	"testing"
)

func TestGenerate(t *testing.T) {
	/*err := Generate(17)
	if err != nil {
		return
	}*/

	/*err := SaveRelations(17)
	if err != nil {
		return
	}*/

	var orderID uint
	orderID = 17111111111111
	// 将订单ID转为字符串
	orderIDStr := fmt.Sprintf("%d", orderID)
	// 固定两位随机字母
	randomLetters := generateRandomLetters(2)
	// 总长度为16，去掉订单ID、-、两位字母后，计算需要的随机数字长度
	maxLength := 20
	randomNumberLength := maxLength - len(orderIDStr) - 1 - len(randomLetters) // 1是减去`-`的长度
	// 根据需要的长度生成随机数
	randomPart := fmt.Sprintf("%0*d", randomNumberLength, randInt(pow(10, randomNumberLength-1), pow(10, randomNumberLength)-1))
	// 拼接订单ID、随机字母、随机数
	code := fmt.Sprintf("%s-%s%s", orderIDStr, randomLetters, randomPart)
	fmt.Println(code)
}

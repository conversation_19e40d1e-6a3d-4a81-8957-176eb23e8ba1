package award

import (
	"errors"
	"go.uber.org/zap"
	"local-life/model"
	"local-life/service"
	"local-life/service/sync"
	"yz-go/component/log"
	"yz-go/source"
)

//门店提成比例=》商品的结算设置
//平台提成比例=》商品的结算设置开启独立，取独立。否则取商品分类的抽成比例
//品牌提成 = 支付金额 - 门店提成 - 平台提成

type LocalLifeOrder struct {
	model.LocalLifeOrder
	OrderItems []model.LocalLifeOrderItem `json:"order_items" gorm:"foreignKey:OrderID"`
}

func SyncSupplyCodeRecord(order LocalLifeOrder, vrfCodes []model.LocalLifeOrderVrfCode) (err error) {
	log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("order", order))
	log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("vrfCodes", vrfCodes))
	// 获取上游核销记录
	var supplyRecords []model.LocalLifeOrderVrfRecord
	err, supplyRecords = sync.GetSupplyOrderCodeRecords(order.GatherSupplyID, order.OrderSN)
	if err != nil {
		log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("err1", err))
		return
	}
	// 获取核销记录中的品牌id和门店id
	var supplyBrandIDs, supplyStoreIDs []uint
	// 上游核销记录map
	var supplyRecordMap = make(map[string]model.LocalLifeOrderVrfRecord)
	for _, record := range supplyRecords {
		supplyBrandIDs = append(supplyBrandIDs, record.BrandID)
		supplyStoreIDs = append(supplyStoreIDs, record.StoreID)
		supplyRecordMap[record.Code] = record
	}
	// 查询本地已导入的品牌和门店，匹配上游对应的本地id
	var brands []model.LocalLifeBrand
	err = source.DB().Model(&model.LocalLifeBrand{}).Where("source = ? and source_id in ?", order.GatherSupplyID, supplyBrandIDs).Find(&brands).Error
	if err != nil {
		log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("err2", err))
		return
	}
	var stores []model.LocalLifeStore
	err = source.DB().Model(&model.LocalLifeStore{}).Where("source = ? and source_id in ?", order.GatherSupplyID, supplyStoreIDs).Find(&stores).Error
	if err != nil {
		log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("err3", err))
		return
	}
	// 保存品牌和门店的映射关系
	var brandMap = make(map[uint]uint)
	for _, brand := range brands {
		brandMap[brand.SourceID] = brand.ID
	}
	var storeMap = make(map[uint]uint)
	for _, store := range stores {
		storeMap[store.SourceID] = store.ID
	}
	var productID uint
	if len(order.OrderItems) > 0 {
		productID = order.OrderItems[0].ProductID
	}
	// 找到本地核销对应的上游核销记录
	var createRecords []model.LocalLifeOrderVrfRecord
	for _, vrfCode := range vrfCodes {
		if record, ok := supplyRecordMap[vrfCode.Code]; ok {
			var bid, sid uint
			if brandID, ok := brandMap[record.BrandID]; ok {
				bid = brandID
			}
			if storeID, ok := storeMap[record.StoreID]; ok {
				sid = storeID
			}
			createRecords = append(createRecords, model.LocalLifeOrderVrfRecord{
				BrandID:       bid,
				OrderID:       order.ID,
				OrderSN:       order.OrderSN,
				PaymentAmount: record.PaymentAmount,
				ProductID:     productID,
				StoreID:       sid,
				CodeID:        vrfCode.ID,
				Code:          record.Code,
				Source:        order.GatherSupplyID,
				SourceID:      record.ID,
			})
		}
	}
	log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("createRecords", createRecords))
	// 保存核销记录
	if len(createRecords) > 0 {
		err = source.DB().Create(&createRecords).Error
		if err != nil {
			log.Log().Error("同步供应链核销记录SyncSupplyCodeRecord", zap.Any("err4", err))
			return
		}
	}
	return
}

// GenerateAward 产生奖励
func GenerateAward(orderID, storeID, count uint, vrfCodeIDs []uint) (err error) {
	// 查询vrfCodes
	var vrfCodes []model.LocalLifeOrderVrfCode
	err = source.DB().Where("id in (?)", vrfCodeIDs).Find(&vrfCodes).Error
	if err != nil || len(vrfCodes) == 0 {
		return errors.New("未找到核销码")
	}
	// 查询订单信息 关联orderItems
	var order LocalLifeOrder
	err = source.DB().Preload("OrderItems").First(&order, orderID).Error
	if err != nil || order.ID == 0 || len(order.OrderItems) == 0 {
		return errors.New("订单不存在或无有效商品")
	}
	// 如果订单是供应链订单，不产生奖励
	if order.GatherSupplyID > 0 {
		err = SyncSupplyCodeRecord(order, vrfCodes)
		if err != nil {
			return err
		}
		return errors.New("供应链订单不产生奖励")
	}
	// 获取订单的结算设置
	var settleSetting model.LocalLifeOrderSettle
	err = source.DB().Where("order_id = ?", orderID).First(&settleSetting).Error
	if err != nil {
		return errors.New("无法获取订单结算设置")
	}
	// 获取门店提成比例
	storeSettleRate := getStoreSettleRate(settleSetting)

	// 获取平台提成比例
	platformRate, err := getPlatformRate(settleSetting, order.OrderItems[0].ProductID)
	if err != nil {
		return err
	}
	// 获取基础设置
	var setting model.Setting
	err, setting = service.GetSetting()
	if err != nil {
		return err
	}
	// 结算期
	settlementPeriod := 0
	if setting.Values.SettlementPeriod > 0 {
		settlementPeriod = setting.Values.SettlementPeriod
	}
	// 结算记录
	var createRecords []model.LocalLifeOrderVrfRecord
	for _, vrfCode := range vrfCodes {
		storeSettleAmount := vrfCode.Amount * storeSettleRate / 10000
		platformAmount := vrfCode.Amount * platformRate / 10000
		var brandSettleAmount uint
		if storeSettleAmount+platformAmount > vrfCode.Amount {
			brandSettleAmount = 0
		} else {
			brandSettleAmount = vrfCode.Amount - storeSettleAmount - platformAmount
		}
		createRecords = append(createRecords, model.LocalLifeOrderVrfRecord{
			BrandID:           vrfCode.BrandID,
			ApplicationID:     order.ApplicationID,
			VUID:              vrfCode.VUID,
			VID:               vrfCode.VID,
			UID:               order.UID,
			OrderID:           orderID,
			OrderSN:           order.OrderSN,
			PaymentAmount:     vrfCode.Amount,
			AwardAmount:       vrfCode.Amount,
			ProductID:         order.OrderItems[0].ProductID,
			StoreID:           storeID,
			CodeID:            vrfCode.ID,
			Code:              vrfCode.Code,
			ProductTitle:      order.OrderItems[0].Title,
			ProductType:       order.OrderItems[0].ProductType,
			SettleStatus:      0,
			StoreSettleRate:   storeSettleRate,
			StoreSettleAmount: storeSettleAmount,
			PlatformRate:      platformRate,
			PlatformAmount:    platformAmount,
			BrandSettleAmount: brandSettleAmount,
			IsSettle:          0,
			SettleDays:        settlementPeriod,
		})
	}
	if len(createRecords) > 0 {
		err = source.DB().Create(&createRecords).Error
		if err != nil {
			return err
		}
	}
	return nil
}

func getStoreSettleRate(settleSetting model.LocalLifeOrderSettle) uint {
	if settleSetting.StoreRate > 0 {
		return settleSetting.StoreRate
	}
	return 0
}

func getPlatformRate(settleSetting model.LocalLifeOrderSettle, productID uint) (uint, error) {
	var platformRate uint
	if settleSetting.Independent == 2 {
		platformRate = settleSetting.Ratio
	} else {
		// 获取商品分类的抽成比例
		var product model.LocalLifeProduct
		err := source.DB().Where("id = ?", productID).First(&product).Error
		if err != nil {
			return 0, err
		}
		var category model.LocalLifeCategory
		err = source.DB().Where("id = ?", product.Category3ID).First(&category).Error
		if err != nil {
			return 0, err
		}
		if category.Ratio > 0 {
			platformRate = category.Ratio
		} else {
			platformRate = 0
		}
	}
	return platformRate, nil
}

// EffectiveAward 奖励生效
func EffectiveAward(orderID uint) (err error) {
	err = source.DB().Model(&model.LocalLifeOrderVrfRecord{}).Where("order_id = ?", orderID).Update("is_settle", 1).Error
	return
}

// InvalidAward 奖励失效
func InvalidAward(orderID uint) (err error) {
	err = source.DB().Model(&model.LocalLifeOrderVrfRecord{}).Where("order_id = ? and is_settle = ?", orderID, 0).Update("settle_status", -1).Error
	return
}

package service

import (
	"fmt"
	"math/rand"
	"strconv"
	"testing"
	"time"
)

func TestGetCertificateTypes(t *testing.T) {
	timestamp := uint(time.Now().Unix())
	id := 10
	for i := 0; i < 5; i++ {
		orderSn := uint(id) + uint(randInt(1000, 9999)) + uint(i) + timestamp*110
		code := generateRandomLetters(2) + strconv.Itoa(int(orderSn))
		fmt.Println(code)
	}
}

func generateRandomLetters(length int) string {
	const letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	result := make([]byte, length)
	for i := 0; i < length; i++ {
		b := make([]byte, 1)
		rand.Read(b)
		result[i] = letters[int(b[0])%len(letters)]
	}
	return string(result)
}

func randInt(min, max int) int {
	rand.Seed(time.Now().UnixNano()) // 每次调用时重新初始化随机数种子
	return min + rand.Intn(max-min)
}

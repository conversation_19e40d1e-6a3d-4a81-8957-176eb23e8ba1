package service

import (
	"errors"
	"gorm.io/gorm"
	"local-life/model"
	"local-life/request"
	"yz-go/source"
)

// CreateImage 创建Store
func CreateImage(info request.ImageSave) (err error) {
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		err = tx.Unscoped().Where("brand_id = ?", info.BrandID).Delete(&model.LocalLifeImage{}).Error
		if err != nil {
			return err
		}
		if len(info.Data) > 0 {

			var newRecommendCount int64

			for k, v := range info.Data {
				info.Data[k].BrandID = info.BrandID
				if v.IsRecommend == 1 {
					newRecommendCount++
				}
			}
			var recommendCount int64
			err, recommendCount = GetImageTagRecommendCount(info.BrandID)
			if err != nil {
				return err
			}
			if recommendCount+newRecommendCount > 6 && newRecommendCount > 0 {
				return errors.New("相册和标签相册中的首页推荐数量不能超过6个")
			}
			err = tx.Create(&info.Data).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	return err
}

// DeleteImage 删除Image
func DeleteImage(category model.LocalLifeImage) (err error) {
	err = source.DB().Delete(&category).Error
	return
}

// DeleteImageByIds 批量删除Image
func DeleteImageByIds(ids []uint) (err error) {
	err = source.DB().Where("id in (?)", ids).Delete(&model.LocalLifeImage{}).Error
	return
}

// UpdateImage 更新Image
//func UpdateImage(category model.Image) (err error) {
//	err = source.DB().Updates(&category).Error
//	return
//}

// FindImage 根据ID获取Image
func FindImage(id uint) (err error, category model.LocalLifeImage) {
	err = source.DB().Where("id = ?", id).First(&category).Error
	return
}

// 根据用户id获取一条门店数据
func FirstImagesByUserId(userId uint) (err error, store model.LocalLifeImage) {
	err = source.DB().Where("UserID = ?", userId).First(&store).Error
	return
}

// GetImageList 获取Image列表
func GetImageList(search request.ImageSearch) (err error, list []model.LocalLifeImage, total int64) {
	db := searchImageGorm(search)

	if err = db.Count(&total).Error; err != nil {
		return
	}

	limit := search.PageSize
	offset := search.PageSize * (search.Page - 1)

	err = db.Limit(limit).Offset(offset).Find(&list).Error

	return
}

// GetImageList 获取Image列表
func GetAppImageList(search request.ImageSearch) (err error, list []model.LocalLifeImage, total int64) {
	db := source.DB().Model(&model.LocalLifeImage{})
	if search.IsRecommend != nil {
		db.Where("`is_recommend` = ?", search.IsRecommend)
	}
	if search.BrandID > 0 {
		db.Where("`brand_id` = ?", search.BrandID)
	}
	if err = db.Count(&total).Error; err != nil {
		return
	}

	limit := search.PageSize
	offset := search.PageSize * (search.Page - 1)

	err = db.Limit(limit).Offset(offset).Find(&list).Error

	return
}

func searchImageGorm(search request.ImageSearch) *gorm.DB {
	db := source.DB().Model(&model.LocalLifeImage{})
	if search.IsRecommend != nil {
		db.Where("`is_recommend` = ?", search.IsRecommend)
	}
	db.Where("`brand_id` = ?", search.BrandID)

	return db
}

// DisplayCategoryByIds 批量上下架
func DisplayImageByIds(ids []uint, isDisplay int) (err error) {
	err = source.DB().Model(&model.LocalLifeImage{}).Where("id in (?)", ids).Update("status", isDisplay).Error
	return
}

package service

import (
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"local-life/model"
	"local-life/service/admin"
	"os"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func BrandSettlement(req model.RequestBrandSearch) (err error, data interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	// 创建db
	var LocalLifeOrderVrfRecord []model.LocalLifeOrderVrfRecord

	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Where("settle_at is  not null")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("settle_at > ? and settle_at < ?", req.TimeStart, req.TimeEnd)
	}

	if req.OrderSN != "" {
		db.Where("order_sn=?", req.OrderSN)
	}

	db.Count(&total)
	db.Where("brand_id=?", req.BrandID)

	db.Limit(limit).Offset(offset).Find(&LocalLifeOrderVrfRecord)

	data = LocalLifeOrderVrfRecord

	return
}

func BrandSettlementExport(adminID uint, info model.RequestBrandSearch) (err error, link string) {

	var total int64
	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Where("settle_at is  not null")
	if info.TimeStart != "" && info.TimeEnd != "" {
		db.Where("settle_at > ? and settle_at < ?", info.TimeStart, info.TimeEnd)
	}
	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)
	}
	db.Where("brand_id=?", info.BrandID)

	db.Count(&total)
	var exportRecord model.LocalLifeOrderExportRecord
	exportRecord.OrderCount = total
	exportRecord.StatusString = "导出中"
	exportRecord.AdminID = adminID
	exportRecord.BrandID = info.BrandID
	err = source.DB().Create(&exportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_settlement"

	f := excelize.NewFile()
	index := f.NewSheet("Sheet1")
	for ei := 1; ei <= int(excelPage); ei++ {
		var LocalLifeOrderVrfRecord []model.LocalLifeOrderVrfRecord
		err = db.
			Order("created_at DESC").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&LocalLifeOrderVrfRecord).Error
		if err != nil {
			return
		}
		//i := 2
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "结算时间")
		f.SetCellValue("Sheet1", "B1", "订单编号")
		f.SetCellValue("Sheet1", "C1", "收入")
		for i, v := range LocalLifeOrderVrfRecord {
			row := i + 2
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(row), v.SettleAt)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(row), strconv.Itoa(int(v.OrderSN)))
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(row), float64(v.AwardAmount)/100)
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "品牌结算导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("BrandSettlementExport：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}
	if excelPage > 1 {
		link = path + "/" + timeString + "订单导出.zip"
		err = admin.Zip(link, links)
	}
	exportRecord.Link = link
	exportRecord.StatusString = "导出完成"
	exportRecord.StartOrderAt = StartOrderAt
	exportRecord.EndOrderAt = EndOrderAt
	err = source.DB().Save(&exportRecord).Error
	return

}
func ProductCountingExport(adminID uint, info model.RequestSearch) (err error, link string) {

	var total int64
	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("sum(platform_amount) as platform_amount ,count(*) as hxcount,max(id) as id,max(product_id) as product_id,product_title,sum(store_settle_amount) as store_settle_amount, sum(brand_settle_amount) as brand_settle_amount")

	if info.TimeStart != "" && info.TimeEnd != "" {
		db.Where("settle_at > ? and settle_at < ?", info.TimeStart, info.TimeEnd)
	}

	if info.ShopId > 0 {
		db.Where("store_id=?", info.ShopId)
	}

	if info.ProductTitle != "" {
		db.Where("product_title like ?", "%"+info.ProductTitle+"%")
	}
	db.Where("brand_id=?", info.BrandID)

	db.Count(&total)
	var exportRecord model.LocalLifeOrderExportRecord
	exportRecord.OrderCount = total
	exportRecord.StatusString = "导出中"
	exportRecord.AdminID = adminID
	exportRecord.BrandID = info.BrandID

	err = source.DB().Create(&exportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_settlement"

	f := excelize.NewFile()
	index := f.NewSheet("Sheet1")
	for ei := 1; ei <= int(excelPage); ei++ {
		var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord
		err = db.Group("product_title").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&LocalLifeOrderVrfRecord).Error

		if err != nil {
			return
		}
		//i := 2
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "商品名称")
		f.SetCellValue("Sheet1", "B1", "商品ID")
		f.SetCellValue("Sheet1", "C1", "核销数量")
		f.SetCellValue("Sheet1", "D1", "品牌收入")
		f.SetCellValue("Sheet1", "E1", "门店收入")
		f.SetCellValue("Sheet1", "F1", "平台抽成")
		for i, v := range LocalLifeOrderVrfRecord {
			row := i + 2
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(row), v.ProductTitle)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(row), v.ProductID)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(row), v.Hxcount)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(row), float64(v.BrandSettleAmount)/100)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(row), float64(v.StoreSettleAmount)/100)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(row), float64(v.PlatformAmount)/100)
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "按商品结算导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("BrandSettlementExport：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}
	if excelPage > 1 {
		link = path + "/" + timeString + "按商品结算导出.zip"
		err = admin.Zip(link, links)
	}
	exportRecord.Link = link
	exportRecord.StatusString = "导出完成"
	exportRecord.StartOrderAt = StartOrderAt
	exportRecord.EndOrderAt = EndOrderAt
	err = source.DB().Save(&exportRecord).Error
	return

}

func StoreCountingExport(adminID uint, info model.RequestSearch) (err error, link string) {

	var total int64
	req := info
	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("local_life_stores.name  as store_name,max(local_life_order_vrf_records.store_id) as store_id,sum(local_life_order_vrf_records.platform_amount) as platform_amount ,count(*) as hxcount,max(local_life_order_vrf_records.id) as id,sum(local_life_order_vrf_records.store_settle_amount) as store_settle_amount, sum(local_life_order_vrf_records.brand_settle_amount) as brand_settle_amount").
		Joins("left join local_life_stores on local_life_stores.id=local_life_order_vrf_records.store_id")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("local_life_order_vrf_records.settle_at > ? and local_life_order_vrf_records.settle_at < ?", req.TimeStart, req.TimeEnd)
	}

	if req.ShopId > 0 {
		db.Where("local_life_order_vrf_records.store_id=?", req.ShopId)
	}

	if req.ProductTitle != "" {
		db.Where("local_life_order_vrf_records.product_title like ?", "%"+req.ProductTitle+"%")
	}
	db.Where("local_life_order_vrf_records.brand_id=?", req.BrandID)

	db.Count(&total)
	var exportRecord model.LocalLifeOrderExportRecord
	exportRecord.OrderCount = total
	exportRecord.StatusString = "导出中"
	exportRecord.AdminID = adminID
	exportRecord.BrandID = info.BrandID

	err = source.DB().Create(&exportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_settlement"

	f := excelize.NewFile()
	index := f.NewSheet("Sheet1")
	for ei := 1; ei <= int(excelPage); ei++ {
		var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord
		err = db.Group("store_id").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&LocalLifeOrderVrfRecord).Error

		if err != nil {
			return
		}
		//i := 2
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "门店名称")
		f.SetCellValue("Sheet1", "B1", "门店ID")
		f.SetCellValue("Sheet1", "C1", "核销数量")
		f.SetCellValue("Sheet1", "D1", "品牌收入")
		f.SetCellValue("Sheet1", "E1", "门店收入")
		f.SetCellValue("Sheet1", "F1", "平台抽成")
		for i, v := range LocalLifeOrderVrfRecord {
			row := i + 2
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(row), v.StoreName)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(row), v.StoreID)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(row), v.Hxcount)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(row), float64(v.BrandSettleAmount)/100)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(row), float64(v.StoreSettleAmount)/100)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(row), float64(v.PlatformAmount)/100)
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "按商品结算导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("BrandSettlementExport：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}
	if excelPage > 1 {
		link = path + "/" + timeString + "按商品结算导出.zip"
		err = admin.Zip(link, links)
	}
	exportRecord.Link = link
	exportRecord.StatusString = "导出完成"
	exportRecord.StartOrderAt = StartOrderAt
	exportRecord.EndOrderAt = EndOrderAt
	err = source.DB().Save(&exportRecord).Error
	return

}
func DateCountingExport(adminID uint, info model.RequestSearch) (err error, link string) {

	var total int64
	req := info
	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("created_at,sum(platform_amount) as platform_amount ,count(*) as hxcount,max(id) as id,sum(store_settle_amount) as store_settle_amount, sum(brand_settle_amount) as brand_settle_amount")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("settle_at > ? and settle_at < ?", req.TimeStart, req.TimeEnd)
	}

	if req.ShopId > 0 {
		db.Where("store_id=?", req.ShopId)
	}

	if req.ProductTitle != "" {
		db.Where("product_title like ?", "%"+req.ProductTitle+"%")
	}
	db.Where("brand_id=?", req.BrandID)

	db.Count(&total)
	var exportRecord model.LocalLifeOrderExportRecord
	exportRecord.OrderCount = total
	exportRecord.StatusString = "导出中"
	exportRecord.AdminID = adminID
	exportRecord.BrandID = info.BrandID

	err = source.DB().Create(&exportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_settlement"

	f := excelize.NewFile()
	index := f.NewSheet("Sheet1")
	for ei := 1; ei <= int(excelPage); ei++ {
		var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord
		err = db.Group("created_at").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&LocalLifeOrderVrfRecord).Error

		if err != nil {
			return
		}
		//i := 2
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "日期")
		f.SetCellValue("Sheet1", "B1", "核销数量")
		f.SetCellValue("Sheet1", "C1", "品牌收入")
		f.SetCellValue("Sheet1", "D1", "门店收入")
		f.SetCellValue("Sheet1", "E1", "平台抽成")
		for i, v := range LocalLifeOrderVrfRecord {
			row := i + 2
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(row), v.CreatedAt)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(row), v.Hxcount)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(row), float64(v.BrandSettleAmount)/100)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(row), float64(v.StoreSettleAmount)/100)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(row), float64(v.PlatformAmount)/100)
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "按商品结算导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("BrandSettlementExport：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}
	if excelPage > 1 {
		link = path + "/" + timeString + "按商品结算导出.zip"
		err = admin.Zip(link, links)
	}
	exportRecord.Link = link
	exportRecord.StatusString = "导出完成"
	exportRecord.StartOrderAt = StartOrderAt
	exportRecord.EndOrderAt = EndOrderAt
	err = source.DB().Save(&exportRecord).Error
	return

}

func ShopSettlementExport(adminID uint, info model.RequestShopSearch) (err error, link string) {

	var total int64

	req := info

	db := source.DB().Select("local_life_order_vrf_records.*,local_life_stores.name as store_name").Model(model.LocalLifeOrderVrfRecord{}).Where("local_life_order_vrf_records.settle_at is  not null").
		Joins("left join local_life_stores on local_life_stores.id=local_life_order_vrf_records.store_id")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("local_life_order_vrf_records.settle_at > ? and local_life_order_vrf_records.settle_at < ?", req.TimeStart, req.TimeEnd)
	}
	if req.OrderSN != "" {
		db.Where("local_life_order_vrf_records.order_sn=?", req.OrderSN)
	}
	if req.ShopID != "" {
		db.Where("local_life_order_vrf_records.store_id=?", req.ShopID)
	}
	if req.ShopName != "" {
		var store model.LocalLifeStore
		source.DB().Where("name like ?", "%"+req.ShopName+"%").First(&store)
		if store.ID > 0 {
			db.Where("local_life_order_vrf_records.store_id=?", store.ID)
		}
	}
	db.Where("local_life_order_vrf_records.brand_id=?", info.BrandID)

	db.Count(&total)
	var exportRecord model.LocalLifeOrderExportRecord
	exportRecord.OrderCount = total
	exportRecord.StatusString = "导出中"
	exportRecord.AdminID = adminID
	exportRecord.BrandID = info.BrandID

	err = source.DB().Create(&exportRecord).Error
	if err != nil {
		return
	}
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	var StartOrderAt, EndOrderAt *source.LocalTime
	path := config.Config().Local.Path + "/export_settlement"

	f := excelize.NewFile()
	index := f.NewSheet("Sheet1")
	for ei := 1; ei <= int(excelPage); ei++ {
		var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord
		err = db.Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&LocalLifeOrderVrfRecord).Error
		if err != nil {
			return
		}
		//i := 2
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "结算时间")
		f.SetCellValue("Sheet1", "B1", "订单编号")
		f.SetCellValue("Sheet1", "C1", "门店名称")
		f.SetCellValue("Sheet1", "D1", "门店ID")
		f.SetCellValue("Sheet1", "E1", "收入")
		for i, v := range LocalLifeOrderVrfRecord {
			row := i + 2
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(row), v.SettleAt)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(row), strconv.Itoa(int(v.OrderSN)))
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(row), v.StoreName)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(row), v.StoreID)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(row), float64(v.AwardAmount)/100)
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "门店结算导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("BrandSettlementExport：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)

	}
	if excelPage > 1 {
		link = path + "/" + timeString + "门店结算导出.zip"
		err = admin.Zip(link, links)
	}
	exportRecord.Link = link
	exportRecord.StatusString = "导出完成"
	exportRecord.StartOrderAt = StartOrderAt
	exportRecord.EndOrderAt = EndOrderAt
	err = source.DB().Save(&exportRecord).Error
	return

}

func ShopSettlement(req model.RequestShopSearch) (err error, data interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	// 创建db
	var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord

	db := source.DB().Select("local_life_order_vrf_records.*,local_life_stores.name as store_name").Model(model.LocalLifeOrderVrfRecord{}).Where("local_life_order_vrf_records.settle_at is  not null").
		Joins("left join local_life_stores on local_life_stores.id=local_life_order_vrf_records.store_id")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("local_life_order_vrf_records.settle_at > ? and local_life_order_vrf_records.settle_at < ?", req.TimeStart, req.TimeEnd)
	}
	if req.OrderSN != "" {
		db.Where("local_life_order_vrf_records.order_sn=?", req.OrderSN)
	}
	if req.ShopID != "" {
		db.Where("local_life_order_vrf_records.store_id=?", req.ShopID)
	}
	if req.ShopName != "" {
		var store model.LocalLifeStore
		source.DB().Where("name like ?", "%"+req.ShopName+"%").First(&store)
		if store.ID > 0 {
			db.Where("local_life_order_vrf_records.store_id=?", store.ID)
		}
	}
	db.Where("local_life_order_vrf_records.brand_id=?", req.BrandID)

	db.Count(&total)
	db.Limit(limit).Offset(offset).Find(&LocalLifeOrderVrfRecord)
	data = LocalLifeOrderVrfRecord
	return
}

func ProductCounting(req model.RequestSearch) (err error, data interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	// 创建db
	var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord

	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("sum(platform_amount) as platform_amount ,count(*) as hxcount,max(id) as id,max(product_id) as product_id,product_title,sum(store_settle_amount) as store_settle_amount, sum(brand_settle_amount) as brand_settle_amount")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("settle_at > ? and settle_at < ?", req.TimeStart, req.TimeEnd)
	}

	if req.ShopId > 0 {
		db.Where("store_id=?", req.ShopId)
	}

	if req.ProductTitle != "" {
		db.Where("product_title like ?", "%"+req.ProductTitle+"%")
	}

	db.Where("brand_id=?", req.BrandID)
	db.Count(&total)

	db.Group("product_title").Limit(limit).Offset(offset).Find(&LocalLifeOrderVrfRecord)

	data = LocalLifeOrderVrfRecord

	return
}

func StoreCounting(req model.RequestSearch) (err error, data interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	// 创建db
	var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord

	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("local_life_stores.name  as store_name,max(local_life_order_vrf_records.store_id) as store_id,sum(local_life_order_vrf_records.platform_amount) as platform_amount ,count(*) as hxcount,max(local_life_order_vrf_records.id) as id,sum(local_life_order_vrf_records.store_settle_amount) as store_settle_amount, sum(local_life_order_vrf_records.brand_settle_amount) as brand_settle_amount").
		Joins("left join local_life_stores on local_life_stores.id=local_life_order_vrf_records.store_id")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("local_life_order_vrf_records.settle_at > ? and local_life_order_vrf_records.settle_at < ?", req.TimeStart, req.TimeEnd)
	}

	if req.ShopId > 0 {
		db.Where("local_life_order_vrf_records.store_id=?", req.ShopId)
	}

	if req.ProductTitle != "" {
		db.Where("local_life_order_vrf_records.product_title like ?", "%"+req.ProductTitle+"%")
	}
	db.Where("local_life_order_vrf_records.brand_id=?", req.BrandID)

	db.Count(&total)
	db.Group("store_id").Limit(limit).Offset(offset).Find(&LocalLifeOrderVrfRecord)

	data = LocalLifeOrderVrfRecord

	return
}

type DateLocalLifeOrderVrfRecord struct {
	model.LocalLifeOrderVrfRecord
	Hxcount   int64  `json:"hxcount"`
	StoreName string `json:"store_name"`
	Name      string `json:"name"`
}

func DateCounting(req model.RequestSearch) (err error, data interface{}, total int64) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	// 创建db
	var LocalLifeOrderVrfRecord []DateLocalLifeOrderVrfRecord

	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("created_at,sum(platform_amount) as platform_amount ,count(*) as hxcount,max(id) as id,sum(store_settle_amount) as store_settle_amount, sum(brand_settle_amount) as brand_settle_amount")

	if req.TimeStart != "" && req.TimeEnd != "" {
		db.Where("settle_at > ? and settle_at < ?", req.TimeStart, req.TimeEnd)
	}

	if req.ShopId > 0 {
		db.Where("store_id=?", req.ShopId)
	}

	if req.ProductTitle != "" {
		db.Where("product_title like ?", "%"+req.ProductTitle+"%")
	}
	db.Where("brand_id=?", req.BrandID)

	db.Count(&total)
	db.Group("created_at").Limit(limit).Offset(offset).Order("id desc").Find(&LocalLifeOrderVrfRecord)

	data = LocalLifeOrderVrfRecord

	return
}

func TotalCounting(req model.RequestSearch) (err error, dataCount map[string]interface{}) {

	db := source.DB().Model(model.LocalLifeOrderVrfRecord{}).Select("sum(platform_amount) as platform_amount ,count(*) as hxcount, sum(store_settle_amount) as store_settle_amount, sum(brand_settle_amount) as brand_settle_amount")
	db.Where("brand_id=?", req.BrandID)

	db.First(&dataCount)

	return
}

func GetPaymentRecord(orderID uint) (err error, payInfo model.LocalLifePayInfo) {
	var order model.LocalLifeOrder
	err = source.DB().Select("id, pay_info_id").Where("id = ?", orderID).First(&order).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	err = source.DB().Where("id = ?", order.PayInfoID).First(&payInfo).Error
	return
}

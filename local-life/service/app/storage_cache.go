package app

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

func GetAppProductIDs(appID uint) (isLoaded bool, productIDs []int) {
	if len(storages) == 0 {
		log.Log().Info("load storages failed", zap.Any("storages len", len(storages)))
		return false, nil
	}
	if cacheLoadAt.Add(2 * time.Minute).Before(time.Now()) {
		log.Log().Info("load storages failed", zap.Any("cacheLoadAt", cacheLoadAt))
		return false, nil
	}

	// 使用 map 来存储产品 ID，避免重复
	productIDMap := make(map[int]struct{})
	for _, s := range storages {
		if s.AppID == appID {
			productIDMap[int(s.ProductID)] = struct{}{}
		}
	}

	// 转换 map 为切片
	for id := range productIDMap {
		productIDs = append(productIDs, id)
	}
	return true, productIDs
}
func CronLoadStorageCache() {
	go func() {
		cacheSyncDeletedAt = time.Now()
		for {
			loadStorages()
			delStoragesWhichDeleted()
			time.Sleep(1 * time.Second)
		}
	}()
}

type storageDeleted struct {
	ID        uint      `json:"id" form:"id" gorm:"primarykey;column:id;comment:主键;autoIncrement;"`        // 主键
	DeletedAt time.Time `json:"deleted_at" form:"deleted_at" gorm:"column:deleted_at;comment:删除时间;index;"` // 删除时间
}

func (storageDeleted) TableName() string {
	return "local_life_product_storages"
}

func delStoragesWhichDeleted() {
	var delStorages []storageDeleted
	err := source.DB().Session(&gorm.Session{Logger: logger.Discard}).
		Model(&storageDeleted{}).
		Where("deleted_at > ?", cacheSyncDeletedAt).
		Order("deleted_at desc").
		Find(&delStorages).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		println(err.Error())
		return
	}

	if len(delStorages) > 0 {
		cacheSyncDeletedAt = delStorages[0].DeletedAt

		// 使用 map 来存储需要删除的 ID
		deleteMap := make(map[uint]struct{})
		for _, s := range delStorages {
			deleteMap[s.ID] = struct{}{}
		}

		// 创建新的切片来存储未删除的记录
		newStorages := make([]storage, 0, len(storages))
		for _, s := range storages {
			if _, exists := deleteMap[s.ID]; !exists {
				newStorages = append(newStorages, s)
			}
		}
		storages = newStorages
	}
}

var storageLastID uint
var cacheLoadAt time.Time
var cacheSyncDeletedAt time.Time
var storages []storage

type storage struct {
	ID        uint `json:"id" form:"id" gorm:"primarykey;column:id;comment:主键;autoIncrement;"`        // 主键
	AppID     uint `json:"app_id" form:"app_id" gorm:"column:app_id;comment:用户id;index;"`             // 用户id
	ProductID uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;index;"` // 产品id
}

func (storage) TableName() string {
	return "local_life_product_storages"
}

func loadStorages() {
	var err error
	var newStorages []storage
	err = source.DB().Session(&gorm.Session{Logger: logger.Discard}).Model(&storage{}).Where("id > ?", storageLastID).Where("deleted_at is null").Find(&newStorages).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		println(err.Error())
	} else {
		storages = append(storages, newStorages...)
		if len(storages) > 0 {
			storageLastID = storages[len(storages)-1].ID
		}
		cacheLoadAt = time.Now()
	}
}

package sync

import (
	"errors"
	"go.uber.org/zap"
	"local-life/connection"
	"local-life/model"
	"local-life/mq_callback"
	"local-life/mqafter"
	"local-life/request"
	"local-life/response"
	adminService "local-life/service/admin"
	"local-life/service/app"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

// 售后同步
func SupplyAfterSale(localLifeAfterSales response.LocalLifeAfterSales, afterSalesMessage mqafter.AfterSalesMessage) (err error) {
	if afterSalesMessage.MessageType == mqafter.AfterSalesCreate || afterSalesMessage.MessageType == mqafter.AfterSalesUpdate || afterSalesMessage.MessageType == mqafter.AfterSalesClose {
		var st AfterSalesSyncSupplyChain
		err, st = InitAfterSale(localLifeAfterSales)
		if err != nil {
			return nil
		}
		var afterSalesId uint
		switch afterSalesMessage.MessageType {
		case mqafter.AfterSalesCreate:
			err, afterSalesId = st.AfterSalesCreate()
			break
		case mqafter.AfterSalesUpdate:
			if localLifeAfterSales.SynAfterSalesId == 0 && localLifeAfterSales.SynAfterSalesIdString == "" {
				err, afterSalesId = st.AfterSalesCreate()
			} else {
				err, afterSalesId = st.AfterSalesUpdate()
			}
			break
		case mqafter.AfterSalesClose:
			err = st.AfterSalesClose()
			break
		}
		if err != nil {
			log.Log().Error("售后申请推送消息失败!", zap.Any("afterSalesMessage", afterSalesMessage), zap.Any("err", err))
		} else {
			err = source.DB().Model(&model.LocalLifeAfterSale{}).Where("id = ?", localLifeAfterSales.ID).Updates(&model.LocalLifeAfterSale{
				SynAfterSalesId: afterSalesId,
			}).Error
			if err != nil {
				log.Log().Error("售后同步成功保存记录失败!", zap.Any("afterSalesMessage", afterSalesMessage), zap.Any("err", err), zap.Any("SynAfterSalesId", afterSalesId))
			}

		}

	}

	return nil
}

type AfterSalesSyncSupplyChain struct {
	LocalLifeSyncSupplyChain
	Token               string                       `json:"token"`
	LocalLifeAfterSales response.LocalLifeAfterSales `json:"localLifeAfterSales"`
}

func InitAfterSale(localLifeAfterSales response.LocalLifeAfterSales) (err error, afterSalesSyncSupplyChain AfterSalesSyncSupplyChain) {
	var chain LocalLifeSyncSupplyChain
	err, chain = GetChainInfo(localLifeAfterSales.GatherSupplyID)
	if err != nil {
		log.Log().Error("获取chain失败"+err.Error(), zap.Any("GatherSupplyID", localLifeAfterSales.SynAfterSalesId))
		return
	}

	// 通过商品来源获取token
	var token string
	err, token = GetChainToken(chain)
	if err != nil {
		log.Log().Error("获取token失败"+err.Error(), zap.Any("chain", chain))
		return
	}
	afterSalesSyncSupplyChain = AfterSalesSyncSupplyChain{
		LocalLifeSyncSupplyChain: chain,
		Token:                    token,
		LocalLifeAfterSales:      localLifeAfterSales,
	}
	return
}

// 申请售后
func (assc AfterSalesSyncSupplyChain) AfterSalesCreate() (err error, AfterSalesId uint) {
	var localLifeOrderDetail app.LocalLifeOrder
	//获取订单和子订单信息
	err, localLifeOrderDetail = OrderDetailByApi(assc.LocalLifeAfterSales.OrderSN, assc.LocalLifeSyncSupplyChain, assc.Token)
	if err != nil {
		return
	}
	var requestData = request.AfterSales{
		OrderID:              localLifeOrderDetail.ID,
		ThirdOrderSN:         strconv.Itoa(int(assc.LocalLifeAfterSales.OrderSN)),
		OrderItemID:          localLifeOrderDetail.OrderItems[0].ID, //因本地生活订单都是只有一个的 所以这里直接获取第一个子订单id
		Amount:               0,
		TechnicalServicesFee: assc.LocalLifeAfterSales.TechnicalServicesFee,
		ReasonType:           assc.LocalLifeAfterSales.ReasonType,
		Reason:               assc.LocalLifeAfterSales.Reason,
		Description:          assc.LocalLifeAfterSales.Description,
		DetailImages:         assc.LocalLifeAfterSales.DetailImages,
		RefundType:           assc.LocalLifeAfterSales.RefundType,
		RefundWay:            assc.LocalLifeAfterSales.RefundWay,
		Num:                  assc.LocalLifeAfterSales.Num,
	}
	err, AfterSalesId = AfterSaleCreateByApi(requestData, assc.LocalLifeSyncSupplyChain, assc.Token)
	if err != nil {
		return
	}

	return
}

// 修改售后
func (assc AfterSalesSyncSupplyChain) AfterSalesUpdate() (err error, AfterSalesId uint) {
	var localLifeOrderDetail app.LocalLifeOrder
	//获取订单和子订单信息
	err, localLifeOrderDetail = OrderDetailByApi(assc.LocalLifeAfterSales.OrderSN, assc.LocalLifeSyncSupplyChain, assc.Token)
	if err != nil {
		return
	}
	var requestData = request.AfterSales{
		Id:                   assc.LocalLifeAfterSales.SynAfterSalesId,
		OrderID:              localLifeOrderDetail.ID,
		ThirdOrderSN:         strconv.Itoa(int(assc.LocalLifeAfterSales.OrderSN)),
		OrderItemID:          localLifeOrderDetail.OrderItems[0].ID, //因本地生活订单都是只有一个的 所以这里直接获取第一个子订单id
		Amount:               0,
		TechnicalServicesFee: assc.LocalLifeAfterSales.TechnicalServicesFee,
		ReasonType:           assc.LocalLifeAfterSales.ReasonType,
		Reason:               assc.LocalLifeAfterSales.Reason,
		Description:          assc.LocalLifeAfterSales.Description,
		DetailImages:         assc.LocalLifeAfterSales.DetailImages,
		RefundType:           assc.LocalLifeAfterSales.RefundType,
		RefundWay:            assc.LocalLifeAfterSales.RefundWay,
		Num:                  assc.LocalLifeAfterSales.Num,
	}
	err, AfterSalesId = AfterSaleSaveByApi(requestData, assc.LocalLifeSyncSupplyChain, assc.Token)
	if err != nil {
		return
	}

	return
}

// 关闭售后
func (assc AfterSalesSyncSupplyChain) AfterSalesClose() (err error) {
	err = AfterSaleCloseByApi(assc.LocalLifeAfterSales.SynAfterSalesId, assc.LocalLifeSyncSupplyChain, assc.Token)
	return
}

// 关闭售后
func (assc AfterSalesSyncSupplyChain) GetAfterSales() (err error, data model.LocalLifeAfterSale) {
	err, data = GetAfterSalesByApi(assc.LocalLifeAfterSales.SynAfterSalesId, assc.LocalLifeSyncSupplyChain, assc.Token)
	return
}

// // 通知上游售后消息已处理
func (assc AfterSalesSyncSupplyChain) AfterMessageSuccess(msg mq_callback.CallBackParam) (err error) {
	err = AfterMessageSuccess(connection.RequestParams{Key: msg.Key, MessageType: msg.MessageType}, assc.LocalLifeSyncSupplyChain, assc.Token)
	return
}

/*
*

	售后消息处理
*/
func SynAfterMessage(msg mq_callback.CallBackParam) (err error) {
	var localLifeAfterSales response.LocalLifeAfterSales
	err = source.DB().Model(&response.LocalLifeAfterSales{}).Where("syn_after_sales_id = ?", msg.Key).First(&localLifeAfterSales).Error
	if err != nil {
		log.Log().Error("本地生活售后不存在!", zap.Any("err", err), zap.Any("msg", msg))
		return nil
	}
	var st AfterSalesSyncSupplyChain
	err, st = InitAfterSale(localLifeAfterSales)
	if err != nil {
		return nil
	}
	err = SynAfterMessageStart(localLifeAfterSales, msg, st)
	if err != nil {
		log.Log().Error("本地生活通知售后消息处理失败", zap.Any("msg", err.Error()))
	}
	errMsg := st.AfterMessageSuccess(msg)
	if errMsg != nil {
		log.Log().Error("本地生活通知售后消息处理完成失败", zap.Any("msg", errMsg.Error()))
	}
	return
}
func SynAfterMessageStart(localLifeAfterSales response.LocalLifeAfterSales, msg mq_callback.CallBackParam, st AfterSalesSyncSupplyChain) (err error) {
	if msg.MessageType == "localLife.loc_after_sales.pass" { //审核通过
		err = adminService.PassAudit(localLifeAfterSales.ID, 0, 0)
		if err != nil {
			log.Log().Error("本地生活售后审核通过失败!", zap.Any("err", err), zap.Any("localLifeAfterSales", localLifeAfterSales))
			err = errors.New("本地生活售后审核通过失败" + err.Error())
			return
		}
	}
	if msg.MessageType == "localLife.loc_after_sales.reject" { //审核驳回

		var afterSalesData model.LocalLifeAfterSale

		err, afterSalesData = st.GetAfterSales()
		if err != nil {
			log.Log().Error("本地生活售后，获取售后详情失败!", zap.Any("err", err), zap.Any("localLifeAfterSales.id", localLifeAfterSales.ID))
			err = errors.New("本地生活售后，获取售后详情失败!" + err.Error())
			return
		}
		err = adminService.Reject(request.RejectRequest{
			Id:    localLifeAfterSales.ID,
			Cause: afterSalesData.Cause,
		}, 0, 0)
		if err != nil {
			log.Log().Error("本地生活售后，审核驳回失败!", zap.Any("err", err), zap.Any("msg", msg))
			err = errors.New("本地生活售后，审核驳回失败!" + err.Error())
			return
		}
	}
	if msg.MessageType == "localLife.loc_after_sales.complete" { //售后完成
		err = adminService.Refund(localLifeAfterSales.ID, 0, 0)
		if err != nil {
			log.Log().Error("本地生活售后，售后完成失败!", zap.Any("err", err), zap.Any("msg", msg))
			err = errors.New("本地生活售后，售后完成失败!" + err.Error())
			return
		}
	}
	if msg.MessageType == "localLife.loc_after_sales.close" { //售后关闭
		err = adminService.Close(localLifeAfterSales.ID, 0, 0, 0, 0)
		if err != nil {
			log.Log().Error("本地生活售后，售后关闭失败!", zap.Any("err", err), zap.Any("msg", msg))
			err = errors.New("本地生活售后，售后关闭失败!" + err.Error())
			return
		}
	}
	return
}

func LocalAfterSalesMessageErrorStart() {
	var localLifeSyncSupplyChains []model.LocalLifeSyncSupplyChain
	var err error
	err = source.DB().Model(&model.LocalLifeSyncSupplyChain{}).Find(&localLifeSyncSupplyChains).Error
	if err != nil {
		return
	}

	if len(localLifeSyncSupplyChains) > 0 {
		for _, item := range localLifeSyncSupplyChains {
			err, chain := GetChainInfo(item.ID)
			if err != nil {
				log.Log().Error("获取chain失败"+err.Error(), zap.Any("GatherSupplyID", item.ID))
				return
			}

			// 通过商品来源获取token
			var token string
			err, token = GetChainToken(chain)
			if err != nil {
				log.Log().Error("获取token失败"+err.Error(), zap.Any("chain", chain))
				return
			}
			err, data := GetMessageError(chain, token)
			if err != nil {
				return
			}
			for _, dataItem := range data {
				_ = mq_callback.PublishMessage(mq_callback.CallBackParam{
					Key:         strconv.Itoa(int(dataItem.AfterSaleId)),
					MessageType: dataItem.AfterSalesMessageType,
				})
			}
		}
	}

}

package route

import (
	"github.com/gin-gonic/gin"
	appv1 "local-life/api/app/v1"
	fv1 "local-life/api/f/v1"
	v1 "local-life/api/v1"
)

// InitAdminPrivateRouter 后端私有路由
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	// 基础设置
	SettingRouter := Router.Group("localLife/setting")
	{
		// 获取Setting
		SettingRouter.GET("find", v1.FindSetting)
		// 更新Setting
		SettingRouter.PUT("update", v1.UpdateSetting)
	}
	// 基础设置
	FinanceRouter := Router.Group("localLife/finance")
	{
		// 统计
		FinanceRouter.POST("productCounting", v1.ProductCounting)
		FinanceRouter.POST("productCountingExport", v1.ProductCountingExport)
		FinanceRouter.POST("storeCounting", v1.StoreCounting)
		FinanceRouter.POST("storeCountingExport", v1.StoreCountingExport)
		FinanceRouter.POST("dateCounting", v1.DateCounting)
		FinanceRouter.POST("dateCountingExport", v1.DateCountingExport)
		FinanceRouter.POST("totalCounting", v1.TotalCounting)

		//品牌结算
		FinanceRouter.POST("brandSettlement", v1.BrandSettlement)
		FinanceRouter.POST("brandSettlementExport", v1.BrandSettlementExport)
		FinanceRouter.POST("shopSettlement", v1.ShopSettlement)
		FinanceRouter.POST("shopSettlementExport", v1.ShopSettlementExport)

	}
	// 分类
	CategoryRouter := Router.Group("localLife/category")
	{
		// 分类上移、下移
		CategoryRouter.POST("move", v1.MoveCategory)
		// 创建分类
		CategoryRouter.POST("create", v1.CreateCategory)
		// 删除分类
		CategoryRouter.DELETE("delete", v1.DeleteCategory)
		// 批量删除分类
		CategoryRouter.DELETE("deleteByIds", v1.DeleteCategoryByIds)
		// 更新分类
		CategoryRouter.PUT("update", v1.UpdateCategory)
		// 获取分类
		CategoryRouter.GET("find", v1.FindCategory)
		// 获取分类列表
		CategoryRouter.GET("list", v1.GetCategoryList)
		// 获取分类子列表
		CategoryRouter.GET("childList", v1.GetCategoryListWithParentId)
		// 批量上下架
		CategoryRouter.POST("displayByIds", v1.DisplayCategoryByIds)
		// 获取当前等级全部分类
		CategoryRouter.GET("all", v1.GetAllCategoryByLevelAndParentId)
	}
	// 品牌
	BrandRouter := Router.Group("localLife/brand")
	{
		// 获取证件类型
		BrandRouter.GET("certificateTypes", v1.GetCertificateTypes)
		// 获取法人证件类型
		BrandRouter.GET("lrCertificateTypes", v1.GetLRCertificateTypes)
		// 获取品牌经营类型
		BrandRouter.GET("types", v1.GetBrandTypes)
		// 品牌列表
		BrandRouter.GET("list", v1.GetBrandList)
		// 更改品牌状态
		BrandRouter.POST("changeStatus", v1.ChangeBrandStatus)
		// 添加品牌
		BrandRouter.POST("add", v1.AddBrand)
		// 获取品牌信息
		BrandRouter.GET("find", v1.FindBrand)
		// 编辑品牌
		BrandRouter.PUT("update", v1.UpdateBrand)
		// 品牌资料变动审核 通过/驳回（驳回理由）
		BrandRouter.POST("infoAudit", v1.BrandInfoAudit)
		// 修改品牌密码
		BrandRouter.POST("updatePassword", v1.UpdateBrandPassword)
	}
	// 品牌入驻申请
	BrandApplyRouter := Router.Group("localLife/brandApply")
	{
		// 获取品牌入驻申请列表
		BrandApplyRouter.GET("list", v1.GetBrandApplyList)
		// 详情
		BrandApplyRouter.GET("find", v1.FindBrandApply)
		// 审核
		BrandApplyRouter.POST("audit", v1.BrandApplyAudit)
	}
	// 商品审核 列表，审核or批量，编辑（获取商品，获取品牌经营分类）
	ProductAuditRouter := Router.Group("localLife/productAudit")
	{
		// 商品审核列表
		ProductAuditRouter.GET("list", v1.GetProductAuditList)
		// 通过 or 批量通过商品审核
		ProductAuditRouter.POST("pass", v1.PassProductAudit)
		// 驳回 or 批量驳回商品审核
		ProductAuditRouter.POST("reject", v1.RejectProductAudit)
		// 获取商品详情
		ProductAuditRouter.GET("info", v1.GetProductInfo)
		// 获取品牌经营分类
		ProductAuditRouter.GET("categories", v1.GetBrandCategories)
		// 修改商品
		ProductAuditRouter.PUT("update", v1.UpdateProduct)
	}

	// 商品管理 列表 批量上架 批量下架 批量删除 批量分类 复制
	ProductRouter := Router.Group("localLife/product")
	{
		// 列表
		ProductRouter.GET("list", v1.GetProductListByAdmin)
		// 批量上架 or 批量下架
		ProductRouter.POST("display", v1.DisplayProductByIDs)
		// 批量删除
		ProductRouter.DELETE("delete", v1.DeleteProduct)
		// 批量分类
		//AdminProductRouter.POST("updateCategory", v1.UpdateCategoryProductByIDs)
		// 复制
		ProductRouter.POST("copy", v1.CopyProduct)
		// 删除所有删选商品
		ProductRouter.DELETE("deleteBySearch", v1.DeleteBySearchByAdmin)
	}

	//门店
	StoreRouter := Router.Group("localLife/store")
	{
		// 删除门店
		StoreRouter.DELETE("delete", v1.DeleteStore)
		// 更新门店
		StoreRouter.PUT("update", v1.UpdateStore)
		// 获取门店
		StoreRouter.GET("find", v1.FindStore)
		// 获取门店列表
		StoreRouter.GET("list", v1.GetStoreList)
		StoreRouter.GET("optionList", v1.GetAdminStoreAllBrandWithoutBrand)
		StoreRouter.POST("displayStoreByIds", v1.DisplayStoreByIds)
		StoreRouter.POST("exportStore", v1.ExportStore)
	}

	//售后--总后台
	AfterSalesRouter := Router.Group("localLife/afterSales")
	{
		// 获取售后列表
		AfterSalesRouter.GET("list", v1.GetAfterSalesListByAdmin)
		//售后详情
		AfterSalesRouter.GET("get", v1.GetAfterSalesById)
		// 审核通过
		AfterSalesRouter.POST("pass", v1.PassAfterSales)
		// 售后退款
		AfterSalesRouter.POST("refund", v1.Refund)
		// 驳回请求
		AfterSalesRouter.POST("reject", v1.RejectAfterSales)
		// 关闭请求
		AfterSalesRouter.POST("close", v1.CloseAfterSales)
		// 测试消息收到推送
		AfterSalesRouter.POST("ceShiAfterSales", v1.CeShiAfterSales)
		// 测试消息收到推送
		AfterSalesRouter.POST("localAfterSalesMessageErrorStart", v1.LocalAfterSalesMessageErrorStart)

	}
	// 订单
	OrderRouter := Router.Group("localLife/order")
	{
		// 获取订单列表 tag：全部订单，待付款，待使用，部分使用，已完成，已关闭
		OrderRouter.GET("list", v1.GetOrderList)
		// 导出订单
		OrderRouter.POST("export", v1.ExportOrder)
		// 订单详情
		OrderRouter.GET("info", v1.GetOrderDetailByAdmin)
		// 商家备注
		OrderRouter.POST("note", v1.OrderNoteByAdmin)
		// 确认付款
		OrderRouter.POST("pay", v1.OrderPay)
		// 确认使用
		OrderRouter.POST("use", v1.OrderUse)
		// 全额退款
		OrderRouter.POST("refund", v1.OrderRefund)
		// 关闭订单
		OrderRouter.POST("close", v1.OrderClose)
		// 获取支付记录
		OrderRouter.GET("getPaymentRecord", v1.GetPaymentRecord)
		// 获取当前订单的适用门店
		OrderRouter.GET("getOrderStore", v1.GetOrderStore)
	}
	// 订单导出记录
	OrderExportRouter := Router.Group("localLife/orderExport")
	{
		// 获取订单导出列表
		OrderExportRouter.GET("list", v1.GetOrderExportList)
		// 删除订单导出列表
		OrderExportRouter.DELETE("delete", v1.DeleteOrderExport)
	}
	// 核销记录
	VerificationRouter := Router.Group("localLife/vrf/record")
	{
		// 获取核销记录列表
		VerificationRouter.GET("list", v1.GetVrfRecordList)
		// 导出核销记录列表
		VerificationRouter.POST("export", v1.ExportVrfRecord)
	}
	// 同步路由 先导入分类，再导入品牌，再导入商，再导入门店品
	SyncRouter := Router.Group("localLife/sync")
	{
		// 同步设置-创建供应链
		SyncRouter.POST("supplyChain/create", v1.CreateSupplyChain)
		// 同步设置-供应链列表
		SyncRouter.GET("supplyChain/list", v1.SupplyChainList)
		// 同步设置-供应链详情（配置信息）
		SyncRouter.GET("supplyChain/find", v1.SupplyChainFind)
		// 同步设置-提交供应链配置
		SyncRouter.POST("supplyChain/submit", v1.SubmitSupplyChain)
		// 同步设置-获取供应链余额
		// 同步设置-获取供应链余额
		SyncRouter.GET("supplyChain/balance", v1.SupplyChainBalance)
		// 同步设置-删除供应链
		SyncRouter.DELETE("supplyChain/delete", v1.SupplyChainDelete)
		// 同步品牌
		SyncRouter.POST("brand/sync", v1.SyncBrand)
		// 同步设置-商品同步
		SyncRouter.POST("supplyChain/product/sync", v1.SyncSupplyChainProduct)
		// 同步所有品牌分类
		SyncRouter.POST("brand/category/sync", v1.SyncBrandCategory)
		// 同步品牌门店
		SyncRouter.POST("brand/store/sync", v1.SyncBrandStore)
	}
}

// InitBrandPrivateRouter 品牌独立后台私有路由 可以考虑使用中间件的方式传递brandID
func InitBrandPrivateRouter(Router *gin.RouterGroup) {
	// 概况
	DashboardRouter := Router.Group("localLife/admin/dashboard")
	{
		// 输码核销
		DashboardRouter.POST("scanCodeVerification", v1.ScanCodeVerification)
		// 在架商品
		DashboardRouter.GET("onSaleProduct", v1.OnSaleProduct)
		// 商品分析
		DashboardRouter.GET("productAnalysis", v1.ProductAnalysis)
		// 数据概览
		DashboardRouter.GET("dataOverview", v1.DataOverview)
		// 商品销量排行
		DashboardRouter.GET("productSalesRanking", v1.ProductSalesRanking)
		// 门店核销排行
		DashboardRouter.GET("storeVerificationRanking", v1.StoreVerificationRanking)
	}
	// 店铺
	AdminShopRouter := Router.Group("localLife/admin/shop")
	{
		// 获取店铺资料信息
		AdminShopRouter.GET("info", v1.GetBrandInfo)
		// 提交资料
		AdminShopRouter.PUT("submit", v1.SubmitBrandInfo)
	}
	// 岗位
	AdminJobRouter := Router.Group("localLife/admin/job")
	{
		// 获取菜单列表
		AdminJobRouter.GET("menus", v1.GetInitJobMenus)
		// 获取职位列表
		AdminJobRouter.GET("list", v1.GetJobList)
		// 获取职位列表 无分页
		AdminJobRouter.GET("listNotPage", v1.GetJobListNotPage)
		// 获取职位详情
		AdminJobRouter.GET("find", v1.FindJob)
		// 创建职位
		AdminJobRouter.POST("create", v1.CreateJob)
		// 更新职位
		AdminJobRouter.PUT("update", v1.UpdateJob)
		// 删除职位
		AdminJobRouter.DELETE("delete", v1.DeleteJob)
	}
	// 成员
	AdminClerkRouter := Router.Group("localLife/admin/clerk")
	{
		// 获取成员列表
		AdminClerkRouter.GET("list", v1.GetClerkList)
		// 获取成员详情
		AdminClerkRouter.GET("find", v1.FindClerk)
		// 创建成员
		AdminClerkRouter.POST("create", v1.CreateClerk)
		// 更新成员
		AdminClerkRouter.PUT("update", v1.UpdateClerk)
		// 删除成员
		AdminClerkRouter.DELETE("delete", v1.DeleteClerk)
	}
	// 经营类目 添加修改商品使用
	AdminCategoryRouter := Router.Group("localLife/admin/category")
	{
		// 获取经营类目列表 无分页 传递级别和父级ID
		AdminCategoryRouter.GET("list", v1.GetCategories)
	}
	// 商品
	AdminProductRouter := Router.Group("localLife/admin/product")
	{
		// 创建商品
		AdminProductRouter.POST("create", v1.CreateProduct)
		// 删除商品 or 批量删除商品
		AdminProductRouter.DELETE("delete", v1.DeleteProduct)
		// 更新商品
		AdminProductRouter.PUT("update", v1.UpdateProduct)
		// 获取商品
		AdminProductRouter.GET("find", v1.FindProduct)
		// 获取商品列表
		AdminProductRouter.GET("list", v1.GetProductList)
		// 单个 or 批量更改上下架状态
		AdminProductRouter.POST("display", v1.DisplayProductByIDs)
		// 删除所有删选商品
		AdminProductRouter.DELETE("deleteBySearch", v1.DeleteBySearch)
		// 复制商品
		AdminProductRouter.POST("copy", v1.CopyProduct)
	}
	//核销员
	VerificationProductRouter := Router.Group("localLife/admin/verification")
	{
		// 创建核销员
		VerificationProductRouter.POST("create", v1.CreateVerification)
		// 删除核销员
		VerificationProductRouter.DELETE("delete", v1.DeleteVerification)
		// 更新核销员
		VerificationProductRouter.PUT("update", v1.UpdateVerification)
		// 获取核销员
		VerificationProductRouter.GET("find", v1.FindVerification)
		// 获取核销员列表
		VerificationProductRouter.GET("list", v1.GetVerificationList)
		// 单个 or 批量更改启用状态
		VerificationProductRouter.POST("statusVerification", v1.StatusVerificationByIDs)

	}
	//门店
	StoreRouter := Router.Group("localLife/admin/store")
	{
		// 创建门店
		StoreRouter.POST("create", v1.CreateStore)
		// 删除门店
		StoreRouter.DELETE("delete", v1.DeleteStore)
		// 更新门店
		StoreRouter.PUT("update", v1.UpdateStore)
		// 获取门店
		StoreRouter.GET("find", v1.FindStore)
		// 获取门店列表
		StoreRouter.GET("list", v1.GetAdminStoreList)
		//获取所有此品牌下面的门店
		StoreRouter.GET("getAdminStoreAll", v1.GetAdminStoreAll)
		StoreRouter.POST("displayStoreByIds", v1.DisplayStoreByIds)
		StoreRouter.POST("exportStore", v1.ExportStore)
		// 导入门店
		StoreRouter.POST("importStore", v1.ImportStore)
		// 获取导入记录列表
		StoreRouter.GET("importRecordList", v1.GetImportRecordList)
		// 获取导入记录详情
		StoreRouter.GET("importRecordInfo", v1.GetImportRecordInfo)
	}
	//相册
	ImageRouter := Router.Group("localLife/admin/images")
	{
		// 创建门店
		ImageRouter.POST("save", v1.CreateImage)
		// 删除门店
		ImageRouter.DELETE("delete", v1.DeleteImage)
		// 更新门店
		//ImageRouter.PUT("update", v1.UpdateImage)
		// 获取门店
		//ImageRouter.GET("find", v1.FindImage)
		// 获取门店列表
		ImageRouter.GET("list", v1.GetImageList)
		ImageRouter.GET("displayImageByIds", v1.DisplayImageByIds)
	}

	//标签相册
	ImageTagRouter := Router.Group("localLife/admin/imageTag")
	{
		// 创建门店
		ImageTagRouter.POST("create", v1.CreateImageTag)
		// 删除门店
		ImageTagRouter.DELETE("delete", v1.DeleteImageTag)
		// 更新门店
		ImageTagRouter.PUT("update", v1.UpdateImageTag)
		// 获取门店
		ImageTagRouter.GET("find", v1.FindImageTag)
		// 获取门店列表
		ImageTagRouter.GET("list", v1.GetImageTagList)
		ImageTagRouter.POST("displayImageTagByIds", v1.DisplayImageTagByIds)
	}
	// 订单
	AdminOrderRouter := Router.Group("localLife/admin/order")
	{
		// 获取订单列表 tag：全部订单，待付款，待使用，部分使用，已完成，已关闭
		AdminOrderRouter.GET("list", v1.GetOrderListByAdmin)
		// 导出订单
		AdminOrderRouter.POST("export", v1.ExportOrderByAdmin)
		// 订单详情
		AdminOrderRouter.GET("info", v1.GetOrderDetailByAdmin)
		// 商家备注
		AdminOrderRouter.POST("note", v1.OrderNoteByAdmin)
		// 获取支付记录
		AdminOrderRouter.GET("getPaymentRecord", v1.GetPaymentRecord)
		// 订单列表返回操作按钮，按钮url接口返回，统一使用总后台接口
		/*// 确认付款
		AdminOrderRouter.POST("pay", v1.OrderPayByAdmin)
		// 确认使用
		AdminOrderRouter.POST("use", v1.OrderUseByAdmin)
		// 全额退款
		AdminOrderRouter.POST("refund", v1.OrderRefundByAdmin)
		// 关闭订单
		AdminOrderRouter.POST("close", v1.OrderCloseByAdmin)*/
	}
	// 订单导出
	AdminOrderExportRouter := Router.Group("localLife/admin/orderExport")
	{
		// 获取订单导出列表
		AdminOrderExportRouter.GET("list", v1.GetOrderExportListByAdmin)
		// 删除
		AdminOrderExportRouter.DELETE("delete", v1.DeleteOrderExportByAdmin)
	}
	// 订单核销明细
	AdminVerificationRouter := Router.Group("localLife/admin/vrf/record")
	{
		// 获取核销明细列表
		AdminVerificationRouter.GET("list", v1.GetVrfRecordListByAdmin)
		// 导出
		AdminVerificationRouter.POST("export", v1.ExportVrfRecordByAdmin)
	}

	//售后--品牌独立后台
	AfterSalesBrandRouter := Router.Group("localLife/Brand/afterSales")
	{
		// 获取售后列表
		AfterSalesBrandRouter.GET("list", v1.GetAfterSalesBrandListByAdmin)
		//售后详情
		AfterSalesBrandRouter.GET("get", v1.GetAfterSalesById)
		// 审核通过
		AfterSalesBrandRouter.POST("pass", v1.PassAfterSalesBrand)
		// 退款
		AfterSalesBrandRouter.POST("refund", v1.RefundBrand)
		// 驳回请求
		AfterSalesBrandRouter.POST("reject", v1.RejectAfterSalesBrand)
		// 关闭请求
		AfterSalesBrandRouter.POST("close", v1.CloseAfterSalesBrand)
	}
}

// InitFrontPrivateRouter 前端私有路由
func InitFrontPrivateRouter(Router *gin.RouterGroup) {
	// 店铺
	FrontRouter := Router.Group("localLife/front")
	{
		//获取本地生活插件是否开启，用户是否有门店，是否是核销员
		FrontRouter.GET("getLocalLifeUse", fv1.GetLocalLifeUse)
		//获取登录用户的所有门店
		FrontRouter.GET("getLocalLifeUseStores", fv1.GetLocalLifeUseStores)
		//获取登录用户的所有核销门店 --核销员
		FrontRouter.GET("getVerificationsByUserId", fv1.GetVerificationsByUserId)
		//统计门店核销订单，未结算收益 已结算收益，待核销订单,支付订单数，退款订单数，在售商品
		FrontRouter.GET("getBrandStatistics", fv1.GetBrandStatistics)
		//输入券码核销   扫码确认核销也使用这个API
		FrontRouter.GET("VerificationsOrderCode", fv1.VerificationsOrderCode)
		//扫码核销详情页面API
		FrontRouter.GET("getScanCodeVerificationDetails", fv1.GetScanCodeVerificationDetails)
		//核销明细列表 -- 门店管理用户
		FrontRouter.GET("getVerificationDetailsList", fv1.GetVerificationDetailsList)
		//核销明细列表 --核销员
		FrontRouter.GET("getVerificationDetailsVerificationList", fv1.GetVerificationDetailsVerificationList)

		//售卖明细
		FrontRouter.GET("getFrontOrderList", fv1.GetFrontOrderList)
		//通过username获取会员信息
		FrontRouter.GET("getUserByUserName", fv1.GetUserByUserName)

	}
	// 核销员--前端API
	FrontVerificationRouter := Router.Group("localLife/front/verification")
	{
		//添加核销员
		FrontVerificationRouter.POST("create", fv1.CreateVerification)
		// 删除核销员
		FrontVerificationRouter.DELETE("delete", fv1.DeleteVerification)
		// 更新核销员
		FrontVerificationRouter.PUT("update", fv1.UpdateVerification)
		// 获取核销员
		FrontVerificationRouter.GET("find", fv1.FindVerification)
		// 获取核销员列表
		FrontVerificationRouter.GET("list", fv1.GetVerificationList)
		// 单个 or 批量更改启用状态
		FrontVerificationRouter.POST("statusVerification", fv1.StatusVerificationByIDs)
	}
	// 经营类目
	FrontCategoryRouter := Router.Group("localLife/front/category")
	{
		// 获取当前等级全部分类
		FrontCategoryRouter.GET("all", fv1.GetAllCategoryByLevelAndParentId)
	}
	// 品牌
	FrontBrandRouter := Router.Group("localLife/front/brand")
	{
		// 验证申请状态
		FrontBrandRouter.GET("verify", fv1.VerifyApplyStatus)
		// 获取证件类型
		FrontBrandRouter.GET("certificateTypes", fv1.GetCertificateTypes)
		// 获取法人证件类型
		FrontBrandRouter.GET("lrCertificateTypes", fv1.GetLRCertificateTypes)
		// 获取品牌经营类型
		FrontBrandRouter.GET("types", fv1.GetBrandTypes)
		// 提交品牌入驻申请
		FrontBrandRouter.POST("apply", fv1.ApplyBrand)
	}
	// 门店管理-商品
	FrontStoreProductRouter := Router.Group("localLife/front/store/product")
	{
		// 获取门店商品列表
		FrontStoreProductRouter.GET("list", fv1.GetStoreProductList)
		// 操作-适用
		FrontStoreProductRouter.POST("visible", fv1.VisibleStoreProduct)
		// 操作-禁用
		FrontStoreProductRouter.POST("invisible", fv1.InvisibleStoreProduct)
	}
}

// InitCallBackRouter 回调路由
func InitCallBackRouter(Router *gin.RouterGroup) {
	// 订单回调
	OrderCallBackRouter := Router.Group("localLife/callback/order")
	{
		// 订单消息推送
		OrderCallBackRouter.POST("notification", fv1.OrderCallBack)
	}
}

// InitAppPrivateRouter 采购端私有路由
func InitAppPrivateRouter(Router *gin.RouterGroup) {
	// 分类
	AppCategoryRouter := Router.Group("localLife/storage/category")
	{
		// 获取当前等级全部分类
		AppCategoryRouter.POST("all", appv1.GetAllCategoryByLevelAndParentId)
		// 获取当前等级全部分类详细信息
		AppCategoryRouter.POST("allBySync", appv1.GetAllCategoryByLevelAndParentIdBySync)
		// 获取指定id的分类详细信息
		AppCategoryRouter.POST("detail", appv1.GetCateDetailByID)
	}
	// 品牌
	AppBrandRouter := Router.Group("localLife/storage/brand")
	{
		// 获取品牌列表
		AppBrandRouter.POST("list", appv1.GetBrandList)
		// 品牌详情
		AppBrandRouter.POST("detail", appv1.GetBrandDetail)
		// 获取证件类型
		AppBrandRouter.POST("certificateTypes", appv1.GetCertificateTypes)
		// 获取法人证件类型
		AppBrandRouter.POST("lrCertificateTypes", appv1.GetLRCertificateTypes)
		// 获取品牌经营类型
		AppBrandRouter.POST("types", appv1.GetBrandTypes)
		// 获取品牌列表用于同步
		AppBrandRouter.POST("listBySync", appv1.GetBrandListBySync)
	}
	// 门店
	AppStoreRouter := Router.Group("localLife/storage/store")
	{
		// 获取门店列表
		AppStoreRouter.POST("list", appv1.GetStoreList)
		// 获取门店详情
		AppStoreRouter.POST("detail", appv1.GetStoreDetail)
		// 获取门店详情 同步
		AppStoreRouter.POST("detailBySync", appv1.GetStoreDetail)
		// 获取指定ids的门店详情
		AppStoreRouter.POST("detailByIds", appv1.GetStoreDetailByIds)
	}

	//相册
	ImageRouter := Router.Group("localLife/storage/images")
	{
		// 创建门店
		ImageRouter.POST("save", appv1.CreateImage)
		// 删除门店
		ImageRouter.DELETE("delete", appv1.DeleteImage)

		// 更新门店
		//ImageRouter.PUT("update", v1.UpdateImage)
		// 获取门店
		//ImageRouter.GET("find", v1.FindImage)
		// 获取门店列表
		ImageRouter.GET("list", appv1.GetImageList)

		ImageRouter.GET("displayImageByIds", appv1.DisplayImageByIds)

	}

	//标签相册
	ImageTagRouter := Router.Group("localLife/storage/imageTag")
	{
		// 创建门店
		ImageTagRouter.POST("create", appv1.CreateImageTag)
		// 删除门店
		ImageTagRouter.DELETE("delete", appv1.DeleteImageTag)

		// 更新门店
		ImageTagRouter.PUT("update", appv1.UpdateImageTag)
		// 获取门店
		ImageTagRouter.GET("find", appv1.FindImageTag)
		// 获取门店列表
		ImageTagRouter.GET("list", appv1.GetImageTagList)

		ImageTagRouter.POST("displayImageTagByIds", appv1.DisplayImageTagByIds)

	}
	// 订单
	AppOrderRouter := Router.Group("localLife/storage/order")
	{
		// 前置校验
		AppOrderRouter.POST("beforeCheck", appv1.BeforeCheck)
		// 下单
		AppOrderRouter.POST("confirm", appv1.Confirm)
		// 订单详情
		AppOrderRouter.POST("detail", appv1.GetOrderDetail)
		// 验证核销码是否有效
		AppOrderRouter.POST("checkCode", appv1.CheckVerificationCode)
		// 获取订单详情（只需要订单id、order_sn和third_order_sn）
		AppOrderRouter.POST("detailBySync", appv1.GetOrderDetailBySync)
		// 获取订单核销码 通过订单编号
		AppOrderRouter.POST("getVrfCodes", appv1.GetOrderVrfCodes)
		// 获取订单核销码 通过订单编号
		AppOrderRouter.POST("getVrfCodesByThirdOrderSN", appv1.GetOrderVrfCodesByThirdOrderSN)
	}
	// 核销记录
	AppVerificationRouter := Router.Group("localLife/storage/vrf/record")
	{
		// 核销记录列表
		AppVerificationRouter.POST("list", appv1.GetVrfRecordList)
		AppVerificationRouter.POST("allBySync", appv1.GetAllVrfRecords)
	}
	// 商品
	AppProductRouter := Router.Group("localLife/storage/product")
	{
		// 添加选品库
		AppProductRouter.POST("addStorage", appv1.AddStorageInfo)
		// 移除选品库
		AppProductRouter.POST("removeStorage", appv1.RemoveStorageInfo)
		// 商品列表
		AppProductRouter.POST("list", appv1.GetProductList)
		// 商品详情
		AppProductRouter.POST("detail", appv1.GetProductDetail)
	}
	// 售后
	AppAfterSaleRouter := Router.Group("localLife/storage/AfterSale")
	{
		// 获取订单支持的售后方式
		AppAfterSaleRouter.GET("getAfterSalesTypeNameMap", appv1.GetAfterSalesTypeNameMap)
		// 获取退款/退货理由
		AppAfterSaleRouter.GET("getReasonList", appv1.GetReasonList)
		//申请售后
		AppAfterSaleRouter.POST("create", appv1.CreateAfterSales)
		// 修改售后
		AppAfterSaleRouter.POST("save", appv1.SaveAfterSales)
		// 获取通过售后id售后详情
		AppAfterSaleRouter.GET("get", appv1.FindAfterSales)
		// 获取通过order_item_id获取售后详情
		AppAfterSaleRouter.GET("getAfterSalesByOrderItemId", appv1.FindAfterSalesByOrderItemId)
		// 取消售后
		AppAfterSaleRouter.POST("close", appv1.Close)
		// 消息成功之后商城通知中台 中台进行改变状态
		AppAfterSaleRouter.POST("messageSuccess", appv1.MessageSuccess)
		// 获取所有状态不是成功的消息
		AppAfterSaleRouter.POST("getMessageError", appv1.GetMessageError)

	}

}

package trade

import (
	applicationLevel "application/level"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"local-life/model"
	stock "local-life/product"
	"sync"
	"user/level"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

type Checkout struct {
	BuyID        uint                    `json:"buy_id"`         // 购买id
	Orders       []LocalLifeOrder        `json:"orders"`         // 订单组
	FailedOrders []LocalLifeOrder        `json:"failed_orders"`  // 失败订单组
	AmountDetail yzResponse.AmountDetail `json:"amount_detail"`  // 金额明细
	GoodsCount   uint                    `json:"goods_count"`    // 商品数量
	Amount       uint                    `json:"amount"`         // 总价
	ThirdOrderSn string                  `json:"third_order_sn"` // 第三方订单号
	Remark       string                  `json:"remark"`         // 买家备注
	Messages     []string                `json:"message"`        // 提示信息
	CanConfirm   bool                    `json:"can_confirm"`    // 可以提交
}

func (c *Checkout) Make() (err error) {
	var count uint
	var productAmount uint
	var fee uint
	var messages []string
	var canConfirm = true
	for _, order := range c.Orders {
		count += order.GoodsTotal
		productAmount += order.ItemAmount
		fee += order.TechnicalServicesFee
		if order.Message != "" {
			messages = append(messages, order.Message)
		}
		canConfirm = canConfirm && order.CanConfirm
	}
	c.GoodsCount = count
	c.CanConfirm = canConfirm
	c.Messages = messages
	c.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "商品总价",
		Amount: int(productAmount),
	})
	c.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "技术服务费",
		Amount: int(fee),
	})
	err = c.AmountDetail.Make()
	if err != nil {
		return
	}
	c.AmountDetail.Title = "总金额"
	c.Amount = uint(c.AmountDetail.Amount)
	return
}

type LocalLifeOrder struct {
	ID                   uint                    `json:"-"`
	BrandID              uint                    `json:"brand_id"`
	Key                  string                  `json:"key"`                    // 标识
	Title                string                  `json:"title"`                  // 标题
	Url                  string                  `json:"url"`                    // 跳转链接
	LogoUrl              string                  `json:"logo_url"`               // logo地址
	Remark               string                  `json:"remark"`                 // 备注
	Amount               uint                    `json:"amount"`                 // 订单总价
	ItemAmount           uint                    `json:"item_amount"`            // 商品金额
	SupplyAmount         uint                    `json:"supply_amount"`          // 商品供货金额
	CostAmount           uint                    `json:"cost_amount"`            // 成本金额
	GoodsTotal           uint                    `json:"goods_total"`            // 商品数量
	TechnicalServicesFee uint                    `json:"technical_services_fee"` // 服务费
	Message              string                  `json:"message"`                // 提示信息
	CanConfirm           bool                    `json:"can_confirm"`            // 可以下单
	Lock                 int                     `json:"lock"`                   // 锁定订单
	OrderItems           OrderItems              `json:"order_items"`            // 订单商品条目
	AmountDetail         yzResponse.AmountDetail `json:"amount_detail"`          // 金额明细
	Application          Application             `json:"application"`            // 应用信息
	User                 User                    `json:"user"`                   // 用户信息
	GatherSupplyID       uint                    `json:"-"`
	ApplicationID        uint                    `json:"-"`
	ApplicationShopID    uint                    `json:"-"`
	UID                  uint                    `json:"-"`
	ErrorMessage         string
	ShoppingCarts        []LocalLifeShoppingCart `json:"shopping_carts" gorm:"-"`                                                             // 购物车信息
	ThirdOrderSN         string                  `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;index;"` // 第三方订单号
	Status               model.OrderStatus       `json:"status" gorm:"column:status;comment:状态;type:tinyint;size:1;default:0;"`               // 状态
	Usage                uint                    `json:"usage"`
	Used                 uint                    `json:"used"`
	UserMobile           string                  `json:"user_mobile"`
	ShopType             int                     `json:"shop_type"`
	OrderType            int                     `json:"order_type"`
}

func (o *LocalLifeOrder) Init(key string, user User, gatherSupplyID uint, application Application, orderItems OrderItems) (err error) {
	o.Key = key
	o.GatherSupplyID = gatherSupplyID
	o.ApplicationID = application.ID
	o.Application = application
	o.UID = user.ID
	o.User = user
	o.OrderItems = orderItems
	o.ApplicationShopID = o.OrderItems.GetApplicationShopID()
	o.GoodsTotal = o.OrderItems.GetGoodsTotal()
	o.BrandID = o.OrderItems.GetBrandID()
	o.Title = fmt.Sprintf("%s等%d件商品", o.OrderItems[0].Title, o.GoodsTotal)
	o.CanConfirm = true
	o.Usage = o.OrderItems.GetUsageTotal()
	o.Used = 0
	if orderItems[0].UserMobile != "" {
		o.UserMobile = orderItems[0].UserMobile
	}
	o.ShopType = orderItems[0].Product.ShopType
	o.OrderType = orderItems[0].Product.ProductType
	// 统计商品总价
	o.ItemAmount = o.OrderItems.GetProductAmount()
	o.SupplyAmount = o.OrderItems.GetSupplyAmount()
	o.CostAmount = o.OrderItems.GetCostAmount()
	o.AmountDetail.AddItem(yzResponse.AmountDetail{
		Title:  "商品总价",
		Amount: int(o.ItemAmount),
	})
	// 服务费
	err, fee, desc := GetTechnicalServicesFee(*o)
	if err != nil {
		return
	}
	o.TechnicalServicesFee = fee
	o.AmountDetail.AddItem(
		yzResponse.AmountDetail{
			Title:  "技术服务费",
			Amount: int(fee),
			Desc:   desc,
		},
	)
	return
}

type ApplicationShop struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:;type:int;size:10;"`
	ShopName      string `json:"shop_name"`
	CallbackLink  string `json:"callback_link"`
	AppSecret     string `json:"app_secret"`
}

type Application struct {
	source.Model
	MemberID   uint `json:"member_id"`
	AppLevelID uint `json:"app_level_id"`
}

func (i Application) TableName() string {
	return "application"
}

func GetFeeRatio(order LocalLifeOrder) (err error, percent int, desc string) {
	err = order.AmountDetail.Make()
	if err != nil {
		return
	}
	if order.Application.AppLevelID != 0 {
		err, percent = applicationLevel.GetLevelDiscountPercent(order.Application.AppLevelID)
		if err != nil {
			return
		}
	} else {
		var feePercent int
		// var enable int
		err, _, feePercent = level.GetLevelTechnicalServicesFeePercent(order.User.LevelID)
		if err != nil {
			return
		}
		if feePercent > 0 {
			percent = feePercent
		}
	}
	if percent < 0 {
		percent = 0
	}
	desc = fmt.Sprintf("服务费比例%d", percent)
	return
}

func GetTechnicalServicesFee(order LocalLifeOrder) (err error, fee uint, desc string) {
	err = order.AmountDetail.Make()
	if err != nil {
		return
	}
	orderAmount := order.AmountDetail.Amount
	percent := 0
	if order.Application.AppLevelID != 0 {
		err, percent = applicationLevel.GetLevelDiscountPercent(order.Application.AppLevelID)
		if err != nil {
			return
		}
	} else {
		var feePercent int
		err, _, feePercent = level.GetLevelTechnicalServicesFeePercent(order.User.LevelID)
		if err != nil {
			return
		}
		if feePercent > 0 {
			percent = feePercent
		}
	}
	if percent < 0 {
		percent = 0
	}
	// percent的单位是万分之一
	fee = uint((orderAmount * percent) / 10000)

	desc = fmt.Sprintf("服务费比例%d", percent)
	return
}

func (o *LocalLifeOrder) Make() (err error) {
	o.AmountDetail.Title = "订单总价"
	err = o.AmountDetail.Make()
	if err != nil {
		return
	}
	o.Amount = uint(o.AmountDetail.Amount)
	return
}

type OrderItems []LocalLifeOrderItem

func (i OrderItems) GetBrandID() (id uint) {
	id = i[0].BrandID
	return
}

func (i OrderItems) GetUsageTotal() (usage uint) {
	for _, orderItem := range i {
		if orderItem.Product.ProductType == 3 {
			if orderItem.Product.Usage == 0 {
				orderItem.Product.Usage = 1
			}
			usage += orderItem.Qty * orderItem.Product.Usage
		} else {
			usage += orderItem.Qty
		}
	}
	return
}

func (i OrderItems) GetApplicationShopID() (id uint) {
	id = i[0].ApplicationShopID
	return
}
func (i OrderItems) GetAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.Amount
	}
	return
}
func (i OrderItems) GetGatherSupplyID() (supplierID uint) {
	supplierID = i[0].GatherSupplyID
	return
}
func (i OrderItems) GetApplication() (application Application) {
	application = i[0].Application
	return
}
func (i OrderItems) GetUser() (user User) {
	user = i[0].User
	return
}

func (i OrderItems) GetFee(percent int) (amount uint) {
	for _, orderItem := range i {
		amount += uint((int(orderItem.PaymentAmount*orderItem.Qty) * percent) / 10000)
	}
	return
}

func (i OrderItems) GetProductAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.Price * orderItem.Qty
	}
	return
}
func (i OrderItems) GetSupplyAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.SupplyAmount
	}
	return
}
func (i OrderItems) GetCostAmount() (amount uint) {
	for _, orderItem := range i {
		amount += orderItem.CostAmount
	}
	return
}
func (i OrderItems) GetGoodsTotal() (qty uint) {
	for _, orderItem := range i {
		qty += orderItem.Qty
	}
	return
}

type LocalLifeOrderItem struct {
	BrandID              uint                    `json:"brand_id"`
	Title                string                  `json:"title"`          // 标题
	SkuTitle             string                  `json:"sku_title"`      // sku标题
	Price                uint                    `json:"price"`          // 成交价
	CostAmount           uint                    `json:"cost_amount"`    // 成本金额
	SupplyAmount         uint                    `json:"supply_amount"`  // sku金额
	PaymentAmount        uint                    `json:"payment_amount"` // 均摊支付金额
	TechnicalServicesFee uint                    `json:"technical_services_fee"`
	Amount               uint                    `json:"amount"`              // 总金额
	Qty                  uint                    `json:"qty"`                 // 数量
	Unit                 string                  `json:"unit"`                // 计量单位
	ImageUrl             string                  `json:"image_url"`           // 图片地址
	GatherSupply         GatherSupply            `json:"gather_supply"`       // 供应链信息
	Options              Options                 `json:"options"`             // 规格信息
	Application          Application             `json:"application"`         // 供应链信息
	ApplicationShopID    uint                    `json:"application_shop_id"` // 供应链信息
	User                 User                    `json:"user"`                //用户信息
	GatherSupplyID       uint                    `json:"gather_supply_id"`
	UID                  uint                    `json:"-"`
	ProductID            uint                    `json:"product_id"`
	SkuID                uint                    `json:"sku_id"`
	OriginalSkuID        uint                    `json:"-"`
	ShoppingCartID       uint                    `json:"shopping_cart_id"`
	AmountDetail         yzResponse.AmountDetail `json:"amount_detail"` // 金额明细
	Product              LocalLifeProduct        `json:"product"`       // 产品信息
	UserMobile           string                  `json:"user_mobile" form:"user_mobile" gorm:"column:user_mobile;comment:手机号;"`
	ProductType          int                     `json:"product_type" form:"product_type" gorm:"column:product_type;comment:商品类型：1团购券2代金券3次卡;type:int;size:1;index;"`
}

func (i LocalLifeOrderItem) Init() (err error) {
	return
}
func (i LocalLifeOrderItem) Make() (err error) {
	err = i.AmountDetail.Make()
	if err != nil {
		return
	}

	return
}

// GetCheckedShoppingCarts
//
// @function: GetCheckedShoppingCarts
// @description: 获取选中的购物车记录
// @return: err error, list interface{}
func GetCheckedShoppingCarts(shoppingCart LocalLifeShoppingCart) (err error, shoppingCartList ShoppingCarts) {
	//排除已删除的商品,避免造成用户无法购买商品
	joinWhere := "INNER join local_life_products on local_life_products.id = local_life_shopping_carts.product_id and local_life_products.deleted_at is null INNER join local_life_product_skus on local_life_product_skus.id = local_life_shopping_carts.sku_id and local_life_product_skus.deleted_at is null"

	db := source.DB().Joins(joinWhere).Preload("Sku").Preload("Product").Preload("User.Application").Preload("Application").
		Where("local_life_shopping_carts.user_id = ?", shoppingCart.UserID).
		Where("local_life_shopping_carts.status = ?", 0).
		Where("local_life_shopping_carts.buy_id = ?", shoppingCart.BuyID).
		Where("local_life_shopping_carts.checked = ?", 1)
	err = db.Find(&shoppingCartList).Error
	if err != nil {
		return
	}
	return
}

// ClearCheckedShoppingCarts
//
// @function: ClearCheckedShoppingCarts
// @description: 清空购物车记录
// @return: err error, list interface{}
func ClearCheckedShoppingCarts(shoppingCart LocalLifeShoppingCart) (err error) {
	err = source.DB().
		Where("user_id = ?", shoppingCart.UserID).
		Where("status = ?", 0).
		Where("buy_id = ?", shoppingCart.BuyID).
		Where("checked = ?", 1).Delete(&LocalLifeShoppingCart{}).Error
	return
}

type CheckoutItem struct {
	CartItem  LocalLifeShoppingCart
	OrderItem LocalLifeOrderItem
}

type LocalLifeShoppingCart struct {
	source.SoftDel
	ID                uint                `json:"id"`
	Qty               uint                `json:"qty"`        // 数量
	SkuID             uint                `json:"sku_id"`     // sku id
	ProductID         uint                `json:"product_id"` // 产品id
	BrandID           uint                `json:"brand_id"`
	AddressID         uint                `json:"address_id"`          // 地址id
	ShippingMethodID  int                 `json:"shipping_method_id"`  // 配送方式id
	ApplicationID     int                 `json:"application_id"`      // 应用id
	ApplicationShopID uint                `json:"application_shop_id"` // 应用id
	UserID            uint                `json:"-"`
	BuyID             uint                `json:"buy_id"`  // 购买id
	Sku               LocalLifeProductSku `json:"sku"`     // sku信息
	Product           LocalLifeProduct    `json:"product"` // 产品信息
	User              User                `json:"user"`    // 会员信息
	Application       Application         `json:"-"`
	Checked           int                 `json:"checked" form:"checked" gorm:"column:checked;comment:选中（1是0否）;default:0;type:tinyint;size:1;"`            //是否选中 1是0否
	IsExpired         int                 `json:"is_expired" form:"is_expired" gorm:"column:is_expired;comment:是否过期（1是0否）;default:0;type:tinyint;size:1;"` //是否失效 1是0否
	ExpiredMessage    string              `json:"expired_message" form:"expired_message" gorm:"column:expired_message;comment:失效原因;type:varchar(255);"`    //失效原因
	ThirdOrderSN      string              `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:第三方订单编号;"`
	// 顾客信息设置：1需要手机号2不需要手机号
	NeedMobile int    `json:"need_mobile" form:"need_mobile" gorm:"column:need_mobile;comment:顾客信息设置：1需要手机号2不需要手机号;type:int;size:1;"`
	UserMobile string `json:"user_mobile" form:"user_mobile" gorm:"column:user_mobile;comment:手机号;"`
}

func (sc LocalLifeShoppingCart) Check() (err error) {
	if sc.Product.IsDisplay == 0 {
		err = errors.New(fmt.Sprintf("商品(%s)已下架", sc.Product.Title))
		return
	}
	err, stockAmount := stock.GetProductStock(sc.Product.ID)
	if stockAmount < sc.Qty {
		err = errors.New(fmt.Sprintf("商品(%s-%s)库存不足%d%s", sc.Product.Title, sc.Sku.Title, sc.Qty, sc.Product.Unit))
		return
	}
	return
}

type ShoppingCarts []LocalLifeShoppingCart

func (scs ShoppingCarts) Check() (error, bool) {
	var err error
	var errs []error
	var errsMsg string
	for _, cs := range scs {
		err = cs.Check()
		if err != nil {
			errs = append(errs, err)
			err = source.DB().Model(&LocalLifeShoppingCart{}).Where("id = ?", cs.ID).Updates(map[string]interface{}{"is_expired": 1, "checked": 0, "expired_message": err.Error()}).Error
			if err != nil {
				log.Log().Error("更新购物车失效失败", zap.Any("cs", cs), zap.Error(err))
			}
		}
	}
	if len(errs) > 0 {
		for _, e := range errs {
			errsMsg += e.Error() + ";"
		}
		return errors.New(errsMsg), false
	}

	return nil, true
}

type LocalLifeProductSku struct {
	source.SoftDel
	ID            uint   `json:"id"`
	Title         string `json:"title"`        // 标题
	Price         uint   `json:"price"`        // 价格
	CostPrice     uint   `json:"cost_price"`   // 成本
	OriginPrice   uint   `json:"origin_price"` // 市场价
	Stock         int    `json:"stock"`        // 库存
	ImageUrl      string `json:"image_url"`    // 图片地址
	ProductID     uint   `json:"-"`
	OriginalSkuID uint   `json:"-"`
}

type Options []Option
type Option struct {
	SpecName     string `json:"spec_name"`      // 规格名
	SpecItemName string `json:"spec_item_name"` // 规格项名
}

func (value Options) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Options) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type User struct {
	ID          uint        `json:"id"`
	LevelID     uint        `json:"level_id"`
	Application Application `json:"application" gorm:"foreignKey:MemberID;references:id"`
}
type LocalLifeProduct struct {
	source.SoftDel
	ID             uint   `json:"id"`
	Title          string `json:"title"`         // 标题
	Category1ID    int    `json:"category_1_id"` // 1级分类id
	Category2ID    int    `json:"category_2_id"` // 2级分类id
	Category3ID    int    `json:"category_3_id"` // 3级分类id
	Sales          int    `json:"sales"`         // 销量
	ImageUrl       string `json:"image_url"`     // 主图地址
	IsDisplay      uint   `json:"-"`             // 1是0否
	Unit           string `json:"unit"`          // 单位
	GatherSupplyID uint   `json:"gather_supply_id"`
	ShopName       string `json:"shop_name"` //阿里店铺名称
	// 商品类型
	ProductType int `json:"product_type"`
	// 可使用次数 类型为次卡时传递，其余类型不传
	Usage    uint `json:"usage"`
	ShopType int  `json:"shop_type"`
	Source   uint `json:"source" form:"source" gorm:"column:source;comment:来源|0自营;"`
}

type GatherSupply struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`      // 名
	ShopLogo   string `json:"shop_logo"` // logo
	Key        string `json:"key" gorm:"column:key"`
	CategoryID uint   `json:"category_id"`
}

func ShoppingCartCheckout(userID uint, shoppingCarts ShoppingCarts) (error, Checkout) {
	var err error
	var checkout Checkout
	if len(shoppingCarts) == 0 {
		err = errors.New("请选择要下单的商品")
	}
	var ok bool
	err, ok = shoppingCarts.Check()
	if !ok {
		return err, checkout
	}
	var items []CheckoutItem
	var item CheckoutItem
	// 将购物车条目转换成订单条目和配送条目
	for _, shoppingCart := range shoppingCarts {
		item.CartItem = shoppingCart
		err, item.OrderItem = GetOrderItemFromShoppingCart(shoppingCart)
		if err != nil {
			return err, checkout
		}
		items = append(items, item)
	}
	// 按照条件分组
	err, itemGroups := GroupItems(items)
	if err != nil {
		return err, checkout
	}

	// 使用 WaitGroup 等待所有 goroutine 完成
	var wg sync.WaitGroup

	// 创建一个 channel 来收集成功的订单，根据 itemGroups 的数量来设置缓冲大小
	successfulOrdersChan := make(chan LocalLifeOrder, len(itemGroups))
	// 创建一个 channel 来收集失败的订单，根据 itemGroups 的数量来设置缓冲大小
	failedOrdersChan := make(chan LocalLifeOrder, len(itemGroups))

	// 每组分别并发结算
	for key, itemGroup := range itemGroups {
		wg.Add(1)
		go func(key string, itemGroup []CheckoutItem) {
			defer wg.Done()

			// 处理每个itemGroup的下单逻辑
			var order LocalLifeOrder
			err, order = GetOrderFromItems(key, itemGroup)
			for _, checkoutItem := range itemGroup {
				order.ShoppingCarts = append(order.ShoppingCarts, checkoutItem.CartItem)
			}
			order.ThirdOrderSN = itemGroup[0].CartItem.ThirdOrderSN
			if err != nil {
				// 尝试发送错误到错误 channel，如果channel已满，则忽略（避免阻塞）
				select {
				case failedOrdersChan <- order:
				}
				return
			}
			// 将成功创建的订单发送到订单 channel
			successfulOrdersChan <- order
		}(key, itemGroup)
	}

	// 创建一个 goroutine 来关闭订单 channel，当所有下单 goroutine 完成时
	go func() {
		wg.Wait()
		close(successfulOrdersChan)
		close(failedOrdersChan)
	}()

	// 从 failedOrdersChan 收集所有失败的订单
	var failedOrders []LocalLifeOrder
	for order := range failedOrdersChan {
		failedOrders = append(failedOrders, order)
	}
	// 如果有失败的订单, 并将所有失败订单的提示信息合并，返回错误
	if len(failedOrders) > 0 {
		var failedShoppingCarts ShoppingCarts
		for i, order := range failedOrders {
			for _, shoppingCart := range order.ShoppingCarts {
				failedShoppingCarts = append(failedShoppingCarts, shoppingCart)
			}
			if i == 0 {
				err = errors.New(order.ErrorMessage)
			} else {
				err = errors.New(err.Error() + ";" + order.ErrorMessage)
			}
		}
		checkout.FailedOrders = failedOrders
		return err, checkout
	}
	// 从 successfulOrdersChan 收集所有订单
	for order := range successfulOrdersChan {
		checkout.Orders = append(checkout.Orders, order)
	}
	if len(checkout.Orders) == 0 {
		err = errors.New("下单失败")
		return err, checkout
	}
	if err != nil {
		return err, checkout
	}
	err = checkout.Make()
	if err != nil {
		return err, checkout
	}
	return nil, checkout
}

func GroupItems(items []CheckoutItem) (err error, itemGroups map[string][]CheckoutItem) {
	itemGroups = map[string][]CheckoutItem{}
	for _, item := range items {
		// 分单key
		key := GetItemKey(item)
		// 根据key分组
		itemGroups[key] = append(itemGroups[key], item)
	}
	return
}

func GetItemKey(item CheckoutItem) string {
	return fmt.Sprintf("%d-%d-%d-%d", item.CartItem.ApplicationID, item.CartItem.ApplicationShopID, item.CartItem.BrandID, item.CartItem.ProductID)
}

func GetOrderItemFromShoppingCart(shoppingCart LocalLifeShoppingCart) (err error, orderItem LocalLifeOrderItem) {
	orderItem.Qty = shoppingCart.Qty
	{
		var levelDiscountPrice uint
		levelDiscountPrice = shoppingCart.Sku.Price
		orderItem.AmountDetail.AddItem(
			yzResponse.AmountDetail{
				Title:  "商品等级优惠",
				Amount: -int((shoppingCart.Sku.Price - levelDiscountPrice) * shoppingCart.Qty),
			},
		)
		orderItem.Price = levelDiscountPrice
	}
	orderItem.CostAmount = shoppingCart.Sku.CostPrice * shoppingCart.Qty
	orderItem.SupplyAmount = shoppingCart.Sku.Price * shoppingCart.Qty
	orderItem.Amount = orderItem.Price * shoppingCart.Qty
	orderItem.PaymentAmount = orderItem.Amount // 暂时与金额相同
	if shoppingCart.Sku.ImageUrl != "" {
		orderItem.ImageUrl = shoppingCart.Sku.ImageUrl
	} else {
		orderItem.ImageUrl = shoppingCart.Product.ImageUrl
	}
	orderItem.Title = shoppingCart.Product.Title
	orderItem.Unit = shoppingCart.Product.Unit
	orderItem.SkuTitle = shoppingCart.Sku.Title
	orderItem.GatherSupplyID = shoppingCart.Product.Source
	orderItem.UID = shoppingCart.UserID
	orderItem.ProductID = shoppingCart.ProductID
	orderItem.SkuID = shoppingCart.SkuID
	orderItem.OriginalSkuID = shoppingCart.Sku.OriginalSkuID
	orderItem.ShoppingCartID = shoppingCart.ID
	orderItem.Application = shoppingCart.User.Application
	orderItem.ApplicationShopID = shoppingCart.ApplicationShopID
	orderItem.User = shoppingCart.User
	orderItem.BrandID = shoppingCart.BrandID
	orderItem.Product = shoppingCart.Product
	orderItem.ProductType = shoppingCart.Product.ProductType
	if shoppingCart.NeedMobile == 1 {
		orderItem.UserMobile = shoppingCart.UserMobile
	}
	// 记录总价
	orderItem.AmountDetail.AddItem(
		yzResponse.AmountDetail{
			Title:  "商品总价",
			Amount: int(orderItem.Amount),
		},
	)

	err = orderItem.Make()
	return
}

func GetOrderFromItems(key string, items []CheckoutItem) (err error, order LocalLifeOrder) {
	// 订单条目
	var orderItems []LocalLifeOrderItem
	for _, item := range items {
		// 订单条目初始化
		err = item.OrderItem.Init()
		if err != nil {
			order.ErrorMessage = order.ErrorMessage + ";" + err.Error()
		}
		orderItems = append(orderItems, item.OrderItem)
	}
	if order.ErrorMessage != "" {
		err = errors.New(order.ErrorMessage)
		return
	}
	// 订单初始化
	err, order = GetOrder(key, orderItems)
	if err != nil {
		order.ErrorMessage = err.Error()
		return
	}

	return
}

func GetOrder(key string, orderItems OrderItems) (err error, order LocalLifeOrder) {
	err = order.Init(key, orderItems.GetUser(), orderItems.GetGatherSupplyID(), orderItems.GetApplication(), orderItems)
	if err != nil {
		return
	}

	err = order.Make()
	if err != nil {
		order.Message = err.Error()
		order.CanConfirm = false
		return
	}
	return
}

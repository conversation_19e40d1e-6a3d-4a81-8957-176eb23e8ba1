package trade

import (
	"github.com/jinzhu/copier"
	"github.com/shopspring/decimal"
	"local-life/model"
	"local-life/order"
)

type Confirm struct {
	Orders []model.Order
}

func ShoppingCartConfirm(checkout Checkout) (err error, confirm Confirm) {
	var orderModels []model.Order
	for _, o := range checkout.Orders {
		orderModel := model.Order{
			LocalLifeOrder: model.LocalLifeOrder{
				Key:                  o.Key,
				Title:                o.Title,
				Amount:               o.Amount,
				ItemAmount:           o.ItemAmount,
				Remark:               o.Remark,
				Status:               0,
				GoodsCount:           o.GoodsTotal,
				GatherSupplyID:       o.GatherSupplyID,
				UID:                  o.UID,
				ThirdOrderSN:         checkout.ThirdOrderSn,
				TechnicalServicesFee: o.TechnicalServicesFee,
				CostAmount:           o.CostAmount,
				SupplyAmount:         o.SupplyAmount,
				ApplicationID:        o.ApplicationID,
				ApplicationShopID:    o.ApplicationShopID,
				Lock:                 o.Lock,
				BrandID:              o.<PERSON>,
				Usage:                o.Usage,
				Used:                 o.Used,
				UserMobile:           o.UserMobile,
				ShopType:             o.ShopType,
				OrderType:            o.OrderType,
			},
			AmountDetail: o.AmountDetail,
		}
		err = copier.CopyWithOption(&orderModel.OrderItems, o.OrderItems, copier.Option{DeepCopy: true})
		if err != nil {
			return
		}
		restTechnicalServicesFee := decimal.NewFromInt(int64(orderModel.TechnicalServicesFee))
		for i, item := range orderModel.OrderItems {
			// 服务费(元)
			var technicalServicesFee decimal.Decimal
			if i+1 == len(orderModel.OrderItems) {
				technicalServicesFee = restTechnicalServicesFee
			} else {
				technicalServicesFee = decimal.NewFromInt(int64(item.Amount*orderModel.TechnicalServicesFee)).DivRound(decimal.NewFromInt(int64(orderModel.ItemAmount)), 0)
				restTechnicalServicesFee = restTechnicalServicesFee.Sub(technicalServicesFee)
			}

			orderModel.OrderItems[i].TechnicalServicesFee = uint(technicalServicesFee.IntPart())
			orderModel.Status = 0
			orderModels = append(orderModels, orderModel)
		}
	}
	err, orders := order.Confirm(orderModels)
	if err != nil {
		return
	}
	confirm.Orders = orders
	return
}

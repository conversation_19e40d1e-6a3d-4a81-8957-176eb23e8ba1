package es

import (
	"context"
	"errors"
	"fmt"
	"github.com/olivere/elastic/v7"
	"gorm.io/gorm"
	"local-life/model"
	"local-life/mq_product"
	"strconv"
	"strings"
	"yz-go/source"
)

// StoreProduct
// where store_type = 1 or (store_type = 2 and elastic.NewMatchQuery("store_id_str", 1)
// where store_type = 1 or (store_type = 2 and elastic.NewMatchQuery("store_id_str", 1) and elastic.NewMatchQuery("visible_id_str", 1)
type StoreProductEs struct {
	// 商品ID
	ID      uint `json:"id"`
	BrandID uint `json:"brand_id"`
	// 商品名称
	Title string `json:"title"`
	// 适用门店类型 1:所有门店 2:指定门店
	StoreType int `json:"store_type" form:"store_type"`
	// 指定门店ID的字符串 空格分隔 "1 2 3 4 5"
	StoreIDStr string `json:"store_id_str" form:"store_id_str"`
	// 门店店铺不可见该商品 "1 2 3" 门店ID1,2,3不可见
	InvisibleIDStr string `json:"invisible_id_str" form:"invisible_id_str"`
}

func createDocument(productID uint, document interface{}) (err error) {
	es, err := source.ES()
	if err != nil {
		return fmt.Errorf("local life create document failed to get ES client: %w", err)
	}
	bulkRequest := es.Bulk().Index("store_product")
	doc := elastic.NewBulkIndexRequest().Id(strconv.Itoa(int(productID))).Doc(document)
	esClient := bulkRequest.Add(doc)
	_, err = esClient.Do(context.Background())
	if err != nil {
		return fmt.Errorf("ES create document operation failed for product %d: %w", productID, err)
	}
	return err
}

// CreateDocument 商品创建消息 创建商品es数据
func CreateDocument(productID uint, messageType mq_product.ProductMessageType) (err error) {
	// 初始
	var productEs StoreProductEs
	err, productEs = getStoreProductEs(productID, messageType)
	// 创建es文档
	err = createDocument(productID, productEs)
	return
}

// UpdateDocument 商品更新消息 更新商品es数据
func UpdateDocument(productID uint, messageType mq_product.ProductMessageType) (err error) {
	es, err := source.ES()
	_, esErr := es.Get().Index("store_product").Id(strconv.Itoa(int(productID))).Do(context.Background())
	// 初始
	var productEs StoreProductEs
	err, productEs = getStoreProductEs(productID, messageType)
	if esErr != nil {
		// 创建es文档
		err = createDocument(productID, productEs)
		if err != nil {
			return
		}
	} else {
		var data map[string]interface{}
		data = getUpdateData(productEs)
		var res *elastic.UpdateResponse
		res, err = es.Update().
			Index("store_product").
			Id(strconv.Itoa(int(productID))).
			Doc(data).
			Do(context.Background())
		if err != nil {
			return
		}
		if res.Result != "updated" {
			err = errors.New(res.Result)
			return
		}
	}
	return
}

// DeleteDocument 商品删除消息 删除商品es数据
func DeleteDocument(productID uint) (err error) {
	es, err := source.ES()
	_, err = es.Delete().
		Index("store_product").
		Id(strconv.Itoa(int(productID))).
		Do(context.Background())
	if err != nil {
		err = errors.New("store_product执行es删除文档操作出错：" + err.Error())
	}
	return
}

func getStoreProductEs(productID uint, messageType mq_product.ProductMessageType) (err error, productEs StoreProductEs) {
	// 初始
	productEs.ID = productID
	// 获取商品数据
	var product model.LocalLifeProduct
	err = source.DB().First(&product, productID).Error
	if err != nil {
		return
	}
	productEs.BrandID = product.BrandID
	productEs.Title = product.Title
	productEs.StoreType = product.ShopType
	// 获取指定门店数据
	if product.ShopType == 2 {
		// 获取关联门店数据
		var stores []model.LocalLifeProductStore
		err = source.DB().Where("product_id = ?", productID).Find(&stores).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
		}
		if err != nil {
			return
		}
		// 获取门店ID
		var storeIDStr []string
		for _, store := range stores {
			storeIDStr = append(storeIDStr, strconv.Itoa(int(store.StoreID)))
		}
		// 门店ID字符串
		productEs.StoreIDStr = strings.Join(storeIDStr, " ")
	}
	if messageType != mq_product.Create && messageType != mq_product.Delete {
		// 获取门店 不可见数据
		var invisibles []model.LocalLifeStoreProductInvisible
		err = source.DB().Where("product_id = ?", productID).Find(&invisibles).Error
		if err != nil {
			return
		}
		// 获取门店ID
		var inVisibleIDStr []string
		for _, invisible := range invisibles {
			inVisibleIDStr = append(inVisibleIDStr, strconv.Itoa(int(invisible.StoreID)))
		}
		// 门店ID字符串
		productEs.InvisibleIDStr = strings.Join(inVisibleIDStr, " ")
	}
	return
}

func getUpdateData(productEs StoreProductEs) (data map[string]interface{}) {
	data = map[string]interface{}{
		"id":               productEs.ID,
		"brand_id":         productEs.BrandID,
		"title":            productEs.Title,
		"store_type":       productEs.StoreType,
		"store_id_str":     productEs.StoreIDStr,
		"invisible_id_str": productEs.InvisibleIDStr,
	}
	return data
}

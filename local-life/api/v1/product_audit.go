package v1

import (
	"github.com/gin-gonic/gin"
	"local-life/request"
	"local-life/service"
	request2 "yz-go/request"
	yzResponse "yz-go/response"
)

func GetProductAuditList(c *gin.Context) {
	var pageInfo request.ProductAuditListParams
	if err := c.Should<PERSON>ind<PERSON>uery(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetProductAuditList(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.<PERSON><PERSON>r(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func PassProductAudit(c *gin.Context) {
	var info request.ProductAuditParams
	if err := c.ShouldBindJSON(&info); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.PassProductAudit(info); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("操作成功", c)
	}
}

func RejectProductAudit(c *gin.Context) {
	var info request.ProductAuditParams
	if err := c.ShouldBindJSON(&info); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.RejectProductAudit(info); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("操作成功", c)
	}
}

func GetProductInfo(c *gin.Context) {
	var req request2.GetById
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, product := service.GetProductInfo(req.Id); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"product": product}, c)
	}
}

func GetBrandCategories(c *gin.Context) {
	var req request.GetBrandCategoriesParams
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, categories := service.GetBrandCategories(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"categories": categories}, c)
	}
}

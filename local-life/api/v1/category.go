package v1

import (
	"github.com/gin-gonic/gin"
	"local-life/model"
	"local-life/request"
	"local-life/service"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func CreateCategory(c *gin.Context) {
	var category model.LocalLifeCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = utils.Verify(category); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.CreateCategory(category); err != nil {
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

func DeleteCategory(c *gin.Context) {
	var category model.LocalLifeCategory
	err := c.Should<PERSON><PERSON>(&category)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteCategory(category); err != nil {
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func DeleteCategoryByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DeleteCategoryByIds(IDS.Ids); err != nil {
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

func UpdateCategory(c *gin.Context) {
	var category model.LocalLifeCategory
	err := c.ShouldBindJSON(&category)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.UpdateCategory(category); err != nil {
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func FindCategory(c *gin.Context) {
	var category model.LocalLifeCategory
	err := c.ShouldBindQuery(&category)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, category = service.FindCategory(category.ID)
	if err != nil {
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(category, c)
	}
}

func GetAllCategoryByLevelAndParentId(c *gin.Context) {
	var pageInfo request.AllCategorySearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, all := service.GetAllCategoryByLevelAndParentId(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(all, c)
	}
}

func GetCategoryList(c *gin.Context) {
	var pageInfo request.CategorySearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCategoryList(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetCategoryListWithParentId(c *gin.Context) {
	var pageInfo request.CategoryChildrenSearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCategoryListWithParentId(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func MoveCategory(c *gin.Context) {
	var params request.MoveParams
	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.MoveCategory(params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
}

func DisplayCategoryByIds(c *gin.Context) {
	var IDS yzRequest.IdsReqWithValue
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.DisplayCategoryByIds(IDS.Ids, IDS.Value); err != nil {
		yzResponse.FailWithMessage("批量操作失败", c)
		return
	} else {
		yzResponse.OkWithMessage("批量操作成功", c)
	}
}

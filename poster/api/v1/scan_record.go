package v1

import (
	"github.com/gin-gonic/gin"
	"poster/request"
	"poster/service"
	yzResponse "yz-go/response"
)

// GetScanRecordList 获取扫码记录列表
func GetScanRecordList(c *gin.Context) {
	var req request.ScanRecordListRequest
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetScanRecordList(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

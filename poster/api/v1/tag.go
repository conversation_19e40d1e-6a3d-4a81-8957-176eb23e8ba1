package v1

import (
	"github.com/gin-gonic/gin"
	"poster/request"
	"poster/service"
	yzResponse "yz-go/response"
)

func GetTagList(c *gin.Context) {
	var req request.TagListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetTagList(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

func AddTag(c *gin.Context) {
	var req request.TagAddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err := service.AddTag(req); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	yzResponse.OkWithMessage("添加成功", c)
}

func UpdateTag(c *gin.Context) {
	var req request.TagAddRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateTag(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("修改成功", c)
}

func DeleteTag(c *gin.Context) {
	var req request.IDRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteTag(req.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

func GetTagDetail(c *gin.Context) {
	var req request.IDRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, tag := service.GetTagDetail(req.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(tag, "获取成功", c)
	}
}

package model

import "yz-go/source"

type HttpResponse struct {
	Code int                `json:"code"`
	Msg  string             `json:"msg"`
	Data []HttpResponseData `json:"data"`
}

type HttpResponseData struct {
	Id                  int     `json:"id"`
	CouponName          string  `json:"couponName"`
	CouponNameType      string  `json:"couponNameType"`
	CouponSuffixName    string  `json:"couponSuffixName"`
	OriginalPrice       float64 `json:"originalPrice"`
	Prices              float64 `json:"prices"`
	Zhekou              float64 `json:"zhekou"`
	ProductNumberDetais string  `json:"product_number_detais"`
	Icon                string  `json:"icon"`
	MD5                 string  `json:"md5"`
}

type UserEquityProduct struct {
	source.Model
	EQID                int    `json:"equity_product_id" gorm:"column:product_id;comment:权益商品Id;"`
	CouponName          string `json:"coupon_name" gorm:"column:coupon_name;comment:产品名称;"`
	CouponNameType      string `json:"coupon_name_type" gorm:"column:coupon_name_type;comment:产品大类;"`
	CouponSuffixName    string `json:"coupon_suffix_name" gorm:"column:coupon_suffix_name;comment:产品后缀;"`
	OriginalPrice       uint   `json:"original_price" gorm:"column:original_price;comment:官方原价;"`
	Prices              uint   `json:"prices" gorm:"column:prices;comment:折扣价（平台供货价）;"`
	Zhekou              uint   `json:"zhekou" gorm:"column:zhekou;comment:折扣;"`
	ProductNumberDetais string `json:"product_number_detais" gorm:"column:product_number_detais;comment:充值账号类型，用户填写账号时需根据账号类型做前端校验，一般为手机号，部分腾讯产品需要填写QQ、微博产品需要填写微博昵称。;"`
	Icon                string `json:"icon" gorm:"column:icon;comment:产品图标;"`
	MD5                 string `json:"md5" gorm:"column:md5;comment:md5;type:varchar(255);"`
	LockStatus          int    `json:"lock_status" gorm:"column:lock_status;comment:锁定状态:0未锁定1已锁定;default:0;"`
}

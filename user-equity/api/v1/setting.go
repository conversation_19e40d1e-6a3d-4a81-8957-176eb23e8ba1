package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"user-equity/model"
	"user-equity/service"
	yzResponse "yz-go/response"
)

func GetSupplyID(c *gin.Context) {
	err, supplyID := service.GetSupplyID()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取供应链id失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"supply_id": supplyID}, c)
}

func FindSetting(c *gin.Context) {
	err, setting := service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取设置失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"setting": setting}, c)
}

func UpdateSetting(c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		return
	}
	err = service.SaveSetting(setting)
	if err != nil {
		yzResponse.FailWithMessage("保存失败", c)
	}
	yzResponse.OkWithMessage("保存成功", c)
}

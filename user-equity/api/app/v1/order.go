package v1

import (
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"math"
	orderModel "order/model"
	orderPay "order/pay"
	paymentModel "payment/model"
	paymentService "payment/service"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"time"
	"trade/checkout"
	"trade/confirm"
	"trade/pay"
	"user-equity/model"
	equity "user-equity/package/order"
	"user-equity/request"
	"user-equity/service"
	"yz-go/cache"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func OrderBeforeCheck(c *gin.Context) {
	var beforeCheckRequest request.OrderBeforeCheckRequest
	err := c.ShouldBindJSON(&beforeCheckRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var res bool
	err, res = service.BeforeCheck(beforeCheckRequest.ProductId, beforeCheckRequest.BuyNum)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(gin.H{
		"result": res,
	}, "获取成功", c)
	return
}

func GetOrderInfo(c *gin.Context) {
	var infoRequest request.YzOrderInfoRequest
	err := c.ShouldBindJSON(&infoRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var shopOrder model.Order
	err, shopOrder = model.GetOrderInfo(infoRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var orderInfo model.UserEquityOrderInfo
	err, orderInfo = model.GetEquityOrderInfo(shopOrder.OrderSN)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(gin.H{
		"result": orderInfo,
	}, "获取成功", c)
	return
}

func RepairOrder(c *gin.Context) {
	var confirmRequest request.YzConfirmRequest
	err := c.ShouldBindJSON(&confirmRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 订单编号查询订单id, 调用Equity.RepairOrder方法
	var orders []orderModel.Order
	err = source.DB().Where("third_order_sn = ?", confirmRequest.OrderSn).Find(&orders).Error
	if err == nil && len(orders) > 0 {
		for _, order := range orders {
			eq := equity.Equity{}
			err = eq.InitSetting(0)
			if err != nil {
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
			err, _ = eq.RepairOrder(order.ID)
			if err != nil {
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
		}
		yzResponse.OkWithMessage("提交成功", c)
		return
	} else {
		yzResponse.FailWithMessage("提交失败,未找到相关订单", c)
		return
	}
}

func EquityOrderInfo(c *gin.Context) {
	var confirmRequest request.YzConfirmRequest
	err := c.ShouldBindJSON(&confirmRequest)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, orders := service.GetEquityOrders(confirmRequest.OrderSn); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(orders, "获取成功", c)
		return
	}
}

func OrderConfirm(c *gin.Context) {
	log.Log().Error("权益下单接口OrderConfirm()")
	var confirmRequest request.YzConfirmRequest
	err := c.ShouldBindJSON(&confirmRequest)
	if err != nil {
		yzResponse.FailWithMessage("参数解析失败", c)
		return
	}
	err, confirmRequest.GatherSupplyID = service.GetSupplyID()
	if err != nil || confirmRequest.GatherSupplyID == 0 {
		yzResponse.FailWithMessage("查询权益供应链id失败", c)
		return
	}
	log.Log().Error("权益下单接口OrderConfirm():参数", zap.Any("request", confirmRequest))
	userID := utils.GetAppUserID(c)
	appID := utils.GetAppID(c)
	appShopID := utils.GetAppShopID(c)
	one := 1
	zero := 0
	var orders []orderModel.Order
	var confirmInfo confirm.Confirm
	err = source.DB().Where("third_order_sn = ?", confirmRequest.OrderSn).Find(&orders).Error
	if confirmRequest.Repair == 1 && err == nil && len(orders) > 0 {
		for _, orderM := range orders {
			eq := equity.Equity{}
			err = eq.InitSetting(0)
			if err != nil {
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
			err, _ = eq.RepairOrder(orderM.ID)
			if err != nil {
				yzResponse.FailWithMessage(err.Error(), c)
				return
			}
			confirmInfo.Orders = append(confirmInfo.Orders, orderM)
		}
		yzResponse.OkWithDetailed(confirmInfo, "操作成功", c)
		return
	}
	if err == nil && len(orders) > 0 {
		//已下单
		for _, orderM := range orders {
			if orderM.Status == 1 {
				yzResponse.FailWithMessage("订单号重复", c)
				return
			}
			confirmInfo.Orders = append(confirmInfo.Orders, orderM)
		}
	} else {
		var skuID uint
		err, skuID = service.GetSkuIDByProductID(confirmRequest.ProductId)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		//未下单
		id, err := cache.GetID("api_buy")
		if err != nil {
			yzResponse.FailWithMessage("buy_id生成失败", c)
			return
		}
		buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
		//插入购物车记录
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:            userID,
			SkuID:             skuID,
			Qty:               uint(confirmRequest.BuyNum),
			Status:            &zero,
			Checked:           &one,
			BuyID:             uint(buyID),
			ApplicationID:     appID,
			ApplicationShopID: appShopID,
			BuyWay:            1,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("权益下单接口OrderConfirm():添加购物车失败", zap.Any("err", err))
			yzResponse.FailWithMessage("添加购物车失败", c)
			return
		}
		// 读取购物车记录
		err, shoppingCarts := checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: 1})
		if err != nil {
			yzResponse.FailWithMessage("读取购物车记录失败", c)
			return
		}
		if len(shoppingCarts) == 0 {
			//  没有购物车记录
			yzResponse.FailWithMessage("请选择要结算的商品", c)
			return
		}
		// 结算信息
		err, checkoutInfo := checkout.ShoppingCartCheckout(userID, shoppingCarts)
		if err != nil {
			yzResponse.FailWithMessage("结算失败", c)
			return
		}
		// 下单
		checkoutInfo.ThirdOrderSn = confirmRequest.OrderSn
		err, confirmInfo = confirm.ShoppingCartConfirm(checkoutInfo)
		if err != nil {
			yzResponse.FailWithMessage("下单失败", c)
			return
		}
		err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: 1})
		if err != nil {
			yzResponse.FailWithMessage("清空购物车失败", c)
			return
		}
	}
	for _, v := range confirmInfo.Orders {
		log.Log().Error("权益下单接口OrderConfirm():订单id", zap.Any("id", v.ID))
		if v.Status > 0 {
			yzResponse.FailWithMessage("订单已支付，无需再次支付!", c)
			return
		} else if v.Status == -1 {
			log.Log().Error("订单已关闭，不能支付!")
			yzResponse.FailWithMessage("订单已关闭，不能支付!", c)
			return
		}
		if v.Status == 0 {
			log.Log().Error("权益下单接口OrderConfirm():订单可以支付")
			err = model.CreateOrderRequest(v.ID, v.OrderSN, confirmRequest)
			if err != nil {
				yzResponse.FailWithMessage("创建CreateOrderRequest失败", c)
				return
			}
			//获取支付信息
			err, payInfo := orderPay.GetPayInfo(v.UserID, []uint{v.ID})
			if err != nil {
				yzResponse.FailWithMessage("获取支付信息失败", c)
				return
			}

			//支付
			if confirmRequest.GatherSupplyID > 0 && collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(16) != false || utils.LocalEnv() == false {
				log.Log().Error("权益下单接口OrderConfirm():支付方式组支付")
				err = pay.SupplyPaidBySort(v, payInfo, confirmRequest.GatherSupplyID)
				if err != nil {
					log.Log().Error("权益下单接口OrderConfirm():支付方式组支付错误", zap.Any("err", err))
					yzResponse.FailWithMessage("支付方式组支付错误", c)
					return
				}
			} else {
				log.Log().Error("权益下单接口OrderConfirm():站内余额支付")
				var payTypeSort []paymentModel.ApplicationPaySort
				err, payTypeSort = paymentService.GetPaySort(appID)
				if err != nil {
					log.Log().Error("权益下单接口OrderConfirm():支付方式获取失败", zap.Any("err", err))
					yzResponse.FailWithMessage("支付方式获取失败", c)
					return
				}
				err = pay.PaidBySort(v, payInfo, payTypeSort)
				if err != nil {
					log.Log().Error("福禄下单接口OrderConfirm():站内余额支付支付错误", zap.Any("err", err))
					yzResponse.FailWithMessage("站内余额支付支付错误", c)
					return
				}
			}
		}
	}
	yzResponse.OkWithDetailed(confirmInfo, "操作成功", c)
	return
}

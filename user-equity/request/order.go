package request

import yzRequest "yz-go/request"

type OrderBeforeCheckRequest struct {
	ProductId int `json:"product_id" form:"product_id" query:"product_id"`
	BuyNum    int `json:"buy_num" form:"buy_num" query:"buy_num"`
}

type YzConfirmRequest struct {
	OrderSn string `json:"order_sn" form:"order_sn" query:"order_sn"`
	// 商品编号
	ProductId int `json:"product_id" form:"product_id" query:"product_id"`
	// 商品规格id
	SkuID int `json:"sku_id" form:"sku_id" query:"sku_id"`
	// 外部订单号
	CustomerOrderNo string `json:"customer_order_no" form:"customer_order_no" query:"customer_order_no"`
	// 充值账号
	ChargeAccount string `json:"charge_account" form:"charge_account" query:"charge_account"`
	// 购买数量
	BuyNum int `json:"buy_num" form:"buy_num" query:"buy_num"`
	// 供应链id
	GatherSupplyID uint `json:"gather_supply_id" form:"gather_supply_id" query:"gather_supply_id"`
	// 重新提交订单
	Repair int `json:"repair" form:"gather_supply_id" query:"gather_supply_id"`
}

type YzOrderInfoRequest struct {
	OrderSn string `json:"order_sn" form:"order_sn" query:"order_sn"`
}

type OrderAdminSearch struct {
	Status        *int   `json:"status" form:"status"`
	RefundStatus  *int   `json:"refund_status" form:"refund_status"`
	StartAT       string `json:"start_at" form:"start_at"`
	EndAT         string `json:"end_at" form:"end_at"`
	OrderSN       string `json:"order_sn" form:"order_sn"`
	ThirdOrderSN  string `json:"third_order_sn" form:"third_order_sn"`
	EquityOrderSN string `json:"equity_order_sn" form:"equity_order_sn"`
	PaySN         string `json:"pay_sn" form:"pay_sn"`
	ShippingSN    string `json:"shipping_sn" form:"shipping_sn"`
	PayTypeID     int    `json:"pay_type_id" form:"pay_type_id"`
	UserID        uint   `json:"user_id" form:"user_id"`
	NickName      string `json:"nick_name" form:"nick_name"`
	UserName      string `json:"user_name" form:"user_name"`
	UserMobile    string `json:"user_mobile" form:"user_mobile"`
	ApplicationID uint   `json:"application_id" form:"application_id"`
	ProductTitle  string `json:"product_title" form:"product_title"`
	ChargeAccount string `json:"charge_account" form:"charge_account"`
	yzRequest.PageInfo
}

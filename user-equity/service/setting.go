package service

import (
	"user-equity/model"
	"yz-go/source"
)

func GetSetting() (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", "user_equity_setting").First(&setting).Error
	return
}

func SaveSetting(setting model.Setting) (err error) {
	setting.Key = "user_equity_setting"
	if setting.ID != 0 {
		err = source.DB().Updates(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	return err
}

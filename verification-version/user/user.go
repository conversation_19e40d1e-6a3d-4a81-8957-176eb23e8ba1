package user

import (
	"version/version"
)

const (
	Flagship     = "flagship"     //旗舰版
	Manufacturer = "manufacturer" //厂家班
	Union        = "union"        //联盟版
)

func GetUsers() map[string]interface{} {
	Version := version.GetVersion()
	userMap := make(map[string]interface{})

	userMap["*************"] = Version[Flagship]     //演示站
	userMap["***************"] = Version[Flagship]   //海诚
	userMap["**************"] = Version[Flagship]    //海宏科技
	userMap["************"] = Version[Flagship]      //杭州微度
	userMap["*************"] = Version[Flagship]     //百纳
	userMap["*************"] = Version[Flagship]     //乐霸
	userMap["**************"] = Version[Flagship]    //淘逛
	userMap["**************"] = Version[Flagship]    //七件事
	userMap["**************"] = Version[Flagship]    //汇杰
	userMap["*************"] = Version[Flagship]     //代理演示站
	userMap["*************"] = Version[Flagship]     //yx 芸信
	userMap["*************"] = Version[Manufacturer] //微米网络
	userMap["***********"] = Version[Flagship]       //海诚演示站
	userMap["**************"] = Version[Flagship]    //微米网络
	userMap["**************"] = Version[Flagship]    //微米网络
	userMap["*************"] = Version[Flagship]     //中联云
	userMap["*************"] = Version[Union]        //七件事
	userMap["**************"] = Version[Flagship]    //七件事演示
	userMap["**************"] = Version[Union]       //七件事-门口电商
	userMap["*************"] = Version[Flagship]     //汇杰测试服务器
	//fmt.Println("七件事-门口电商", userMap["**************"])
	return userMap
}

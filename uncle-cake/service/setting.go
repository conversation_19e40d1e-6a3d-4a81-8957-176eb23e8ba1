package service

import (
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"uncle-cake/model"
	model2 "yz-go/model"
	"yz-go/source"
)

func SetSetting(setting model.Setting) (err error) {

	//var maps = make(map[string]string)

	mapData, _ := json.Marshal(setting)

	var sysSetting model2.SysSetting

	sysSetting.Key = "aliOpenSetting"
	//maps["value"] = string(mapData)
	err = source.DB().Where("`key` = ?", "aliOpenSetting").First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		source.DB().Where("`key` = ?", "aliOpenSetting").Create(&sysSetting)
	} else {
		sysSetting.Value = string(mapData)
		err = source.DB().Where("`key` = ?", "aliOpenSetting").Updates(&sysSetting).Error
	}

	return
}

func GetSetting() (err error, sysSetting model2.SysSetting) {
	err = source.DB().Table("sys_settings").Where("`key` = ?", "aliOpenSetting").First(&sysSetting).Error
	return
}

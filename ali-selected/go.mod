module ali-selected

go 1.21

//gin-vue-admin v1.0.0
//github.com/dgrijalva/jwt-go v3.2.0+incompatible
//github.com/gin-gonic/gin v1.6.3
//github.com/gookit/color v1.3.6
//github.com/olivere/elastic/v7 v7.0.24
//github.com/streadway/amqp v1.1.0
//go.uber.org/zap v1.16.0
//gorm.io/gorm v1.25.5

require (
	category v1.0.0
	gin-vue-admin v1.0.0
	github.com/gin-gonic/gin v1.6.3
	github.com/gogf/gf v1.16.9
	github.com/streadway/amqp v1.1.0
	github.com/writethesky/stbz-sdk-golang v1.0.1
	github.com/xingliuhua/leaf v1.1.2
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	order v1.0.0
	product v1.0.0
	public-supply v1.0.0
	shipping v1.0.0
	yz-go v1.0.0
)

replace (
	category => ../category
	convergence => ../convergence-pay
	gin-vue-admin v1.0.0 => ../gin-vue-admin/server
	notification => ../notification
	order => ../order

	payment => ../payment
	product => ../product
	public-supply => ../public-supply
	region => ../region
	shipping => ../shipping
	user => ../user
	wechatpay-go-main => ../wechatpay-go-main
	yz-go => ../yz-go
)

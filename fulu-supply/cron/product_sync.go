package cron

import (
	"fulu-supply/model"
	"fulu-supply/package/goods"
	"fulu-supply/service"
	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushProductSyncHandle() {
	task := cron.Task{
		Key:  "fuluSupplyProductSync",
		Name: "数字权益插件同步商品",
		// 每30分钟执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			Sync()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func Sync() {
	source.DB().Model(&model.FuluSupplyGoods{}).Where("product_source = 0").Update("md5", "")
	err, supplyID := service.GetFuluSupplyID()
	if err != nil {
		return
	}
	err = goods.ImportGoodsRun(supplyID)
	if err != nil {
		log.Log().Error("福禄供应链定时同步福禄商品失败", zap.Any("err", err))
	}
}

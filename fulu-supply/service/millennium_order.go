package service

import (
	"encoding/json"
	"errors"
	"fulu-supply/model"
	"github.com/li-bao-jia/millennium"
	"github.com/li-bao-jia/millennium/pkg"
	mOrder "github.com/li-bao-jia/millennium/pkg/order"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	orderModel "order/model"
	"order/mq"
	"order/order"
	"public-supply/common"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

// MillenniumCheckOrders 千禧券调用订单查询接口查询订单结果、并处理订单状态
func MillenniumCheckOrders() (err error) {
	var client *millennium.ApiClient
	if client, err = getMillenniumApiClient(); err != nil {
		return
	}

	var fuLuOrders []model.FuluSupplyOrderResult
	err = source.DB().Where("status = ? and product_source = ?", 0, 127).Find(&fuLuOrders).Error
	if err != nil {
		return
	}

	// 循环处理订单
	for _, fuLuOrder := range fuLuOrders {
		err = nil
		if err = MillenniumCheckOrder(client, fuLuOrder); err != nil {
			log.Log().Info("千禧券计划任务:订单处理"+fuLuOrder.CustomerOrderNo, zap.Any("err", err))

			// 肖鹏(七件事)要求千禧券订单状态为failed，推送消息给下游
			var orderModel orderModel.Order
			_ = source.DB().Where("order_sn = ?", fuLuOrder.CustomerOrderNo).First(&orderModel).Error
			if orderModel.ID > 0 {
				_ = mq.PublishMessage(orderModel.ID, mq.OrderFailed, 0)
			}
			continue
		}
	}
	return
}

func MillenniumCheckOrder(client *millennium.ApiClient, fuLuOrder model.FuluSupplyOrderResult) (err error) {
	// 根据福禄订单，查询需要处理的千禧券订单
	var millenniumOrders []model.MillenniumOrder
	err = source.DB().Where("order_sn = ? and order_state = ?", fuLuOrder.CustomerOrderNo, "processing").Find(&millenniumOrders).Error
	if err != nil {
		return
	}

	// 循环千禧订单数据，验证订单状态
	var (
		resp      pkg.ApiResponse
		orderData mOrder.Order
	)
	for _, millenniumOrder := range millenniumOrders {

		log.Log().Info("千禧券计划任务:单号" + fuLuOrder.CustomerOrderNo)

		resp, err = client.CallApi(&mOrder.QueryOrder{}, mOrder.QueryOrderParams{OutOrderNo: millenniumOrder.OutOrderNo})
		if err != nil {
			log.Log().Info("千禧券计划任务:单号"+fuLuOrder.CustomerOrderNo, zap.Any("err", err))
			return
		}

		if resp.Code != 0 {
			log.Log().Info("千禧券计划任务:单号" + fuLuOrder.CustomerOrderNo + resp.Msg)
			return
		}

		// 转换订单数据
		if err = json.Unmarshal([]byte(resp.Data), &orderData); err != nil {
			log.Log().Info("千禧券计划任务:单号"+fuLuOrder.CustomerOrderNo, zap.Any("err", err))
			return
		}

		log.Log().Info("千禧券计划任务:单号" + fuLuOrder.CustomerOrderNo + "，状态" + orderData.OrderState)

		// 如果订单状态仍在处理中，不做处理
		if orderData.OrderState == "processing" {
			continue
		}

		// 如果订单状态failed、success，更改千禧券订单状态
		err = source.DB().Model(&millenniumOrder).Updates(map[string]interface{}{"order_state": orderData.OrderState, "error": ""}).Error
		if err != nil {
			log.Log().Info("千禧券计划任务:单号" + fuLuOrder.CustomerOrderNo + "，更新状态错误")
			continue
		}

		// 如果订单状态failed，不再验证卡密逻辑
		if orderData.OrderState == "failed" {
			continue
		}

		// 千禧券数据1卡密2直充
		if orderData.OrderType == 1 {
			var cards []mOrder.Card
			// 返回资源是字符串，需要再次转译
			if err = json.Unmarshal([]byte(orderData.Cards), &cards); err != nil {
				return
			}
			var fuLuCards []model.FuluSupplyOrderCards
			for _, card := range cards {
				// 卡密解密
				var CardPwd, CardNumber string
				if CardPwd, err = client.DecryptAES256ECB(card.CardPwd); err != nil {
					return
				}
				if CardNumber, err = client.DecryptAES256ECB(card.CardNumber); err != nil {
					return
				}

				fuLuCards = append(fuLuCards, model.FuluSupplyOrderCards{
					OrderId:         orderData.OrderNo,
					CustomerOrderNo: fuLuOrder.CustomerOrderNo,
					CardDeadline:    card.CardDeadline,
					CardPwd:         CardPwd,
					CardNumber:      CardNumber,
					ProductSource:   common.MILLENNIUM_SOURCE,
				})
			}
			if len(fuLuCards) > 0 {
				if err = source.DB().Create(&fuLuCards).Error; err != nil {
					return
				}
			}
		}
	}

	// 根据千禧券拆单的状态，计算该笔订单是否完成
	err = source.DB().Where("order_sn = ?", fuLuOrder.CustomerOrderNo).Find(&millenniumOrders).Error
	if err != nil {
		return
	}
	isAllSuccess := true
	isPartFailed := false
	for _, millenniumOrder := range millenniumOrders {
		// 一笔订单未成功，则订单未完成
		if millenniumOrder.OrderState != "success" {
			isAllSuccess = false
		}
		// 一订单状态 failed，更新 fulu_supply_order_results.error字段
		if millenniumOrder.OrderState == "failed" {
			isPartFailed = true
		}
	}

	// 部分订单失败，更新 fulu_supply_order_results.error字段
	if isPartFailed {
		// 按客户要求修改错误提示：1-话费 2-流量 3-卡密 4-直充;
		var errMsg string

		switch fuLuOrder.OrderType {
		case 3:
			errMsg = "卡密生成失败"
		case 4:
			errMsg = "充值失败"
		default:
			errMsg = "部分订单状态 failed"
		}

		err = source.DB().Model(&fuLuOrder).Updates(model.FuluSupplyOrderResult{Error: errMsg}).Error
		return
	}

	// 订单全部成功，更改 fulu_supply_order_results 成功，走订单完成逻辑
	if isAllSuccess {
		// 修改福禄表状态
		err = source.DB().Model(&fuLuOrder).Updates(map[string]interface{}{"status": 1, "order_state": "success", "error": ""}).Error
		if err != nil {
			return
		}

		var shopOrder orderModel.Order
		err = source.DB().Where("`order_sn` = ?", fuLuOrder.CustomerOrderNo).First(&shopOrder).Error
		if err != nil {
			return
		}

		if err = order.Send(shopOrder.ID); err != nil {
			log.Log().Error("千禧券:订单发货操作失败!", zap.Any("err", err))
			return
		}

		if err = order.Received(shopOrder.ID); err != nil {
			log.Log().Error("千禧券:订单收货操作失败!", zap.Any("err", err))
			return
		}
	}
	return
}

// 查询千禧券错误订单，循环更改单号重新推送

func MillenniumRepairOrder(orderRequest model.FuluSupplyOrderRequest) (err error) {
	var millenniumOrders []model.MillenniumOrder
	err = source.DB().Where("order_sn = ? and order_state = ?", orderRequest.OrderSN, "failed").Find(&millenniumOrders).Error
	if err != nil {
		return
	}

	// 千禧ApiClient
	var client *millennium.ApiClient
	if client, err = getMillenniumApiClient(); err != nil {
		return
	}

	for i, millenniumOrder := range millenniumOrders {
		// 失败订单软删除
		if err = source.DB().Delete(&millenniumOrder).Error; err != nil {
			log.Log().Error("千禧券-错误订单重新推送：删除失败" + err.Error())
			return
		}
		// 新单数据
		newMillenniumOrder := model.MillenniumOrder{
			OrderSN:    millenniumOrder.OrderSN,
			OutOrderNo: millenniumOrder.OutOrderNo + "_" + strconv.Itoa(i),
			ProductId:  millenniumOrder.ProductId,
			BuyNum:     1,
			OrderType:  2,
			OrderState: "processing",
			Account:    orderRequest.ChargeAccount,
		}
		// 创建新单
		if err = source.DB().Create(&newMillenniumOrder).Error; err != nil {
			log.Log().Error("千禧券-错误订单重新推送：创建新单失败" + err.Error())
			return
		}
		// 推送订单
		if err = pushMillenniumOrder(client, newMillenniumOrder); err != nil {
			source.DB().Model(&newMillenniumOrder).Updates(model.MillenniumOrder{OrderState: "failed", Error: err.Error()})
			log.Log().Error("千禧券-错误订单重新推送：推送订单失败" + err.Error())
			return
		}
		// 修改 error 字段
		if err = source.DB().Model(&model.FuluSupplyOrderResult{}).Where("customer_order_no = ?", newMillenniumOrder.OrderSN).Updates(map[string]interface{}{"error": ""}).Error; err != nil {
			log.Log().Error("千禧券-错误订单重新推送：修改 error 字段失败" + err.Error())
			return
		}
	}
	return
}

// MillenniumConfirmOrder 千禧券订单推送
// processing 需要定时查询更新订单状态
// success 订单成功走订单完成逻辑
// failed 上游订单处理错误，需要更改单号重新推送
// 如果订单存在存在 failed，修改 fulu_supply_order_results.error 字段，可以重新提交订单
func MillenniumConfirmOrder(orderRequest model.FuluSupplyOrderRequest) (err error, info *stbz.APIResult) {
	// 福禄记录
	var fuLuSupplyOrderResult = model.FuluSupplyOrderResult{
		CustomerOrderNo: strconv.Itoa(int(orderRequest.OrderSN)),
		ProductId:       orderRequest.ProductId,
		OrderState:      "processing",
		OrderType:       3, // 默认订单类型卡密
		Status:          0,
		ProductSource:   common.MILLENNIUM_SOURCE,
		BuyNum:          orderRequest.BuyNum,
		ChargeAccount:   orderRequest.ChargeAccount,
	}
	if orderRequest.FuluGoods.ProductType == "直充" {
		fuLuSupplyOrderResult.OrderType = 4
	}

	// 如果没找到，就创建一个新纪录
	err = source.DB().FirstOrCreate(&fuLuSupplyOrderResult, model.FuluSupplyOrderResult{CustomerOrderNo: strconv.Itoa(int(orderRequest.OrderSN))}).Error
	if err != nil {
		log.Log().Error("千禧券-类型127创建OrderResult数据错误" + err.Error())
		return
	}

	// 推送千禧券订单，如果存在错误更新error字段，不存在错误，清空error字段
	if err = PushMillenniumConfirmOrder(orderRequest); err != nil {
		log.Log().Error("千禧券-类型127第三方返回数据错误" + err.Error())
		source.DB().Model(&fuLuSupplyOrderResult).Updates(model.FuluSupplyOrderResult{OrderState: "failed", Error: err.Error()})
		return
	}
	source.DB().Model(&fuLuSupplyOrderResult).Updates(map[string]interface{}{"order_state": "processing", "error": ""})

	return
}

func PushMillenniumConfirmOrder(orderRequest model.FuluSupplyOrderRequest) (err error) {
	// 千禧ApiClient
	var client *millennium.ApiClient
	if client, err = getMillenniumApiClient(); err != nil {
		return
	}

	// 推送订单
	if orderRequest.FuluGoods.ProductType == "直充" {
		err = MillenniumConfirmChargeOrder(client, orderRequest)
	} else {
		err = MillenniumConfirmCardOrder(client, orderRequest)
	}
	return
}

func MillenniumConfirmCardOrder(client *millennium.ApiClient, orderRequest model.FuluSupplyOrderRequest) (err error) {
	millenniumOrder := model.MillenniumOrder{
		OrderSN:    orderRequest.OrderSN,
		OutOrderNo: strconv.Itoa(int(orderRequest.OrderSN)) + "_" + strconv.Itoa(1),
		ProductId:  orderRequest.ProductId,
		BuyNum:     1,
		OrderType:  2,
		OrderState: "processing",
		Account:    orderRequest.ChargeAccount,
	}
	// 插入千禧券订单数据
	err = source.DB().FirstOrCreate(&millenniumOrder, model.MillenniumOrder{OutOrderNo: millenniumOrder.OutOrderNo}).Error
	if err != nil {
		log.Log().Error("千禧券-卡密创建MillenniumOrder数据错误" + err.Error())
		return
	}

	// 设置请求参数
	data := mOrder.CardOrderParams{
		ProductId:  orderRequest.ProductId,     // 千禧券商品ID
		OutOrderNo: millenniumOrder.OutOrderNo, // 单号
		BuyNum:     orderRequest.BuyNum,        // 购买数量（⚠️：目前单笔数量最大50）
	}

	resp, err := client.CallApi(&mOrder.CardOrder{}, data)
	if err != nil {
		log.Log().Error("千禧券-直充订单推送错误1:" + err.Error())
		return
	}

	if resp.Code != 0 {
		log.Log().Error("千禧券-直充订单推送错误2:" + resp.Msg)
		err = errors.New(resp.Msg)
		return
	}

	// 转换订单数据
	var cOrder mOrder.Order
	if err = json.Unmarshal([]byte(resp.Data), &cOrder); err != nil {
		log.Log().Error("千禧券-直充订单推送错误3:" + err.Error())
		return
	}

	updateDataMap := map[string]interface{}{
		"order_no":               cOrder.OrderNo,
		"out_order_no":           cOrder.OutOrderNo,
		"product_id":             cOrder.ProductId,
		"product_name":           cOrder.ProductName,
		"order_type":             cOrder.OrderType,
		"order_price":            cOrder.OrderPrice,
		"order_state":            cOrder.OrderState,
		"create_time":            cOrder.CreateTime,
		"finish_time":            cOrder.FinishTime,
		"operator_serial_number": cOrder.OperatorSerialNumber,
		"buy_num":                cOrder.BuyNum,
		"account":                cOrder.Account,
	}
	if err = source.DB().Model(&millenniumOrder).Updates(updateDataMap).Error; err != nil {
		log.Log().Error("千禧券-卡密订单推送错误"+err.Error(), zap.Any("err", cOrder))
		return
	}

	// 卡密关联字段更新
	if err = source.DB().Model(&model.FuluSupplyOrderResult{}).Where("customer_order_no = ?", strconv.Itoa(int(orderRequest.OrderSN))).Update("order_id", cOrder.OrderNo).Error; err != nil {
		return
	}

	return
}

func MillenniumConfirmChargeOrder(client *millennium.ApiClient, orderRequest model.FuluSupplyOrderRequest) (err error) {
	// 千禧券单笔直充订单商品数量只能唯1（此处做兼容处理）
	var mOrders []model.MillenniumOrder
	for i := 1; i <= orderRequest.BuyNum; i++ {
		mOrders = append(mOrders, model.MillenniumOrder{
			OrderSN:    orderRequest.OrderSN,
			OutOrderNo: strconv.Itoa(int(orderRequest.OrderSN)) + "_" + strconv.Itoa(i),
			ProductId:  orderRequest.ProductId,
			BuyNum:     1,
			OrderType:  2,
			OrderState: "processing",
			Account:    orderRequest.ChargeAccount,
		})
	}

	// 批量插入千禧券订单
	if err = source.DB().Create(&mOrders).Error; err != nil {
		log.Log().Error("千禧券-直充创建MillenniumOrder数据错误" + err.Error())
		return
	}

	// 查询插入的千禧券订单
	if err = source.DB().Where("order_sn = ?", orderRequest.OrderSN).Find(&mOrders).Error; err != nil {
		log.Log().Error("千禧券-直充查询MillenniumOrder数据错误" + err.Error())
		return
	}

	// 循环推送千禧券订单
	for _, v := range mOrders {
		if err = pushMillenniumOrder(client, v); err != nil {
			source.DB().Model(&v).Updates(model.MillenniumOrder{OrderState: "failed", Error: err.Error()})
			log.Log().Error("千禧券-错误订单重新推送：推送失败" + err.Error())
			continue
			// 保证每一笔订单都有推送
		}
	}
	return
}

func pushMillenniumOrder(client *millennium.ApiClient, millenniumOrder model.MillenniumOrder) (err error) {
	// 设置请求参数
	data := mOrder.ChargeOrderParams{
		ProductId:  millenniumOrder.ProductId,  // 千禧券商品ID
		OutOrderNo: millenniumOrder.OutOrderNo, // 单号
		Account:    millenniumOrder.Account,    // 充值账号
		BuyNum:     1,                          // 购买数量（⚠️：目前只支持单笔充值）
	}

	resp, err := client.CallApi(&mOrder.ChargeOrder{}, data)
	if err != nil {
		log.Log().Error("千禧券-直充订单推送错误1:" + err.Error())
		return
	}

	if resp.Code != 0 {
		log.Log().Error("千禧券-直充订单推送错误2:", zap.Any("err", resp))
		err = errors.New(resp.Msg)
		return
	}

	var cOrder mOrder.Order
	if err = json.Unmarshal([]byte(resp.Data), &cOrder); err != nil {
		log.Log().Error("千禧券-直充订单推送错误3:" + err.Error())
		return
	}

	updateDataMap := map[string]interface{}{
		"order_no":               cOrder.OrderNo,
		"out_order_no":           cOrder.OutOrderNo,
		"product_id":             cOrder.ProductId,
		"product_name":           cOrder.ProductName,
		"order_type":             cOrder.OrderType,
		"order_price":            cOrder.OrderPrice,
		"order_state":            cOrder.OrderState,
		"create_time":            cOrder.CreateTime,
		"finish_time":            cOrder.FinishTime,
		"operator_serial_number": cOrder.OperatorSerialNumber,
		"buy_num":                cOrder.BuyNum,
		"account":                cOrder.Account,
		"error":                  "",
	}
	if err = source.DB().Model(&millenniumOrder).Updates(updateDataMap).Error; err != nil {
		log.Log().Error("千禧券-直充订单推送错误"+err.Error(), zap.Any("err", cOrder))
		return
	}
	return
}

func getMillenniumApiClient() (client *millennium.ApiClient, err error) {
	var setting model.Setting
	if err, setting = MillenniumSetting(); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先配置基础信息")
		return
	}

	// 正式环境
	client = millennium.NewApiClient(
		"https://openapi.qianxiquan.com",
		setting.Values.BaseInfo.AppKey,
		setting.Values.BaseInfo.AppSecret,
	)

	// 测试环境
	//client = millennium.NewApiClient(
	//	"https://testopen.qianxiquan.com",
	//	setting.Values.BaseInfo.AppKey,
	//	setting.Values.BaseInfo.AppSecret,
	//)

	return
}

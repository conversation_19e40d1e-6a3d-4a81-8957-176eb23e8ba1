package service

import (
	"application/model"
	"context"
	"errors"
	"fmt"
	fuluSupplyModel "fulu-supply/model"
	"fulu-supply/request"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/olivere/elastic/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"math"
	"os"
	productModel "product/model"
	"product/mq"
	"product/service"
	"strconv"
	"time"
	userEquityModel "user-equity/model"
	"yz-go/common_data"
	"yz-go/component/log"
	YzGoConfig "yz-go/config"
	yzRequest "yz-go/request"
	"yz-go/source"
	"yz-go/utils"
)

type Application struct {
	source.Model
	AppLevelID       uint             `json:"appLevelId" form:"appLevelId" gorm:"column:app_level_id;comment:;type:int;size:10;"`
	MemberId         int              `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}

func (Application) TableName() string {
	return "application"
}

type ApplicationLevel struct {
	source.Model
	ServerRadio       int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:;type:int;size:10;"`
	NumMax            int `json:"numMax" form:"numMax" gorm:"column:num_max;comment:;type:int;size:10;"`
	EquityServerRadio int `json:"equityServerRadio" form:"equityServerRadio" gorm:"column:equity_server_radio;comment:数字权益商品手续费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}

type ApiProduct struct {
	source.Model
	GatherSupplyID uint            `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"`        // 供应链id
	SourceGoodsID  uint            `json:"source_goods_id" form:"source_goods_id" gorm:"column:source_goods_id;index;comment:商品来源ID;"`         // 商品来源
	IsDisplay      int             `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"` // 上架（1是0否）
	FuluGoodsInfo  FuluSupplyGoods `json:"fulu_goods_info"  gorm:"foreignKey:SourceGoodsID;references:ProductId"`
}

func (ApiProduct) TableName() string {
	return "products"
}

type Product struct {
	productModel.Product
	AgreementPrice        uint                              `json:"agreement_price" gorm:"-"` //协议价
	SalePrice             uint                              `json:"sale_price" gorm:"-"`      //销售价
	Brand                 Brand                             `json:"brand"  form:"brand"`
	Category1             Category                          `json:"category_1" form:"category_1"`
	Category2             Category                          `json:"category_2" form:"category_2"`
	Category3             Category                          `json:"category_3" form:"category_3"`
	FuluGoodsInfo         FuluSupplyGoods                   `json:"fulu_goods_info"  gorm:"foreignKey:SourceGoodsID;references:ProductId"`
	UserEquityProductInfo userEquityModel.UserEquityProduct `json:"user_equity_product_info"  gorm:"foreignKey:SourceGoodsID;references:EQID"`
	SyncProductTypeName   string                            `json:"sync_product_type_name" form:"sync_product_type_name"`
	PriceRate             float64                           `json:"price_rate" form:"price_rate"` // 利润率(price - cost_price) / cost_price * 100%
}

func (p *Product) AfterFind(tx *gorm.DB) (err error) {
	var fuluSupplyID uint
	err, fuluSupplyID = GetFuluSupplyID()
	if err != nil {
		return
	}
	if p.GatherSupplyID == fuluSupplyID {
		p.SyncProductTypeName = "数字权益"
	} else {
		p.SyncProductTypeName = "会员权益"
	}
	if p.FuluGoodsInfo.ProductSource == 126 {
		p.SyncProductTypeName = "权益商品"
	}
	if p.FuluGoodsInfo.ProductSource == 127 {
		p.SyncProductTypeName = "千禧券"
	}
	// 利润率(price - cost_price) / cost_price * 100%
	if p.Price > 0 && p.CostPrice > 0 && p.Price > p.CostPrice {
		p.PriceRate = service.Decimal((float64(p.Price)-float64(p.CostPrice))/float64(p.CostPrice)) * 100
	} else {
		p.PriceRate = 0
	}
	return
}

type FuluSupplyGoods struct {
	source.Model
	ProductId       int     `json:"product_id" gorm:"column:product_id;comment:商品Id;"`
	ProductIdString string  `json:"product_id_string" gorm:"column:product_id_string;comment:第三方商品id;"`
	ProductType     string  `json:"product_type" gorm:"column:product_type;comment:库存类型：卡密、直充;type:varchar(255);"`
	ProductSource   int     `json:"product_source" gorm:"column:product_source;comment:商品来源:0福禄1权益;default:0;"`
	FaceValue       float64 `json:"face_value" gorm:"column:face_value;comment:面值;"`
	LockStatus      int     `json:"lock_status" gorm:"column:lock_status;comment:商品来源:0未锁定1已锁定;default:0;"`
}

type Brand struct {
	ID   uint   `json:"id"`
	Name string `json:"name" form:"name"`
}
type Category struct {
	ID    uint   `json:"id"`
	Name  string `json:"name" form:"name"`
	Image string `json:"image" form:"image"`
	Icon  string `json:"icon" form:"icon"`
}

func GetProductByID(id uint) (err error, product ApiProduct) {
	err = source.DB().Preload("FuluGoodsInfo").Where("id = ?", id).First(&product).Error
	return
}

func testDel() (err error) {
	err = mq.PublishMessage(4, mq.Delete, 0)
	return err
}

func GetProductDetailList(ids []int, appID uint) (err error, list []Product) {
	var application Application
	err = source.DB().Preload("ApplicationLevel").Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}
	var supplyID uint
	err, supplyID = GetFuluSupplyID()
	if err != nil {
		log.Log().Error("查询福禄供应链id失败!", zap.Any("err", err))
		return
	}
	db := source.DB().Preload(clause.Associations)
	db = db.Where("id in ? and gather_supply_id = ?", ids, supplyID)
	err = db.Find(&list).Error

	var productList []Product
	for _, product := range list {
		product.AgreementPrice = product.Price
		product.SalePrice = uint(math.Floor(float64(product.GuidePrice) * (1 + float64(application.ApplicationLevel.EquityServerRadio)/10000))) // 建议销售价
		//权益商品是字符串的id
		if product.SourceGoodsID == 0 {
			source.DB().Model(&model.FuluSupplyGoods{}).Where("product_id_string = ?", product.SourceGoodsIDString).First(&product.FuluGoodsInfo)
		}
		productList = append(productList, product)
	}

	return err, productList
}

func RelieveLockProduct(productID uint) (err error) {
	var product Product
	err = source.DB().Where("id = ?", productID).First(&product).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if product.ID == 0 || product.SourceGoodsID == 0 {
		err = errors.New("查询商品失败")
	}
	err = source.DB().Model(&FuluSupplyGoods{}).Where("product_id = ?", product.SourceGoodsID).Updates(map[string]interface{}{"lock_status": 0, "md5": ""}).Error
	if err != nil {
		return
	}
	return
}

func LockProduct(productID uint) (err error) {
	var product Product
	err = source.DB().Where("id = ?", productID).First(&product).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if product.ID == 0 || product.SourceGoodsID == 0 {
		err = errors.New("查询商品失败")
	}
	err = source.DB().Model(&FuluSupplyGoods{}).Where("product_id = ?", product.SourceGoodsID).Update("lock_status", 1).Error
	if err != nil {
		return
	}
	return
}

func DeleteProduct(productID uint) (err error) {
	return
}

func DisplayProductByIds(ids yzRequest.IdsReqWithValue) (err error) {
	var handleData []uint
	for _, v := range ids.Ids {
		var product Product
		err = source.DB().First(&product, v).Error
		if err != nil {
			continue
		}
		handleData = append(handleData, v)
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return
	}
	if len(handleData) > 0 {
		err = source.DB().Model(&Product{}).Where("`id` in ?", handleData).Update("is_display", ids.Value).Error
		if err != nil {
			return
		}
		for _, id := range handleData {
			if ids.Value == 0 {
				err = mq.PublishMessage(id, mq.Undercarriage, 0)
			} else {
				err = mq.PublishMessage(id, mq.OnSale, 0)
			}
		}
	} else {
		return errors.New("商品未审核通过")
	}
	return
}

func SearchEsProductIds(boolQ *elastic.BoolQuery) (ids []uint, err error) {
	var es *elastic.Client

	if es, err = source.ES(); err != nil {
		return
	}

	var res *elastic.SearchResult
	/*filterQ := elastic.NewBoolQuery()
	filterQ = filterQ.Filter(boolQ)*/
	res, err = es.Search("product" + common_data.GetOldProductIndex()).
		Size(10000).
		Query(boolQ).
		//Source(true). // 指定获取所有字段
		Do(context.Background())
	if err != nil {
		return
	}

	var list []service.ProductElasticSearch

	if list, err = service.GetSearchResult(res); err != nil {
		return
	}

	for _, product := range list {
		ids = append(ids, product.ID)
	}

	return
}

func getEsBoolQuery(search request.ProductSearch) *elastic.BoolQuery {
	boolQ := elastic.NewBoolQuery()

	if search.PriceRate.From > 0 {
		boolQ.Must(elastic.NewRangeQuery("price_rate").Gte(search.PriceRate.From))
	}
	if search.PriceRate.To > search.PriceRate.From {
		boolQ.Must(elastic.NewRangeQuery("price_rate").Lte(search.PriceRate.To))
	}

	return boolQ
}

func ExportProductList(info request.ProductSearch) (err error, link string) {
	var db *gorm.DB
	// 获取搜索GORM
	if err, db = searchProductGorm(info); err != nil {
		return
	}

	var products []Product
	// 查询数据
	if err = db.Order("id desc").Find(&products).Error; err != nil {
		return
	}

	// 创建一个工作表
	f := excelize.NewFile()

	f.SetCellValue("Sheet1", "A1", "商品id")
	f.SetCellValue("Sheet1", "B1", "供应渠道")
	f.SetCellValue("Sheet1", "C1", "上游ID")
	f.SetCellValue("Sheet1", "D1", "商品名称")
	f.SetCellValue("Sheet1", "E1", "商品类型")
	f.SetCellValue("Sheet1", "F1", "状态")
	f.SetCellValue("Sheet1", "G1", "库存")
	f.SetCellValue("Sheet1", "H1", "销量")
	f.SetCellValue("Sheet1", "I1", "零售价(面值)")
	f.SetCellValue("Sheet1", "J1", "供货价")
	f.SetCellValue("Sheet1", "K1", "成本价")

	i := 2
	for _, v := range products {
		statusName := "下架"
		if v.IsDisplay == 1 {
			statusName = "上架"
		}
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.SyncProductTypeName)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.SourceGoodsID)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Title)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.FuluGoodsInfo.ProductType)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), statusName)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.Stock)
		f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.Sales)
		f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), float64(v.OriginPrice)/100)
		f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), float64(v.Price)/100)
		f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), float64(v.CostPrice)/100)
		i++
	}

	//index :=
	// 设置工作簿的默认工作表
	f.SetActiveSheet(f.NewSheet("Sheet1"))

	path := YzGoConfig.Config().Local.Path + "/fulu_products"

	if exist, _ := utils.PathExists(path); !exist {
		// 创建文件夹
		err = os.MkdirAll(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}

	link = path + "/" + time.Now().Format("20060102150405") + "数字权益商品导出.xlsx"

	if err = f.SaveAs(link); err != nil {
		return
	}

	return
}

func searchProductGorm(info request.ProductSearch) (err error, db *gorm.DB) {
	db = source.DB().Preload(clause.Associations).Model(&Product{}).Where("is_plugin = ?", 1)

	if info.Keyword != "" {
		db.Where("`title` LIKE ?", "%"+info.Keyword+"%")
	}
	if info.Category1ID != 0 {
		db = db.Where("category1_id = ?", info.Category1ID)
	}
	if info.Category2ID != 0 {
		db = db.Where("category2_id = ?", info.Category2ID)
	}
	if info.Category3ID != 0 {
		db = db.Where("category3_id = ?", info.Category3ID)
	}
	if info.IsDisplay != 0 {
		if info.IsDisplay == 2 {
			db.Where("is_display = ?", 1)
		}
		if info.IsDisplay == 3 {
			db.Where("is_display != ?", 1)
		}
	}
	if info.StockStatus != 0 {
		if info.StockStatus == 2 {
			db.Where("stock > ?", 0)
		}
		if info.StockStatus == 3 {
			db.Where("stock = ?", 0)
		}
	}
	// ES搜索利润率
	if info.PriceRate.To > 0 {
		var pIds []uint
		if pIds, err = SearchEsProductIds(getEsBoolQuery(info)); err != nil {
			return
		}
		if len(pIds) > 0 {
			db.Where("source_goods_id IN ?", pIds)
		}
	}

	// 福禄商品表搜索
	if info.SyncProductType != 0 || info.ProductType != 0 {

		searchFuLuProduct := SearchFuLuProduct{
			ProductSource: info.SyncProductType,
		}
		if info.ProductType == 2 {
			searchFuLuProduct.ProductType = "直充"
		}
		if info.ProductType == 3 {
			searchFuLuProduct.ProductType = "卡密"
		}
		var fIds []uint
		if fIds, err = SearchFuLuProductIds(searchFuLuProduct); err != nil {
			return
		}

		db.Where("source_goods_id IN ?", fIds)
	}

	// 商品表gather_supply_id存储值都是 福禄供应链ID，用source字段区分福禄商品、权益商品、千禧券商品
	var supplyID uint
	if err, supplyID = GetFuluSupplyID(); err != nil {
		return
	}

	db.Where("gather_supply_id = ?", supplyID)

	return
}

func GetProductList(info request.ProductSearch) (err error, list interface{}, total int64) {

	db := source.DB().Preload(clause.Associations).Model(&Product{}).Where("is_plugin = ?", 1)

	var products []Product

	if info.Keyword != "" {
		db.Where("`title` LIKE ?", "%"+info.Keyword+"%")
	}
	if info.Category1ID != 0 {
		db = db.Where("category1_id = ?", info.Category1ID)
	}
	if info.Category2ID != 0 {
		db = db.Where("category2_id = ?", info.Category2ID)
	}
	if info.Category3ID != 0 {
		db = db.Where("category3_id = ?", info.Category3ID)
	}
	if info.IsDisplay != 0 {
		if info.IsDisplay == 2 {
			db.Where("is_display = ?", 1)
		}
		if info.IsDisplay == 3 {
			db.Where("is_display != ?", 1)
		}
	}
	if info.StockStatus != 0 {
		if info.StockStatus == 2 {
			db.Where("stock > ?", 0)
		}
		if info.StockStatus == 3 {
			db.Where("stock = ?", 0)
		}
	}
	// ES搜索利润率
	if info.PriceRate.To > 0 {
		var pIds []uint
		pIds, err = SearchEsProductIds(getEsBoolQuery(info))
		if err != nil {
			return
		}
		db.Where("id IN ?", pIds)
	}

	// 福禄商品表搜索
	if info.SyncProductType != 0 || info.ProductType != 0 {

		searchFuLuProduct := SearchFuLuProduct{
			ProductSource: info.SyncProductType,
		}
		if info.ProductType == 2 {
			searchFuLuProduct.ProductType = "直充"
		}
		if info.ProductType == 3 {
			searchFuLuProduct.ProductType = "卡密"
		}
		var fIds []uint
		if fIds, err = SearchFuLuProductIds(searchFuLuProduct); err != nil {
			return
		}

		db.Where("source_goods_id IN ?", fIds)
	}

	// 商品表gather_supply_id存储值都是 福禄供应链ID，用source字段区分福禄商品、权益商品、千禧券商品
	var supplyID uint
	if err, supplyID = GetFuluSupplyID(); err != nil {
		return
	}

	db.Where("gather_supply_id = ?", supplyID)

	if err = db.Count(&total).Error; err != nil {
		return
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	err = db.Order("id desc").Limit(limit).Offset(offset).Find(&products).Error

	return err, products, total
}

func GetProductListByApp(info request.ProductSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Preload(clause.Associations).Model(&Product{})

	var products []Product
	db = db.Where("deleted_at is NULL")
	db.Where("is_plugin = ?", 1)
	if info.Keyword != "" {
		db.Where("`title` LIKE ?", "%"+info.Keyword+"%")
		//db.Where("(instr(products.title,"+info.Keyword+")>0)")
	}
	db.Where("is_display = ?", 1)
	if info.StockStatus != 0 {
		if info.StockStatus == 2 {
			db.Where("stock > ?", 0)
		}
		if info.StockStatus == 3 {
			db.Where("stock = ?", 0)
		}
	}
	fuluDB := source.DB().Model(&fuluSupplyModel.FuluSupplyGoods{})
	if info.SyncProductType == 2 {
		fuluDB.Where("product_source = ?", 1)
	}
	if info.SyncProductType == 1 {
		fuluDB.Where("product_source = ?", 0)
	}
	if info.SyncProductType == 126 {
		fuluDB.Where("product_source = ?", 126)
	}
	// 1:全部,2:直冲,3:卡密
	if info.ProductType != 0 {
		if info.ProductType == 2 {
			fuluDB.Where("product_type = ?", "直充")
		}
		if info.ProductType == 3 {
			fuluDB.Where("product_type = ?", "卡密")
		}
	}
	var fuluProductIds []int
	err = fuluDB.Pluck("product_id", &fuluProductIds).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if len(fuluProductIds) > 0 {
		db.Where("source_goods_id IN ?", fuluProductIds)
	}
	// 供应渠道 1:福禄 2:会员权益
	var supplyID uint
	err, supplyID = GetFuluSupplyID()
	if err != nil {
		return
	}
	db.Where("gather_supply_id = ?", supplyID)

	err = db.Count(&total).Error

	err = db.Order("id desc").Limit(limit).Offset(offset).Find(&products).Error
	//因为权益商品的id是字符串
	for key, item := range products {
		if item.SourceGoodsID == 0 {
			source.DB().Model(&model.FuluSupplyGoods{}).Where("product_id_string = ?", item.SourceGoodsIDString).First(&products[key].FuluGoodsInfo)
		}
	}
	return err, products, total
}

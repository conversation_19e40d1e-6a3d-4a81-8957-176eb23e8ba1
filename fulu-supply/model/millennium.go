package model

import "yz-go/source"

// 千禧券订单表

type MillenniumOrder struct {
	source.Model
	OrderSN              uint   `json:"order_sn" gorm:"column:order_sn;comment:中台单号;"`
	OrderNo              string `json:"order_no" gorm:"order_no;comment:千禧券订单编号"`
	OutOrderNo           string `gorm:"out_order_no;comment:请求单号，唯一"`
	ProductId            int    `json:"product_id" gorm:"product_id;comment:千禧券商品Id"`
	ProductName          string `json:"product_name" gorm:"product_name;comment:商品名称"`
	BuyNum               int    `json:"buy_num" gorm:"buy_num;comment:购买数量"`
	OrderType            int    `json:"order_type" gorm:"order_type;comment:订单类型 1-卡密 2-直充"`
	OrderPrice           string `json:"order_price" gorm:"order_price;comment:交易单价"`
	OrderState           string `json:"order_state" gorm:"order_state;comment:订单状态 success-成功 processing-处理中 failed-失败"`
	OrderServiceState    string `json:"order_service_state" gorm:"order_service_state;comment:订单售后状态 null-无售后 processing-售后处理中 finished-处理完成"`
	Account              string `json:"account" gorm:"account;comment:充值账号"`
	CreateTime           string `json:"create_time" gorm:"create_time;comment:创建时间"`
	FinishTime           string `json:"finish_time" gorm:"finish_time;comment:订单完成时间"`
	OperatorSerialNumber string `json:"operator_serial_number" gorm:"operator_serial_number;comment:运营商流水号"`
	Error                string `json:"error" gorm:"error;comment:错误信息;type:text"`
}

// 千禧券选品库
// 存储上游千禧券商品信息
// 定时同步：创建、更新、删除

type MillenniumCloudProduct struct {
	source.Model
	ProductId     uint    `json:"product_id" gorm:"column:product_id;comment:第三方商品id,int类型;"`
	ProductName   string  `json:"product_name" gorm:"column:product_name;comment:商品名称;type:varchar(255);"`
	FaceValue     float64 `json:"face_value" gorm:"column:face_value;comment:面值;"`
	ProductType   string  `json:"product_type" gorm:"column:product_type;comment:库存类型：卡密、直充;type:varchar(255);"`
	PurchasePrice float64 `json:"purchase_price" gorm:"column:purchase_price;comment:单价（单位：分）;"`
	Storage       uint    `json:"storage" gorm:"column:storage;comment:库存;"`
	SalesStatus   string  `json:"sales_status" gorm:"column:sales_status;comment:销售状态：下架、上架、维护中、库存维护（本接口只取上架状态的商品）;type:varchar(255);"`
	Details       string  `json:"details" gorm:"column:details;comment:商品详情;type:text;"`
	ProfitRate    float64 `json:"profit_rate" gorm:"column:profit_rate;default:0;comment:利润率(面值-协议价)/协议价*100%;"`
	MD5           string  `json:"md5" gorm:"column:md5;comment:md5;type:varchar(255);"`
	IsImport      int     `json:"is_import" gorm:"-"` // 0未导入状态，1已导入状态
}

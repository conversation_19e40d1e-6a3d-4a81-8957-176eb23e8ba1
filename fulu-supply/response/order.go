package response

import (
	fuluSupplyModel "fulu-supply/model"
	"gorm.io/gorm"
	"order/model"
	purchaseService "purchase-account/service"
	"yz-go/response"
	"yz-go/source"
)

type OrderList struct {
	Tags []Tag `json:"tags"`
	response.PageResult
}
type Tag struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}
type User struct {
	ID       uint   `json:"id"`
	NickName string `json:"nickname"`
	Mobile   string `json:"mobile"`
	Avatar   string `json:"avatar"`
	Username string `json:"username"`
}

type Application struct {
	ID       uint   `json:"id"`
	AppName  string `json:"app_name"`
	ShopName string `json:"shop_name"`
}

func (Application) TableName() string {
	return "application"
}

type Order struct {
	model.Order
	User             User                                  `json:"user"`
	GatherSupply     GatherSupply                          `json:"gather_supply" gorm:"foreignKey:GatherSupplyID"`
	PayInfo          PayInfo                               `json:"pay_info" gorm:"foreignKey:PayInfoID"`
	Button           interface{}                           `json:"button" gorm:"-"`
	ShopName         string                                `json:"shop_name"`
	TypeName         string                                `json:"type_name"`
	PayType          string                                `json:"pay_type"`
	Application      Application                           `json:"application"`
	ApplicationShop  ApplicationShop                       `json:"application_shop" gorm:"foreignKey:application_shop_id;references:id"` //应用商城
	FuluOrderInfo    fuluSupplyModel.FuluSupplyOrderResult `json:"fulu_order_info"  gorm:"foreignKey:OrderSN;references:CustomerOrderNo"`
	MillenniumOrders []fuluSupplyModel.MillenniumOrder     `json:"millennium_orders" gorm:"foreignKey:OrderSN;references:OrderSN"`
}

type ApplicationShop struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:;type:int;size:10;"`
	ShopName      string `json:"shop_name"`
	CallbackLink  string `json:"callback_link"`
	AppSecret     string `json:"app_secret"`
}

type OrderStatus int8

const (
	WaitPay OrderStatus = iota
	WaitSend
	WaitReceive
	Completed
	Closed OrderStatus = -1
)

type Button struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}

func GetButton(status OrderStatus) interface{} {
	buttonMap := map[OrderStatus][]Button{
		WaitPay:     {{Title: "确认付款", Url: "order/pay"}, {Title: "关闭订单", Url: "order/close"}},
		WaitSend:    {{Title: "确认发货", Url: "order/send"}, {Title: "全额退款", Url: "order/orderRefund"}},
		WaitReceive: {{Title: "确认收货", Url: "order/receive"}, {Title: "全额退款", Url: "order/orderRefund"}},
		Completed:   {{Title: "已完成", Url: ""}, {Title: "全额退款", Url: "order/orderRefund"}},
		Closed:      {{Title: "已关闭", Url: ""}},
	}
	return buttonMap[status]
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {

	b.Button = GetButton(OrderStatus(b.Status))
	b.ShopName = "卡券商品"
	if b.GatherSupply.CategoryID == 98 {
		b.TypeName = "福禄"
	} else {
		b.TypeName = "会员权益"
	}

	_, payType := purchaseService.GetSupplyPayType(b.GatherSupplyID)
	for _, v := range payType {
		if b.PayTypeID == v.Code {
			b.PayType = v.Name
		}
	}
	if b.PayTypeID == -1 {
		b.PayType = "后台支付"
	}

	if b.PayType == "" {
		b.PayType = "未知"
	}
	return
}

type GatherSupply struct {
	ID         uint   `json:"id"`
	Name       string `json:"name"`
	Logo       string `json:"logo"` //logo
	CategoryID uint   `json:"category_id"`
}

type PayInfo struct {
	ID     uint   `json:"id"`
	PaySn  string `json:"pay_sn"`
	Status string `json:"status"`
}

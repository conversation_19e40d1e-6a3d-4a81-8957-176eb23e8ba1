POST {{api}}/fileUploadAndDownload/upload
Content-Type: multipart/form-data; boundary=WebAppBoundary
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxOTQwNDM4NywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4Nzk4NTg3fQ.VTvhNQUSX_fm032-yjLi-e5FEJb7hFVQp-ISgj34SpU
x-user-id: 1

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="file.txt"

< ../../supply-chain/uploads/file/22a4d9b04fe95c9893b41e2fde83a427_20210220143947.txt
--WebAppBoundary--


###
GET {{api}}product/findProduct?id=7
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDc0ZGVkNDktOGJjMy00M2FjLThlYTktYjg4YTc1YzIwMzVkIiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxODgwMTgwOCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4MTk2MDA4fQ.G-K4VX5Yh-29uzrHAhGpxsXVcCR-tjFeQeQEQIU1sHs
x-user-id: 1

{}
###
GET {{api}}product/getProductList?page=1&pageSize=10&is_display=&title=&category1_id=&category2_id=&category3_id=&maxPrice=&minPrice=&is_new=0&is_hot=0&is_recommend=0&is_promotion=0&supplier_id=&filter=3
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMjUxODAzMSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIxOTEyMjMxfQ.xzXKzYIdg3brv8AVcbwm2qJ7KMv_kbBJvZBE2s1nBRo
x-user-id: 1

###
POST {{api}}product/createProduct
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxOTQwNDM4NywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4Nzk4NTg3fQ.VTvhNQUSX_fm032-yjLi-e5FEJb7hFVQp-ISgj34SpU
x-user-id: 1

{
  "category1_id": 1,
  "category2_id": 8,
  "category3_id": 57,
  "sort": 0,
  "title": "T恤",
  "desc": "好看的T恤",
  "image_url": "",
  "gallery": [],
  "sales": 0,
  "service": "7天无理由退换",
  "detail_images": "",
  "attrs": [
    {
      "name": "产地",
      "value": "中国"
    }
  ],
  "unit": "件",
  "skus": [
    {
      "optionName": "红色",
      "title": "红色",
      "sn": "",
      "weight": 0,
      "volume": 100,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "红色",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        }
      ]
    }
  ]
}

###get
POST {{api}}/api/product/get?id=1
Content-Type: application/json

###
POST {{api}}/api/product/relation/list
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDc0ZGVkNDktOGJjMy00M2FjLThlYTktYjg4YTc1YzIwMzVkIiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxODgwMTgwOCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4MTk2MDA4fQ.G-K4VX5Yh-29uzrHAhGpxsXVcCR-tjFeQeQEQIU1sHs
x-user-id: 1

{
  "product_id": 3
}
###
POST {{api}}/api/product/supplier/list
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDc0ZGVkNDktOGJjMy00M2FjLThlYTktYjg4YTc1YzIwMzVkIiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxODgwMTgwOCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4MTk2MDA4fQ.G-K4VX5Yh-29uzrHAhGpxsXVcCR-tjFeQeQEQIU1sHs
x-user-id: 1

{
  "supplier_id": 1
}

###
POST {{api}}/api/product/list?title=手机&page=2&pageSize=20
Content-Type: application/json
x-token: {{f-token}}
x-user-id: 1

{
  "price": 1
}

###
GET {{api}}/api/product/listByCollectionId?collection_id=5
Content-Type: application/json

###
POST {{api}}/api/comment/list
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxNzAwNDk4OSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE2Mzk5MTg5fQ._Quw2-83lQNgbBWNUEZ6ZVe-VXui7Xf34Ghiix5CeXA
x-user-id: 1

{
  "product_id": 12
}

###
POST {{api}}/api/comment/create
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiNzk5ZTIyZWEtYWEwOS00MDA5LWI3ZWItZWM1ZDc4NjJiMDE2IiwiVXNlcm5hbWUiOiIiLCJJRCI6NCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyODU3OTMzNywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjI3OTczNTM3fQ.VPvL2R0xu1d59xh74MnzbXJy7xdrAGF34pm7uKDtQqM

{
  "avatar": "asdasd",
  "content": "22222",
  "imageUrls": "https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/202183/1627961588github.png,https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/202183/1627961591logo_login1.png",
  "level": 4,
  "des_level": 4,
  "shop_level": 4,
  "express_level": 4,
  "nickname": "wqm1",
  "orderId": 147,
  "productId": 1000053,
  "type": 1,
  "userId": 5,
  "product_attr": "红色+大"
}

###
POST {{api}}/fileUploadAndDownload/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryiklI1mIss8UMTKNT
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxNzAwNDk4OSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE2Mzk5MTg5fQ._Quw2-83lQNgbBWNUEZ6ZVe-VXui7Xf34Ghiix5CeXA
x-user-id: 1

{}

###
POST {{api}}/product/setAttributeStatus
Content-Type: application/json

{
  "column": "is_hot",
  "id": 3593
}

###
POST {{api}}/product/displayProductByIds
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMjUxODAzMSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIxOTEyMjMxfQ.xzXKzYIdg3brv8AVcbwm2qJ7KMv_kbBJvZBE2s1nBRo
x-User-Id: 1

{
  "value": 1,
  "ids": [3594,3593]
}

###
GET {{api}}/product/getSupplierOptionList
Content-Type: application/json

###
PUT {{api}}/product/updateProduct
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxOTU3MTU1MywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4OTY1NzUzfQ.lCLTXnCz14nkbA6XQkj8pe1VzBRKteVfMh4OWoFCxcg
x-user-id: 1

{
  "id": 3833,
  "category1_id": 1,
  "category2_id": 8,
  "category3_id": 57,
  "sort": 0,
  "title": "666000",
  "desc": "123123",
  "image_url": "http://localhost:8888/uploads/file/cb1201cb054c7a1c86aff708454e4ab4_20210422115102.jpeg",
  "gallery": [
    {
      "type": 1,
      "src": "http://localhost:8888/uploads/file/68962a4d8a352d61a6a2949e3c75eb64_20210422141133.jpg",
      "url": "http://localhost:8888/uploads/file/68962a4d8a352d61a6a2949e3c75eb64_20210422141133.jpg",
      "uid": 1619071928323,
      "status": "success"
    }
  ],
  "sales": 100,
  "service": "",
  "detail_images": [
    "http://localhost:8888/uploads/file/68962a4d8a352d61a6a2949e3c75eb64_20210422141154.jpg",
    "http://localhost:8888/uploads/file/cb1201cb054c7a1c86aff708454e4ab4_20210422141158.jpeg"
  ],
  "attrs": [
    {
      "name": "产地",
      "value": "中国"
    }
  ],
  "unit": "77",
  "skus": [
    {
      "optionName": "白+S",
      "title": "白+S",
      "sn": "",
      "weight": 0,
      "volume": 0,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "白",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        },
        {
          "id": 0,
          "value": "S",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "尺寸"
          }
        }
      ]
    },
    {
      "optionName": "白+L",
      "title": "白+L",
      "sn": "",
      "weight": 0,
      "volume": 0,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "白",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        },
        {
          "id": 0,
          "value": "L",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "尺寸"
          }
        }
      ]
    },
    {
      "optionName": "绿+S",
      "title": "绿+S",
      "sn": "",
      "weight": 0,
      "volume": 0,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "绿",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        },
        {
          "id": 0,
          "value": "S",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "尺寸"
          }
        }
      ]
    },
    {
      "optionName": "绿+L",
      "title": "绿+L",
      "sn": "",
      "weight": 0,
      "volume": 0,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "绿",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        },
        {
          "id": 0,
          "value": "L",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "尺寸"
          }
        }
      ]
    },
    {
      "optionName": "红+S",
      "title": "红+S",
      "sn": "",
      "weight": 0,
      "volume": 0,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "红",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        },
        {
          "id": 0,
          "value": "S",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "尺寸"
          }
        }
      ]
    },
    {
      "optionName": "红+L",
      "title": "红+L",
      "sn": "",
      "weight": 0,
      "volume": 0,
      "price": 0,
      "cost_price": 0,
      "origin_price": 0,
      "stock": 0,
      "spec_items": [
        {
          "id": 0,
          "value": "红",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "颜色"
          }
        },
        {
          "id": 0,
          "value": "L",
          "spec_id": 0,
          "spec": {
            "id": 0,
            "title": "尺寸"
          }
        }
      ]
    }
  ],
  "qualifications": [
    {
      "title": "123",
      "src": "http://localhost:8888/uploads/file/f914c66f0aa887aec5f2835c323c8d79_20210422141936.jpg"
    }
  ],
  "price": 200,
  "origin_price": 300,
  "cost_price": 400
}

###
POST {{api}}/category/createCategory
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYxOTU3MTU1MywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjE4OTY1NzUzfQ.lCLTXnCz14nkbA6XQkj8pe1VzBRKteVfMh4OWoFCxcg
x-user-id: 1

{
  "name": "aasdasd",
  "parent_id": 0,
  "level": 1
}


###
GET {{api}}/collection/getCollectionList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMTU2Nzk0MywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIwOTYyMTQzfQ.d8t6bCCgwmyCQL7FxYy-hokrvG9KT5hlP-vamMUfvpk
x-user-id: 1

###
GET {{api}}/collection/findCollection?id=5
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMjUxODAzMSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIxOTEyMjMxfQ.xzXKzYIdg3brv8AVcbwm2qJ7KMv_kbBJvZBE2s1nBRo
x-user-id: 1


###
POST {{api}}/collection/createCollection
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMTU2Nzk0MywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIwOTYyMTQzfQ.d8t6bCCgwmyCQL7FxYy-hokrvG9KT5hlP-vamMUfvpk
x-user-id: 1

{
  "title": "asd",
  "desc": "asd",
  "cover": "asd",
  "Products" : [
    {"id": 111},
    {"id": 222}
  ]
}

###
PUT {{api}}/collection/updateCollection
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMTU2Nzk0MywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIwOTYyMTQzfQ.d8t6bCCgwmyCQL7FxYy-hokrvG9KT5hlP-vamMUfvpk
x-user-id: 1

{
  "id": 2,
  "title": "asd",
  "desc": "asd",
  "cover": "asd",
  "Products" : [
    {"id": 222},
    {"id": 333}
  ]
}


###
DELETE {{api}}/collection/deleteCollection
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyMTU2Nzk0MywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIwOTYyMTQzfQ.d8t6bCCgwmyCQL7FxYy-hokrvG9KT5hlP-vamMUfvpk
x-user-id: 1

{
  "id": 1
}


###
POST {{api}}/api/product/storage/list
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMWZlYmQxOTItZWM4YS00ZmUzLTliOTAtYThmMTFmZWM3NjA1IiwiSUQiOjEsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmCIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTYyNDUxODg5NywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjIzOTEzMDk3fQ.FvvrDwgL8TB1MG-ikxBvD5iBGwpTFvcr9isQ_MkVuE4
x-user-id: 1

{
  "page": 1,
  "pageSize": 10,
  "title": "qwe"
}
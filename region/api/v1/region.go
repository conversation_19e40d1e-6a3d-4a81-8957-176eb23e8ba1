package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	regionModel "region/model"
	"region/request"
	"region/service"
	"yz-go/component/log"
	"yz-go/response"
)

// @Tags 区域
// @Summary 获取省市区列表
// @accept application/json
// @Produce application/json
// @Param data body request.AreaSearch true "获取Area列表"
// @Success 200 {object} []model.Region{}
// @Router /region/list [post]
func GetAreaList(c *gin.Context) {
	var area request.AreaSearch
	err := c.ShouldBindQuery(&area)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err, list := service.GetAreaInfoList(area)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		if len(list) == 0 {
			list = append(list, regionModel.Region{
				ID:       0,
				ParentID: 0,
				Name:     "其他",
				Level:    4,
			})
		}
		response.OkWithDetailed(response.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

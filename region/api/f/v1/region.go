package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	fv1 "region/api/v1"
	_ "region/model"
	"region/service"
	"yz-go/component/log"
	"yz-go/response"
)

// GetAreaList
// @Tags 区域
// @Summary 获取省市区列表
// @accept application/json
// @Produce application/json
// @Param data body request.AreaSearch true "获取Area列表"
// @Success 200 {object} []model.Region{}
// @Router /api/region/list [post]
func GetAreaList(c *gin.Context) {
	fv1.GetAreaList(c)
}


// @Tags 区域
// @Summary 获取省市区所有数据
// @accept application/json
// @Produce application/json
// @Param data body request.AreaSearch true "获取省市区所有数据"
// @Success 200 {object} []model.Region{}
// @Router /region/getAreas [post]
func GetAreas(c *gin.Context) {

	if err, list := service.GetAreas(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}


// @Tags 区域
// @Summary 获取省市区所有数据
// @accept application/json
// @Produce application/json
// @Param data body request.AreaSearch true "获取省市区所有数据"
// @Success 200 {object} []model.Region{}
// @Router /region/getAreas [post]
func GetAreasData(c *gin.Context) {

	if err, list := service.GetAreasData(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	} else {
		response.OkWithDetailed(list, "获取成功", c)
	}
}

package merge

import (
	"context"
	"encoding/json"
	"github.com/olivere/elastic/v7"
	"region/mapping"
	"yz-go/source"
)

func FindCountyRegionsNoMatchScoreGtThan(index1 string, index2 string, size int, score float64) (err error) {
	var regions []mapping.Region
	regions, err = GetRegionsFormEsIndex(index1, size)
	if err != nil {
		return
	}
	for _, region := range regions {
		regions, err = GetRegionsByNameWhichScoreGtThan(index2, region.FullName, region.Name, score)
		if err != nil {
			return
		}
		if len(regions) == 0 {
			println(region.FullName)
		}
	}
	return
}
func FindRegionsNoMatchScoreGtThan(size int, score float64) (err error) {
	var regions []mapping.Region
	regions, err = GetRegionsFormEsIndex("ims_yz_region", size)
	if err != nil {
		return
	}
	for _, region := range regions {
		regions, err = GetRegionsByNameWhichScoreGtThan("yz_region", region.FullName, region.Name, score)
		if err != nil {
			return
		}
		if len(regions) == 0 {
			println(region.FullName)
		}
	}
	return
}

func GetRegionsByNameWhichScoreGtThan(indexName string, fullName string, name string, score float64) (regions []mapping.Region, err error) {
	// 从 ES 中获取地区数据
	client, err := source.ES()
	if err != nil {
		return
	}
	// 创建一个搜索请求
	// 构建查询
	boolQuery := elastic.NewBoolQuery().
		Must(
			elastic.NewMatchQuery("name", name),
			elastic.NewMatchQuery("full_name", fullName),
		)
	searchResult, err := client.Search().Index(indexName).Query(boolQuery).Do(context.Background())

	if err != nil {
		return
	}
	if searchResult.Hits.TotalHits.Value > 0 {
		for _, hit := range searchResult.Hits.Hits {
			if *hit.Score < score {
				break
			}
			var region mapping.Region
			err = json.Unmarshal(hit.Source, &region)
			if err != nil {
				return
			}
			regions = append(regions, region)
		}
	}
	return
}
func GetRegionsFormEsIndex(indexName string, size int) (regions []mapping.Region, err error) {
	// 从 ES 中获取地区数据
	client, err := source.ES()
	if err != nil {
		return
	}
	// 取出所有地区
	searchResult, err := client.Search(indexName).Size(size).Do(context.Background())
	if err != nil {
		return
	}
	if searchResult.Hits.TotalHits.Value > 0 {

		for _, hit := range searchResult.Hits.Hits {

			var region mapping.Region
			err = json.Unmarshal(hit.Source, &region)
			if err != nil {
				return
			}
			regions = append(regions, region)
		}
	}
	return
}

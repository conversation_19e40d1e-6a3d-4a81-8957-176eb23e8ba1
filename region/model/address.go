package model

import (
	"yz-go/source"
)

type Address struct {
	source.Model
	Realname   string `json:"realname" gorm:"column:realname;comment:收件人;type:varchar(30);size:30;"`                 // 收件人
	Mobile     string `json:"mobile" gorm:"column:mobile;comment:收件人手机号;type:varchar(20);size:20;index;"`            // 手机号
	CountryId  int    `json:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;index;"`            // 国id
	ProvinceId int    `json:"province_id" gorm:"column:province_id;comment:省份id;type:int;index;"`                    // 省id
	CityId     int    `json:"city_id" gorm:"column:city_id;comment:城市id;type:int;index;"`                            // 市id
	CountyId   int    `json:"county_id" gorm:"column:county_id;comment:区id;type:int;index;"`                         // 区id
	TownId     int    `json:"town_id" gorm:"column:town_id;comment:街道id;type:int;index;"`                            // 街道id
	Province   string `json:"province" gorm:"-"`                                                                     // 省
	City       string `json:"city"  gorm:"-"`                                                                        // 市
	County     string `json:"county"  gorm:"-"`                                                                      // 区
	Town       string `json:"town"  gorm:"-"`                                                                        // 街
	Detail     string `json:"detail" validate:"required" form:"detail" gorm:"column:detail;comment:详细地址;type:text;"` //详细地址
	Lng        string `json:"lng" form:"lng" gorm:"column:lng;comment:经度;type:varchar(20);size:20;"`                 // 经度
	Lat        string `json:"lat" form:"lat" gorm:"column:lat;comment:维度;type:varchar(20);size:20;"`                 // 维度
}

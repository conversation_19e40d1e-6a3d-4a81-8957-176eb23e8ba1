package service

import (
	"context"
	"course-distribution/model"
	"course-distribution/request"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	model5 "order/model"
	order2 "order/order"
	response "order/response"
	"order/service"
	model2 "product/model"
	"product/other"
	pservice "product/service"
	model4 "public-supply/model"
	setting2 "public-supply/setting"
	"regexp"
	"strconv"
	"strings"
	"time"
	levelModel "user/level"
	model6 "user/model"
	log2 "yz-go/component/log"
	model3 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

func CreateLecturer(data model.Lecturer) (err error) {

	err = source.DB().Create(&data).Error
	return

}

func UpdateLecturer(data model.Lecturer) (err error) {

	err = source.DB().Where("id=?", data.ID).Updates(&data).Error
	return

}

func DeleteLecturer(data model.RequestLecturer) (err error) {

	err = source.DB().Where("id=?", data.ID).Delete(&model.Lecturer{}).Error
	return

}

func FindLecturer(request model.RequestLecturer) (err error, data []model.LecturerSearch, total int64) {
	limit := request.PageSize
	offset := request.PageSize * (request.Page - 1)
	db := source.DB().Model(&model.LecturerSearch{}).Preload("User").Where("gather_supply_id=0")
	if request.Uid > 0 {
		db.Where("uid=?", request.Uid)
	}
	if request.Mobile != "" {
		var user []int64
		source.DB().Model(model6.User{}).Where("username like ?", "%"+request.Mobile+"%").Pluck("id", &user)

		if len(user) > 0 {
			db.Where("uid in (?)", user)

		}
	}
	if request.LecturerName != "" {
		db.Where("lecturer_name like ?", "%"+request.LecturerName+"%")
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&data).Error
	var LecturerList []model.LecturerSearch
	for _, item := range data {

		var LecturerDivided model.LecturerDivided

		source.DB().Where("lecturer_id=? and status=1", item.ID).Select("sum(amount) as amount").First(&LecturerDivided)
		item.TotalShare = LecturerDivided.Amount

		var CurriculumId []int64
		source.DB().Model(model.Curriculum{}).Where("lecturer_id=?", item.ID).Pluck("product_id", &CurriculumId)

		if len(CurriculumId) > 0 {
			var TotalSales int64
			source.DB().Model(model2.OrderItem{}).Where("product_id in (?)", CurriculumId).Select("sum(qty) as TotalSales").First(&TotalSales)

			item.TotalSales = uint(TotalSales)
		}

		var CurriculumNumber int64
		source.DB().Model(model.Curriculum{}).Where("lecturer_id=?", item.ID).Count(&CurriculumNumber)
		item.CurriculumNumber = uint(CurriculumNumber)
		LecturerList = append(LecturerList, item)

	}
	data = LecturerList
	return err, data, total

}

func FindThirdLecturer(request model.RequestLecturer) (err error, data []model.LecturerSearch, total int64) {
	limit := request.PageSize
	offset := request.PageSize * (request.Page - 1)
	db := source.DB().Model(&model.Lecturer{}).Where("gather_supply_id > 0")
	if request.Uid < 0 {
		db.Where("uid=?", request.Uid)
	}
	//if request.Mobile != "" {
	//	var user []int64
	//	source.DB().Model(model6.User{}).Where("username like ?", "%"+request.Mobile+"%").Pluck("id", &user)
	//
	//	if len(user) > 0 {
	//		db.Where("uid in (?)", user)
	//
	//	}
	//}
	if request.LecturerName != "" {
		db.Where("lecturer_name like ?", "%"+request.LecturerName+"%")
	}
	if request.GatherSupplyId > 0 {
		db.Where("gather_supply_id = ?", request.GatherSupplyId)
	}
	db.Where("gather_supply_id >= ?", 1)

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&data).Error

	var LecturerList []model.LecturerSearch
	for _, item := range data {

		var CurriculumId []int64
		source.DB().Model(model.Curriculum{}).Where("lecturer_id=?", item.ID).Pluck("product_id", &CurriculumId)

		var TotalSales int64
		source.DB().Model(model2.OrderItem{}).Where("product_id in (?)", CurriculumId).Select("sum(qty) as TotalSales").First(&TotalSales)

		item.TotalSales = uint(TotalSales)

		var CurriculumNumber int64
		source.DB().Model(model.Curriculum{}).Where("lecturer_id=?", item.ID).Count(&CurriculumNumber)
		item.CurriculumNumber = uint(CurriculumNumber)
		LecturerList = append(LecturerList, item)

	}
	data = LecturerList
	return err, data, total

}

func CreateChapter(data model.Curriculum) (err error) {

	var product pservice.ProductForUpdate

	product.Title = data.CurriculumName
	product.Category1ID = data.Category1ID
	product.Category2ID = data.Category2ID
	product.Category3ID = data.Category3ID
	product.Price = data.Price
	product.CostPrice = data.CostPrice
	product.OriginPrice = data.RetailPrice
	product.GuidePrice = data.RetailPrice
	product.ImageUrl = data.CurriculumImg
	product.PluginID = 18
	product.DetailImages = data.Detailed
	product.Stock = 99999999999
	if data.Status != nil {
		status := data.Status
		product.IsDisplay = int(*status)
	}

	product.IsPlugin = 0

	var skuList []pservice.Sku
	var sku pservice.Sku

	sku.Price = product.Price
	sku.CostPrice = product.CostPrice
	sku.GuidePrice = product.GuidePrice
	sku.OriginPrice = product.GuidePrice
	sku.Title = product.Title
	sku.Stock = 9999999999
	sku.IsDisplay = product.IsDisplay

	skuList = append(skuList, sku)

	product.Skus = skuList

	productErr, productID := pservice.CreateProduct(product)
	if err != nil {
		fmt.Println(productErr)
		return
	}

	data.ProductID = productID

	if data.RetailPrice > data.Price {
		data.Profit = Decimal((float64(data.RetailPrice) - float64(data.Price)) / float64(data.Price))

	}

	//data.Status =
	err = source.DB().Create(&data).Error

	return

}

func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		//log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}
func CreateGatherSupplyChapter(data model.CurriculumList, requestData model.RequestCurriculum) (err error) { //创建供应链导入的 课程商品

	var product pservice.ProductForUpdate
	var pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice uint

	//var goods model4.Goods

	err, pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice = GetPricingPrice(model4.Goods{
		AgreementPrice: data.Product.Price,
		ActivityPrice:  data.Product.ActivityPrice,
		GuidePrice:     data.Product.GuidePrice,
		MarketPrice:    data.Product.OriginPrice,
		CostPrice:      data.Product.CostPrice,
		Source:         101,
	}, &baseSetting)

	if requestData.Category1ID > 0 && requestData.Category2ID > 0 && requestData.Category3ID > 0 {
		product.Category1ID = requestData.Category1ID
		product.Category2ID = requestData.Category2ID
		product.Category3ID = requestData.Category3ID
	} else {

		var cate1, cate2, cate3 model.Category
		display := 1

		cate1.IsDisplay = &display
		cate1.ParentID = 0
		cate1.Level = 1
		cate1.Name = data.Category1Name
		source.DB().Where("name=? and level=? and parent_id=?", cate1.Name, 1, 0).FirstOrCreate(&cate1)
		//}

		//if len(cateList) > 1 && cateList[1] != "" {
		cate2.IsDisplay = &display
		cate2.ParentID = cate1.ID
		cate2.Level = 2
		cate2.Name = data.Category2Name
		source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, 2, cate1.ID).FirstOrCreate(&cate2)
		//}

		//if len(cateList) > 2 && cateList[2] != "" {
		cate3.IsDisplay = &display
		cate3.ParentID = cate2.ID
		cate3.Level = 3
		cate3.Name = data.Category3Name
		source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, 3, cate2.ID).FirstOrCreate(&cate3)
		//}
		//

		product.Category1ID = cate1.ID
		product.Category2ID = cate2.ID
		product.Category3ID = cate3.ID

	}

	product.Title = data.CurriculumName
	product.Price = psalePrice
	product.CostPrice = pcostPrice
	product.OriginPrice = poriginPrice
	product.GuidePrice = pguidePrice
	product.ActivityPrice = pactivityPrice
	product.ImageUrl = data.Product.ImageUrl
	product.DetailImages = data.Product.DetailImages
	product.IsDisplay = data.Product.IsDisplay
	product.IsPlugin = 1
	product.GatherSupplyID = requestData.GatherSupplyID
	product.SourceGoodsID = data.Product.ID
	product.Source = 101
	product.Stock = data.Product.Stock

	var skuList []pservice.Sku
	var sku pservice.Sku

	sku.Price = product.Price
	sku.CostPrice = product.CostPrice
	sku.GuidePrice = product.GuidePrice
	sku.OriginPrice = product.GuidePrice
	sku.ActivityPrice = product.ActivityPrice
	sku.Title = product.Title
	sku.IsDisplay = product.IsDisplay
	sku.Stock = int(product.Stock)
	if len(data.Product.Skus) > 0 {
		sku.OriginalSkuID = int(data.Product.Skus[0].ID)

	}
	skuList = append(skuList, sku)

	product.Skus = skuList

	//赋值重新计算后的课程价格

	productErr, productID := pservice.CreateProduct(product)
	if productErr != nil {
		fmt.Println(productErr)
		return
	}
	var insertData model.Curriculum
	insertData.CurriculumImg = data.CurriculumImg
	insertData.CurriculumCode = data.CurriculumCode
	insertData.Detailed = data.Detailed
	//insertData.Reward=data.Reward
	insertData.Chapter = data.Chapter
	insertData.TimeLimit = data.TimeLimit
	insertData.Status = data.Status
	insertData.SalesModel = data.SalesModel
	insertData.ProductID = productID
	insertData.Category1ID = product.Category1ID
	insertData.Category2ID = product.Category2ID
	insertData.Category3ID = product.Category3ID

	var lecturer model.Lecturer
	lecturer.LecturerName = data.Lecturer.LecturerName
	lecturer.Img = data.Lecturer.Img
	lecturer.Detailed = data.Lecturer.Detailed
	lecturer.Introduce = data.Lecturer.Introduce
	lecturer.GatherSupplyID = requestData.GatherSupplyID
	lecturer.Other = data.Lecturer.Other
	lecturer.Label = data.Lecturer.Label
	lecturer.SourceLecturerId = data.Lecturer.ID

	err = source.DB().Where("source_lecturer_id=? and gather_supply_id=?", data.Lecturer.ID, requestData.GatherSupplyID).FirstOrCreate(&lecturer).Error
	if err != nil {
		log2.Log().Error("导入课程创建讲师失败", zap.Any("err", err))
	}

	insertData.LecturerID = lecturer.ID
	insertData.CurriculumName = data.CurriculumName
	insertData.Price = product.Price
	insertData.CostPrice = product.CostPrice
	insertData.RetailPrice = product.OriginPrice
	insertData.SourceGoodsID = data.ID
	insertData.GatherSupplyID = requestData.GatherSupplyID
	insertData.ProductID = productID
	if insertData.RetailPrice > insertData.Price {
		insertData.Profit = Decimal((float64(insertData.RetailPrice) - float64(insertData.Price)) / float64(insertData.Price))
	}
	insertData.Reward = 0
	err = source.DB().Create(&insertData).Error
	if err != nil {
		log2.Log().Error("导入供应链课程到本地错误", zap.Any("err", err), zap.Any("err", insertData), zap.Any("err", requestData))
	}

	return

}

func UpdateChapter(data model.CurriculumList) (err error) {
	var product pservice.ProductForUpdate

	err = source.DB().Preload("Skus").Where("id=?", data.ProductID).First(&product).Error

	product.ID = data.ProductID
	product.Title = data.CurriculumName
	product.Price = data.Price
	product.CostPrice = data.CostPrice
	product.OriginPrice = data.RetailPrice
	product.GuidePrice = data.RetailPrice
	product.ImageUrl = data.CurriculumImg
	product.DetailImages = data.Detailed
	product.Stock = ************
	//if data.Status != nil {
	//	product.IsDisplay = int(*data.Status)
	//
	//}
	var skuList []pservice.Sku
	var sku pservice.Sku

	sku.Price = product.Price
	//sku.ID = 2424167
	sku.CostPrice = product.CostPrice
	sku.GuidePrice = product.GuidePrice
	sku.Title = product.Title
	sku.ProductID = data.ProductID
	//sku.IsDisplay = product.IsDisplay
	sku.Stock = ************

	skuList = append(skuList, sku)
	product.Skus = skuList

	err = pservice.UpdateProduct(product)
	if err != nil {
		fmt.Println(err)
		return
	}

	err = source.DB().Where("id=?", data.ID).Updates(&data).Error
	return

}

func UpdateChapterStatus(data model.CurriculumList) (err error) {
	var product pservice.ProductForUpdate
	var curriculum model.Curriculum
	err = source.DB().Where("id=?", data.ID).First(&curriculum).Error
	if err != nil {
		return
	}

	err = source.DB().Preload("Skus").Where("id=?", curriculum.ProductID).First(&product).Error

	status := *data.Status
	product.IsDisplay = int(status)

	//if data.Status != nil {
	//	product.IsDisplay = int(*data.Status)
	//
	//}
	//var skuList []pservice.Sku
	//var sku pservice.Sku
	//
	//sku.Price = product.Price
	////sku.ID = 2424167
	//sku.CostPrice = product.CostPrice
	//sku.GuidePrice = product.GuidePrice
	//sku.Title = product.Title
	//sku.ProductID = data.ProductID
	////sku.IsDisplay = product.IsDisplay
	//sku.Stock = ************
	//
	//skuList = append(skuList, sku)
	//product.Skus = skuList

	err = pservice.UpdateProduct(product)
	if err != nil {
		fmt.Println(err)
		return
	}

	err = source.DB().Where("id=?", data.ID).Updates(&data).Error
	return

}

func UpdateGatherSupplyChapter(lecturerID, curriculumId, productId uint, data model.CurriculumList) (err error) {
	var product pservice.ProductForUpdate
	var updateCurriculum model.CurriculumList

	err = source.DB().Preload("Skus").Where("id=?", productId).First(&product).Error
	log2.Log().Info("UpdateProduct-1", zap.Any("info", product))

	var pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice uint

	//var goods model4.Goods

	err, pcostPrice, psalePrice, pactivityPrice, pguidePrice, poriginPrice = GetPricingPrice(model4.Goods{
		AgreementPrice: data.Product.Price,
		ActivityPrice:  data.Product.ActivityPrice,
		GuidePrice:     data.Product.GuidePrice,
		MarketPrice:    data.Product.OriginPrice,
		CostPrice:      data.Product.CostPrice,
		Source:         101,
	}, &baseSetting)

	if product.ID == 0 {
		product.ID = productId

	}
	product.Title = data.CurriculumName
	product.Price = psalePrice
	product.CostPrice = pcostPrice
	product.OriginPrice = poriginPrice
	product.GuidePrice = pguidePrice
	product.ActivityPrice = pactivityPrice
	product.ImageUrl = data.Product.ImageUrl
	product.DetailImages = data.Product.DetailImages
	product.IsDisplay = data.Product.IsDisplay
	product.IsPlugin = 1
	//product.GatherSupplyID = requestData.GatherSupplyID
	product.SourceGoodsID = data.Product.ID
	product.Source = 101
	product.Stock = 9999999999

	var skuList []pservice.Sku
	var sku pservice.Sku

	skulen := len(product.Skus)
	if skulen > 0 {
		sku.ID = product.Skus[0].ID

	}
	sku.Price = product.Price
	sku.CostPrice = product.CostPrice
	sku.GuidePrice = product.GuidePrice
	sku.OriginPrice = product.GuidePrice
	sku.ActivityPrice = product.ActivityPrice
	sku.Title = product.Title
	sku.IsDisplay = product.IsDisplay
	if len(data.Product.Skus) > 0 {
		sku.OriginalSkuID = int(data.Product.Skus[0].ID)

	}
	sku.Stock = int(product.Stock)
	skuList = append(skuList, sku)

	product.Skus = skuList

	log2.Log().Info("UpdateProduct-5", zap.Any("info", product))

	err = pservice.UpdateProduct(product)
	CproductId := product.ID
	if err != nil {
		fmt.Println(err)
		return
	}
	updateCurriculum.CurriculumCode = data.CurriculumCode
	updateCurriculum.CurriculumName = data.CurriculumName
	updateCurriculum.CurriculumImg = data.CurriculumImg
	updateCurriculum.Status = data.Status
	updateCurriculum.SalesModel = data.SalesModel
	updateCurriculum.TimeLimit = data.TimeLimit
	//updateCurriculum.Price = product.Price
	//updateCurriculum.CostPrice = product.CostPrice
	//updateCurriculum.RetailPrice = product.GuidePrice
	updateCurriculum.GatherSupplyID = product.GatherSupplyID
	updateCurriculum.Chapter = data.Chapter
	updateCurriculum.SourceGoodsID = data.ID
	//updateCurriculum = data
	//data.ProductID = productId
	//updateCurriculum.GatherSupplyID = product.GatherSupplyID
	//updateCurriculum.SourceGoodsID = data.ID
	updateCurriculum.ID = curriculumId

	var lecturer model.Lecturer
	lecturer.LecturerName = data.Lecturer.LecturerName
	lecturer.Img = data.Lecturer.Img
	lecturer.Detailed = data.Lecturer.Detailed
	lecturer.Introduce = data.Lecturer.Introduce
	lecturer.GatherSupplyID = product.GatherSupplyID
	lecturer.Uid = 0
	lecturer.Other = data.Lecturer.Other
	lecturer.Label = data.Lecturer.Label

	source.DB().Where("id=?", lecturerID).Updates(&lecturer)

	//data.CurriculumName = data.CurriculumName
	updateCurriculum.Price = product.Price
	updateCurriculum.CostPrice = product.CostPrice
	updateCurriculum.RetailPrice = product.OriginPrice
	updateCurriculum.ProductID = CproductId
	updateCurriculum.LecturerID = lecturer.ID
	if updateCurriculum.RetailPrice > updateCurriculum.Price {
		updateCurriculum.Profit = Decimal((float64(updateCurriculum.RetailPrice) - float64(updateCurriculum.Price)) / float64(updateCurriculum.Price))
	}
	err = source.DB().Omit("Lecturer").Updates(&updateCurriculum).Error
	return

}

func DeleteChapter(data model.RequestCurriculum) (err error) {

	var curriculum model.Curriculum
	err = source.DB().Preload("Product").First(&curriculum, "id=?", data.ID).Error
	if err != nil {
		return
	}
	err = source.DB().Where("id=?", data.ID).Delete(&model.Curriculum{}).Error
	if err != nil {
		return
	}

	var Product model2.Product
	Product.ID = curriculum.ProductID

	err = pservice.DeleteProduct(Product)
	return

}

func FindChapter(request model.RequestCurriculum) (err error, data []model.Curriculum, total int64) {
	limit := request.PageSize
	offset := request.PageSize * (request.Page - 1)
	db := source.DB().Model(&model.Curriculum{}).Preload("Product").Preload("Lecturer")
	if request.CurriculumName != "" {
		db.Where("curriculum_name like ?", "%"+request.CurriculumName+"%")
	}
	if request.ID > 0 {
		db.Where("id=?", request.ID)
	}
	if request.Status != nil {
		db.Where("status=?", request.Status)
	}
	if request.LecturerID > 0 {
		db.Where("lecturer_id=?", request.LecturerID)
	}
	if request.Category1ID > 0 {
		db.Where("category1_id=?", request.Category1ID)
	}
	if request.Category2ID > 0 {
		db.Where("category2_id=?", request.Category2ID)
	}
	if request.Category3ID > 0 {
		db.Where("category3_id=?", request.Category3ID)
	}
	if request.CurriculumCode != "" {
		db.Where("curriculum_code=?", request.CurriculumCode)
	}
	if request.GatherSupplyID > 0 {
		db.Where("gather_supply_id=?", request.GatherSupplyID)
	}
	if request.PriceStart != nil && request.PriceEnd != nil {
		db = db.Where("price between ? and ?", request.PriceStart, request.PriceEnd)
	}

	if request.SortType != "" { //
		db.Order(request.SortType + " " + request.Sort)

	}
	//if request.Name != "" {
	//	db.Where("name like ?", "%"+request.Name+"%")
	//}
	//db.Where("gather_supply_id >= ?", 1)

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&data).Error
	return err, data, total

}

type ImportCurriculumModel struct {
	GatherSupplyId uint `json:"gather_supply_id"`
	Cate1          uint `json:"cate1"`
	Cate2          uint `json:"cate2"`
	Cate3          uint `json:"cate3"`
}

func ImportCurriculum(data []model.CurriculumList, requestData model.RequestCurriculum) {
	var gatherSupplyIDArr []int64
	source.DB().Model(model.Curriculum{}).Where("gather_supply_id=?", requestData.GatherSupplyID).Pluck("source_goods_id", &gatherSupplyIDArr)
	for _, item := range data {
		if !collection.Collect(gatherSupplyIDArr).Contains(item.ID) {
			if len(item.Product.Skus) == 0 {
				log2.Log().Error("导入课程数错误", zap.Any("无sku", item))
				continue
			}
			err := CreateGatherSupplyChapter(item, requestData)
			if err != nil {
				return
			}
		}
		//if containers.Container()
	}

}

type SourceResponse struct {
	Code int         `json:"code"`
	Data interface{} `json:"data"`
	Msg  string      `json:"msg"`
}

func InitToken(gatherSupplyID uint) (err error, header map[string]string) {
	var h = make(map[string]string)
	var ctx = context.Background()
	var token string

	token, err = source.Redis().Get(ctx, "gatherSupply"+strconv.Itoa(int(gatherSupplyID))).Result()

	if token == "" || err != nil {
		err, token = GetToken(gatherSupplyID)
		if err != nil {
			return
		}
		h["x-token"] = token

	} else {
		h["x-token"] = token
		//var result []byte
		//err, result = utils.Get(selfData.BaseInfo.Host+"/app/application/getSupplySource", h)
		//var response SourceResponse
		//err = json.Unmarshal(result, &response)
		//if err != nil {
		//	return
		//}
		//
		//if response.Code == 6 {
		//	err, token = GetToken(gatherSupplyID)
		//	if err != nil {
		//		return
		//	}
		//	h["x-token"] = token
		//
		//}
	}

	return err, h
}

type GetTokenRequest struct {
	AppKey    string `json:"app_key" validate:"required"`
	AppSecret string `json:"app_secret"  validate:"required"`
}

type BatchPageResult struct {
	List     []other.Product `json:"list"`
	Total    int64           `json:"total"`
	Page     int             `json:"page"`
	PageSize int             `json:"pageSize"`
	NextUrl  string          `json:"next_url"`
}
type TokenResponse struct {
	Code int    `json:"code"`
	Data Token  `json:"data"`
	Msg  string `json:"msg"`
}

type Token struct {
	Token  string `json:"token"`
	Expire int64  `json:"expiresAt"`
}

func InitSetting(gatherSupplyID uint) (err error, selfSupplySetting model.SelfSupplySetting) {
	var setting model3.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &selfSupplySetting)
	if err != nil {
		return
	}
	if selfSupplySetting.BaseInfo.AppKey == "" || selfSupplySetting.BaseInfo.AppSecret == "" || selfSupplySetting.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	//
	//if SelfToken == nil || SelfToken.Expire < time.Now().Unix()*1000 {
	//	var self = Self{}
	//	err = self.InitToken()
	//	if err != nil {
	//		return
	//	}
	//}
	return
}
func GetToken(gatherSupplyID uint) (err error, token string) {
	var ctx = context.Background()

	var selfSettingData model.SelfSupplySetting
	err, selfSettingData = InitSetting(gatherSupplyID)
	if err != nil {
		return
	}
	var requestParam GetTokenRequest
	requestParam.AppSecret = selfSettingData.BaseInfo.AppSecret
	requestParam.AppKey = selfSettingData.BaseInfo.AppKey
	var result []byte
	err, result = utils.Post(selfSettingData.BaseInfo.Host+"/app/application/getToken", requestParam, nil)
	var response TokenResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.Code != 0 {
		err = errors.New(response.Msg)
		return
	}
	token = response.Data.Token
	err = source.Redis().Set(ctx, "gatherSupply"+strconv.Itoa(int(gatherSupplyID)), response.Data.Token, time.Second*86400).Err()
	if err != nil {
		return
	}
	return
}

func UpdateCurriculum(requestData model.RequestCurriculum) (err error) {
	//InitGatherSupplySetting(requestData.GatherSupplyID)
	var curriculum []model.Curriculum
	err = source.DB().Where("gather_supply_id=?", requestData.GatherSupplyID).Find(&curriculum).Error
	tokenErr, header := InitToken(requestData.GatherSupplyID)
	if tokenErr != nil {
		log2.Log().Error("UpdateCurriculum InitToken err", zap.Any("err", tokenErr))
		return
	}
	for _, item := range curriculum {

		var curriculumDetail model.CurriculumDetail
		var data = make(map[string]interface{})
		data["id"] = item.SourceGoodsID

		postErr, resData := utils.Post(baseSetting.BaseInfo.Host+"/app/publicCurriculum/selectCurriculumDetail", data, header) //查询选择的供应链数据
		if postErr != nil {
			log2.Log().Error("UpdateCurriculum Post err", zap.Any("err", postErr))

			err = postErr
			return
		}
		err = json.Unmarshal(resData, &curriculumDetail)
		if err != nil {
			log2.Log().Error("UpdateCurriculum Unmarshal err", zap.Any("err", err))
			return
		}

		UpdateGatherSupplyChapter(item.LecturerID, item.ID, item.ProductID, curriculumDetail.Data)

		//url:="app/publicCurriculum/selectCurriculumDetail"
		//
		//fmt.Println(url)

	}
	return

}

func ImportGatherSupplyCurriculum(requestData model.RequestCurriculum) (err error) {

	err = InitGatherSupplySetting(requestData.GatherSupplyID)
	if err != nil {
		return
	}
	item := 1
	for item <= 999999 {
		//var headMap = make(map[string]string)
		var HttpCurriculumData model.HttpCurriculumList
		var data = make(map[string]interface{})
		data["page"] = item
		data["pageSize"] = 50

		tokenErr, header := InitToken(requestData.GatherSupplyID)
		if tokenErr != nil {
			return
		}

		//headMap["x-token"] = "33"
		postErr, resData := utils.Post(baseSetting.BaseInfo.Host+"/app/publicCurriculum/selectImportCurriculum", data, header) //查询选择的供应链数据
		if postErr != nil {
			err = postErr
			return
		}

		err = json.Unmarshal(resData, &HttpCurriculumData)
		if err != nil {
			return
		}

		if len(HttpCurriculumData.Data.List) <= 0 {
			//item = 1
			//err = errors.New("当前所选供应链暂无可导入课程数据")
			return
		}
		ImportCurriculum(HttpCurriculumData.Data.List, requestData)

		item++

	}

	return

}

var sysSetting model3.SysSetting
var baseSetting model.SelfSupplySetting

func InitGatherSupplySetting(gatherSupplyID uint) (err error) {

	gatherSupplyId := strconv.Itoa(int(gatherSupplyID))
	err = source.DB().Where("`key`=?", "gatherSupply"+gatherSupplyId).First(&sysSetting).Error
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(sysSetting.Value), &baseSetting)
	if err != nil {
		return
	}

	if baseSetting.BaseInfo.Host == "" {
		err = errors.New("请配置此供应链host")
		return
	}

	return

}

func SelectSupplyCount(requestData model.RequestCount) (err error, IsImport, NotImport int64) {

	gatherSupplyId := strconv.Itoa(int(requestData.ID))
	err = source.DB().Where("`key`=?", "gatherSupply"+gatherSupplyId).First(&sysSetting).Error
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(sysSetting.Value), &baseSetting)
	if err != nil {
		return
	}

	if baseSetting.BaseInfo.Host == "" {
		err = errors.New("请配置此供应链host")
		return
	}

	var data interface{}
	var headMap = make(map[string]string)
	var curriculumCount model.CurriculumCount
	url := baseSetting.BaseInfo.Host + "/api/publicCurriculum/selectCurriculumCount"
	postErr, resData := utils.Post(url, data, headMap)
	//postErr, resData := utils.Post("http://127.0.0.1:8888/api/publicCurriculum/selectCurriculumCount", data, headMap)
	if postErr != nil {
		err = postErr
		return
	}

	err = json.Unmarshal(resData, &curriculumCount)
	if err != nil {
		return
	}

	err = source.DB().Model(model.Curriculum{}).Where("gather_supply_id=?", requestData.ID).Count(&IsImport).Error
	if err != nil {
		return
	}
	NotImport = curriculumCount.Count - IsImport
	return

}

func SelectCurriculumCount() (err error, count int64) {

	err = source.DB().Model(model.Curriculum{}).Count(&count).Error

	return

}

func SelectCurriculumDetail(requestData model.RequestCurriculum) (err error, data model.CurriculumList) {

	err = source.DB().Where("id=?", requestData.ID).Preload("Product").Preload("Product.Skus").Preload("Lecturer").First(&data).Error

	return

}

// 根据商品id查询课程详情
func SelectCurriculumProductDetail(uid uint, requestData model.RequestCurriculum) (err error, data model.CurriculumList) {

	err = source.DB().Where("product_id=?", requestData.ID).Preload("Product").Preload("Lecturer").First(&data).Error

	var order []Order

	err = source.DB().Where("user_id=? and status=?", uid, 3).Preload("OrderItems").Find(&order).Error

	for _, item := range order {

		for _, item2 := range item.OrderItems {
			if item2.ProductID == requestData.ID {

				now := time.Now()
				limitTime := item.PaidAt
				after := limitTime.AddDate(0, 0, int(data.TimeLimit))
				days := int(after.Sub(now).Hours() / 24)
				if now.Unix() < after.Unix() {
					data.IsPurchase = 1
				} else {
					data.IsPurchase = 0
				}
				if data.IsPurchase == 1 {
					data.EndTime = strconv.Itoa(days)
					data.EndTimeString = after.Format("2006-01-02 15:04:05")
				} else {
					data.EndTime = "已过期"
				}
			}
		}

	}
	data.Product.NormalPrice = data.Product.Price
	if uid > 0 {
		var user model6.User
		err = source.DB().Where("id = ?", uid).First(&user).Error
		if err != nil {
			return
		}

		if data.Product.UserPriceSwitch == 1 {
			var calculateRes bool
			data.Product.Price, _, calculateRes = data.Product.UserPrice.GetProductLevelDiscountPrice(data.Product.Price, user.LevelID)
			// 商品没有设置该等级，使用默认折扣
			if calculateRes == false {
				var discount, isDefault int
				err, discount, isDefault = levelModel.GetLevelDiscountPercent(user.LevelID)
				if isDefault == 0 {
					err, data.Product.Price = levelModel.GetLevelDiscountAmount(data.Product.Price, data.Product.CostPrice, discount)
					if err != nil {
						return
					}
				}
			}
		} else {
			var discount, isDefault int
			err, discount, isDefault = levelModel.GetLevelDiscountPercent(user.LevelID)
			if isDefault == 0 {
				err, data.Product.Price = levelModel.GetLevelDiscountAmount(data.Product.Price, data.Product.CostPrice, discount)
				if err != nil {
					return
				}
			}
		}
	}

	var Chapter []model.Chapter
	json.Unmarshal([]byte(data.Chapter), &Chapter)
	data.ChapterCount = len(Chapter)

	//var SubsectionCount = 0
	for _, item2 := range Chapter {

		data.SubsectionCount = data.SubsectionCount + len(item2.Subsection)
		//SubsectionCount = SubsectionCount + len(item2.Subsection)
	}

	return

}

// 查询单条讲师并且关联查询课程
func FindLecturerAndCurriculum(requestData model.RequestCurriculum) (err error, data model.LecturerSearch) {

	err = source.DB().Where("id=?", requestData.ID).Preload("Curriculum").Preload("Curriculum.Product").First(&data).Error

	//统计课程下的所有章节总数

	data.ChapterCount = 0
	for index, item := range data.Curriculum {
		var Chapter []model.Chapter
		json.Unmarshal([]byte(item.Chapter), &Chapter)
		//if err != nil {
		//	return err, model.LecturerSearch{}
		//}

		var SubsectionCount = 0
		for _, item2 := range Chapter {

			data.SubsectionCount = data.SubsectionCount + len(item2.Subsection)
			SubsectionCount = SubsectionCount + len(item2.Subsection)
		}
		data.Curriculum[index].SubsectionCount = SubsectionCount
		data.Curriculum[index].ChapterCount = len(Chapter)
		data.ChapterCount = data.ChapterCount + len(Chapter)

	}

	return

}

func AddStorage(data model.CurriculumStorage) error {
	err := source.DB().Create(&data).Error
	return err

}

func DeleteStorage(data model.DeleteStorageData) error {
	err := source.DB().Where("app_id=? and product_id in (?)", data.AppID, data.Ids).Delete(&model.CurriculumStorage{}).Error
	return err

}

//给其他站点提供导入课程接口

func SelectImportCurriculum(request model.RequestCurriculum) (err error, data []model.CurriculumList, total int64) {
	limit := request.PageSize
	fmt.Println("limit", limit)
	offset := request.PageSize * (request.Page - 1)
	fmt.Println("page", request.Page)
	fmt.Println("offset", offset)

	db := source.DB().Model(&model.CurriculumList{}).Preload("Product").Preload("Product.Skus").Preload("Lecturer")

	if request.IsImport == "1" {

		var storages []int64
		//difference := collection.Collect(storages).Diff(result).ToIntArray()

		err = source.DB().Model(model.CurriculumStorage{}).Where("app_id=?", request.AppId).Pluck("product_id", &storages).Error
		if err != nil {
			return
		}
		if len(storages) > 0 {
			db.Where("id not in (?)", storages)
		}

	}
	if request.IsImport == "2" {
		var storages []int64
		err = source.DB().Model(model.CurriculumStorage{}).Where("app_id=?", request.AppId).Pluck("product_id", &storages).Error
		if err != nil {
			return
		}
		if len(storages) > 0 {
			db.Where("id  in (?)", storages)
		}

	}

	if request.LecturerID > 0 {
		db.Where("lecturer_id=?", request.LecturerID)
	}
	if request.Category1ID > 0 {
		db.Where("category1_id=?", request.Category1ID)
	}
	if request.Category2ID > 0 {
		db.Where("category2_id=?", request.Category2ID)
	}
	if request.Category3ID > 0 {
		db.Where("category3_id=?", request.Category3ID)
	}
	if request.PriceStart != nil && request.PriceEnd != nil {
		db.Where("price between ? and ?", request.PriceStart, request.PriceEnd)
	}
	//if request.CostPriceStart > 0 && request.CostPriceEnd > 0 {
	//	db.Where("cost_price between ? and ?", request.CostPriceStart, request.CostPriceEnd)
	//}
	if request.RetailPriceStart != nil && request.RetailPriceEnd != nil {
		db.Where("retail_price between ? and ?", request.RetailPriceStart, request.RetailPriceEnd)
	}
	if request.Status != nil {
		db.Where("status = ?", &request.Status)
	}
	if request.CurriculumName != "" {
		db.Where("curriculum_name like ?", "%"+request.CurriculumName+"%")
	}

	if request.ProfitStart != "" && request.ProfitEnd != "" {
		db.Where("profit between ? and ?", request.ProfitStart, request.ProfitEnd)

	}

	if request.SortType != "" { //
		db.Order(request.SortType + " " + request.Sort)
	}

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&data).Error
	return err, data, total

}

func SavaBaseSetting(request model.BaseSetting) (err error) {
	jsonData, jsonErr := json.Marshal(request)
	if jsonErr != nil {
		err = jsonErr
		return
	}
	var sysSetting model3.SysSetting
	db := source.DB().Model(&sysSetting)
	err = db.Where("`key`=?", "curriculumBaseSetting").First(&sysSetting).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		sysSetting.Key = "curriculumBaseSetting"
	}
	sysSetting.Value = string(jsonData)
	err = source.DB().Save(&sysSetting).Error

	return

}

func GetBaseSetting() (err error, data model.BaseSetting) {

	var setting model3.SysSetting

	err = source.DB().Where("`key`=?", "curriculumBaseSetting").First(&setting).Error
	if err != nil {
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &data)

	return
}

func SupplyList(request model.RequestLecturer) (err error, gatherSupplyList []model4.GatherSupply, total int64) {
	limit := request.PageSize
	offset := request.PageSize * (request.Page - 1)

	db := source.DB().Where("is_plugin=1 and category_id=2").Model(model4.GatherSupply{})

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&gatherSupplyList).Error

	for k, v := range gatherSupplyList {
		//gatherSupplyList[k].Category = service2.GetGatherSupplyCategory(v.CategoryID)
		var goodsCounts, orderCounts, amountCounts int64

		err = source.DB().Model(model4.SupplyGoods{}).Where("gather_supply_id = ?", v.ID).Count(&goodsCounts).Error
		err = source.DB().Model(model2.Order{}).Where("gather_supply_id = ?", v.ID).Count(&orderCounts).Error
		err = source.DB().Model(model2.Order{}).Where("gather_supply_id = ?", v.ID).Pluck("COALESCE(SUM(amount), 0) as amount1", &amountCounts).Error
		gatherSupplyList[k].GoodsCount = uint(goodsCounts)
		gatherSupplyList[k].OrderAmount = uint(amountCounts)
		gatherSupplyList[k].OrderCount = uint(orderCounts)
		//if v.CategoryID > 0 {
		//	err, gatherSupplyList[k].SupplyBalance = GetSupplyBalance(v.ID)
		//	if err != nil {
		//		err = nil
		//		continue
		//	}
		//}

	}
	return

}

// 查询讲师分成列表
func FindLecturerAward(request model.RequestLecturer) (err error, data []model.LecturerDivided, total int64, TotalOrderAmount, TotalAmount, Settled, Unsettled uint) {

	limit := request.PageSize
	offset := request.PageSize * (request.Page - 1)
	db := source.DB().Model(&model.LecturerDivided{}).Preload("Lecturer").Preload("Lecturer.User").Preload("Order").Preload("Curriculum")
	if request.Uid > 0 {
		var Lecturer model.Lecturer
		source.DB().Where("uid=?", request.Uid).First(&Lecturer)
		if err == nil {
			db.Where("lecturer_id=?", Lecturer.ID)
		}
	}
	if request.Mobile != "" {
		var user model6.User
		source.DB().Where("username =?", request.Mobile).First(&user)
		var Lecturer model.Lecturer
		source.DB().Where("uid=?", user.ID).First(&Lecturer)
		if Lecturer.ID > 0 {
			db.Where("lecturer_id=?", Lecturer.ID)
		}
	}
	if request.LecturerName != "" {

		var ids []int64
		source.DB().Model(model.Lecturer{}).Where("lecturer_name like ?", "%"+request.LecturerName+"%").Pluck("id", &ids)
		db.Where("lecturer_id in (?)", ids)
	}
	if request.Status != "" {
		db.Where("status =?", request.Status)
	}
	if request.CurriculumName != "" {
		var Curriculum model.Curriculum
		source.DB().Where("curriculum_name like ?", "%"+request.CurriculumName+"%").First(&Curriculum)
		if Curriculum.ID > 0 {
			db.Where("curriculum_id =?", Curriculum.ID)
		}
	}
	if request.CurriculumID > 0 {
		db.Where("curriculum_id =?", request.CurriculumID)
	}
	if request.OrderSN != "" {
		db.Where("order_sn =?", request.OrderSN)
	}
	//db.Where("gather_supply_id >= ?", 1)

	//var aaa uint

	var dbUnsettled, dbSettled, dbTotalAmount model.LecturerDivided
	source.DB().Where("status=0").Select("sum(amount) as amount").First(&dbUnsettled)
	source.DB().Select("sum(amount) as amount").First(&dbTotalAmount)

	//统计当前奖励的 订单金额
	var orderLecturerDivided []int64
	var order order2.Order
	source.DB().Model(model.LecturerDivided{}).Pluck("order_sn", &orderLecturerDivided)
	source.DB().Where("order_sn in (?)", orderLecturerDivided).Select("sum(amount) as amount").First(&order)
	TotalOrderAmount = order.Amount
	TotalAmount = dbTotalAmount.Amount
	Unsettled = dbUnsettled.Amount
	source.DB().Where("status=1").Select("sum(amount) as amount").First(&dbSettled)

	Settled = dbSettled.Amount

	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&data).Error
	return

}

func TryCurriculum(reqData model.RequestTryCurriculum) (err error, data interface{}) {

	var curriculum model.Curriculum
	err = source.DB().Where("product_id=?", reqData.ProductID).First(&curriculum).Error
	if err != nil {
		return
	}
	var Chapter []model.Chapter
	err = json.Unmarshal([]byte(curriculum.Chapter), &Chapter)
	if err != nil {
		fmt.Println(err)
		return
	}
	for _, item := range Chapter {
		//Cid := strconv.Itoa(int(item.ID))
		if item.ID == reqData.ChapterID {
			for _, sitem := range item.Subsection {
				//Sid := strconv.Itoa(int(sitem.ID))
				if sitem.ID == reqData.SubsectionID {
					data = sitem
					return
				}
			}
		}
	}

	return
}

func GetKey() (err error, data string) {
	var curriculumBaseSetting model.BaseSetting
	var sysSetting model3.SysSetting
	err = source.DB().Where("`key`=?", "curriculumBaseSetting").First(&sysSetting).Error
	if err != nil {
		return
	}

	err = json.Unmarshal([]byte(sysSetting.Value), &curriculumBaseSetting)
	if err != nil {
		return
	}

	data = curriculumBaseSetting.Key
	return

}

type UpLevelUrlData struct {
	Code int              `json:"code"`
	Data model.Subsection `json:"data"`
	Msg  string           `json:"msg"`
}

func HigherLevelKey(requestData model.RequestTryCurriculum, reqData model.Curriculum) (err error, data model.Subsection) {

	var Curriculum model.Curriculum
	err = source.DB().Where("product_id=?", reqData.ProductID).Preload("Product").First(&Curriculum).Error
	if err != nil {
		return
	}

	//查询当前课程的供应链设置
	err = InitGatherSupplySetting(Curriculum.GatherSupplyID)
	if err != nil {
		log2.Log().Error("初始化课程供应链错误", zap.Any("err", err))
		return
	}

	//查询当前课程的供应链设置

	var headMap = make(map[string]string)
	var postData = make(map[string]interface{})
	postData["order_sn"] = requestData.OrderSN
	postData["product_id"] = Curriculum.Product.SourceGoodsID
	postData["chapter_id"] = requestData.ChapterID
	postData["subsection_id"] = requestData.SubsectionID
	var tokenErr error
	tokenErr, headMap = InitToken(Curriculum.GatherSupplyID)
	if tokenErr != nil {
		return
	}
	postErr, resData := utils.Post(baseSetting.BaseInfo.Host+"/app/publicCurriculum/getVideoUrl", postData, headMap) //查询选择的供应链数据
	if postErr != nil {
		err = postErr
		return
	}

	var upLevelUrlData UpLevelUrlData
	err = json.Unmarshal(resData, &upLevelUrlData)

	data = upLevelUrlData.Data
	if err != nil {
		log2.Log().Error("请求返回解析出错")
		return
	}

	return

}

func GetVideoUrl(reqData model.RequestTryCurriculum) (err error, data model.Subsection) {

	log2.Log().Info("课程接到url 获取请求", zap.Any("info", reqData))

	var Curriculum model.Curriculum
	err = source.DB().Where("product_id=?", reqData.ProductID).Preload("Product").First(&Curriculum).Error
	if err != nil {
		return
	}

	var Chapter []model.Chapter
	err = json.Unmarshal([]byte(Curriculum.Chapter), &Chapter)
	if err != nil {
		log2.Log().Error("GetVideoUrl 解析章节错误", zap.Any("info", err))
		return
	}

	for _, item := range Chapter {
		if item.ID == reqData.ChapterID {
			for _, sitem := range item.Subsection {
				if sitem.ID == reqData.SubsectionID {

					//var order model5.Order
					//err = source.DB().Where("status >=1  and third_order_sn=?", reqData.OrderSN).First(&order).Error
					//if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
					if reqData.OrderSN == "" {
						if Curriculum.GatherSupplyID > 0 { //没购买过的三方导入的课程，去上游请求是否试看
							log2.Log().Info("没买过的课程去三方请求", zap.Any("info", reqData), zap.Any("info", Curriculum))
							//OrderSN := strconv.Itoa(int(order.OrderSN))
							//reqData.OrderSN = OrderSN
							HigherLevelKeyErr, upUrl := HigherLevelKey(reqData, Curriculum)
							if HigherLevelKeyErr != nil {
								return
							}
							sitem.Url = upUrl.Url
							log2.Log().Info("没买过的课程去三方请求-生成url", zap.Any("info", sitem.Url))

						} else { //  没买过的本地课程
							log2.Log().Info("没买过的课程本地课程直接生成url", zap.Any("info", reqData), zap.Any("info", Curriculum))
							GetKeyErr, key := GetKey()
							log2.Log().Info("没买过的课程本地key", zap.Any("info", key))

							if GetKeyErr != nil {
								log2.Log().Info("没买过的课程本地key错误", zap.Any("info", GetKeyErr))

								err = GetKeyErr
								return
							}
							err, sitem.Url = ParseUrl(sitem.Url, key, sitem.TryTime)
							log2.Log().Info("没买过的课程本地课程直接生成url-", zap.Any("info", sitem.Url))

						}

					} else { //买过

						var order model5.Order
						err = source.DB().Where("status >=1  and third_order_sn=?", reqData.OrderSN).First(&order).Error
						if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
							log2.Log().Error("买过的课程 中台没查到", zap.Any("info", reqData))
							return
						}

						if Curriculum.GatherSupplyID > 0 { //购买过的三方导入的课程，去上游请求是否试看
							log2.Log().Info("买过的课程 三方课程去上游请求", zap.Any("info", reqData), zap.Any("info", Curriculum))

							OrderSN := strconv.Itoa(int(order.OrderSN))
							reqData.OrderSN = OrderSN
							HigherLevelKeyErr, upUrl := HigherLevelKey(reqData, Curriculum)
							if HigherLevelKeyErr != nil {
								return
							}
							sitem.Url = upUrl.Url
							log2.Log().Info("买过的课程去三方课程请求生成url-", zap.Any("info", upUrl))

						} else { //  买过的本地课程
							log2.Log().Info("买过的课程本地课程请求", zap.Any("info", reqData), zap.Any("info", Curriculum))

							GetKeyErr, key := GetKey()
							log2.Log().Info("买过的课程本地key", zap.Any("info", key))

							if GetKeyErr != nil {
								log2.Log().Info("买过的课程本地key错误", zap.Any("info", GetKeyErr))
								err = GetKeyErr
								return
							}
							err, sitem.Url = UnrestrictedParseUrl(sitem.Url, key)

							if err != nil {
								log2.Log().Error("UnrestrictedParseUrl err ", zap.Any("err", err))
							}

							if Curriculum.SalesModel == 2 { // 如果有期限限制，检测 买过的 课程订单是否过期
								var orderDate model5.Order
								err = source.DB().Where("third_order_sn=?", reqData.OrderSN).Where("SYSDATE() < DATE_ADD(`paid_at`, INTERVAL ? DAY)", Curriculum.TimeLimit).First(&orderDate).Error
								if err != nil && errors.Is(err, gorm.ErrRecordNotFound) { //过期
									log2.Log().Error("买过的课程  已过期", zap.Any("info", reqData), zap.Any("err", Curriculum.TimeLimit))
									err, sitem.Url = ParseUrl(sitem.Url, key, sitem.TryTime)
								}
							}
							log2.Log().Info("买过的课程本地生成url-", zap.Any("info", sitem.Url))
						}

					}

					data = sitem
					return
				}
			}
		}
	}

	return
}

func VideoUrl(reqData model.RequestTryCurriculum) (err error, data model.Subsection) {

	log2.Log().Info("课程接到url 获取请求", zap.Any("info", reqData))

	var Curriculum model.Curriculum
	err = source.DB().Where("product_id=?", reqData.ProductID).Preload("Product").First(&Curriculum).Error
	if err != nil {
		return
	}

	var checkOrder order2.Order
	var IsPurchase int
	//查询订单是否包含当前商品
	err = source.DB().Debug().Where("orders.user_id=? and orders.status >=1", reqData.Uid).Preload("OrderItems").Joins("left join order_items on order_items.order_id = orders.id").Where("order_items.product_id = ?", reqData.ProductID).First(&checkOrder).Error

	if checkOrder.ID > 0 {
		IsPurchase = 1
	}

	var Chapter []model.Chapter
	err = json.Unmarshal([]byte(Curriculum.Chapter), &Chapter)
	if err != nil {
		log2.Log().Error("GetVideoUrl 解析章节错误", zap.Any("info", err))
		return
	}

	for _, item := range Chapter {
		if item.ID == reqData.ChapterID {
			for _, sitem := range item.Subsection {
				if sitem.ID == reqData.SubsectionID {

					if IsPurchase == 0 {
						log2.Log().Info("没买过的课程本地课程直接生成url", zap.Any("info", reqData), zap.Any("info", Curriculum))
						GetKeyErr, key := GetKey()
						log2.Log().Info("没买过的课程本地key", zap.Any("info", key))
						if GetKeyErr != nil {
							log2.Log().Info("没买过的课程本地key错误", zap.Any("info", GetKeyErr))
							err = GetKeyErr
							return
						}
						err, sitem.Url = ParseUrl(sitem.Url, key, sitem.TryTime)
						log2.Log().Info("没买过的课程本地课程直接生成url-", zap.Any("info", sitem.Url))

					} else { //买过

						log2.Log().Info("买过的课程本地课程请求", zap.Any("info", reqData), zap.Any("info", Curriculum))

						GetKeyErr, key := GetKey()
						log2.Log().Info("买过的课程本地key", zap.Any("info", key))

						if GetKeyErr != nil {
							log2.Log().Info("买过的课程本地key错误", zap.Any("info", GetKeyErr))
							err = GetKeyErr
							return
						}
						err, sitem.Url = UnrestrictedParseUrl(sitem.Url, key)

						if err != nil {
							log2.Log().Error("UnrestrictedParseUrl err ", zap.Any("err", err))
						}

						if Curriculum.SalesModel == 2 { // 如果有期限限制，检测 买过的 课程订单是否过期
							var orderDate model5.Order
							err = source.DB().Where("id=?", checkOrder.ID).Where("SYSDATE() < DATE_ADD(`paid_at`, INTERVAL ? DAY)", Curriculum.TimeLimit).First(&orderDate).Error
							if err != nil && errors.Is(err, gorm.ErrRecordNotFound) { //过期
								log2.Log().Error("买过的课程  已过期", zap.Any("info", reqData), zap.Any("err", Curriculum.TimeLimit))
								err, sitem.Url = ParseUrl(sitem.Url, key, sitem.TryTime)
							}
						}
						log2.Log().Info("买过的课程本地生成url-", zap.Any("info", sitem.Url))

					}

					data = sitem
					return
				}
			}
		}
	}

	return
}

func ParseUrl(reqUrl, reqKey string, tryTime uint) (err error, data string) {
	//reqData.ProductID

	//url := "https://1307062653.vod2.myqcloud.com/9ee3fe03vodcq1307062653/a61e5f7c243791579881226265/FA9EmKlYfC8A.mp4"

	url := reqUrl
	//key := "HNAHdpyTuijQQrq6pjKZ"
	key := reqKey

	tAfter, _ := time.ParseDuration("1h")
	exper := strconv.Itoa(int(tryTime * 60))
	now := time.Now()
	fmt.Println("当前时间", now.Unix(), now)
	addNow := now.Add(tAfter)
	fmt.Println("增加时间", addNow.Unix(), addNow)
	t := strconv.FormatInt(addNow.Unix(), 16)
	us := t
	fmt.Println(us)

	flowsRegexp := regexp.MustCompile(`^((http[s]?|ftp):\/)?\/?([^:\/\s]+)((\/\w+)*\/)([\w\-\.]+[^#?\s]+)(.*)?(#[\w\-]+)?$`)
	params := flowsRegexp.FindStringSubmatch(url)
	if len(params) < 4 {
		err = errors.New("解析url错误")
		return
	}
	fmt.Println(params[4])

	dir := params[4]

	sign := utils.MD5V([]byte(key + dir + t + exper + us))

	//fmt.Println(sign)
	//
	//fmt.Println(url)

	newUrl := url + "?t=" + t + "&exper=" + exper + "&us=" + us + "&sign=" + sign

	fmt.Println("生成的url", newUrl)

	data = newUrl

	return

}

func UnrestrictedParseUrl(reqUrl, reqKey string) (err error, data string) {

	url := reqUrl
	//key := "HNAHdpyTuijQQrq6pjKZ"
	key := reqKey

	tAfter, _ := time.ParseDuration("1h")
	exper := strconv.Itoa(0)
	now := time.Now()
	fmt.Println("当前时间", now.Unix(), now)
	addNow := now.Add(tAfter)
	fmt.Println("增加时间", addNow.Unix(), addNow)
	t := strconv.FormatInt(addNow.Unix(), 16)
	us := t
	fmt.Println(us)

	flowsRegexp := regexp.MustCompile(`^((http[s]?|ftp):\/)?\/?([^:\/\s]+)((\/\w+)*\/)([\w\-\.]+[^#?\s]+)(.*)?(#[\w\-]+)?$`)
	params := flowsRegexp.FindStringSubmatch(url)
	if len(params) < 4 {
		err = errors.New("解析url错误")
		return
	}
	fmt.Println(params[4])

	dir := params[4]

	sign := utils.MD5V([]byte(key + dir + t + exper + us))

	//fmt.Println(sign)
	//
	//fmt.Println(url)

	newUrl := url + "?t=" + t + "&exper=" + exper + "&us=" + us + "&sign=" + sign

	fmt.Println("生成的url", newUrl)

	data = newUrl

	return

}

//
//@function: GetOrderInfoList
//@description: 分页获取Order记录
//@param: info request.OrderSearch
//@return: err error, list interface{}, total int64

// 如果含有time.Time 请自行import time包
type Application struct {
	source.Model

	MemberId int `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`

	User model6.User `json:"user" gorm:"foreignKey:MemberId;references:ID"`
}

func (Application) TableName() string {
	return "application"
}

type OrderItem struct {
	model5.OrderItem
	Curriculum model.Curriculum `json:"curriculum" gorm:"foreignKey:ProductID;references:ProductID"`
}
type Order struct {
	response.Order

	OrderItemsCurriculum OrderItem `json:"order_items_curriculum"`

	Application Application `json:"application_user"`
}

func GetOrderInfoList(info request.OrderAdminSearch) (err error, list []Order, total int64, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	order := Order{}
	db := source.DB().Model(&order).Preload(clause.Associations).Preload("OrderItems.AfterSales").Preload("Application.User").Preload("OrderItemsCurriculum.Curriculum").Preload("OrderItemsCurriculum.Curriculum.Lecturer")

	db.Where("plugin_id=?", info.PluginID)

	if info.CurriculumName != "" {

		var curriculumIds, orderIds []int64
		source.DB().Model(model.Curriculum{}).Where("curriculum_name like ?", "%"+info.CurriculumName+"%").Pluck("product_id", &curriculumIds)
		source.DB().Model(model5.OrderItem{}).Where("product_id in (?)", curriculumIds).Pluck("order_id", &orderIds)

		db.Where("id in (?)", orderIds)
	}

	//db.Where("gather_supply_type!=?", 8)
	// 如果有条件搜索 下方会自动创建搜索语句
	var WaitPayNumDb = source.DB().Model(&order)
	WaitPayNumDb.Where("plugin_id=?", info.PluginID)

	var WaitSendNumDb = source.DB().Model(&order)
	WaitSendNumDb.Where("plugin_id=?", info.PluginID)

	var WaitReceiveNumDb = source.DB().Model(&order)
	WaitReceiveNumDb.Where("plugin_id=?", info.PluginID)

	var CompletedNumDb = source.DB().Model(&order)
	CompletedNumDb.Where("plugin_id=?", info.PluginID)

	var ClosedNumDb = source.DB().Model(&order)
	ClosedNumDb.Where("plugin_id=?", info.PluginID)

	var BackNumDb = source.DB().Model(&order)
	BackNumDb.Where("plugin_id=?", info.PluginID)

	var RefundNumDb = source.DB().Model(&order)
	RefundNumDb.Where("plugin_id=?", info.PluginID)

	if info.LecturerID > 0 {
		var Lecturer []int64
		source.DB().Model(model.LecturerDivided{}).Where("lecturer_id=?", info.LecturerID).Pluck("order_sn", &Lecturer)

		if len(Lecturer) > 0 {
			db.Where("order_sn in (?)", Lecturer)

			WaitPayNumDb.Where("order_sn in (?)", Lecturer)
			WaitSendNumDb.Where("order_sn in (?)", Lecturer)
			WaitReceiveNumDb.Where("order_sn in (?)", Lecturer)
			CompletedNumDb.Where("order_sn in (?)", Lecturer)
			ClosedNumDb.Where("order_sn in (?)", Lecturer)
			BackNumDb.Where("order_sn in (?)", Lecturer)
			RefundNumDb.Where("order_sn in (?)", Lecturer)

		}
	}

	if info.SupplierID != nil {
		if *info.SupplierID == 999999 {
			db.Where("`supplier_id` > 0")
			WaitPayNumDb.Where("`supplier_id` > 0")
			WaitSendNumDb.Where("`supplier_id` > 0")
			WaitReceiveNumDb.Where("`supplier_id` > 0")
			CompletedNumDb.Where("`supplier_id` > 0")
			ClosedNumDb.Where("`supplier_id` > 0")
			BackNumDb.Where("`supplier_id` > 0")
			RefundNumDb.Where("`supplier_id` > 0")
		} else {
			db.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			WaitPayNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			WaitSendNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			WaitReceiveNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			CompletedNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			ClosedNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			BackNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
			RefundNumDb.Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0")
		}
	}

	if info.ApplicationID > 0 {
		db.Where("`application_id` = ?", info.ApplicationID)
		WaitPayNumDb.Where("`application_id` = ?", info.ApplicationID)
		WaitSendNumDb.Where("`application_id` = ?", info.ApplicationID)
		WaitReceiveNumDb.Where("`application_id` = ?", info.ApplicationID)
		CompletedNumDb.Where("`application_id` = ?", info.ApplicationID)
		ClosedNumDb.Where("`application_id` = ?", info.ApplicationID)
		BackNumDb.Where("`application_id` = ?", info.ApplicationID)
		RefundNumDb.Where("`application_id` = ?", info.ApplicationID)
	}

	// 福禄供应链
	var supply service.GatherSupply
	err = source.DB().Unscoped().Where("category_id = ?", 98).First(&supply).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("查询供应链失败")
		return
	}
	// 指定供应链
	if info.GatherSupplierID != nil {
		db.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		WaitPayNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		WaitSendNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		WaitReceiveNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		CompletedNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		ClosedNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		BackNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
		RefundNumDb.Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0")
	} else {
		if supply.ID != 0 {
			db.Where("gather_supply_id != ?", supply.ID)
			WaitPayNumDb.Where("gather_supply_id != ?", supply.ID)
			WaitSendNumDb.Where("gather_supply_id != ?", supply.ID)
			WaitReceiveNumDb.Where("gather_supply_id != ?", supply.ID)
			CompletedNumDb.Where("gather_supply_id != ?", supply.ID)
			ClosedNumDb.Where("gather_supply_id != ?", supply.ID)
			BackNumDb.Where("gather_supply_id != ?", supply.ID)
			RefundNumDb.Where("gather_supply_id != ?", supply.ID)
		}
	}

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`pay_info_id` in ?", payInfoIds)
		WaitPayNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitSendNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitReceiveNumDb.Where("`pay_info_id` in ?", payInfoIds)
		CompletedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		ClosedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		BackNumDb.Where("`pay_info_id` in ?", payInfoIds)
		RefundNumDb.Where("`pay_info_id` in ?", payInfoIds)
	}
	if info.PayTypeID != 0 {
		db.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitPayNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitSendNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitReceiveNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		CompletedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		ClosedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		BackNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		RefundNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
	}
	if info.UserID > 0 {
		db.Where("`user_id` = ?", info.UserID)
		WaitPayNumDb.Where("`user_id` = ?", info.UserID)
		WaitSendNumDb.Where("`user_id` = ?", info.UserID)
		WaitReceiveNumDb.Where("`user_id` = ?", info.UserID)
		CompletedNumDb.Where("`user_id` = ?", info.UserID)
		ClosedNumDb.Where("`user_id` = ?", info.UserID)
		BackNumDb.Where("`user_id` = ?", info.UserID)
		RefundNumDb.Where("`user_id` = ?", info.UserID)
	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		db.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitPayNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitSendNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitReceiveNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		CompletedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		ClosedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		BackNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		RefundNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
	}
	if info.Status != nil {
		if *info.Status == 5 {
			db.Where("`refund_status` = ?", model5.Refunding)
			WaitPayNumDb.Where("`refund_status` = ?", model5.Refunding)
			WaitSendNumDb.Where("`refund_status` = ?", model5.Refunding)
			WaitReceiveNumDb.Where("`refund_status` = ?", model5.Refunding)
			CompletedNumDb.Where("`refund_status` = ?", model5.Refunding)
			ClosedNumDb.Where("`refund_status` = ?", model5.Refunding)
			BackNumDb.Where("`refund_status` = ?", model5.Refunding)
			RefundNumDb.Where("`refund_status` = ?", model5.Refunding)
		} else if *info.Status == 6 {
			db.Where("`refund_status` = ?", model5.RefundComplete)
			WaitPayNumDb.Where("`refund_status` = ?", model5.RefundComplete)
			WaitSendNumDb.Where("`refund_status` = ?", model5.RefundComplete)
			WaitReceiveNumDb.Where("`refund_status` = ?", model5.RefundComplete)
			CompletedNumDb.Where("`refund_status` = ?", model5.RefundComplete)
			ClosedNumDb.Where("`refund_status` = ?", model5.RefundComplete)
			BackNumDb.Where("`refund_status` = ?", model5.RefundComplete)
			RefundNumDb.Where("`refund_status` = ?", model5.RefundComplete)
		} else {
			db.Where("`status` = ?", info.Status)

		}
	}
	if info.RefundStatus != nil {
		db.Where("`refund_status` = ?", info.RefundStatus)
		WaitPayNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitSendNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitReceiveNumDb.Where("`refund_status` = ?", info.RefundStatus)
		CompletedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		ClosedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		BackNumDb.Where("`refund_status` = ?", info.RefundStatus)
		RefundNumDb.Where("`refund_status` = ?", info.RefundStatus)
	}
	if info.OrderSN != "" {
		db.Where("`order_sn` = ?", info.OrderSN)
		WaitPayNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitSendNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitReceiveNumDb.Where("`order_sn` = ?", info.OrderSN)
		CompletedNumDb.Where("`order_sn` = ?", info.OrderSN)
		ClosedNumDb.Where("`order_sn` = ?", info.OrderSN)
		BackNumDb.Where("`order_sn` = ?", info.OrderSN)
		RefundNumDb.Where("`order_sn` = ?", info.OrderSN)
	}
	if info.SupplySN != "" {
		db.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		WaitPayNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		WaitSendNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		WaitReceiveNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		CompletedNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		ClosedNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		BackNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		RefundNumDb.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
	}
	if info.ThirdOrderSN != "" {
		db.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		WaitPayNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		WaitSendNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		WaitReceiveNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		CompletedNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		ClosedNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		BackNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		RefundNumDb.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
	}
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitPayNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitSendNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitReceiveNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		CompletedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		ClosedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		BackNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		RefundNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitPayNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitSendNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitReceiveNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		CompletedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		ClosedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		BackNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		RefundNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(model5.OrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)

	}

	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.User{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`user_id` in ?", userIds)
		WaitPayNumDb.Where("`user_id` in ?", userIds)
		WaitSendNumDb.Where("`user_id` in ?", userIds)
		WaitReceiveNumDb.Where("`user_id` in ?", userIds)
		CompletedNumDb.Where("`user_id` in ?", userIds)
		ClosedNumDb.Where("`user_id` in ?", userIds)
		BackNumDb.Where("`user_id` in ?", userIds)
		RefundNumDb.Where("`user_id` in ?", userIds)

	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model5.ShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`shipping_address_id` in ?", shippingIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", shippingIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		BackNumDb.Where("`shipping_address_id` in ?", shippingIds)
		RefundNumDb.Where("`shipping_address_id` in ?", shippingIds)

	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(model5.ShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`shipping_address_id` in ?", userIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", userIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", userIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", userIds)
		BackNumDb.Where("`shipping_address_id` in ?", userIds)
		RefundNumDb.Where("`shipping_address_id` in ?", userIds)

	}

	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(model5.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)

	}

	//if info.CloudOrderId != 0 {
	//	var orderIds []uint
	//	source.DB().Model(&CloudOrderItem{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds)
	//	db.Where("id in ?", orderIds)
	//	WaitPayNumDb.Where("`id` in ?", orderIds)
	//	WaitSendNumDb.Where("`id` in ?", orderIds)
	//	WaitReceiveNumDb.Where("`id` in ?", orderIds)
	//	CompletedNumDb.Where("`id` in ?", orderIds)
	//	ClosedNumDb.Where("`id` in ?", orderIds)
	//	BackNumDb.Where("`id` in ?", orderIds)
	//	RefundNumDb.Where("`id` in ?", orderIds)
	//}

	err = db.Count(&total).Error
	if info.Status != nil && *info.Status == model5.WaitReceive {
		db.Order("sent_at DESC")
	} else {
		db.Order("created_at DESC")
	}
	err = db.Limit(limit).Offset(offset).Find(&list).Error
	err = WaitPayNumDb.Where("status = ?", model5.WaitPay).Count(&WaitPayNum).Error
	err = WaitSendNumDb.Where("status = ?", model5.WaitSend).Count(&WaitSendNum).Error
	err = WaitReceiveNumDb.Where("status = ?", model5.WaitReceive).Count(&WaitReceiveNum).Error
	err = CompletedNumDb.Where("status = ?", model5.Completed).Count(&CompletedNum).Error
	err = ClosedNumDb.Where("status = ?", model5.Closed).Count(&ClosedNum).Error
	err = BackNumDb.Where("refund_status = ?", model5.Refunding).Count(&BackNum).Error
	err = RefundNumDb.Where("refund_status = ?", model5.RefundComplete).Count(&RefundNum).Error
	return err, list, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum
}

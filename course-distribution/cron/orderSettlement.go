package cron

import (
	model2 "course-distribution/model"
	"encoding/json"
	"finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	log2 "yz-go/component/log"
	"yz-go/cron"
	model3 "yz-go/model"
	"yz-go/source"
)

func CourseOrderSettlementHandle() {

	cronStr := "0 */10 * * * ?"

	cron.PushTask(cron.Task{
		Key:  "CourseOrderSettlementHandle",
		Name: "CourseOrderSettlementHandle定时更新",
		Spec: cronStr, //"0/3 * * * * *"
		Handle: func(task cron.Task) {
			OrderSettlement()

		},
		Status: cron.ENABLED,
	})

}

func OrderSettlement() (err error) {

	var sysSetting model3.SysSetting
	err = source.DB().Where("`key`=?", "curriculumBaseSetting").First(&sysSetting).Error
	if err != nil {
		log2.Log().Error("课程定时任务结算错误", zap.Any("err", err))
		return
	}

	var BaseSetting model2.BaseSetting

	err = json.Unmarshal([]byte(sysSetting.Value), &BaseSetting)
	if err != nil {
		log2.Log().Error("课程定时任务json解析 设置错误", zap.Any("err", err))
		return
	}

	if BaseSetting.SettlementPeriod < 0 {
		return
	}

	var order []model2.LecturerDivided
	err = source.DB().Where("status=?", 0).Preload("Order").Where("created_at < DATE_SUB(SYSDATE(), INTERVAL ? DAY)", BaseSetting.SettlementPeriod).Preload("Lecturer").Preload("Lecturer.User").Find(&order).Error
	if err != nil {
		return
	}
	log2.Log().Info("课程定时任务结算数据-天", zap.Any("err", BaseSetting.SettlementPeriod))

	for _, item := range order {
		log2.Log().Info("课程定时任务结算数据", zap.Any("err", item))
		if item.Order.Status >= 3 {
			Settlement(item)
		}
	}

	return
}

func Settlement(order model2.LecturerDivided) (err error) {

	var lecturerDivided model2.LecturerDivided

	lecturerDivided.Status = 1

	err = source.DB().Where("order_sn=?", order.OrderSN).Updates(&lecturerDivided).Error
	if err != nil {
		log2.Log().Error("更新课程，讲师奖励发放", zap.Any("err", err))
		return
	}

	var income model.UserIncomeDetails
	income.UserID = int(order.Lecturer.Uid)
	income.OrderSn = int(order.OrderSN)
	income.IncomeType = model.Course
	income.Amount = order.Amount
	err = incomeService.IncreaseIncome(income)
	if err != nil {
		log2.Log().Error("讲师分成出现错误", zap.Any("err", err))
		return
	}
	return

}

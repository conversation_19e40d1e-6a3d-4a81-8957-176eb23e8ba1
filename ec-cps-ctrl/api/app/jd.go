package app

import (
	"ec-cps-ctrl/service"
	"github.com/gin-gonic/gin"
	"net/http"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func GoodsCategory(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.GoodsCategory(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func QueryJingfenGoods(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}

	if err, data := service.QueryJingfenGoods(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func QueryGoods(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.QueryGoods(request, utils.GetAppUserID(c)); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func GetJdSkuid(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.GetJdSkuid(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func ItemDetail(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.ItemDetail(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func MaterialQuery(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.MaterialQuery(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func ActivityQuery(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.ActivityQuery(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func OrderDetails2(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.OrderDetails2(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func UrlPrivilege(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	request["parent_app_id"] = utils.GetAppID(c)
	if err, data := service.UrlPrivilege(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

func ByUnionidPromotion(c *gin.Context) {
	var request map[string]interface{}
	err := c.ShouldBindJSON(&request)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, data := service.ByUnionidPromotion(request); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.SignResponse(data, c)
		c.JSON(http.StatusOK, data)
	}

}

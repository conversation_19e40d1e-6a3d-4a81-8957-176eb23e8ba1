package setting

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
)

type SysSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}
type Value struct {
	ShopName                     string               `json:"shop_name"`
	ShopLogo                     string               `json:"shop_logo"`
	ShopLogoSquare               string               `json:"shop_logo_square"` //商城logo 方形
	AdminShopLogo                string               `json:"admin_shop_logo"`
	AdminLoginLogo               string               `json:"admin_login_logo"`
	AdminLoginTitle              string               `json:"admin_login_title"`
	AdminLoginBackground         string               `json:"admin_login_background"`
	ShopIcon                     string               `json:"shop_icon"`
	ShopLogoBottom               string               `json:"shop_logo_bottom"`
	ShopQrCode                   string               `json:"shop_qr_code"`
	ShopAppCode                  string               `json:"shop_app_code"`
	Copyright                    string               `json:"copyright"`
	Address                      string               `json:"address"`
	AddressAdmin                 string               `json:"address_admin"`
	FooterContent                string               `json:"footer_content"`                  //商城底部信息（富文本）
	ServiceLink                  string               `json:"service_link"`                    //客服链接
	ServiceCode                  string               `json:"service_code"`                    //客服图片
	ShopStoreLicenses            model.JsonStringList `json:"ShopStoreLicenses"`               //营业执照
	ShopAnnouncement             string               `json:"shop_announcement"`               //店铺公告
	ShopStoreReturnsInstructions string               `json:"shop_store_returns_instructions"` //退换货说明\
	WxMiniServiceType            int                  `json:"wx_mini_service_type"`            //小程序客服类型 1第三方 2客服二维码
	WxMiniAppId                  string               `json:"wx_mini_app_id"`                  //小程序APPID
	WxMiniServiceUrl             string               `json:"wx_mini_service_url"`             //小程序客服路径
	WxMiniServiceCode            string               `json:"wx_mini_service_code"`            //小程序客服二维码
	MobileLogo                   string               `json:"mobile_logo"`
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

var shopSetting *Value

func GetSysSetting(key string) (err error, sysSetting SysSetting) {
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}
func Get() (err error, setting Value) {
	if shopSetting == nil {
		var sysSetting SysSetting
		err, sysSetting = GetSysSetting("shop_setting")
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		shopSetting = &sysSetting.Value
	}
	return err, *shopSetting
}

func Reset() {
	//重置全局变量 start
	shopSetting = nil
}

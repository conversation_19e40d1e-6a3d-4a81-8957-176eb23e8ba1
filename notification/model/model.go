package model

import (
	"yz-go/source"
)

type PurchasingBalance struct {
	source.Model

	OrderID      string `json:"order_id" form:"order_id" gorm:"column:order_id;comment:支付订单号;type:varchar(100);size:100;"`
	Uid          uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	Amount       uint   `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	BusinessType int    `json:"business_type" form:"business_type" gorm:"column:business_type;comment:业务类型(1采购2api采购3充值);type:int;"`
	Status       int    `json:"status" form:"status" gorm:"column:status;comment:状态(0消息未通知，1消息已通知);type:int;"`
	Balance      uint   `json:"balance" form:"balance" gorm:"column:balance;comment:余额;type:int;"`
	PayType      int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type;comment:充值方式(1汇聚2平台余额);type:smallint;size:1;"`
	PaySN        uint   `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:充值sn;"`
	User         User   `json:"user" gorm:"foreignKey:Uid"`
	Remarks      string `json:"remarks" form:"remarks"`
	IsPlugin     int    `json:"is_plugin" form:"is_plugin"`
	AccountId    int    `json:"account_id" form:"account_id"`
	AccountName  string `json:"account_name" form:"account_name" `
}

type User struct {
	source.Model
	Mobile             string         `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar             string         `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username           string         `json:"username"  form:"username" gorm:"comment:用户登录名"`
	Password           string         `json:"-"  gorm:"comment:用户登录密码"`
	NickName           string         `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status             int            `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	LevelId            int            `json:"level_id" form:"level_id" gorm:"column:level_id;comment:会员等级;type:smallint;size:1;"`
	AccountBalanceInfo AccountBalance `gorm:"foreignKey:Uid;references:ID"`
}

// 站内账户余额
type AccountBalance struct {
	source.Model
	Balance
}

// 结构体基类
type Balance struct {
	PurchasingBalance uint `json:"purchasing_balance" form:"purchasing_balance" gorm:"column:purchasing_balance;comment:采购余额;type:bigint;size:100;"`
	SettlementBalance uint `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;comment:结算余额;type:bigint;size:100;"`
	Type              int  `json:"type" form:"type" gorm:"column:type;comment:余额类型(1汇聚2平台);type:smallint;size:1;"`
	Uid               int  `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
}

type Withdrawal struct {
	source.Model
	SupplierId       uint              `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"`
	UserId           uint              `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用戶id;"`
	OrderSn          string            `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:订单号;type:varchar(255);"`
	WithdrawalAmount uint              `json:"withdrawal_amount" form:"withdrawal_amount" gorm:"column:withdrawal_amount;comment:提现金额; default:0;"`
	PoundageAmount   uint              `json:"poundage_amount" form:"poundage_amount" gorm:"column:poundage_amount;comment:手续费金额;default:0;"`
	ServiceTax       uint              `json:"service_tax" form:"service_tax" gorm:"column:service_tax;comment:劳务税;default:0;"`
	IncomeAmount     uint              `json:"income_amount" form:"income_amount" gorm:"column:income_amount;comment:收入金额;default:0"`
	WithdrawalStatus uint              `json:"withdrawal_status" form:"withdrawal_status" gorm:"column:withdrawal_status;default:0;comment:提现状态(0待审核，1通过，2无效);"`
	RemitStatus      uint              `json:"remit_status" form:"remit_status" gorm:"column:remit_status;comment:打款状态(0待打款，1已打款,3打款中);default:0;"`
	WithdrawalMode   uint              `json:"withdrawal_mode" form:"withdrawal_mode" gorm:"column:withdrawal_mode;comment:提现方式(1手动打款，2汇聚);default:0;"`
	WithdrawalType   uint              `json:"withdrawal_type" form:"withdrawal_type" gorm:"column:withdrawal_type;default:0;comment:提现类型(1站内余额 2 收入);"`
	UserBankID       uint              `json:"user_bank_id" form:"user_bank_id" gorm:"column:user_bank_id;default:0;comment:用户卡;"`
	Remarks          string            `json:"remarks"`
	PayTime          *source.LocalTime `json:"pay_time"`
	User             User              `json:"user" gorm:"foreignKey:UserId"`
}

type SupplierOrderNotificationRecord struct {
	source.Model
	SupplierID uint `json:"supplier_id" gorm:"index"`
}

type BalanceNotificationRecord struct {
	source.Model
	UserID        uint `json:"user_id" gorm:"index"`
	BalanceAmount int  `json:"balance_amount"`
}

// 如果含有time.Time 请自行import time包
type Supplier struct {
	source.Model
	Mobile string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:电话号;type:varchar(255);size:255;"`
}

type Order struct {
	source.Model
	OrderSN uint `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`
	UserID  uint `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`
	User    User `json:"user" gorm:"foreignKey:UserID;"`
}

package service

import (
	yzRequest "yz-go/request"
	"yz-go/source"
	"shipping/model"
)

//
//@function: CreateShippingMethod
//@description: 创建Shipping记录
//@param: shipping model.ShippingMethod
//@return: err error

func CreateShippingMethod(shipping model.ShippingMethod) (err error) {
	err = source.DB().Create(&shipping).Error
	return err
}

//
//@function: DeleteShippingMethod
//@description: 删除Shipping记录
//@param: shipping model.ShippingMethod
//@return: err error

func DeleteShippingMethod(shipping model.ShippingMethod) (err error) {
	err = source.DB().Delete(&shipping).Error
	return err
}

//
//@function: DeleteShippingMethodByIds
//@description: 批量删除Shipping记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteShippingMethodByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.ShippingMethod{}, "id in ?", ids.Ids).Error
	return err
}

//
//@function: UpdateShippingMethod
//@description: 更新Shipping记录
//@param: shipping *model.ShippingMethod
//@return: err error

func UpdateShippingMethod(shipping model.ShippingMethod) (err error) {
	err = source.DB().Save(&shipping).Error
	return err
}

//
//@function: GetShippingMethod
//@description: 根据id获取Shipping记录
//@param: id uint
//@return: err error, shipping model.ShippingMethod

func GetShippingMethod(id uint) (err error, shipping model.ShippingMethod) {
	err = source.DB().Where("id = ?", id).First(&shipping).Error
	return
}

//
//@function: GetShippingMethodList
//@description: 获取Shipping
//@return: err error, list interface{}

func GetShippingMethodList() (err error, list interface{}) {
	// 创建db
	db := source.DB().Model(&model.ShippingMethod{})
	var shipping []model.ShippingMethod
	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Where("status > ?", 0).Find(&shipping).Error
	return err, shipping
}

package route

import (
	"github.com/gin-gonic/gin"
	fv1 "shipping/api/f/v1"
	"shipping/api/v1"
)

// 后台私有
func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	ShippingRouter := Router.Group("shipping")
	{
		ShippingRouter.POST("list", v1.GetShippingList)                                    // 获取Shipping列表
		ShippingRouter.POST("createExpressTemplate", v1.CreateExpressTemplate)             // 新建ExpressTemplates
		ShippingRouter.DELETE("deleteExpressTemplate", v1.DeleteExpressTemplate)           // 删除ExpressTemplates
		ShippingRouter.DELETE("deleteExpressTemplatesById", v1.DeleteExpressTemplateByIds) // 批量删除ExpressTemplates
		ShippingRouter.PUT("updateExpressTemplate", v1.UpdateExpressTemplate)              // 更新ExpressTemplates
		ShippingRouter.GET("findExpressTemplate", v1.FindExpressTemplate)                  // 根据ID获取ExpressTemplates
		ShippingRouter.GET("getExpressTemplateList", v1.GetExpressTemplateList)            // 获取ExpressTemplates列表
	}
}

// 后台公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {
	ShippingRouter := Router.Group("shipping")
	{
		ShippingRouter.POST("company/list", v1.GetCompanyList)  // 获取快递公司列表
		ShippingRouter.GET("info", v1.GetExpressInfo)           // 获取快递公司列表
		ShippingRouter.GET("statistic", v1.GetExpressStatistic) // 获取快递接口信息
	}
}

// 前端公共
func InitUserPublicRouter(Router *gin.RouterGroup) {
	ShippingRouter := Router.Group("shipping")
	{
		ShippingRouter.GET("list", fv1.GetShippingList)        // 获取Shipping列表
		ShippingRouter.GET("company/list", fv1.GetCompanyList) // 获取快递公司列表
		ShippingRouter.GET("info", v1.GetExpressInfo)          // 获取物流详情

	}
}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {

}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	ShippingRouter := Router.Group("shipping")
	{
		ShippingRouter.POST("cloud/createExpressTemplate", v1.CloudCreateExpressTemplate)        // 新建ExpressTemplates
		ShippingRouter.DELETE("cloud/deleteExpressTemplate", v1.DeleteExpressTemplate)      // 删除ExpressTemplates
		ShippingRouter.PUT("cloud/updateExpressTemplate", v1.CloudUpdateExpressTemplate)         // 更新ExpressTemplates
		ShippingRouter.GET("cloud/findExpressTemplate", v1.CloudFindExpressTemplate)             // 根据ID获取ExpressTemplates
		ShippingRouter.GET("cloud/getExpressTemplateList", v1.CloudGetExpressTemplateList)       // 获取ExpressTemplates列表
		ShippingRouter.GET("cloud/getExpressTemplateAllList", v1.GetExpressTemplateAllList) // 获取ExpressTemplates列表
	}
}

package express

import (
	"reflect"
	"testing"
)

func TestGetCompanyByCode(t *testing.T) {
	type args struct {
		code string
	}
	tests := []struct {
		name     string
		args     args
		wantErr  error
		wantName string
	}{
		{
			name: "normal",
			args: args{
				code: "xdexpress",
			},
			wantName: "迅达速递",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotName := GetCompanyByCode(tt.args.code)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.<PERSON><PERSON>("GetCompanyByCode() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if gotName != tt.wantName {
				t.<PERSON>("GetCompanyByCode() gotName = %v, want %v", gotName, tt.wantName)
			}
		})
	}
}

func TestGetCompanyList(t *testing.T) {
	tests := []struct {
		name string
		want []Company
	}{
		{
			name:"normal",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			GetCompanyList()
		})
	}
}

package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	"product/model"
	response2 "product/response"
	"strconv"
	"time"
	userModel "user/model"
	"yz-go/component/log"
	"yz-go/source"
)

type ProductAlbumSummaryHome struct {
	source.Model
	ProductAlbumID uint              `json:"product_album_id" form:"product_album_id" gorm:"column:product_album_id;index"`
	Sort           uint              `json:"sort" form:"sort"`
	Name           string            `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Tags           Tags              `json:"tags" form:"tags" gorm:"column:tags;type:text"`
	Products       Products          `json:"products" form:"products" gorm:"column:products;type:longtext"`
	UserInfo       UserInfo          `json:"user_info" gorm:"column:user_info;type:text"`
	IsShare        int               `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ProductCount   uint              `json:"product_count" gorm:"column:product_count;comment:商品数量;"`
	SalesTotal     uint              `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	AlbumCreatedAt *source.LocalTime `json:"album_created_at" gorm:"column:album_created_at;"`
}

func (ProductAlbumSummaryHome) TableName() string {
	return "product_album_summaries"
}

type ProductAlbumSummary struct {
	source.Model
	ProductAlbumID uint              `json:"product_album_id" form:"product_album_id" gorm:"column:product_album_id;index"`
	Sort           uint              `json:"sort" form:"sort"`
	Name           string            `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Tags           Tags              `json:"tags" form:"tags" gorm:"column:tags;type:text"`
	Products       Products          `json:"products" form:"products" gorm:"column:products;type:longtext"`
	UserInfo       UserInfo          `json:"user_info" gorm:"column:user_info;type:text"`
	IsShare        int               `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ProductCount   uint              `json:"product_count" gorm:"column:product_count;comment:商品数量;"`
	SalesTotal     uint              `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	AlbumCreatedAt *source.LocalTime `json:"album_created_at" gorm:"column:album_created_at;"`
}

type Tags []string

func (value Tags) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Tags) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type Products []ProductItem

type ProductItem struct {
	ID       uint   `json:"id" form:"id"`
	ImageUrl string `json:"image_url" form:"image_url"`
}

func (value Products) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Products) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type UserInfo struct {
	ID       uint   `json:"id" form:"id"`
	Nickname string `json:"nickname" form:"nickname"`
	Avatar   string `json:"avatar" form:"avatar"`
}

func (value UserInfo) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *UserInfo) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type ProductAlbumMigration struct {
	source.Model
	Sort        uint   `json:"sort" form:"sort" gorm:"column:sort;comment:排序;default:0"`
	Uid         uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name        string `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe    string `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers      Covers `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	IsShare     int    `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ImportCount uint   `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount uint   `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	SalesTotal  uint   `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	GatherId    uint   `json:"gather_id" form:"gather_id" gorm:"column:gather_id;comment:供应链id;default:0;"`
}

func (ProductAlbumMigration) TableName() string {
	return "product_albums"
}

type ProductAlbum struct {
	source.Model
	Sort            uint                          `json:"sort" form:"sort"`
	Uid             uint                          `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name            string                        `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe        string                        `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers          Covers                        `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	Relation        []ProductAlbumRelationTag     `json:"relations,omitempty" form:"relations"`
	RelationProduct []ProductAlbumRelationProduct `json:"relation_product,omitempty" form:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare         int                           `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ImportCount     uint                          `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount     uint                          `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	SalesTotal      uint                          `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	GatherId        uint                          `json:"gather_id" form:"gather_id" gorm:"column:gather_id;comment:供应链id;default:0;"`
}

type ProductAlbumBySelect struct {
	source.Model
	Uid             uint                          `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name            string                        `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe        string                        `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers          Covers                        `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	Relation        []ProductAlbumRelationTag     `json:"relations" form:"relations" gorm:"foreignKey:ProductAlbumID"`
	RelationProduct []ProductAlbumRelationProduct `json:"relation_product" form:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare         int                           `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ShareName       string                        `json:"share_name"`
	ImportCount     uint                          `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount     uint                          `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	ProductCount    int64                         `json:"product_count"`
	SalesTotal      uint                          `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	UserInfo        userModel.User                `json:"user_info" gorm:"foreignKey:Uid"`
	GatherId        uint                          `json:"gather_id" form:"gather_id" gorm:"column:gather_id;comment:供应链id;default:0;"`
	GatherSupply    GatherSupply                  `json:"gather_supply" gorm:"foreignKey:GatherId"`
	SourceName      string                        `json:"source_name"`
}

func (ProductAlbumBySelect) TableName() string {
	return "product_albums"
}

func (PBS *ProductAlbumBySelect) AfterFind(tx *gorm.DB) (err error) {
	if PBS.GatherId == 0 {
		PBS.SourceName = "平台"
	} else {
		PBS.SourceName = PBS.GatherSupply.Name
	}
	if PBS.IsShare == 1 {
		PBS.ShareName = "共享"
	} else {
		PBS.ShareName = "私有"
	}
	err = tx.Model(&ProductAlbumRelationProduct{}).Where("product_album_id = ?", PBS.ID).Count(&PBS.ProductCount).Error
	if err != nil {
		return
	}
	return
}

// 专辑封面
type Covers []Cover
type Cover struct {
	Src string `json:"src"` // 资源链接
}

func (value Covers) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Covers) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type ProductAlbumRelationTagMigration struct {
	source.Model
	ProductAlbumID uint `json:"product_album_id" form:"product_album_id" gorm:"index"`
	TagID          uint `json:"tag_id" form:"tag_id" gorm:"index"`
}

func (ProductAlbumRelationTagMigration) TableName() string {
	return "product_album_relation_tags"
}

type GatherSupply struct {
	ID   uint   `json:"id" form:"id"`     // 供应链ID
	Name string `json:"name" form:"name"` // 供应链名称
}

type ProductAlbumRelationTag struct {
	source.Model
	ProductAlbumID uint `json:"product_album_id" form:"product_album_id" gorm:"index"`
	TagID          uint `json:"tag_id" form:"tag_id" gorm:"index"`
	Tag            Tag  `json:"tag" gorm:"foreignKey:TagID"`
}

type ProductAlbumRelationProductMigration struct {
	source.Model
	ProductAlbumID uint `json:"product_album_id" form:"product_album_id" gorm:"index"`
	ProductID      uint `json:"product_id" form:"product_id" gorm:"index"`
	OffShelf       uint `json:"off_shelf" form:"off_shelf" gorm:"default:0;comment:1已下架;index"`
}

func (ProductAlbumRelationProductMigration) TableName() string {
	return "product_album_relation_products"
}

type ProductAlbumRelationProduct struct {
	source.Model
	ProductAlbumID uint    `json:"product_album_id" form:"product_album_id" gorm:"index"`
	ProductID      uint    `json:"product_id" form:"product_id" gorm:"index"`
	OffShelf       uint    `json:"off_shelf" form:"off_shelf" gorm:"default:0;comment:1已下架;index"`
	Product        Product `json:"product" gorm:"foreignKey:ProductID"`
}

type Product struct {
	ID               uint              `json:"id" form:"id" gorm:"primarykey"`
	CreatedAt        *source.LocalTime `json:"created_at" gorm:"index;"`
	Title            string            `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"`      // 标题
	OriginPrice      uint              `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice       uint              `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	Price            uint              `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价、给采购端的协议价
	NormalPrice      uint              `json:"normal_price" form:"normal_price" gorm:"-"`                                                  //批发价(商品正常售价)                                                    // 供货价、给采购端的协议价
	ExecPrice        uint              `json:"exec_price" form:"exec_price" gorm:"column:cost_price"`                                      // 成本价
	CostPrice        uint              `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	ActivityPrice    uint              `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	MinPrice         uint              `json:"min_price" form:"min_price" gorm:"column:min_price;comment:最低价(单位:分);"`                // 最低价(单位:分)
	Stock            uint              `json:"stock" form:"stock" gorm:"column:stock;default:0;comment:库存数量;index"`                    // 库存数量
	Sales            uint              `json:"sales" form:"sales" gorm:"column:sales;comment:销量;"`                                       // 销量
	FeedbackRate     int               `json:"feedback_rate" form:"feedback_rate" gorm:"column:feedback_rate;comment:好评率;"`             // 好评率
	CostRate         float64           `json:"cost_rate" form:"cost_rate" gorm:"column:cost_rate;comment:利润率;"`                         // 利润率
	ProfitRate       float64           `json:"profit_rate"`
	ActivityRate     float64           `json:"activity_rate" form:"activity_rate" gorm:"column:activity_rate;comment:营销利润率;"`                     // 利润率
	Sn               string            `json:"sn" form:"sn" gorm:"column:sn;comment:产品编号;type:varchar(255);size:255;"`                             // 产品编号
	Code             string            `json:"code" form:"code" gorm:"column:code;comment:自定义编码;type:varchar(255);size:255;"`                     // 自定义编码
	ImageUrl         string            `json:"image_url" gorm:"column:image_url;comment:图片url;"`                                                     // 图片url
	VideoUrl         string            `json:"video_url" gorm:"column:video_url;comment:视频url;"`                                                     // 视频url
	AgreementPrice   uint              `json:"agreement_price" gorm:"-"`                                                                               //协议价
	MarketPrice      uint              `json:"market_price" gorm:"-"`                                                                                  //市场价
	SalePrice        uint              `json:"sale_price" gorm:"-"`                                                                                    //销售价
	IsDisplay        int               `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"` // 上架（1是0否）
	SupplierID       uint              `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`
	Supplier         Supplier          `json:"supplier,omitempty" form:"supplier"`
	Skus             []response2.Sku   `json:"skus" gorm:"foreignKey:ProductID;"`
	MaxPrice         int               `json:"max_price" example:100`
	MinOriginPrice   uint              `json:"min_origin_price" gorm:"-"`                         // 市场价、划线价（分为单位）
	MaxOriginPrice   uint              `json:"max_origin_price" gorm:"-"`                         // 市场价、划线价（分为单位）
	MaxGuidePrice    uint              `json:"max_guide_price" gorm:"-"`                          // 指导价（分为单位）
	MinGuidePrice    uint              `json:"min_guide_price" gorm:"-"`                          // 指导价（分为单位）
	MaxActivityPrice uint              `json:"max_activity_price" gorm:"-"`                       // 营销价（分为单位）
	MaxNormalPrice   uint              `json:"max_normal_price" form:"max_normal_price" gorm:"-"` // 最高售价（分为单位）
	MinNormalPrice   uint              `json:"min_normal_price" form:"min_normal_price" gorm:"-"` // 最低售价（分为单位）
	GatherSupplyID   uint              `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"`
	GatherSupply     GatherSupply      `json:"gather_supply" gorm:"foreignKey:GatherSupplyID"`
	// 会员价独立开关
	UserPriceSwitch int `json:"user_price_switch" gorm:"column:user_price_switch;comment:会员价独立开关;"`
	// 会员价设置
	UserPrice model.UserPrice `json:"user_price" gorm:"column:user_price;comment:会员价设置;"`
}

type Supplier struct {
	ID   int    `json:"id" gorm:"column:id"`
	Name string `json:"name" gorm:"column:name"`
}

func decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		log.Log().Error("数据转换失败", zap.Any("err", err))
		return 0
	}
	return value
}

func (p *Product) AfterFind(tx *gorm.DB) (err error) {
	if p.Price <= 0 || p.GuidePrice <= 0 || float64(p.GuidePrice)-float64(p.Price) <= 0 {
		p.CostRate = 0
	} else {
		p.CostRate = decimal((float64(p.GuidePrice) - float64(p.Price)) / float64(p.Price) * 100)
	}

	p.AgreementPrice = p.Price

	p.MarketPrice = p.OriginPrice
	if p.ActivityPrice > 0 {
		if p.ActivityPrice > p.AgreementPrice && p.ActivityPrice > 0 && p.AgreementPrice > 0 {
			p.ActivityRate = decimal((float64(p.ActivityPrice) - float64(p.AgreementPrice)) / float64(p.AgreementPrice))
		} else {
			p.ActivityRate = 0
		}
		p.CostPrice = p.ActivityPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
	} else {
		p.CostPrice = p.AgreementPrice //建议成本价 如果存在ActivityPrice就用ActivityPrice+技术服务费，否则用price加上技术服务费
	}
	p.SalePrice = p.GuidePrice //建议销售价

	return
}

type ProductAlbumBySelectAndYzApi struct {
	source.Model
	Uid             uint                          `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name            string                        `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe        string                        `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers          Covers                        `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	Relation        []ProductAlbumRelationTag     `json:"relations,omitempty" form:"relations" gorm:"foreignKey:ProductAlbumID"`
	RelationProduct []ProductAlbumRelationProduct `json:"relation_product,omitempty" form:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare         int                           `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ShareName       string                        `json:"share_name"`
	ImportCount     uint                          `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount     uint                          `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	ProductCount    uint                          `json:"product_count"`
	SalesTotal      uint                          `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	TimeName        string                        `json:"time_name"`
}

type ProductAlbumBySelectAndApi struct {
	source.Model
	Uid             uint                          `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name            string                        `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe        string                        `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers          Covers                        `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	Relation        []ProductAlbumRelationTag     `json:"relations,omitempty" form:"relations" gorm:"foreignKey:ProductAlbumID"`
	RelationProduct []ProductAlbumRelationProduct `json:"relation_product,omitempty" form:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare         int                           `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ShareName       string                        `json:"share_name"`
	ImportCount     uint                          `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount     uint                          `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	ProductCount    int64                         `json:"product_count"`
	SalesTotal      uint                          `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	TimeName        string                        `json:"time_name"`
	UserInfo        userModel.User                `json:"user_info" gorm:"foreignKey:Uid"`
}

type ProductAlbumRelationByFind struct {
	source.Model
	Sort     uint                      `json:"sort" form:"sort"`
	Uid      uint                      `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name     string                    `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe string                    `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers   Covers                    `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	Relation []ProductAlbumRelationTag `json:"relations" form:"relations" gorm:"foreignKey:ProductAlbumID"`
	// RelationProduct []ProductAlbumRelationProduct `json:"relation_product" form:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare      int            `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ImportCount  uint           `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount  uint           `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	ProductCount int64          `json:"product_count"`
	SalesTotal   uint           `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	TimeName     string         `json:"time_name"`
	UserInfo     userModel.User `json:"user_info" gorm:"foreignKey:Uid"`
}

type ProductAlbumRelation struct {
	source.Model
	Sort            uint                          `json:"sort" form:"sort"`
	Uid             uint                          `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	Name            string                        `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Describe        string                        `json:"describe" form:"describe" gorm:"column:describe;comment:描述;type:longtext;"`
	Covers          Covers                        `json:"covers" form:"covers" gorm:"column:covers;comment:专辑封面;type:longtext;"`
	Relation        []ProductAlbumRelationTag     `json:"relations" form:"relations" gorm:"foreignKey:ProductAlbumID"`
	RelationProduct []ProductAlbumRelationProduct `json:"relation_product" form:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare         int                           `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	ImportCount     uint                          `json:"import_count" gorm:"column:import_count;comment:导入数量;"`
	BrowseCount     uint                          `json:"browse_count" gorm:"column:browse_count;comment:访问数量;"`
	ProductCount    int64                         `json:"product_count"`
	SalesTotal      uint                          `json:"sales_total" gorm:"column:sales_total;comment:销量;"`
	TimeName        string                        `json:"time_name"`
	UserInfo        userModel.User                `json:"user_info" gorm:"foreignKey:Uid"`
}

func (ProductAlbumBySelectAndYzApi) TableName() string {
	return "product_albums"
}

func (ProductAlbumBySelectAndApi) TableName() string {
	return "product_albums"
}

func (ProductAlbumRelation) TableName() string {
	return "product_albums"
}

func (ProductAlbumRelationByFind) TableName() string {
	return "product_albums"
}

func (PBSA *ProductAlbumBySelectAndApi) AfterFind(tx *gorm.DB) (err error) {
	var productIds []uint
	err = tx.Model(ProductAlbumRelationProduct{}).Where("product_album_id = ?", PBSA.ID).Pluck("product_id", &productIds).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	err = tx.Model(&Product{}).Where("id in ?", productIds).Where("is_display = ?", 1).Count(&PBSA.ProductCount).Error
	if err != nil {
		return
	}
	nowTime, err := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02 15:04:05"), time.Local)
	if err != nil {
		return
	}
	albumTime, err := time.ParseInLocation("2006-01-02 15:04:05", PBSA.CreatedAt.Format("2006-01-02 15:04:05"), time.Local)
	if err != nil {
		return
	}
	if albumTime.Before(nowTime) {
		diff := nowTime.Unix() - albumTime.Unix()
		if diff < 60 {
			PBSA.TimeName = strconv.FormatInt(diff, 10) + "秒前"
		}
		if diff > 60 && diff < 3600 {
			PBSA.TimeName = strconv.FormatInt(int64(math.Floor(float64(diff/60))), 10) + "分前"
		}
		if diff > 3600 && diff < 43200 {
			PBSA.TimeName = strconv.FormatInt(int64(math.Floor(float64(diff/3600))), 10) + "小时前"
		}
		if diff > 43200 && diff < 86400 {
			PBSA.TimeName = "一天前"
		}
		if diff > 86400 && diff < 129600 {
			PBSA.TimeName = "两天前"
		}
		if diff > 129600 {
			_, month, day := albumTime.Date()
			PBSA.TimeName = strconv.Itoa(int(month)) + "月" + strconv.Itoa(day) + "日"
		}
	}
	return
}

func (PBSA *ProductAlbumRelation) AfterFind(tx *gorm.DB) (err error) {
	var productIds []uint
	err = tx.Model(ProductAlbumRelationProduct{}).Where("product_album_id = ?", PBSA.ID).Pluck("product_id", &productIds).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	err = tx.Model(&Product{}).Where("id in ?", productIds).Where("is_display = ?", 1).Count(&PBSA.ProductCount).Error
	if err != nil {
		return
	}
	nowTime, err := time.ParseInLocation("2006-01-02 15:04:05", time.Now().Format("2006-01-02 15:04:05"), time.Local)
	if err != nil {
		return
	}
	albumTime, err := time.ParseInLocation("2006-01-02 15:04:05", PBSA.CreatedAt.Format("2006-01-02 15:04:05"), time.Local)
	if err != nil {
		return
	}
	if albumTime.Before(nowTime) {
		diff := nowTime.Unix() - albumTime.Unix()
		if diff < 60 {
			PBSA.TimeName = strconv.FormatInt(diff, 10) + "秒前"
		}
		if diff > 60 && diff < 3600 {
			PBSA.TimeName = strconv.FormatInt(int64(math.Floor(float64(diff/60))), 10) + "分前"
		}
		if diff > 3600 && diff < 43200 {
			PBSA.TimeName = strconv.FormatInt(int64(math.Floor(float64(diff/3600))), 10) + "小时前"
		}
		if diff > 43200 && diff < 86400 {
			PBSA.TimeName = "一天前"
		}
		if diff > 86400 && diff < 129600 {
			PBSA.TimeName = "两天前"
		}
		if diff > 129600 {
			_, month, day := albumTime.Date()
			PBSA.TimeName = strconv.Itoa(int(month)) + "月" + strconv.Itoa(day) + "日"
		}
	}
	return
}

func (ProductInfoList) TableName() string {
	return "products"
}

type ProductInfoList struct {
	ID        uint              `json:"id" form:"id" gorm:"primarykey"`
	UpdatedAt *source.LocalTime `json:"updated_at"`
	CreatedAt *source.LocalTime `json:"created_at" gorm:"index;"`
	Title     string            `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;index;"` // 标题
	// 三级分类

	OriginPrice   uint `json:"origin_price" form:"origin_price" gorm:"column:origin_price;comment:市场价(单位:分);"`       // 市场价 市场价
	GuidePrice    uint `json:"guide_price" form:"guide_price" gorm:"column:guide_price;comment:供货价(单位:分);"`          //指导价
	Price         uint `json:"price" form:"price" gorm:"column:price;comment:供货价(单位:分);"`                            // 供货价、给采购端的协议价
	CostPrice     uint `json:"cost_price" form:"cost_price" gorm:"column:cost_price;comment:成本价(单位:分);"`             // 成本价，上游给的协议价
	ActivityPrice uint `json:"activity_price" form:"activity_price" gorm:"column:activity_price;comment:营销价(单位:分);"` // 营销价
	MinBuyQty     uint `json:"min_buy_qty" form:"min_buy_qty" gorm:"column:min_buy_qty;default:0;comment:最小起订量;"`     // 最小起订量

	//如果有activity_price时，那么cost_price为activity_price，否则为price
	Stock        uint   `json:"stock" form:"stock" gorm:"column:stock;default:0;comment:库存数量;index"`            // 库存数量
	Sales        uint   `json:"sales" form:"sales" gorm:"column:sales;comment:销量;"`                               // 销量
	FeedbackRate int    `json:"feedback_rate" form:"feedback_rate" gorm:"column:feedback_rate;comment:好评率;"`     // 好评率
	Sn           string `json:"sn" form:"sn" gorm:"column:sn;comment:产品编号;type:text;"`                          // 产品编号
	Code         string `json:"code" form:"code" gorm:"column:code;comment:自定义编码;type:varchar(255);size:255;"` // 自定义编码

	IsNew        int `json:"is_new" form:"is_new" gorm:"column:is_new;comment:新品（1是0否）;type:smallint;size:1;index;"`                   // 新品（1是0否）
	IsRecommend  int `json:"is_recommend" form:"is_recommend" gorm:"column:is_recommend;comment:推荐（1是0否）;type:smallint;size:1;index;"` // 推荐（1是0否）
	IsHot        int `json:"is_hot" form:"is_hot" gorm:"column:is_hot;comment:热销（1是0否）;type:smallint;size:1;index;"`                   // 热销（1是0否）
	IsPromotion  int `json:"is_promotion" form:"is_promotion" gorm:"column:is_promotion;comment:促销（1是0否）;type:smallint;size:1;index;"` // 促销（1是0否）
	IsDisplay    int `json:"is_display" form:"is_display" gorm:"column:is_display;comment:上架（1是0否）;type:smallint;size:1;index;"`       // 上架（1是0否）
	StatusLock   int `json:"status_lock" form:"status_lock" gorm:"column:status_lock;comment:锁定（1是0否）;type:smallint;size:1;index;"`    // 锁定（1是0否）
	SingleOption int `json:"single_option" form:"single_option" gorm:"column:single_option;comment:单规格(1是0否);type:smallint;size:1;"`  // 单规格（1是0否）

	ImageUrl       string `json:"image_url" gorm:"column:image_url;comment:图片url;"`                                      // 图片url
	Unit           string `json:"unit" form:"unit" gorm:"column:unit;comment:单位(件,个);type:varchar(255);size:255;"`     // 单位(件,个)
	Barcode        string `json:"barcode" form:"barcode" gorm:"column:barcode;comment:条形码;type:varchar(255);size:255;"` // 条形码
	FreightType    int    `json:"freight_type" form:"freight_type" gorm:"column:freight_type;type:smallint;comment:运费类型（0统一，1模板， 2第三方运费，3包邮）;"`
	MaxPrice       uint   `json:"maxPrice" form:"maxPrice" gorm:"column:max_price;comment:最高价(单位:分);"` // 最高价(单位:分)
	MinPrice       uint   `json:"minPrice" form:"minPrice" gorm:"column:min_price;comment:最低价(单位:分);"` // 最低价(单位:分)
	MaxCostPrice   uint   `json:"maxCostPrice" form:"maxCostPrice" gorm:"-"`                                 // 最高价(单位:分)
	MinCostPrice   uint   `json:"minCostPrice" form:"minCostPrice" gorm:"-"`                                 // 最低价(单位:分)
	MaxOriginPrice uint   `json:"maxOriginPrice" form:"maxOriginPrice" gorm:"-"`                             // 最高价(单位:分)
	MinOriginPrice uint   `json:"minOriginPrice" form:"minOriginPrice" gorm:"-"`                             // 最低价(单位:分)

	BrandID           uint `json:"brand_id" form:"brand_id" gorm:"index"`                                                           // 品牌
	SupplierID        uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"`                // 供应商id
	GatherSupplyID    uint `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"` // 供应链id
	Category1ID       uint `json:"category1_id" form:"category1_id" gorm:"index"`                                                   // 一级分类
	Category2ID       uint `json:"category2_id" form:"category2_id" gorm:"index"`                                                   // 二级分类
	Category3ID       uint `json:"category3_id" form:"category3_id" gorm:"index"`
	FreightTemplateID uint `json:"freight_template_id" form:"freight_template_id" gorm:"column:freight_template_id;comment:配送模板id;"` // 配送模板id

	Source        int     `json:"source" form:"source" gorm:"column:source;comment:商品来源;type:int;"`                           //     1云仓 2京东 6阿里 7天猫  8苏宁 100YZH 99永源 98中台云仓 101中台 102跨境 103dwd
	Sort          int     `json:"sort" form:"sort" gorm:"column:sort;comment:商品排序;type:int;"`                                 //     商品排序（序号小的在前）
	SourceGoodsID uint    `json:"source_goods_id" form:"source_goods_id" gorm:"column:source_goods_id;index;comment:商品来源ID;"` // 商品来源
	LocationID    uint    `json:"location_id" form:"location_id" gorm:"column:location_id;index;comment:站点ID;"`                 // 商品来源
	Freeze        uint    `json:"freeze" form:"freeze" gorm:"column:freeze;comment:是否冻结;default:0;"`                          // 冻结
	ShopLevel     int     `json:"shop_level" form:"shop_level" gorm:"column:shop_level;default:0;type:int(2);"`
	DesLevel      int     `json:"des_level" form:"des_level" gorm:"column:des_level;default:0;type:int(2);"`
	ExpressLevel  int     `json:"express_level" form:"express_level" gorm:"column:express_level;default:0;type:int(2);"`
	Level         int     `json:"level" form:"level" gorm:"column:level;default:0;type:int(2);"`
	ChildTitle    string  `json:"child_title" form:"child_title" gorm:"column:child_title;comment:简称;type:varchar(255);size:255;index;"` // 简称
	BillPosition  int     `json:"bill_position" form:"bill_position"  gorm:"column:bill_position;default:1;"`                              //发票信息存储位置  1商品本体 2sku
	MinProfitRate float64 `json:"min_profit_rate" form:"min_profit_rate" gorm:"-"`                                                         //利润率
	MaxProfitRate float64 `json:"max_profit_rate" form:"max_profit_rate" gorm:"-"`                                                         //利润率
	SupplyLine    string  `json:"supply_line"`
	ProfitRate    float64 `json:"profit_rate"`
}

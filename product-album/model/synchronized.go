package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

func (RelationSynchro) TableName() string {
	return "product_album_relation_synchro"
}

// RelationSynchro 共享专辑被采购端同步记录表（记录被哪些采购端同步过）
type RelationSynchro struct {
	source.Model
	ExportAppID    uint `json:"export_app_id" form:"export_app_id" gorm:"index;not null;comment:导出APP ID"`
	ProductAlbumID uint `json:"product_album_id" form:"product_album_id" gorm:"index;not null;comment:共享专辑ID"`
}

func (AppGatherAlbum) TableName() string {
	return "product_albums"
}

type AppGatherAlbum struct {
	source.Model
	Name            string                        `json:"name"`
	Describe        string                        `json:"describe"`
	Covers          Covers                        `json:"covers"`
	Relation        []ProductAlbumRelationTag     `json:"relations" gorm:"foreignKey:ProductAlbumID"`
	RelationProduct []ProductAlbumRelationProduct `json:"relation_product" gorm:"foreignKey:ProductAlbumID"`
	IsShare         int                           `json:"is_share"`
	ImportCount     uint                          `json:"import_count"`
	BrowseCount     uint                          `json:"browse_count"`
	ProductCount    int64                         `json:"product_count"`
	SalesTotal      uint                          `json:"sales_total"`
	TimeName        string                        `json:"time_name"`
	ImportStatus    string                        `json:"import_status"`
}

func (PBS *AppGatherAlbum) AfterFind(tx *gorm.DB) (err error) {
	err = tx.Model(&ProductAlbumRelationProduct{}).Where("product_album_id = ?", PBS.ID).Count(&PBS.ProductCount).Error
	if err != nil {
		return
	}
	return
}

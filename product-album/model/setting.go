package model

import (
	"yz-go/source"
)

type SettingSave struct {
	source.Model
	Key    string `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values string `json:"value" gorm:"column:value;comment:值;type:json;"`
}

type Setting struct {
	source.Model
	Key    string `json:"key" form:"key" gorm:"column:key;comment:关键字;type:varchar(255);size:255;"`
	Values Value  `json:"value" gorm:"column:value;comment:值;type:json;"`
}

func (Setting) TableName() string {
	return "sys_settings"
}

func (SettingSave) TableName() string {
	return "sys_settings"
}

type Value struct {
	IsOpen       int           `json:"is_open"`       // 共享选品专辑开启关闭
	Permission   Permissions   `json:"permissions"`   // 共享开关会员权限
	DefaultCover DefaultCovers `json:"default_cover"` // 选品专辑默认封面
	// pc端首页专辑开关
	HomeAlbumSwitch int `json:"home_album_switch"` // 0:关闭 1:开启
}

type Permissions []uint

// DefaultCovers 专辑封面
type DefaultCovers []DefaultCover

type DefaultCover struct {
	Src string `json:"src"` // 资源链接
}

func (AutoSynchroSetting) TableName() string {
	return "sys_settings"
}

type AutoSynchroSetting struct {
	source.Model
	Key    string           `json:"key" form:"key"`
	Values AutoSynchroValue `json:"value" form:"value"`
}

type AutoSynchroValue struct {
	GatherIds  []uint `json:"gather_ids"`  // 中台供应链ids
	Uid        uint   `json:"uid"`         // 会员ID
	SyncHour   int    `json:"sync_hour"`   // 同步时间(时)
	SyncMinute int    `json:"sync_minute"` // 同步时间(分钟)
}

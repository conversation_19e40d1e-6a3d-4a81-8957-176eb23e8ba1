package cron

import (
	"encoding/json"
	"errors"
	"gorm.io/gorm"
	"product-album/model"
	"product-album/request"
	"product-album/service"
	"time"
	"yz-go/cron"
)

func SynchroHandle() {
	task := cron.Task{
		Key:  "productAlbumSynchro",
		Name: "选品专辑自动同步",
		Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			SynchroAlbums()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func SynchroAlbums() {
	setting, err := getAutoSynchroValue()
	if err != nil {
		return
	}
	// 判断当前时间是否满足同步条件
	now := time.Now()
	hour := now.Hour()
	minute := now.Minute()
	if setting.SyncHour == hour && setting.SyncMinute == minute {
		// 判断会员存在、供应链勾选不为空 执行同步
		if setting.Uid > 0 && len(setting.GatherIds) > 0 {
			for _, gatherId := range setting.GatherIds {
				if err = synchroGatherAlbums(gatherId, setting.Uid); err != nil {
					return
				}
			}
		}
	}
}

func getAutoSynchroValue() (autoSynchroValue model.AutoSynchroValue, err error) {
	var setting model.SettingSave

	if setting, err = service.GetAutoSynchroSetting(); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if setting.ID > 0 {
		err = json.Unmarshal([]byte(setting.Values), &autoSynchroValue)
		if err != nil {
			return
		}
	}
	return
}

func synchroGatherAlbums(gatherId, uid uint) (err error) {
	search := request.GatherAlbumListSearch{
		IsImport: 1,
		GatherID: gatherId,
	}
	err, list, _ := service.GetGatherAlbumList(search)
	if err != nil {
		return
	}

	// 组装需要导入专辑ids
	var albumIds []uint
	for _, album := range list {
		albumIds = append(albumIds, album.ID)
	}

	// 获取需要同步的专辑数据
	err, albums := service.GetAlbumImportData(gatherId, albumIds)
	if err != nil {
		return
	}

	// 同步专辑数据
	if err = service.ImportAlbum(albums, gatherId, uid); err != nil {
		return
	}
	return
}

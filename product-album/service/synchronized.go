package service

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"product-album/model"
	"product-album/request"
	"yz-go/source"
)

//
// 响应下游请求，提供专辑同步服务
//

// GetAppGatherAlbumList 响应下游请求
//
// 返回下游搜索的供应链开启共享的专辑数据（包含是否导入状态:import_status 1已导入 0未导入）
func GetAppGatherAlbumList(search request.AppGatherAlbumListSearch) (err error, list interface{}, total int64) {
	db := source.DB().Model(&model.AppGatherAlbum{}).
		Select("product_albums.*", "CASE WHEN relation.id IS NOT NULL THEN 1 ELSE 0 END AS import_status").
		Joins("LEFT JOIN product_album_relation_synchro AS relation ON product_albums.id = relation.product_album_id And relation.export_app_id = ?", search.AppId)
	//默认查询共享
	if search.IsShare == 2 {
		db.Where("is_share = 2").Where("uid = ?", search.AppUserId)
	} else {
		db.Where("is_share = 1")
	}
	// 是否导入搜索：0全部 1未导入 2已导入
	if search.IsImport != 0 {
		switch search.IsImport {
		case 1:
			db.Where("relation.id IS NULL")
		case 2:
			db.Where("relation.id IS NOT NULL")
		}
	}
	// 专辑名称搜索
	if search.AlbumName != "" {
		db.Where("name LIKE ?", "%"+search.AlbumName+"%")
	}
	// 时间搜索
	if search.EndAT != "" {
		db.Where("product_albums.created_at <= ?", search.EndAT)
	}
	// 时间搜索
	if search.StartAT != "" {
		db.Where("product_albums.created_at >= ?", search.StartAT)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	var albums []model.AppGatherAlbum
	var limit = search.PageSize
	var offset = search.PageSize * (search.Page - 1)

	err = db.Order("sort desc, id  desc").Limit(limit).Offset(offset).Find(&albums).Error
	if err != nil {
		return
	}

	return err, albums, total
}

// GetAppGatherAlbumDetail 响应下游请求
//
// 返回专辑详情（专辑+专辑商品分页数据）
func GetAppGatherAlbumDetail(info request.AppGatherAlbumDetailSearch) (err error, album model.AppGatherAlbum, productList []model.Product, total int64) {
	// 更新专辑浏览次数
	if err = addBrowseTime(info.AlbumId); err != nil {
		return
	}

	// 查询与专辑关联的产品Id列表
	productIds := make([]uint, 0)
	err = source.DB().Model(model.ProductAlbumRelationProduct{}).Where("product_album_id = ? and off_shelf = ?", info.AlbumId, 0).Pluck("product_id", &productIds).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 查询专辑详情
	err = source.DB().Preload("Relation").Preload("Relation.Tag").Where("id = ?", info.AlbumId).First(&album).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	// 查询产品列表 todo 此处位置可以调用商品列表分页查询 或 单独封装一个商品列表分页查询
	db := source.DB().Model(&model.Product{}).Where("id in ?", productIds)
	err = db.Count(&total).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	err = db.Where("is_display = ?", 1).Limit(limit).Offset(offset).Find(&productList).Error

	return err, album, productList, total
}

func ExportAlbumList(appId uint, pIds []uint) (err error, albums []model.AppGatherAlbum) {
	// 验证pIds
	if err = validatePIds(pIds); err != nil {
		return
	}
	// 更新被导入次数
	if err = AddImportTime(pIds); err != nil {
		return
	}
	// 创建被导入记录
	if err = createExportRelation(appId, pIds); err != nil {
		return
	}
	// 返回需要被导入的数据(专辑、专辑标签、专辑绑定的商品IDs)
	err = source.DB().
		Preload("Relation").
		Preload("Relation.Tag").
		Preload("RelationProduct").
		Where("id in ?", pIds).
		Find(&albums).
		Error
	return
}

// 验证被导出的专辑ids参数
func validatePIds(pIds []uint) (err error) {
	// 验证参数重复
	duplicates := findDuplicates(pIds)
	if len(duplicates) > 0 {
		err = errors.New(fmt.Sprintf("专辑ID重复:%v", duplicates))
		return
	}
	// 验证参数准确
	err, eIds := getExistingAlbumIds(pIds)
	if err != nil {
		return
	}
	notFoundIds := difference(pIds, eIds)
	if len(notFoundIds) > 0 {
		err = errors.New(fmt.Sprintf("专辑不存在，ID:%v", notFoundIds))
		return
	}
	return
}

// 创建被导入记录
func createExportRelation(eAppId uint, pIds []uint) (err error) {
	// 获取所有现有的记录
	err, eIds := getExistingSynchroPIds(eAppId, pIds)
	if err != nil {
		return err
	}
	// 取出差集
	var relationData []model.RelationSynchro
	for _, pId := range difference(pIds, eIds) {
		relationData = append(relationData, model.RelationSynchro{
			ProductAlbumID: uint(pId),
			ExportAppID:    eAppId,
		})
	}
	// 批量创建新的关系
	return source.DB().CreateInBatches(relationData, 1000).Error
}

// 两个切片的并集
func union(slice1, slice2 []uint) []uint {
	var m = make(map[uint]bool)
	for _, item := range slice1 {
		m[item] = true
	}
	var result []uint
	for _, item := range slice2 {
		if _, exists := m[item]; exists {
			result = append(result, item)
			m[item] = true
		}
	}
	return result
}

// 两个切片的差集
func difference(slice1, slice2 []uint) []uint {
	// 使用map记录第二个切片中的元素
	slice2Map := make(map[uint]bool)
	for _, value := range slice2 {
		slice2Map[value] = true
	}
	// 遍历第一个切片，找出不在slice2Map中的元素
	diff := make([]uint, 0)
	for _, value := range slice1 {
		if _, found := slice2Map[value]; !found {
			diff = append(diff, value)
		}
	}
	return diff
}

// 找出重复数据
func findDuplicates(slice []uint) []uint {
	// 创建一个map来记录每个值出现的次数
	countMap := make(map[uint]int)
	duplicates := make([]uint, 0)
	// 遍历切片，记录每个值出现的次数
	for _, value := range slice {
		countMap[value]++
	}
	// 遍历map，找出出现次数大于1的值
	for value, count := range countMap {
		if count > 1 {
			duplicates = append(duplicates, value)
		}
	}
	return duplicates
}

// 获取已创建的导入关系
func getExistingSynchroPIds(eAppId uint, pIds []uint) (err error, eIds []uint) {
	err = source.DB().
		Model(model.RelationSynchro{}).
		Where("product_album_id in ?", pIds).
		Where("export_app_id = ?", eAppId).
		Pluck("product_album_id", &eIds).
		Error
	return
}

// 获取存在的专辑ids
func getExistingAlbumIds(pIds []uint) (err error, eIds []uint) {
	err = source.DB().
		Model(model.AppGatherAlbum{}).
		Where("id in ?", pIds).
		Pluck("id", &eIds).
		Error
	return
}

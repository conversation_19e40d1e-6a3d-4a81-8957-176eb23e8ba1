package service

import (
	"errors"
	"fmt"
	"gorm.io/gorm"
	"product-album/model"
	"product-album/request"
	userModel "user/model"
	"yz-go/source"
)

func DeleteAlbumSummary(albumIDs []uint) (err error) {
	if len(albumIDs) == 0 {
		return errors.New("albumIDs不能为空")
	}
	// 通过albumID删除专辑摘要
	err = source.DB().Where("product_album_id in ?", albumIDs).Delete(&model.ProductAlbumSummary{}).Error
	if err != nil {
		return fmt.Errorf("DeleteAlbumSummary Error：%w", err)
	}
	return nil
}

func MaintainAlbumSummary(albumID uint) (err error) {
	// 查询专辑摘要, 如果不存在, 组合数据, 并插入. 如果存在, 更新数据
	var albumSummary model.ProductAlbumSummary
	err = source.DB().Where("product_album_id = ?", albumID).First(&albumSummary).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	if err != nil {
		return fmt.Errorf("MaintainAlbumSummary查询专辑摘要 Error：%w", err)
	}
	// 查询专辑
	var album ProductAlbum
	err = source.DB().Preload("UserInfo").Where("id = ?", albumID).First(&album).Error
	if err != nil {
		return fmt.Errorf("MaintainAlbumSummary查询专辑 Error：%w", err)
	}
	// 查询关联标签
	var relationTags []model.ProductAlbumRelationTag
	err = source.DB().Preload("Tag").Where("product_album_id = ?", albumID).Find(&relationTags).Error
	if err != nil {
		return fmt.Errorf("MaintainAlbumSummary查询关联标签 Error：%w", err)
	}
	// 把标签转换为字符串数组
	var tagNames []string
	for _, relationTag := range relationTags {
		if relationTag.Tag.Name == "" {
			continue
		}
		tagNames = append(tagNames, relationTag.Tag.Name)
	}
	// 查询出销量前3的上架中的商品
	var productWithImage []ProductWithImage
	err, productWithImage = GetTop3Products(albumID)
	if err != nil {
		return fmt.Errorf("MaintainAlbumSummary查询销量前3的上架中的商品 Error：%w", err)
	}
	// 查询出总销量和上架中的商品数量
	var albumStats AlbumStats
	err, albumStats = GetAlbumStats(albumID)
	if err != nil {
		return fmt.Errorf("MaintainAlbumSummary查询专辑统计数据 Error：%w", err)
	}
	albumSummary.Sort = album.Sort
	albumSummary.Name = album.Name
	albumSummary.Tags = tagNames
	// 清空原有的商品数据
	albumSummary.Products = []model.ProductItem{}
	for _, w := range productWithImage {
		albumSummary.Products = append(albumSummary.Products, model.ProductItem{
			ID:       w.ProductID,
			ImageUrl: w.ImageUrl,
		})
	}
	albumSummary.UserInfo.ID = album.UserInfo.ID
	albumSummary.UserInfo.Nickname = album.UserInfo.NickName
	albumSummary.UserInfo.Avatar = album.UserInfo.Avatar
	albumSummary.IsShare = album.IsShare
	albumSummary.ProductCount = albumStats.TotalOnShelfProducts
	albumSummary.SalesTotal = albumStats.TotalSales
	// 插入数据
	if albumSummary.ID == 0 {
		albumSummary.ProductAlbumID = album.ID
		albumSummary.AlbumCreatedAt = album.CreatedAt
		err = source.DB().Create(&albumSummary).Error
	} else {
		// 更新数据
		err = source.DB().Model(&albumSummary).Updates(albumSummary).Error
	}
	if err != nil {
		return fmt.Errorf("MaintainAlbumSummary插入或更新专辑摘要 Error：%w", err)
	}
	return nil
}

type AlbumStats struct {
	TotalSales           uint `json:"total_sales"`
	TotalOnShelfProducts uint `json:"total_on_shelf_products"`
}

func GetAlbumStats(productAlbumID uint) (err error, albumStats AlbumStats) {
	// 查询总销量和上架商品总数
	err = source.DB().Table("product_album_relation_products").
		Select("SUM(products.sales) AS total_sales, COUNT(products.id) AS total_on_shelf_products").
		Joins("JOIN products ON product_album_relation_products.product_id = products.id").
		Where("product_album_relation_products.product_album_id = ? AND product_album_relation_products.off_shelf = ?", productAlbumID, 0).
		Scan(&albumStats).Error
	if err != nil {
		return err, AlbumStats{}
	}
	return nil, albumStats
}

type ProductWithImage struct {
	ProductID uint   `json:"product_id"`
	ImageUrl  string `json:"image_url" gorm:"column:image_url;type:varchar(255);comment:图片url;size:255;"`
}

func GetTop3Products(albumID uint) (err error, productWithImage []ProductWithImage) {
	// 查询销量前3的上架商品
	err = source.DB().Table("product_album_relation_products").
		Select("products.id AS product_id, products.image_url").
		Joins("JOIN products ON product_album_relation_products.product_id = products.id").
		Where("product_album_relation_products.product_album_id = ? AND product_album_relation_products.off_shelf = ?", albumID, 0).
		Order("products.sales DESC, product_album_relation_products.id DESC").
		Limit(3).
		Scan(&productWithImage).Error
	if err != nil {
		return fmt.Errorf("GetTop20Products查询销量前3的上架中的商品 Error：%w", err), nil
	}
	return nil, productWithImage
}

type ProductAlbum struct {
	source.Model
	Name     string         `json:"name" form:"name" gorm:"column:name;comment:专辑名称;type:varchar(500);size:500;"`
	Sort     uint           `json:"sort" form:"sort"`
	IsShare  int            `json:"is_share" gorm:"column:is_share;comment:共享状态 1:共享 2：私有;type:int;"`
	Uid      uint           `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
	UserInfo userModel.User `json:"user_info" gorm:"foreignKey:Uid"`
}

func HomeList(info request.HomeListSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.ProductAlbumSummaryHome{})
	var albumSummaries []model.ProductAlbumSummaryHome
	db = db.Where("is_share = ?", 1)
	err = db.Count(&total).Error
	err = db.Order("sort desc, product_album_id  desc").Limit(limit).Offset(offset).Find(&albumSummaries).Error
	return err, albumSummaries, total
}

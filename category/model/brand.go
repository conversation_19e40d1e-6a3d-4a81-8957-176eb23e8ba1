// 自动生成模板Brands
package model

import (
	"gorm.io/gorm"
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type Brand struct {
	source.Model
	Name             string `json:"name" form:"name" gorm:"column:name;comment:品牌名称;type:varchar(255);size:255;index"`
	<PERSON>as            string `json:"alias" form:"alias" gorm:"column:alias;comment:品牌别名;type:varchar(255);size:255;"`
	Desc             string `json:"desc" form:"desc" gorm:"column:desc;comment:描述;type:text;"`
	Logo             string `json:"logo" form:"logo" gorm:"column:logo;comment:品牌logo;type:varchar(255);size:255;"`
	IsRecommend      *bool  `json:"isRecommend" form:"isRecommend" gorm:"column:is_recommend;comment:是否推荐;type:int;size:1;"`
	IsRecommendTitle string `json:"isRecommendTitle"`                                       //启用、禁用
	Source           int    `json:"source"`                                                 //供应链ID
	SupplierID       uint   `json:"supplier_id" form:"supplier_id" gorm:"default:0;index;"` //供应商ID
	ConsumerData     string `json:"consumer_data"`                                          //仓库编号
}

func (b *Brand) AfterFind(tx *gorm.DB) (err error) {

	var is bool = false
	if b.IsRecommend != &is {
		b.IsRecommendTitle = "启用"
	} else {
		b.IsRecommendTitle = "禁用"
	}
	return
}

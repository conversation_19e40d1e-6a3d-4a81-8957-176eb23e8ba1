package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"shama-supply/component/goods"
	"shama-supply/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

func Init(c *gin.Context) {

	var Id yzRequest.GetById
	err := c.ShouldBindJSON(&Id)
	if err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	}
	err = goods.Init(Id.Id)
	if err != nil {
		log.Log().Error("同步失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithMessage("后台同步中，预计10-20分钟同步完成", c)
	}

}

func TestShamaOrderStockOutMessages(c *gin.Context) {

	err := service.ShamaOrderStockOutMessages()
	if err != nil {
		log.Log().Error("同步失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithMessage("成功", c)
	}

}

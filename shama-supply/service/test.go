package service

import (
	"fmt"
	"shama-supply/listener"
)

func ShamaOrderStockOutMessages() (err error) {
	if listener.ShamaSupplyID == nil {
		err = listener.GetShamaGatherInfo()
		if err != nil {
			fmt.Println("OrderStockOut注册失败:不存在shama供应链")
			return
		}
	}
	err = listener.HandleShamaOrderStockOutMessages([]byte("{\"orderSn\":\"233138802073010176\",\"parentOrderSn\":\"233138801787142144\",\"rootOrderSn\":\"233138801745199104\",\"packages\":[{\"logisticsCode\":\"yuantong\",\"logisticsName\":\"圆通速递\",\"waybillCode\":\"111111111111\"}],\"type\":1}"))
	if err != nil {
		return
	}
	return
}

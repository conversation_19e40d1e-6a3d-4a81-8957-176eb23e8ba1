package listener

import (
	"fmt"
	"github.com/streadway/amqp"
	"go.uber.org/zap"
	"public-supply/common"
	"public-supply/model"
	source_remote "shama-supply/remote_source"
	"strconv"
	"time"
	model2 "user/model"
	"yz-go/component/log"
	"yz-go/mq"
	"yz-go/source"
)

var ShamaSupplyID *uint

func GetShamaGatherInfo() (err error) {
	if ShamaSupplyID == nil {
		var gatherSupply model.GatherSupply
		err = source.DB().Where("category_id = ?", common.SUPPLY_SHAMA).First(&gatherSupply).Error
		if err != nil {
			fmt.Println("SkuPriceChange注册失败:不存在shama供应链")
			log.Log().Error("SkuPriceChange注册失败:不存在shama供应链")
		}
		ShamaSupplyID = &gatherSupply.ID
	}

	return
}

var channel = make(map[string]*amqp.Channel)

type Handle func([]byte) error

var handles map[string]Handle

func init() {
	handles = map[string]Handle{}
	mq.PushConsumer(ConsumeRabbitMQ)
}
func PushHandles(queueName string, handle Handle) {
	handles[queueName] = handle
}

func Consumer(queueName string, handle func([]byte) error) {
	// 连接 RabbitMQ 服务器
	channel[queueName] = source_remote.MQ(*ShamaSupplyID)
	// 检查连接是否成功打开
	if channel[queueName] == nil {
		fmt.Println("Failed to open channel")
		return
	}

	// 设置每个消费者一次性获取的消息数量为 1
	//err := channel.Qos(
	//	1,     // prefetch count
	//	0,     // prefetch size
	//	false, // global
	//)
	//if err != nil {
	//	fmt.Println("设置 QoS 失败:", err)
	//	return
	//}

	// 消费消息
	msgs, err := channel[queueName].Consume(
		queueName, // 队列名称
		"shlj"+strconv.Itoa(int(time.Now().Unix()))+model2.RandStr(6), // 消费者标签
		// 消费者标签
		false, // 是否自动应答
		false, // 是否独占
		false, // 是否等待服务器返回
		false, // 是否阻塞
		nil,   // 额外参数
	)
	if err != nil {
		log.Log().Info(queueName+":shama消费者绑定错误:"+err.Error(), zap.Any("err", err))
		// 断线重连逻辑
		reconnectConsumer(queueName, handle)
		return
	}

	forever := make(chan bool)

	// 启动一个协程来消费消息
	go func() {
		for d := range msgs {
			err = handle(d.Body)
			if err != nil {
				fmt.Println(err)
				log.Log().Info("shama消费者错误:"+err.Error(), zap.Any("data", string(d.Body)))
				// 成功接收消息
				err = d.Ack(false)
				if err != nil {
					fmt.Println(err)
					reconnectConsumer(queueName, handle)
					return
				}
			} else {
				// 成功接收消息
				err = d.Ack(true)
				if err != nil {
					fmt.Println(err)
					reconnectConsumer(queueName, handle)
					return
				}
			}
		}

		// 断线重连逻辑
		reconnectConsumer(queueName, handle)
	}()

	fmt.Println(queueName + "绑定成功,Waiting for messages. To exit press CTRL+C")
	<-forever
}

func reconnectConsumer(queueName string, handle Handle) {
	// 断线重连逻辑
	for {
		fmt.Println("Attempting to reconnect...")
		// 关闭当前连接
		if channel[queueName] != nil {
			channel[queueName].Close()
			channel[queueName] = nil
		}

		// 重新连接
		channel[queueName] = source_remote.MQ(*ShamaSupplyID)
		if channel[queueName] != nil {
			// 如果重新连接成功，则重新启动消费者
			go Consumer(queueName, handle)
			return
		}

		// 如果连接失败，等待一段时间后重试
		time.Sleep(1 * time.Second)
	}
}

func ConsumeRabbitMQ() {
	for queueName, handle := range handles {
		go Consumer(queueName, handle)
	}
}

package listener

import (
	"after-sales/model"
	"after-sales/service"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	model4 "order/model"
	common3 "shama-supply/common"
	response2 "shama-supply/response"
	"yz-go/component/log"
	"yz-go/source"
)

func PushShamaAfsCloseConsumer() {
	if ShamaSupplyID == nil {
		err := GetShamaGatherInfo()
		if err != nil {
			fmt.Println("AfsClose注册失败:不存在shama供应链")
			log.Log().Error("AfsClose注册失败:不存在shama供应链")
			return
		}
	}
	requestParams := make(map[string]interface{})
	requestParams["name"] = "afs_close"
	requestJsonData, err := json.Marshal(requestParams)
	if err != nil {
		return
	}
	var result []byte
	err, result = common3.RequestShamaApiPost(requestJsonData, "/supply/mq/subscribe", *ShamaSupplyID)
	if err != nil {
		fmt.Println("SkuPriceChange注册失败:" + err.Error())
		log.Log().Error("SkuPriceChange注册失败:" + err.Error())
		return
	}
	var response response2.ShamaMqSubscribeResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	if response.Code != 200 {
		log.Log().Error("队列订阅接口请求失败:" + response.Msg)
		return
	}
	PushHandles(response.Data.Key, HandleAfsCloseMessages)
}

func HandleAfsCloseMessages(msg []byte) (err error) {
	log.Log().Info("shama接到售后关闭/驳回申请的消息", zap.Any("data", string(msg)))
	var data AfsClose
	err = json.Unmarshal(msg, &data)
	if err != nil {
		log.Log().Info("shama接到售后关闭/驳回申请的消息,执行错误", zap.Any("err", err))
		return nil
	}
	var as model4.AfterSales
	err = source.DB().Where("syn_after_sales_id_string = ?", data.AfsSn).First(&as).Error
	if err != nil {
		log.Log().Info("shama接到售后关闭/驳回申请的消息,执行错误1", zap.Any("err", err))
		return nil
	}
	//审核驳回
	var resAfterSaleAudit model.AfterSalesAudit
	err = source.DB().Where("after_sales_id = ?", as.ID).Last(&resAfterSaleAudit).Error
	if err != nil {
		log.Log().Info("shama接到售后关闭/驳回申请的消息,执行错误2", zap.Any("err", err))
		return nil
	}
	resAfterSaleAudit.Cause = data.Result
	err = service.RejectAudit(resAfterSaleAudit, 0, 0)
	if err != nil {
		log.Log().Info("shama接到售后关闭/驳回申请的消息,执行错误3", zap.Any("err", err))
		return nil
	}
	return nil
}

type AfsClose struct {
	OrderSn      string `json:"orderSn"`
	AfsSn        string `json:"afsSn"`
	ChannelAfsSn string `json:"channelAfsSn"`
	Result       string `json:"result"`
}

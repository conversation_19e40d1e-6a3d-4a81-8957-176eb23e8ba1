package product

import (
	"encoding/json"
	"fmt"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
)

type SearchParams struct {
	Keyword         string `json:"keyword"`         // 搜索关键字，需要编码
	Cid1            int    `json:"cid1"`            // 一级分类
	Cid2            int    `json:"cid2"`            // 二级分类
	CatId           int    `json:"catId"`           // 分类Id,只支持三级类目Id
	PageIndex       int    `json:"pageIndex"`       // 当前第几页
	PageSize        int    `json:"pageSize"`        // 当前页显示
	Min             string `json:"min"`             // 价格区间搜索，低价
	Max             string `json:"max"`             // 价格区间搜索，高价
	Brands          string `json:"brands"`          // 品牌搜索多个品牌以逗号分隔，需要编码
	AreaIds         string `json:"areaIds"`         // 一级地址,二级地址,三级地址英文逗号分隔 传入后且仅redisStore= true搜索地址有货的商品 示例：1,2810,51081
	RedisStore      bool   `json:"redisStore"`      // true仅搜索有货，false全部
	SortType        string `json:"sortType"`        // 销量降序="sale_desc"; 价格升序="price_asc"; 价格降序="price_desc"; 上架时间降序="winsdate_desc"; 按销量排序_15天销售额="sort_totalsales15_desc"; 按15日销量排序="sort_days_15_qtty_desc"; 按30日销量排序="sort_days_30_qtty_desc"; 按15日销售额排序="sort_days_15_gmv_desc"; 按30日销售额排序="sort_days_30_gmv_desc";
	PriceCol        string `json:"priceCol"`        // 价格汇总 priceCol=”yes”
	ExtAttrCol      string `json:"extAttrCol"`      // 扩展属性汇总 extAttrCol=”yes”
	MergeSku        bool   `json:"mergeSku"`        // true:合并同一产品不同规格sku; false:不合并同一产品不同规格sku
	SortExpandAttrs string `json:"sortExpandAttrs"` // 扩展属性筛选JSON,当需要扩展属性筛选时传入此值.且入参extAttrCol需要为”yes”; 入参来源是上次搜索接口返回结果里的字段: expandAttrAggregate, 入参具体格式:[{"expandSortId": "12","expandAttrs": [{"valueId" : "1a23"},{"valueId" : "12a4"}]}],expandSortId取值对应上次搜索结果里的expandAttrAggregate.expandSortId, 而expandAttrs里的valueId取值是expandAttrAggregate.expandAttrs.valueId,即筛选一个大类下的多个子分类, 比如要选苹果手机的内存版本,有128G和256G,其中的'内存版本'就是大类(假设id为100),而128G(假设id为1)和256G(假设id为2)分别是2个小类, 则要筛选128G的内存版本的传参为:[{'expandSortId': '100','expandAttrs': [{'valueId' : '1'}]}],注意把单引号换成双引号并且转义
}

/**
 * 4.12 搜索商品
 *
 * 根据搜索条件查询符合要求的商品列表
 */

func Search(accessToken string, search SearchParams) (result SearchResult, err error) {
	url := "https://bizapi.jd.com/api/search/search"

	params := map[string]interface{}{
		"token":      accessToken,
		"keyword":    search.Keyword,
		"catId":      search.CatId,
		"pageIndex":  search.PageIndex,
		"pageSize":   search.PageSize,
		"min":        search.Min,
		"max":        search.Max,
		"brands":     search.Brands,
		"cid1":       search.Cid1,
		"cid2":       search.Cid2,
		"areaIds":    search.AreaIds,
		"redisStore": search.RedisStore,
		"sortType":   search.SortType,
	}

	resp, err := jdVopPkg.PostForm(url, params)
	if err != nil {
		return
	}

	var response SearchResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Success == false {
		err = fmt.Errorf("搜索商品失败，code：%s，原因：%s", response.ResultCode, response.ResultMessage)
		return
	}
	return response.Result, nil
}

type SearchResponse struct {
	Success       bool         `json:"success"`
	Result        SearchResult `json:"result"`
	ResultCode    string       `json:"resultCode"`
	ResultMessage string       `json:"resultMessage"`
}

type SearchResult struct {
	ResultCount            int64             `json:"resultCount"`
	PageCount              int64             `json:"pageCount"`
	PageSize               int64             `json:"pageSize"`
	PageIndex              int64             `json:"pageIndex"`
	BrandAggregate         BrandAggregate    `json:"brandAggregate"`
	CategoryAggregate      CategoryAggregate `json:"categoryAggregate"`
	ExpandAttrAggregate    string            `json:"expandAttrAggregate"`
	PriceIntervalAggregate []PriceInterval   `json:"priceIntervalAggregate"`
	HitResult              []ProductDetails  `json:"hitResult"`
}

type BrandAggregate struct {
	PinyinAggr []string    `json:"pinyinAggr"`
	BrandList  []BrandInfo `json:"brandList"`
}

type BrandInfo struct {
	ID     string `json:"id"`
	Pinyin string `json:"pinyin"`
	Name   string `json:"name"`
}

type PriceInterval struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

type CategoryAggregate struct {
	FirstCategory  []Category `json:"firstCategory"`
	SecondCategory []Category `json:"secondCategory"`
	ThirdCategory  []Category `json:"thridCategory"`
}

type Category struct {
	CatID    uint   `json:"catId"`
	Count    uint   `json:"count"`
	ParentID uint   `json:"parentId"`
	Field    string `json:"field"`
	Name     string `json:"name"`
	Weight   int    `json:"weight"`
}

type ProductDetails struct {
	WarePId  string `json:"warePId"`  // 商品spuid
	WareID   string `json:"wareId"`   // 商品id
	WareName string `json:"wareName"` // 商品名称
	BrandID  string `json:"brandId"`  // 品牌id
	Brand    string `json:"brand"`    // 品牌名称
	ImageURL string `json:"imageUrl"` // 商品图片url
	Cid1     string `json:"cid1"`     // 一级类目id
	Cid2     string `json:"cid2"`     // 二级类目id
	CatID    string `json:"catId"`    // 三级类目id
	Cid1Name string `json:"cid1Name"` // 一级分类名
	Cid2Name string `json:"cid2Name"` // 二级分类名
	CatName  string `json:"catName"`  // 三级分类名
	WState   string `json:"wstate"`   // 上柜状态，1.有效
	Wyn      string `json:"wyn"`      // 商品状态，1.有效
	Synonyms string `json:"synonyms"` // 商品同义词
}

package balance

import (
	"encoding/json"
	"fmt"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
)

/**
 * 8.1 查询余额
 *
 * 查询金采和预存款余额的余额
 */

func GetUnionBalance(username, accessToken string) (result UnionBalanceResult, err error) {
	url := "https://bizapi.jd.com/api/price/getUnionBalance"

	params := map[string]interface{}{
		"token": accessToken,
		"pin":   username,
		"type":  "1",
	}

	resp, err := jdVopPkg.PostForm(url, params)
	if err != nil {
		return
	}

	var response UnionBalanceResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Success == false {
		err = fmt.Errorf("查询余额失败，code：%s，原因：%s", response.ResultCode, response.ResultMessage)
		return
	}
	return response.Result, nil
}

type UnionBalanceResponse struct {
	Success       bool               `json:"success"`
	Result        UnionBalanceResult `json:"result"`
	ResultCode    string             `json:"resultCode"`
	ResultMessage string             `json:"resultMessage"`
}

type UnionBalanceResult struct {
	Balance UnionBalance `json:"balance"`
}

type UnionBalance struct {
	Pin         string  `json:"pin"`         // 入参的pin值
	RemainLimit float64 `json:"remainLimit"` // 账户余额
}

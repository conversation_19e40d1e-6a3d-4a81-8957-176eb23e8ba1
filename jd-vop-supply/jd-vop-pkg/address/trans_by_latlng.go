package address

import (
	"encoding/json"
	"fmt"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
)

type TransByLatLngParams struct {
	Lng float64 `json:"lng"` // 经度
	Lat float64 `json:"lat"` // 纬度
}

/**
 * 3.7 根据经纬度获取京东地址编码
 *
 * 根据经纬度获取对应的京东地址编码，此方案不能保证获取的地址编码百分百准确，建议优先推荐使用逐级选择的方法
 */

func TransByLatLng(accessToken string, request TransByLatLngParams) (result TransResult, err error) {
	urlStr := "https://bizapi.jd.com/api/area/getJDAddressFromLatLng"

	params := map[string]interface{}{
		"token": accessToken,
		"lng":   request.Lng,
		"lat":   request.Lat,
	}

	resp, err := jdVopPkg.PostForm(urlStr, params)
	if err != nil {
		return
	}

	var response TransResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Success == false {
		err = fmt.Errorf("经纬度转地址编码失败，code：%s，原因：%s", response.ResultCode, response.ResultMessage)
		return
	}
	return response.Result, nil
}

package address

import (
	"encoding/json"
	"fmt"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
)

/**
 * 3.2 查询二级地址
 *
 * 根据京东一级地址ID，查询京东二级地址列表
 */

func GetCity(accessToken string, provinceId int) (result map[string]int, err error) {
	urlStr := "https://bizapi.jd.com/api/area/getCity"

	params := map[string]interface{}{
		"token": accessToken,
		"id":    provinceId,
	}

	resp, err := jdVopPkg.PostForm(urlStr, params)
	if err != nil {
		return
	}

	var response CurlAddressResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Success == false {
		err = fmt.Errorf("查询二级地址失败，code：%s，原因：%s", response.ResultCode, response.ResultMessage)
		return
	}
	return response.Result, nil
}

package address

import "testing"

func TestGetToken(t *testing.T) {

	token := "8kLNCL44FOsHhY7wu1VV1pM12"

	// 3.1 查询一级地址
	// _, _ = GetProvince(token)

	// 3.2 查询二级地址
	// _, _ = GetCity(token, 10)

	// 3.3 查询三级地址
	// _, _ = GetArea(token, 698)

	// 3.4 查询四级地址
	// _, _ = GetTown(token, 45815)

	// 3.5 验证地址有效性
	//_, _ = ValidateAddress(token, ValidateParams{
	//	AccessToken: token,
	//	ProvinceId:  10,
	//	CityId:      698,
	//	AreaId:      45815,
	//	TownId:      52316,
	//})

	// 3.6 地址详情转换京东地址编码
	_, _ = Trans(token, "广州市白云区棠景街道三元里大道1070号")

	// 3.7 根据经纬度获取京东地址编码
	//_, _ = TransByLatLng(token, TransByLatLngParams{
	//	Lat: 45.773500,
	//	Lng: 126.596200,
	//})
}

package goods

import (
	categoryModel "category/model"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"go.uber.org/zap"
	"gorm.io/gorm/clause"
	jdVopPkg "jd-vop-supply/jd-vop-pkg"
	jdVopPkgBalance "jd-vop-supply/jd-vop-pkg/balance"
	jdVopPkgProduct "jd-vop-supply/jd-vop-pkg/product"
	"jd-vop-supply/model"
	"jd-vop-supply/mq_goods"
	"jd-vop-supply/service"
	productModel "product/model"
	publicSupplyCallback "public-supply/callback"
	publicSupplyCommon "public-supply/common"
	publicSupplyModel "public-supply/model"
	publicSupplyRequest "public-supply/request"
	publicSupplyService "public-supply/service"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

type JdVop struct {
	SysSetting  service.SysSetting
	GatherId    uint
	AccessToken string
}

func (jd *JdVop) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (jd *JdVop) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {
	return
}

func (jd *JdVop) CommodityAssembly(list []publicSupplyModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*productModel.Product, recordErrors []publicSupplyModel.SupplyGoodsImportRecordErrors) {
	return
}

func (jd *JdVop) DeleteGoods(id uint) (err error) {
	return
}

func (jd *JdVop) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {
	var result jdVopPkgBalance.UnionBalanceResult
	if result, err = jdVopPkgBalance.GetUnionBalance(jd.SysSetting.BaseInfo.Username, jd.AccessToken); err != nil {
		return
	}

	return nil, result.Balance.RemainLimit * 100
}

func (jd *JdVop) GetGoods(info publicSupplyRequest.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {
	db := service.SearchJdVopProductGorm(toJdVopProductSearch(info))

	//db.Where("sku_id IN (100134554708,100114256884, 100113496627, 100019344523, 100005829893, 6536559, 100104258549)")

	if err = db.Count(&total).Error; err != nil {
		return
	}

	limit := info.Limit
	offset := info.Limit * (info.Page - 1)

	var list []model.JdVopProduct
	if err = db.Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return
	}

	var products []publicSupplyModel.Goods
	for _, item := range list {
		products = append(products, jdVopProductToGoods(item))
	}

	return err, products, total, 0
}

func (jd *JdVop) ImportGoodsRun(info publicSupplyRequest.GetGoodsSearch) (err error, data interface{}) {
	db := service.SearchJdVopProductGorm(toJdVopProductSearch(info))

	// 导入数量
	var total int64
	if err = db.Count(&total).Error; err != nil || total <= 0 {
		return
	}

	// 从选品库查询数据
	var jdVopProducts []model.JdVopProduct
	if err = db.Preload(clause.Associations).Find(&jdVopProducts).Error; err != nil {
		return
	}

	// 查询已导入的 importIdsMap
	var importIdsMap map[uint]bool
	if importIdsMap, err = service.GetImportIdsMap(info.GatherSupplyID); err != nil {
		return
	}

	var (
		handleCount int
		handleIds   []uint               // 待操作的ids
		handleGoods []model.JdVopProduct // 待操作的商品列表
	)
	// 循环数据，组装最终操作需要的数据
	for _, jdVopProduct := range jdVopProducts {
		if _, ok := importIdsMap[jdVopProduct.SkuId]; !ok {
			handleCount++
			handleIds = append(handleIds, jdVopProduct.SkuId)
			handleGoods = append(handleGoods, jdVopProduct)
		}
	}

	// 搜索条件字符串
	var searchText []byte
	if searchText, err = json.Marshal(info); err != nil {
		return
	}

	importRecord := publicSupplyModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             getOrderNo(),
		EstimatedQuantity: int(total),
		Status:            1,
		Source:            strconv.Itoa(publicSupplyCommon.JD_VOP_SOURCE),
		RepeatQuantity:    int(total) - handleCount,
		SearchCriteria:    string(searchText),
	}
	if err = source.DB().Create(&importRecord).Error; err != nil || total <= 0 {
		return
	}

	// 更改选品库导入状态
	if err = source.DB().Model(model.JdVopProduct{}).Where("sku_id in ?", handleIds).Update("is_import", 1).Error; err != nil {
		return
	}

	cateList := strings.Split(info.Categorys, ",")
	var cateId1, cateId2, cateId3 int
	if len(cateList) >= 3 {
		cateId1, _ = strconv.Atoi(cateList[0])
		cateId2, _ = strconv.Atoi(cateList[1])
		cateId3, _ = strconv.Atoi(cateList[2])
	}

	var listGoods []*productModel.Product
	var recordError []publicSupplyModel.SupplyGoodsImportRecordErrors
	listGoods, recordError, err = jd.convertProduct(handleGoods, cateId1, cateId2, cateId3)
	if err != nil {
		return
	}

	if len(listGoods) > 0 {
		err = publicSupplyService.FinalProcessing(listGoods, importRecord.Batch)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = publicSupplyService.FinalProcessingError(recordError, importRecord.Batch)
		if err != nil {
			return
		}
	}

	// 更新商品导入记录状态
	err = source.DB().Model(publicSupplyModel.SupplyGoodsImportRecord{}).Where("batch=?", importRecord.Batch).Update("completion_status", 1).Error

	return
}

func (jd *JdVop) GetCategory(info publicSupplyRequest.GetCategorySearch) (err error, data interface{}) {
	return
}

func (jd *JdVop) GetGroup() (err error, data interface{}) {
	return
}

func (jd *JdVop) GetCategoryChild(pid int, info publicSupplyRequest.GetCategoryChild) (err error, data interface{}) {
	var categories []model.JdVopCategory
	if err = source.DB().Where("parent_id = ?", pid).Find(&categories).Error; err != nil {
		return
	}

	return nil, categories
}

func (jd *JdVop) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	return
}

func (jd *JdVop) GoodsPriceAlert(GoodsData publicSupplyCallback.GoodsCallBack) (err error) { return }

func (jd *JdVop) InitSetting(gatherId uint) (err error) {
	// 查询基础设置
	if jd.SysSetting, err = service.GetSetting(gatherId); err != nil {
		return
	}

	if jd.SysSetting.BaseInfo.ClientSecret == "" || jd.SysSetting.BaseInfo.ClientID == "" || jd.SysSetting.BaseInfo.Username == "" || jd.SysSetting.BaseInfo.Password == "" {
		err = errors.New("请先填写供应链配置")
		return
	}

	// 获取当前采集源ID
	jd.GatherId = gatherId

	// 初始化配置信息
	jdVopConfig := jdVopPkg.JdVopConfig{
		Username:     jd.SysSetting.BaseInfo.Username,
		Password:     jd.SysSetting.BaseInfo.Password,
		ClientId:     jd.SysSetting.BaseInfo.ClientID,
		ClientSecret: jd.SysSetting.BaseInfo.ClientSecret,
	}

	// 生成 access_token
	accessTokenKey := fmt.Sprintf("JD_VOP_ACCESS_TOKEN_%d", gatherId)

	var ctx = context.Background()
	if jd.AccessToken, _ = source.Redis().Get(ctx, accessTokenKey).Result(); jd.AccessToken == "" {
		var result jdVopPkg.AccessTokenResult
		if result, err = jdVopPkg.GetAccessToken(jdVopConfig); err != nil {
			return
		}

		if err = source.Redis().SetEX(ctx, accessTokenKey, result.AccessToken, 24*time.Hour).Err(); err != nil {
			return
		}

		jd.AccessToken = result.AccessToken
	}

	return
}

func (jd *JdVop) ImportSelectGoodsRun(info publicSupplyModel.SelectGoods) (err error, list interface{}) {
	importCount := len(info.List)
	if importCount <= 0 {
		err = errors.New("导入的是空数据")
		return
	}

	// 根据提交数据获取 submitIds
	var submitIds []uint
	for _, item := range info.List {
		submitIds = append(submitIds, uint(item.ID))
	}

	db := service.SearchJdVopProductGorm(service.SearchJdVopProductParams{
		GatherId: info.GatherSupplyID,
		SkuIds:   submitIds,
	})

	// 从选品库查询数据
	var jdVopProducts []model.JdVopProduct
	if err = db.Preload(clause.Associations).Find(&jdVopProducts).Error; err != nil {
		log.Log().Error("京东VOP 选品库数据获取失败", zap.Any("err", err))
		return
	}

	// 查询已导入的 importIdsMap
	var importIdsMap map[uint]bool
	if importIdsMap, err = service.GetImportIdsMap(info.GatherSupplyID); err != nil {
		return
	}

	var (
		handleCount int
		handleIds   []uint               // 待操作的ids
		handleGoods []model.JdVopProduct // 待操作的商品列表
	)
	// 循环数据，组装最终操作需要的数据
	for _, jdVopProduct := range jdVopProducts {
		if _, ok := importIdsMap[jdVopProduct.SkuId]; !ok {
			handleCount++
			handleIds = append(handleIds, jdVopProduct.SkuId)
			handleGoods = append(handleGoods, jdVopProduct)
		}
	}

	importRecord := publicSupplyModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             getOrderNo(),
		EstimatedQuantity: importCount,
		Status:            1,
		Source:            strconv.Itoa(publicSupplyCommon.JD_VOP_SOURCE),
		RepeatQuantity:    importCount - handleCount,
	}

	// 没有导入数据，则直接完成
	if handleCount <= 0 {
		importRecord.Status = 2
		importRecord.CompletionStatus = 1
	}
	if err = source.DB().Create(&importRecord).Error; err != nil || handleCount <= 0 {
		return
	}

	// 更改选品库导入状态
	if err = source.DB().Model(model.JdVopProduct{}).Where("sku_id in ?", handleIds).Update("is_import", 1).Error; err != nil {
		return
	}

	cateList := strings.Split(info.Categorys, ",")
	var cateId1, cateId2, cateId3 int
	if len(cateList) >= 3 {
		cateId1, _ = strconv.Atoi(cateList[0])
		cateId2, _ = strconv.Atoi(cateList[1])
		cateId3, _ = strconv.Atoi(cateList[2])
	}

	var listGoods []*productModel.Product
	var recordError []publicSupplyModel.SupplyGoodsImportRecordErrors
	listGoods, recordError, err = jd.convertProduct(handleGoods, cateId1, cateId2, cateId3)
	if err != nil {
		return
	}

	if len(listGoods) > 0 {
		err = publicSupplyService.FinalProcessing(listGoods, importRecord.Batch)
		if err != nil {
			return
		}
	}
	if len(recordError) > 0 {
		err = publicSupplyService.FinalProcessingError(recordError, importRecord.Batch)
		if err != nil {
			return
		}
	}

	// 更新商品导入记录状态
	err = source.DB().Model(publicSupplyModel.SupplyGoodsImportRecord{}).Where("batch=?", importRecord.Batch).Update("completion_status", 1).Error
	return
}

func (jd *JdVop) InitGoods() (err error) {
	// 通过商品搜索接口获取分类、品牌等信息，写入本地数据库
	var skuSearchResult jdVopPkgProduct.SearchResult
	if skuSearchResult, err = jdVopPkgProduct.Search(jd.AccessToken, jdVopPkgProduct.SearchParams{}); err != nil {
		return
	}

	// 同步商品品牌
	if err = jd.synchronizeBrandToLocal(skuSearchResult.BrandAggregate.BrandList); err != nil {
		return
	}

	// 同步商品分类
	if err = jd.synchronizeCategoryToLocal(skuSearchResult.CategoryAggregate); err != nil {
		return
	}

	// 如果商品总数量为0，直接返回
	if skuSearchResult.ResultCount == 0 {
		return
	}

	// 根据商品池，全量更新选品库商品信息
	var poolResult []jdVopPkgProduct.PoolResult
	if poolResult, err = jdVopPkgProduct.GetPool(jd.AccessToken); err != nil {
		return
	}

	for _, pool := range poolResult {
		// 获取上游 pool 下所有sku
		var poolAllSkus []uint
		poolAllSkus, err = jdVopPkgProduct.GetPoolAllSkus(jd.AccessToken, jdVopPkgProduct.PoolSkuParams{
			PageNum:  pool.PageNum,
			PageSize: 1000,
			Offset:   0,
		})
		if err != nil {
			return
		}

		poolAllSkuIdsMap := make(map[uint]bool, len(poolAllSkus))
		for _, skuId := range poolAllSkus {
			poolAllSkuIdsMap[skuId] = true
		}

		// 查询数据库中所有的 SKU
		var dbSkus []uint
		if err = source.DB().Model(model.JdVopProduct{}).Where("gather_id = ? AND pool = ?", jd.GatherId, pool.PageNum).Pluck("sku_id", &dbSkus).Error; err != nil {
			return
		}

		// 找出 poolAllSkus 中存在但数据库中不存在的 SKU
		var missingSkus []uint
		for _, skuId := range dbSkus {
			if _, ok := poolAllSkuIdsMap[skuId]; !ok {
				missingSkus = append(missingSkus, skuId)
			}
		}

		// 删除选品库中上游已删除的商品
		if len(missingSkus) > 0 {
			err = service.BatchDeleteDatabaseBySkuIds(jd.GatherId, pool.PageNum, missingSkus)
			if err != nil {
				return
			}
		}

		// 循环 skus 同步或更新到本地选品库，上游限制，商品单价单次最多100个，此处拆分
		for _, skuIds := range chunkSlice(poolAllSkus, 100) {
			// 放入 mq 自动更新
			err = mq_goods.PublishMessage(mq_goods.Message{
				MessageType: mq_goods.UpdateSkuDatabase,
				GatherId:    jd.GatherId,
				SkuIds:      skuIds,
				Level:       1,
				Pool:        pool.PageNum,
			})
			if err != nil {
				return
			}
		}
	}
	return
}

func (jd *JdVop) RunConcurrent(wg *sync.WaitGroup, info publicSupplyRequest.GetCategorySearch, i int) (err error) {
	return
}

func (jd *JdVop) SynchronizeProductsToLocal() (err error) {
	return
}

func (jd *JdVop) synchronizeBrandToLocal(brands []jdVopPkgProduct.BrandInfo) (err error) {
	var brandMap map[uint]model.JdVopBrand
	if brandMap, err = service.GetBrandIdMap(jd.GatherId); err != nil {
		return
	}

	var (
		insertBrands []model.JdVopBrand
		updateBrands []model.JdVopBrand
	)
	for _, item := range brands {
		iMd5s := service.ToMd5(item)
		id, _ := strconv.ParseUint(item.ID, 10, 0)
		if brand, ok := brandMap[uint(id)]; ok {
			// 检查 MD5 是否相同
			if brand.Md5 == iMd5s {
				continue
			}
			updateBrands = append(updateBrands, model.JdVopBrand{
				Model:    source.Model{ID: brand.ID},
				GatherID: jd.GatherId,
				BrandID:  uint(id),
				Name:     item.Name,
				Pinyin:   item.Pinyin,
				Md5:      iMd5s,
			})
		} else {
			insertBrands = append(insertBrands, model.JdVopBrand{
				GatherID: jd.GatherId,
				BrandID:  uint(id),
				Name:     item.Name,
				Pinyin:   item.Pinyin,
				Md5:      iMd5s,
			})
		}
	}

	if len(updateBrands) > 0 {
		if err = service.BatchUpdateBrands(updateBrands); err != nil {
			return
		}
	}

	if len(insertBrands) > 0 {
		if err = service.BatchInsertBrands(insertBrands); err != nil {
			return
		}
	}

	return
}

func (jd *JdVop) synchronizeCategoryToLocal(categories jdVopPkgProduct.CategoryAggregate) (err error) {
	var categoriesMap map[uint]model.JdVopCategory
	if categoriesMap, err = service.GetCategoryIdMap(jd.GatherId); err != nil {
		return
	}

	var (
		insertCategories []model.JdVopCategory
		updateCategories []model.JdVopCategory
	)
	// 处理分类函数
	processCategories := func(catItems []jdVopPkgProduct.Category, catClass int) {
		for _, item := range catItems {
			iMd5s := service.ToMd5(item)
			if category, ok := categoriesMap[item.CatID]; ok {
				// 检查 MD5 是否相同
				if category.Md5 == iMd5s {
					continue
				}
				updateCategories = append(updateCategories, model.JdVopCategory{
					Model:    source.Model{ID: category.ID},
					GatherID: jd.GatherId,
					CatID:    item.CatID,
					ParentID: item.ParentID,
					Name:     item.Name,
					Weight:   item.Weight,
					CatClass: catClass,
					Md5:      iMd5s,
				})
			} else {
				insertCategories = append(insertCategories, model.JdVopCategory{
					GatherID: jd.GatherId,
					CatID:    item.CatID,
					ParentID: item.ParentID,
					Name:     item.Name,
					Weight:   item.Weight,
					CatClass: catClass,
					Md5:      iMd5s,
				})
			}
		}
	}

	// 处理一级、二级和三级分类
	processCategories(categories.FirstCategory, 1)
	processCategories(categories.SecondCategory, 2)
	processCategories(categories.ThirdCategory, 3)

	// 批量更新和插入分类
	if len(updateCategories) > 0 {
		if err = service.BatchUpdateCategories(updateCategories); err != nil {
			return
		}
	}

	if len(insertCategories) > 0 {
		if err = service.BatchInsertCategories(insertCategories); err != nil {
			return
		}
	}

	return
}

func (jd *JdVop) RunSelectGoodsConcurrent(orderPN string, list []publicSupplyModel.Goods, category string, key string, gatherSupplyID uint) (err error) {
	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var (
		listGoods   []*productModel.Product
		recordError []publicSupplyModel.SupplyGoodsImportRecordErrors
	)
	err, listGoods, recordError = jd.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}

	if len(listGoods) > 0 {
		if err = publicSupplyService.FinalProcessing(listGoods, orderPN); err != nil {
			return
		}
	}

	if len(recordError) > 0 {
		if err = publicSupplyService.FinalProcessingError(recordError, orderPN); err != nil {
			return
		}
	}

	return
}

func (jd *JdVop) convertProduct(list []model.JdVopProduct, cateId1, cateId2, cateId3 int) (listGoods []*productModel.Product, recordErrors []publicSupplyModel.SupplyGoodsImportRecordErrors, err error) {
	for _, item := range list {
		product := productModel.Product{
			ProductModel: productModel.ProductModel{
				FreightType:    2,
				Source:         publicSupplyCommon.JD_VOP_SOURCE,
				SourceGoodsID:  item.SkuId,
				GatherSupplyID: jd.GatherId,
				Title:          item.Title,
				ImageUrl:       item.ImageUrl,
				Gallery:        item.Gallery,
				DetailImages:   item.Introduction,
				Unit:           item.Unit,
				MD5:            item.Md5,
				Stock:          9999,
				IsPlugin:       0,
				IsDisplay:      1,
				SingleOption:   1,
				Price:          item.AgreementPrice,
				CostPrice:      item.CostPrice,
				GuidePrice:     item.GuidePrice,
				OriginPrice:    item.MarketPrice,
				ActivityPrice:  item.ActivityPrice,
			},
		}

		// 定价策略
		if jd.SysSetting.Pricing.Strategy == 2 {
			// 供货价
			if jd.SysSetting.Pricing.YzhNewSupplySales == 1 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplySalesMarketing, 10, 32)
				price := float64(item.ActivityPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.Price = uint(priceRes * 100)
			}
			if jd.SysSetting.Pricing.YzhNewSupplySales == 2 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplySalesAgreement, 10, 32)
				price := float64(item.AgreementPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.Price = uint(priceRes * 100)
			}

			// 零售价
			if jd.SysSetting.Pricing.YzhNewSupplyOrigin == 1 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplyOriginAgreement, 10, 32)
				price := float64(item.AgreementPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.OriginPrice = uint(priceRes * 100)
			}
			if jd.SysSetting.Pricing.YzhNewSupplyOrigin == 2 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplyOriginMarketing, 10, 32)
				price := float64(item.ActivityPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.OriginPrice = uint(priceRes * 100)
			}

			// 指导价
			if jd.SysSetting.Pricing.YzhNewSupplyGuide == 1 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplyGuideAgreement, 10, 32)
				price := float64(item.AgreementPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.GuidePrice = uint(priceRes * 100)
			}
			if jd.SysSetting.Pricing.YzhNewSupplyGuide == 2 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplyGuideMarketing, 10, 32)
				price := float64(item.ActivityPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.GuidePrice = uint(priceRes * 100)
			}

			// 营销价
			if jd.SysSetting.Pricing.YzhNewSupplyActivity == 1 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplyActivityAgreement, 10, 32)
				price := float64(item.AgreementPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.GuidePrice = uint(priceRes * 100)
			}
			if jd.SysSetting.Pricing.YzhNewSupplyActivity == 2 {
				rate, _ := strconv.ParseUint(jd.SysSetting.Pricing.YzhNewSupplyActivityMarketing, 10, 32)
				price := float64(item.ActivityPrice/100) * float64(rate) / 100
				priceStr := fmt.Sprintf("%.2f", price)
				priceRes, _ := strconv.ParseFloat(priceStr, 64)

				product.GuidePrice = uint(priceRes * 100)
			}
		}

		// 品牌处理
		if item.BrandName != "" {
			brand := categoryModel.Brand{
				Name:   item.BrandName,
				Source: int(jd.GatherId),
			}
			if err = source.DB().Where(brand).FirstOrCreate(&brand).Error; err != nil {
				return
			}
			product.BrandID = brand.ID
		}

		// 分类处理
		if jd.SysSetting.UpdateInfo.Category == 1 {
			display := 1
			cate1 := categoryModel.Category{
				CategoryModel: categoryModel.CategoryModel{
					Name:      item.Category1.Name,
					ParentID:  0,
					Level:     1,
					IsDisplay: &display,
				},
			}
			source.DB().Where("name=? and level=? and parent_id=?", cate1.Name, 1, 0).FirstOrCreate(&cate1)

			cate2 := categoryModel.Category{
				CategoryModel: categoryModel.CategoryModel{
					Name:      item.Category2.Name,
					ParentID:  cate1.ID,
					Level:     2,
					IsDisplay: &display,
				},
			}
			source.DB().Where("name=? and level=? and parent_id=?", cate2.Name, 2, cate1.ID).FirstOrCreate(&cate2)

			cate3 := categoryModel.Category{
				CategoryModel: categoryModel.CategoryModel{
					Name:      item.Category3.Name,
					ParentID:  cate2.ID,
					Level:     3,
					IsDisplay: &display,
				},
			}
			source.DB().Where("name=? and level=? and parent_id=?", cate3.Name, 3, cate2.ID).FirstOrCreate(&cate3)

			product.Category1ID = cate1.ID
			product.Category2ID = cate2.ID
			product.Category3ID = cate3.ID
		}

		if cateId1 > 0 {
			product.Category1ID = uint(cateId1)
		}
		if cateId2 > 0 {
			product.Category2ID = uint(cateId2)
		}
		if cateId3 > 0 {
			product.Category3ID = uint(cateId3)
		}

		// 规格
		product.Skus = []productModel.Sku{
			{
				SkuModel: productModel.SkuModel{
					OriginalSkuID: int64(product.SourceGoodsID),
					Title:         product.Title,
					Stock:         9999,
					IsDisplay:     1,
					ImageUrl:      product.ImageUrl,
					Price:         product.Price,
					CostPrice:     product.CostPrice,
					GuidePrice:    product.GuidePrice,
					OriginPrice:   product.OriginPrice,
					ActivityPrice: product.ActivityPrice,
					Options: productModel.Options{
						productModel.Option{
							SpecName:     "规格",
							SpecItemName: "默认",
						},
					},
				},
			},
		}

		listGoods = append(listGoods, &product)
	}
	return
}

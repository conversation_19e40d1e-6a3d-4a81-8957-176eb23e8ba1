package order

import (
	_ "embed"
	"encoding/json"
	"fmt"
	v1 "order/api/v1"
	"order/express"
	"strings"
	"yz-go/component/log"
)

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

type Company struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func ExpressList(name string) (err error, code string) {

	for _, item := range GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

type ExpressName struct {
	Name string `json:"name"`
	NO   string `json:"no"`
}

var Companies []Company

//go:embed kd.json
var kd string

func GetCompanyList() []Company {
	if Companies == nil {
		// 公司列表
		err := json.Unmarshal([]byte(kd), &Companies)
		if err != nil {
			log.Log().Error(err.Error())
		}
	}
	return Companies
}

type Response struct {
	Code int                    `json:"code"`
	Data []express.OrderExpress `json:"data"`
	Msg  string                 `json:"msg"`
}

type ExpressInfo struct {
	Data struct {
		Info struct {
			Name string `json:"name"`
			No   string `json:"no"`
		} `json:"info"`
	} `json:"data"`
}

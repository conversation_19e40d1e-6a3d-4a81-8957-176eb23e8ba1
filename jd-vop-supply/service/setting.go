package service

import (
	"encoding/json"
	publicSupplySetting "public-supply/setting"
	"strconv"
	yzModel "yz-go/model"
)

type SysSetting struct {
	BaseInfo   BaseInfo   `json:"baseInfo"`
	UpdateInfo UpdateInfo `json:"update"`
	Pricing    Pricing    `json:"pricing"`
}

type BaseInfo struct {
	ClientSecret string `json:"client_secret"`
	ClientID     string `json:"client_id"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	StoreName    string `json:"storeName"`
	TicketTel    string `json:"ticket_tel"`
	TicketTitle  string `json:"ticket_title"`
	TicketNumber string `json:"ticket_number"`
}

type UpdateInfo struct {
	Cron         string `json:"cron"`
	BaseInfo     uint   `json:"baseInfo"`
	Category     uint   `json:"category"`
	CostPrice    uint   `json:"costPrice"`
	CurrentPrice uint   `json:"currentPrice"`
}

type Pricing struct {
	Strategy                          int    `json:"strategy"`
	YzhNewSupplySales                 int    `json:"YzhNewSupplySales"`
	YzhNewSupplySalesGuide            string `json:"YzhNewSupplySalesGuide"`
	YzhNewSupplySalesAgreement        string `json:"YzhNewSupplySalesAgreement"`
	YzhNewSupplySalesMarketing        string `json:"YzhNewSupplySalesMarketing"`
	YzhNewSupplySalesOriginalPrice    string `json:"YzhNewSupplySalesOriginalPrice"`
	YzhNewSupplyCost                  int    `json:"YzhNewSupplyCost"`
	YzhNewSupplyCostAgreement         string `json:"YzhNewSupplyCostAgreement"`
	YzhNewSupplyCostMarketing         string `json:"YzhNewSupplyCostMarketing"`
	YzhNewSupplyCostOriginalPrice     string `json:"YzhNewSupplyCostOriginalPrice"`
	YzhNewSupplyOrigin                int    `json:"YzhNewSupplyOrigin"`
	YzhNewSupplyOriginAgreement       string `json:"YzhNewSupplyOriginAgreement"`
	YzhNewSupplyOriginMarketing       string `json:"YzhNewSupplyOriginMarketing"`
	YzhNewSupplyOriginOriginalPrice   string `json:"YzhNewSupplyOriginOriginalPrice"`
	YzhNewSupplyActivity              int    `json:"YzhNewSupplyActivity"`
	YzhNewSupplyActivityAgreement     string `json:"YzhNewSupplyActivityAgreement"`
	YzhNewSupplyActivityMarketing     string `json:"YzhNewSupplyActivityMarketing"`
	YzhNewSupplyActivityOriginalPrice string `json:"YzhNewSupplyActivityOriginalPrice"`
	YzhNewSupplyGuide                 int    `json:"YzhNewSupplyGuide"`
	YzhNewSupplyGuideAgreement        string `json:"YzhNewSupplyGuideAgreement"`
	YzhNewSupplyGuideMarketing        string `json:"YzhNewSupplyGuideMarketing"`
	YzhNewSupplyGuideOriginalPrice    string `json:"YzhNewSupplyGuideOriginalPrice"`
}

func GetSetting(gatherId uint) (data SysSetting, err error) {
	var sysSetting yzModel.SysSetting
	if err, sysSetting = publicSupplySetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherId))); err != nil {
		return
	}

	if err = json.Unmarshal([]byte(sysSetting.Value), &data); err != nil {
		return
	}
	return
}

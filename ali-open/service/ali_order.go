package service

import (
	"ali-open/model"
	request2 "ali-open/request"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	url2 "net/url"
	model2 "order/model"
	"public-supply/request"
	"strconv"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

func (ali *<PERSON><PERSON>) ConfirmOrder(request request.RequestConfirmOrder, skuMap model.SkuMap, key []string, SkuMap model.SkuMapSetting) (err error) {
	stringArr := key
	shopId := stringArr[0]
	url := "https://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.fastCreateOrder/" + ali.Key

	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	if stringArr[2] == "1" {
		reqData.Add("flow", "general")
	} else {
		reqData.Add("flow", "saleproxy")

	}
	AddressOrder := make(map[string]string)
	//OrderSku := make(map[string]string)
	var Consignee string
	if len(request.RequestSaleBeforeCheck.Address.Consignee) == 3 {
		Consignee = request.RequestSaleBeforeCheck.Address.Consignee + "*"
	} else {
		Consignee = request.RequestSaleBeforeCheck.Address.Consignee
	}
	AddressOrder["fullName"] = Consignee
	AddressOrder["mobile"] = request.RequestSaleBeforeCheck.Address.Phone
	AddressOrder["phone"] = request.RequestSaleBeforeCheck.Address.Phone
	AddressOrder["postCode"] = "000000"
	AddressOrder["cityText"] = request.Address.City
	AddressOrder["provinceText"] = request.Address.Province
	AddressOrder["areaText"] = request.Address.Area
	AddressOrder["townText"] = request.Address.Street
	AddressOrder["address"] = request.Address.Description
	AddressData, err := json.Marshal(AddressOrder)

	//OrderSku["offerId"] = "610831911087"
	//OrderSku["specId"] = "96cb8e080867bf37312718893de9b736"
	//OrderSku["quantity"] = "1"
	SkuData, err := json.Marshal(skuMap)

	reqData.Add("addressParam", string(AddressData))
	reqData.Add("cargoParamList", string(SkuData))
	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))
	log.Log().Info("alibb订单请求数据", zap.Any("info", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("ali下单返回数据：", zap.Any("info", string(resData)))

	var orderRes model.OrderRes
	var order model2.Order
	err = json.Unmarshal(resData, &orderRes)
	if err != nil {
		return
	}
	order.GatherSupplyType = 7

	if orderRes.Success != true {
		log.Log().Error("阿里巴巴下单错误", zap.Any("err", orderRes))
		order.GatherSupplyMsg = orderRes.Message
		err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
		return
	}

	Note := "阿里巴巴已同步下单" + " - 账户:" + shopId + "- 单号：" + orderRes.Result.OrderId

	var orderInfo model2.Order
	source.DB().Where("order_sn=?", request.OrderSn.OrderSn).First(&orderInfo)

	note := orderInfo.Note + "-" + Note
	//source.DB().Table("orders").Where("order_sn=?", request.OrderSn.OrderSn).UpdateColumn("note", gorm.Expr("CONCAT_WS('-',note,?)", Note))
	source.DB().Table("orders").Where("order_sn=?", request.OrderSn.OrderSn).Updates(map[string]interface{}{"note": note, "gather_supply_sn": gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", orderRes.Result.OrderId), "gather_supply_type": 7})

	//source.DB().Table("orders").Where("order_sn=?", request.OrderSn.OrderSn).UpdateColumn("gather_supply_sn", gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", orderRes.Result.OrderId))

	if orderRes.Result.OrderId != "" {
		orderID, _ := strconv.Atoi(orderRes.Result.OrderId)
		var orderList []model.AliOrder
		for _, items := range skuMap {
			orderList = append(orderList, model.AliOrder{
				OrderID:   orderRes.Result.OrderId,
				ProductID: items.OfferId,
				SkuID:     items.SpecId,
				ShopID:    shopId,
				OrderSN:   request.OrderSn.OrderSn,
			})

		}

		err = source.DB().Create(&orderList).Error
		if err != nil {
			log.Log().Error("创建阿里巴巴订单错误~", zap.Any("err", err))
		}

		log.Log().Info("阿里支付设置", zap.Any("info", stringArr[1]))
		if stringArr[1] != "1" {

			ali.Pay(int64(orderID))

		} else {
			log.Log().Info("阿里支付设置取商品循环", zap.Any("info", SkuMap))

			var autoPay int64 = 0
			for _, item := range SkuMap { //两个商品 只要有一个 设置不自动支付   两单就都不自动付款
				log.Log().Info("阿里支付设置取商品循环：", zap.Any("info", item))
				autoPay = autoPay + item.AutoPay
			}

			if autoPay == 0 {
				ali.Pay(int64(orderID))
			}

		}
	}

	return
}

func (ali *Alibb) Pay(orderID int64) (err error) {

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.pay.protocolPay.preparePay/" + ali.Key

	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	var Data = make(map[string]int64)
	Data["orderId"] = orderID

	var jsonData []byte
	jsonData, err = json.Marshal(Data)
	if err != nil {
		log.Log().Error("阿里巴巴支付错误", zap.Any("err", err))
		return
	}

	reqData.Add("tradeWithholdPreparePayParam", string(jsonData))
	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))

	var resData []byte
	log.Log().Info("aliopen 支付请求信息", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("aliopen 支付返回信息", zap.Any("info", string(resData)))

	var payRes model.PayRes

	err = json.Unmarshal(resData, &payRes)
	if err != nil {
		log.Log().Error("阿里巴巴支付错误1", zap.Any("err", err))
		return
	}

	if payRes.Success != true {
		updateErr := source.DB().Model(model.AliOrder{}).Where("order_id=?", orderID).Updates(map[string]interface{}{"pay_status": 1}).Error
		if err != nil {
			log.Log().Error("阿里巴巴更新支付状态为失败1", zap.Any("err", updateErr))
			return
		}
	} else {
		updateErr := source.DB().Model(model.AliOrder{}).Where("order_id=?", orderID).Updates(map[string]interface{}{"pay_status": 0}).Error
		if err != nil {
			log.Log().Error("阿里巴巴更新支付状态为失败0", zap.Any("err", updateErr))
			return
		}
	}

	log.Log().Info("阿里巴巴支付返回结果", zap.Any("err", payRes))

	return
}

// 获取物流发货信息
func (ali *Alibb) GetLogisticsInfos(orderID string) (err error, CompanyName, No string) {

	if ali.Key == "" {
		ali.Init()
	}
	if ali.Key == "" {
		return
	}

	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.logistics/alibaba.trade.getLogisticsInfos.buyerView/" + ali.Key

	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	reqData.Add("orderId", orderID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))
	log.Log().Info("请求数据", zap.Any("info", reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)

	var LogisticsInfo request2.LogisticsInfo
	json.Unmarshal(resData, &LogisticsInfo)

	if LogisticsInfo.Success == true {

		for _, item := range LogisticsInfo.Result {
			if item.OrderEntryIds == orderID {
				CompanyName = item.LogisticsCompanyName
				No = item.LogisticsBillNo
				return

			}
		}

		//for _, item := range LogisticsInfo.Result {
		//
		//	fmt.Println(item.LogisticsCompanyName, item.LogisticsBillNo)
		//
		//}
		fmt.Println("已经发货")
	}

	//fmt.Println(string(resData))

	return
}

func (ali *Alibb) AlibbOrderSelect(orderID string, productID int64) (err error, CompanyName, No string) {
	url := "http://gw.open.1688.com/openapi/param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/" + ali.Key

	reqData := url2.Values{}
	reqData.Add("access_token", ali.Token)
	reqData.Add("orderId", orderID)
	reqData.Add("webSite", "1688")

	reqData.Add("_aop_signature", Sign(url, ali.Secret, reqData))

	var resData []byte
	err, resData = utils.PostForm(url, reqData, nil)
	log.Log().Info("alibb供应链代发货订单查询返回结果", zap.Any("info", string(resData)))

	var ResOrderData request2.ResOrderData

	json.Unmarshal(resData, &ResOrderData)
	if ResOrderData.Success != "true" {
		log.Log().Info("alibb供应链代发货订单查询错误", zap.Any("info", string(resData)))

		return
	}

	for _, item := range ResOrderData.Result.ProductItems {

		if item.ProductID == productID {

			for _, LogisticsItems := range ResOrderData.Result.NativeLogistics.LogisticsItems {

				if strings.Contains(LogisticsItems.SubItemIds, item.SubItemIDString) {
					CompanyName = LogisticsItems.LogisticsCompanyName
					No = LogisticsItems.LogisticsBillNo
					if LogisticsItems.LogisticsCompanyName == "" {
						CompanyName = LogisticsItems.NoLogisticsName
						No = LogisticsItems.NoLogisticsTel
					}

					return
					//err, CompanyName, No = ali.GetLogisticsInfos(item.SubItemIDString)

				}

			}

			return

		}

	}

	fmt.Println(string(resData))

	return

}

package model

import (
	"product/model"
	pubmodel "public-supply/model"
	"yz-go/source"
)

type SaleLimitList struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  []struct {
		GoodsSkuCode  string `json:"goodsSkuCode"`
		SaleLimitType int    `json:"saleLimitType"`
		ProvinceIds   string `json:"provinceIds"`
		CityIds       string `json:"cityIds"`
		AreaIds       string `json:"areaIds"`
	} `json:"result"`
	Time int64 `json:"time"`
}
type YzhSaleStatus struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  []struct {
		GoodsSkuCode  string `json:"goodsSkuCode"`
		ShelvesStatus int    `json:"shelvesStatus"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type ImageList struct {
	ImgUrl  string `json:"imgUrl"`
	ImgMain int    `json:"imgMain"`
	ImgSort int    `json:"imgSort"`
}

type YzhGoodsStock struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  []struct {
		GoodsSkuCode string `json:"goodsSkuCode"`
		StockStatus  int    `json:"stockStatus"`
		StockNum     int    `json:"stockNum"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type YzhImgList struct {
	GoodsSkuCode string      `json:"goodsSkuCode"`
	ImageList    []ImageList `json:"imageList"`
}

type YzhImg struct {
	Success bool         `json:"success"`
	Code    string       `json:"code"`
	Desc    string       `json:"desc"`
	Result  []YzhImgList `json:"result"`
	Time    int64        `json:"time"`
}

type ResTokenData struct {
	Success bool   `json:"success"`
	Code    string `json:"code"`
	Desc    string `json:"desc"`
	Result  struct {
		AccessToken string `json:"accessToken"`
		ExpiresAt   string `json:"expiresAt"`
	} `json:"result"`
	Time int64 `json:"time"`
}

type YzhAllGoodsId struct {
	RESPONSESTATUS string             `json:"RESPONSE_STATUS"`
	TOTALAMOUNT    int                `json:"TOTAL_AMOUNT"`
	RESULTDATA     map[string][]int64 `json:"RESULT_DATA"`
}

type GoodsStock struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     struct {
		ProductId   int  `json:"product_id"`
		StockNum    uint `json:"stock_num"`
		StockStatus bool `json:"stock_status"`
	} `json:"RESULT_DATA"`
}

type YzhProduct struct {
	source.Model
	ProductId      int     `json:"productId"`
	Name           string  `json:"name"`
	Type           string  `json:"type"`
	ThumbnailImage string  `json:"thumbnailImage"`
	Brand          string  `json:"brand"`
	ProductCate    int     `json:"productCate"`
	ProductCode    string  `json:"productCode"`
	Status         string  `json:"status"`
	MarketPrice    float32 `json:"marketPrice"`
	RetailPrice    float32 `json:"retailPrice"`
	ProductPlace   string  `json:"productPlace"`
	//	Features           string        `json:"features"`
	Hot                bool          `json:"hot"`
	CreateTime         string        `json:"createTime"`
	Is7ToReturn        bool          `json:"is7ToReturn"`
	ProductImage       model.Gallery `json:"productImage" gorm:"type:text"`
	ProductDescription string        `json:"productDescription" gorm:"type:text"`
	Cate1              uint          `json:"cate1" `
	Cate2              uint          `json:"cate2" `
	Cate3              uint          `json:"cate3" `
	CateNames          string        `json:"cate_names" `
	ActivityRate       float64       `json:"activity_rate" form:"activity_rate"`
}

type PRODUCTIMAGE struct {
	ImageUrl  string `json:"imageUrl"`
	OrderSort int    `json:"orderSort"`
}

type RESULTANTS struct {
	PRODUCTDATA        YzhProduct     `json:"PRODUCT_DATA"`
	PRODUCTIMAGE       []PRODUCTIMAGE `json:"PRODUCT_IMAGE"`
	PRODUCTDESCRIPTION string         `json:"PRODUCT_DESCRIPTION"`
}
type YzhGoodsDetail struct {
	RESPONSESTATUS string     `json:"RESPONSE_STATUS"`
	RESULTDATA     RESULTANTS `json:"RESULT_DATA"`
	CategoryNames  string     `json:"category_names"`
}

type RESULTDATA struct {
	ID       uint   `json:"id"`
	Code     uint   `json:"code"`
	Name     string `json:"name"`
	ParentId uint   `json:"parentId"`
	Level    int    `json:"level"`
	Status   bool   `json:"status"`
}

type YzhCateGory struct {
	RESPONSESTATUS string       `json:"RESPONSE_STATUS"`
	RESULTDATA     []RESULTDATA `json:"RESULT_DATA"`
}
type YzhCateGoryDetail struct {
	RESPONSESTATUS string     `json:"RESPONSE_STATUS"`
	RESULTDATA     RESULTDATA `json:"RESULT_DATA"`
}

type SaleStatus struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     []struct {
		Status    bool   `json:"status"`
		Message   string `json:"message"`
		ProductID uint   `json:"product_id"`
	} `json:"RESULT_DATA"`
}

type StockNumber struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     []struct {
		ProductId   int  `json:"product_id"`
		StockStatus bool `json:"stock_status"`
	} `json:"RESULT_DATA"`
}

func (ProductForUpdate) TableName() string {
	return "products"
}

type ProductForUpdate struct {
	source.Model
	Title             string                  `json:"title"`
	OriginPrice       uint                    `json:"origin_price"`
	GuidePrice        uint                    `json:"guide_price"`
	Price             uint                    `json:"price"`
	Stock             uint                    `json:"stock"`
	CostPrice         uint                    `json:"cost_price"`
	ActivityPrice     uint                    `json:"activity_price"`
	Sales             uint                    `json:"sales"`
	Sn                string                  `json:"sn"`
	SingleOption      int                     `json:"single_option"`
	Desc              string                  `json:"desc"`
	ImageUrl          string                  `json:"image_url"`
	VideoUrl          string                  `json:"video_url"`
	Unit              string                  `json:"unit"`
	Barcode           string                  `json:"barcode"`
	Freight           int                     `json:"freight"`
	FreightType       int                     `json:"freight_type"`
	DetailImages      string                  `json:"detail_images"`
	BrandID           uint                    `json:"brand_id"`
	SupplierID        uint                    `json:"supplier_id"`
	GatherSupplyID    uint                    `json:"gather_supply_id"`
	Category1ID       uint                    `json:"category1_id"`
	Category2ID       uint                    `json:"category2_id"`
	Category3ID       uint                    `json:"category3_id"`
	FreightTemplateID uint                    `json:"freight_template_id"`
	Source            int                     `json:"source"`
	Sort              int                     `json:"sort"`
	SourceGoodsID     uint                    `json:"source_goods_id"`
	ChildTitle        string                  `json:"child_title"`
	Skus              []model.Sku             `json:"skus" gorm:"foreignKey:ProductID"`
	Gallery           model.Gallery           `json:"gallery"`
	Qualifications    model.Qualifications    `json:"qualifications"`
	Attrs             model.Attrs             `json:"attrs"`
	MaxPrice          uint                    `json:"max_price"`
	MinPrice          uint                    `json:"min_price"`
	MinBuyQty         uint                    `json:"min_buy_qty"` // 最少起订量
	Code              string                  `json:"code"`
	Weight            int                     `json:"weight" form:"weight" gorm:"-"`
	SkuID             uint                    `json:"sku_id" form:"sku_id" gorm:"-"`
	BillPosition      int                     `json:"bill_position" form:"bill_position"` //发票信息存储位置  1商品本体 2sku
	TaxCode           string                  `json:"tax_code"`                           //税收分类编码
	TaxShortName      string                  `json:"tax_short_name"`                     //税收分类简称
	TaxOption         string                  `json:"tax_option"`                         //规格型号
	TaxUnit           string                  `json:"tax_unit"`                           //单位
	FavorablePolicy   string                  `json:"favorable_policy"`                   //优惠政策
	IsFavorablePolicy int                     `json:"is_favorable_policy"`                //是否使用优惠政策
	FreeOfTax         int                     `json:"free_of_tax"`                        //0正常税率1出口免税和其他免税优惠政策2不征增值税3普通零税率
	ShortCode         string                  `json:"short_code"`                         // 商品简码
	TaxMeasurePrice   uint                    `json:"tax_measure_price"`                  //税收计量单价
	TaxRate           int                     `json:"tax_rate"`                           //税率
	IsTaxLogo         int                     `json:"is_tax_logo"`
	IsDisplay         int                     `json:"is_display"`
	StatusLock        int                     `json:"status_lock"`
	MD5               string                  `json:"md5"`
	SkuImage          string                  `json:"sku_image" form:"sku_image" gorm:"-"`                                                                  //sku图片 单规格使用
	OriginalSkuId     int                     `json:"original_sku_id" form:"original_sku_id" gorm:"-"`                                                      //规格第第三方id 单规格使用
	YzhProductTemp    pubmodel.YzhProductTemp `json:"yzh_product_temp" form:"yzh_product_temp" gorm:"foreignKey:source_goods_id;references:goods_sku_code"` // 阿里商品

}

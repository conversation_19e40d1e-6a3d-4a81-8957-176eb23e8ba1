package service

import (
	catemodel "category/model"
	stbz "github.com/writethesky/stbz-sdk-golang"
	"public-supply/model"
	"sync"
	"yz-go/source"
	//"yz-go/source"
)

var (
	wg       sync.WaitGroup
	mutex    sync.Mutex
	category []model.Category
	config   *stbz.Config
)

//func BatchImportDatabaseA( info request.GetCategorySearch) (err error){
//
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.GET,
//		"/v2/Category/GetCategory",
//		map[string]string{},
//		g.Map{"parent_id":0 , "source": info.Source},
//	)
//
//	data:= result.Data
//	aaa,_:=json.Marshal(data)
//
//	mjsona :=string(aaa)
//	cateItem := []model.Category{}
//	err = json.Unmarshal([]byte(mjsona), &cateItem)
//
//	for _,elem:=range cateItem{
//
//				var catei= new (catemodel.Category)
//				catei.Name=elem.Title
//				catei.Level=elem.Level+1
//				catei.ParentID=elem.ParentId
//				catei.Source=elem.Source
//				catei.Sort=elem.Sort
//				catei.IsDisplay=elem.State
//				_,id:=CreateCategory(catei);
//				if id > 0{
//					BatchImportDatabaseB(id,uint(elem.Id),info)
//					fmt.Println("一级创建成功",elem.Title,id,elem.ThirdId)
//				}
//
//
//	}
//return
//
//}
//
//
//func BatchImportDatabaseB(pid uint,ppid uint,info request.GetCategorySearch )(err error){
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.GET,
//		"/v2/Category/GetCategory",
//		map[string]string{},
//		g.Map{"parent_id":ppid , "source": info.Source},
//	)
//
//	data:= result.Data
//	aaa,_:=json.Marshal(data)
//
//	mjsona :=string(aaa)
//	cateItem := []model.Category{}
//	err = json.Unmarshal([]byte(mjsona), &cateItem)
//
//	for _,elem:=range cateItem{
//
//		var catei= new (catemodel.Category)
//		catei.Name=elem.Title
//		catei.Level=elem.Level+1
//		catei.ParentID=pid
//		catei.Source=elem.Source
//		catei.Sort=elem.Sort
//		catei.IsDisplay=elem.State
//		_,id:=CreateCategory(catei);
//		if id > 0{
//			BatchImportDatabaseC(id,uint(elem.Id),info)
//			fmt.Println("二级创建成功",elem.Title,id)
//		}
//
//
//
//	}
//
//
//	return
//
//
//}
//
//
//
//func BatchImportDatabaseC(pid uint,ppid uint,info request.GetCategorySearch )(err error){
//
//
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.GET,
//		"/v2/Category/GetCategory",
//		map[string]string{},
//		g.Map{"parent_id":ppid , "source": info.Source},
//	)
//
//	data:= result.Data
//	aaa,_:=json.Marshal(data)
//
//	mjsona :=string(aaa)
//	cateItem := []model.Category{}
//	err = json.Unmarshal([]byte(mjsona), &cateItem)
//
//	for _,elem:=range cateItem{
//
//
//		if elem.Level!=2{
//			fmt.Println("查询三级leve非2",elem.Title,elem.Id,elem.ParentId)
//		}
//		var catei= new (catemodel.Category)
//		catei.Name=elem.Title
//		catei.Level=elem.Level+1
//		catei.ParentID=pid
//		catei.Source=elem.Source
//		catei.Sort=elem.Sort
//		catei.IsDisplay=elem.State
//		_,id:=CreateCategory(catei);
//		if id > 0{
//			fmt.Println("三级创建成功",elem.Title,id)
//		}
//
//	}
//
//	return
//
//}

//func CreateCategory(category []*catemodel.Category) (err error) {
//	t := reflect.TypeOf(category[0])
//	insertList := reflect.New(reflect.SliceOf(t)).Elem()
//	for _, elem := range category {
//		insertList = reflect.Append(insertList, reflect.ValueOf(elem))
//	}
//	if err := source.DB().Table("categories").CreateInBatches(insertList.Interface(), 2).Error; err != nil {
//		log.Fatal(err)
//	}
//	return err
//}
func DeleteCategory(cate_source int) (err error) {
	err = source.DB().Where("source=?", cate_source).Delete(catemodel.Category{}).Error
	return err
}

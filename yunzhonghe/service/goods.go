package service

import (
	"fmt"
	"public-supply/model"
	_ "sync"
	_ "time"

	"yz-go/source"
)

var search map[string]interface{}

// 分割数组，根据传入的数组和分割大小，将数组分割为大小等于指定大小的多个数组，如果不够分，则最后一个数组元素小于其他数组
func splitArray(arr []model.Goods, num int64) [][]model.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]model.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]model.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// 求交集
func intersect(slice1, slice2 []int) []int {
	m := make(map[int]int)
	nn := make([]int, 0)
	for _, v := range slice1 {
		m[v]++
	}

	for _, v := range slice2 {
		times, _ := m[v]
		if times == 1 {
			nn = append(nn, v)
		}
	}
	return nn
}

func Intersection(slice1, slice2 []int) []int {
	m := make(map[int]int)
	nn := make([]int, 0)
	inter := intersect(slice1, slice2)
	for _, v := range inter {
		m[v]++
	}

	for _, value := range slice1 {
		times, _ := m[value]
		if times == 0 {
			nn = append(nn, value)
		}
	}
	return nn

}

//func ImportSelectGoodsRun(info model.SelectGoods) (err error, list interface{}) {
//	orderPN := goods.GetOrderNo()
//	goods.GlobalOrderSN = orderPN
//	goodsRecord := model.SupplyGoodsImportRecord{
//		SysUserId:         info.SysUserID,
//		Batch:             orderPN,
//		EstimatedQuantity: len(info.List),
//		Status:            1,
//	}
//	var result []int
//	err = source.DB().Model(model.SupplyGoods{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck("supply_goods_id", &result).Error
//	if err != nil {
//		log2.Println("查询供应链商品id错误", err)
//		return
//	}
//	if len(info.List) <= 0 {
//
//		log2.Println("导入空数据")
//		err = errors.New("导入的是空数据")
//		return
//	}
//	idsArr := goods.GetIdArr(info.List)
//	difference := collection.Collect(idsArr).Diff(result).ToIntArray()
//	repeat := len(idsArr) - len(difference)
//	goodsRecord.RepeatQuantity = repeat //重复数据
//	if len(difference) <= 0 {
//		goodsRecord.Status = 2
//		goodsRecord.CompletionStatus = 1
//		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
//		return
//	}
//	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
//	var goodsList []model.Goods
//	for _, v := range difference {
//		for _, item := range info.List {
//			if item.ID == v {
//				goodsList = append(goodsList, item)
//			}
//		}
//
//	}
//	info.List = goodsList
//	arrList := splitArray(info.List, 20)
//	for index, item := range arrList {
//		wg.Add(1)
//		fmt.Println("循环", index)
//		RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
//	}
//	wg.Wait()
//	err = goods.SetImportRecordCompletion(orderPN)
//	fmt.Println("全部完成：")
//	return
//}
//
//func ImportSelectGoods(info model.SelectGoods) {
//	go ImportSelectGoodsRun(info)
//}

func DeleteSupplyGoods(id uint) (err error) {

	err = source.DB().Unscoped().Delete(model.SupplyGoods{}, "product_id = ? ", id).Error

	return
}

//获取本地策略价格

func RunSelectGoodsConcurrent(orderPN string, list []model.Goods, category string, key string, gatherSupplyID uint) (err error) {

	//defer wg.Done()
	//
	//cateList := strings.Split(category, ",")
	//var cateId1, cateId2, cateId3 int
	//cateId1, err = strconv.Atoi(cateList[0])
	//cateId2, err = strconv.Atoi(cateList[1])
	//cateId3, err = strconv.Atoi(cateList[2])
	//
	//var listGoods []*pmodel.Product
	//var goodsClass = goods.NewGoods(key)
	//err = goodsClass.InitSetting(gatherSupplyID)
	//if err != nil {
	//	return
	//}
	//err, listGoods = goodsClass.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	//
	//if len(listGoods) > 0 {
	//	goods.FinalProcessing(listGoods, orderPN)
	//}

	return

}

//
//func GetKeyForSpeceName(SpecNameID int, Names []model.SpecsName) (data model.SpecsName) {
//	for _, item := range Names {
//		if item.ID == SpecNameID {
//			data = item
//			return
//		}
//	}
//	return
//}

//func GetKeyValue(Values []model.SpecsValue, spec_value_ids string, Names []model.SpecsName) (value model.SpecsValue, name model.SpecsName) {
//	for _, item := range Values {
//		//specValueIds, _ := strconv.Atoi(spec_value_ids)
//		idsArr := strings.Split(spec_value_ids, "_")
//		//if item.ID == specValueIds {
//		if collection.Collect(idsArr).Contains(strconv.Itoa(item.ID)) {
//			name = GetKeyForSpeceName(item.SpecNameID, Names)
//			value = item
//			return
//		}
//	}
//	return
//}

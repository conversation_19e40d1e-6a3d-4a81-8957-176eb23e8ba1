package v1

import (
	"distributor/model"
	"distributor/request"
	"distributor/service"
	"errors"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	yzResponse "yz-go/response"
)

// 新建分销等级
func CreateLevel(c *gin.Context) {
	var level model.DistributorLevel
	err := c.ShouldBindJSON(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateLevel(level); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// 更新分销等级
func UpdateLevel(c *gin.Context) {
	var level model.DistributorLevel
	err := c.ShouldBind<PERSON>N(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateLevel(level); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改成功", c)
	}
}

// 删除分销等级
func DeleteLevel(c *gin.Context) {
	var level model.DistributorLevel
	err := c.ShouldBindJSON(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteLevel(level.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// 根据ID获取分销等级
func FindLevel(c *gin.Context) {
	var level model.DistributorLevel
	err := c.ShouldBindJSON(&level)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, level := service.FindLevel(level.ID); err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"level": level}, c)
	}
}

// 分页获取分销等级列表
func GetLevelList(c *gin.Context) {
	var pageInfo request.LevelSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetLevelList(pageInfo); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// 获取全部分销等级无分页
func GetAllLevel(c *gin.Context) {
	if err, levels := service.GetAllLevel(); err != nil {
		//log.log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"levels": levels}, "获取成功", c)
	}
}

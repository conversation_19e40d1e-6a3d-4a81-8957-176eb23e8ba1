package service

import (
	"distributor/model"
	"errors"
	"gorm.io/gorm"
	"yz-go/source"
)

const SettingKey = "distributor_setting"

// 获取设置
func GetSetting() (err error, setting model.Setting) {
	err = source.DB().Where("`key` = ?", SettingKey).First(&setting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
	}
	return
}

// 修改设置
func SaveSetting(setting model.Setting) (err error) {
	setting.Key = SettingKey
	if setting.ID != 0 {
		err = source.DB().Updates(&setting).Error
	} else {
		err = source.DB().Create(&setting).Error
	}
	return err
}

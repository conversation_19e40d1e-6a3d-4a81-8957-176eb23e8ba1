package v1

import (
	model2 "gin-vue-admin/admin/model"
	response2 "gin-vue-admin/admin/model/response"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"math"
	"product/stock"
	"shipping/method"
	"shop/setting"
	"shopping-cart/model"
	"shopping-cart/request"
	"shopping-cart/response"
	"shopping-cart/service"
	"strconv"
	"time"
	ufv1 "user/api/f/v1"
	"yz-go/cache"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
)

func ExportOrderList(c *gin.Context) {
	userId := ufv1.GetUserID(c)

	if err, link := service.Export(userId); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		service2.CreateOperationRecord(ufv1.GetUserID(c), 3, c.<PERSON>(), "导出了订单")
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
func UpdateBatchOrder(c *gin.Context) {
	var batchOrder model.BatchOrder
	var err error
	err = c.ShouldBindJSON(&batchOrder)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateBatchOrder(batchOrder); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

func DeleteBatchOrderById(c *gin.Context) {
	var batchOrder model.BatchOrder
	var err error
	err = c.ShouldBindJSON(&batchOrder)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := ufv1.GetUserID(c)
	if err := service.DeleteBatchOrderById(userId, batchOrder.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

func DeleteBatchOrderByError(c *gin.Context) {
	userId := ufv1.GetUserID(c)
	if err := service.DeleteBatchOrderByError(userId); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除错误数据成功", c)
	}
}

func FindBatchOrder(c *gin.Context) {
	var batchOrder model.BatchOrder
	var err error
	err = c.ShouldBindQuery(&batchOrder)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reBatchOrder := service.FindBatchOrder(batchOrder.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"batch_order": reBatchOrder}, c)
	}
}

func UploadFile(c *gin.Context) {
	var file model2.ExaFileUploadAndDownload
	noSave := c.DefaultQuery("noSave", "0")
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Log().Error("接收文件失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("接收文件失败", c)
		return
	}
	UserID := ufv1.GetUserID(c)
	GroupId := c.Request.FormValue("groupId")
	i, _ := strconv.Atoi(GroupId)
	fileData := map[string]uint{"uid": UserID, "groupId": uint(i)}
	err, file = service.UploadFile(header, noSave, fileData) // 文件上传后拿到文件路径
	if err != nil {
		log.Log().Error("修改数据库链接失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改数据库链接失败", c)
		return
	}
	if c.Request.FormValue("index") != "" {
		file.Index, err = strconv.Atoi(c.Request.FormValue("index"))
	}
	if err != nil {
		log.Log().Error("修改数据库链接失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("修改数据库链接失败", c)
		return
	}
	yzResponse.OkWithDetailed(response2.ExaFileResponse{File: file}, "上传成功", c)
}

func BatchOrder(c *gin.Context) {
	var sendQuery request.SendQuery
	var err error
	err = c.ShouldBindJSON(&sendQuery)
	if err != nil {
		log.Log().Error("获取失败1", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userId := ufv1.GetUserID(c)
	err = service.DeleteBatchOrder(userId)
	if err != nil {
		log.Log().Error("获取失败2", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, batchOrders, successCount, failCount := service.CreateBatchOrder(userId, sendQuery)
	if err != nil {
		log.Log().Error("获取失败3", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	id, err := cache.GetID("product_buy")
	if err != nil {
		log.Log().Error("buy_id生成失败", zap.Any("err", err))
		yzResponse.FailWithMessage("buy_id生成失败", c)
		return
	}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	yzResponse.OkWithDetailed(gin.H{
		"list":          batchOrders,
		"success_count": successCount,
		"fail_count":    failCount,
		"buy_id":        uint(buyID),
	}, "获取成功", c)
	return
}

func GetBatchOrderCarts(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	// 购物车记录列表
	var shoppingCartList ShoppingCartList
	var err error
	err, shoppingCartList.ShoppingCarts = service.GetExcelCartList(userID)
	if err != nil {
		log.Log().Error("获取失败3", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 已失效的购物车记录排在最后
	var expiredShoppingCarts []response.ShoppingCart
	var notExpiredShoppingCarts []response.ShoppingCart
	for _, cart := range shoppingCartList.ShoppingCarts {
		if *cart.IsExpired == 1 {
			expiredShoppingCarts = append(expiredShoppingCarts, cart)
		} else {
			notExpiredShoppingCarts = append(notExpiredShoppingCarts, cart)
		}
	}
	shoppingCartList.ShoppingCarts = append(notExpiredShoppingCarts, expiredShoppingCarts...)

	err, shopSetting := setting.Get()
	if err != nil {
		return
	}
	defaultSupplier := response.Supplier{
		ID:       0,
		Name:     shopSetting.ShopName,
		ShopLogo: shopSetting.ShopLogo,
	}
	for i, cart := range shoppingCartList.ShoppingCarts {
		if *cart.Checked == 1 {
			shoppingCartList.GoodsCount += cart.Qty
			shoppingCartList.Amount += cart.Sku.Price * cart.Qty
		}
		if cart.SupplierID == 0 {
			shoppingCartList.ShoppingCarts[i].Supplier = &defaultSupplier
		}
		var skuStock uint
		err, skuStock = stock.GetSkuStock(cart.SkuID)
		if err != nil {
			return
		}
		shoppingCartList.ShoppingCarts[i].Stock = skuStock
	}

	err, shoppingCartList.ShippingMethods = method.GetList()
	if err != nil {
		return
	}
	if len(shoppingCartList.ShoppingCarts) > 0 {
		shoppingCartList.BuyID = shoppingCartList.ShoppingCarts[0].BuyID
	}
	yzResponse.OkWithDetailed(shoppingCartList, "获取成功", c)
}

func CreateCartByBatchOrder(c *gin.Context) {
	var batchOrderCart request.BatchOrderCart
	var err error
	err = c.ShouldBindJSON(&batchOrderCart)
	if err != nil {
		log.Log().Error("获取失败1", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if batchOrderCart.BuyID == 0 {
		yzResponse.FailWithMessage("参数错误, buy_id不能为空", c)
		return
	}
	userID := ufv1.GetUserID(c)
	var msgs []string
	err, msgs = service.CreateCartByBatchOrder(userID, batchOrderCart)
	log.Log().Info("创建购物车失败", zap.Any("创建购物车失败", msgs))
	if err != nil {
		log.Log().Error("创建购物车错误", zap.Any("err", err))

		yzResponse.FailWithMessage("创建购物车错误", c)
		return
	}
	// 购物车记录列表
	var shoppingCartList ShoppingCartList
	err, shoppingCartList.ShoppingCarts = service.GetExcelCartList(userID)
	if err != nil {
		log.Log().Error("获取失败3", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, shopSetting := setting.Get()
	if err != nil {
		return
	}
	defaultSupplier := response.Supplier{
		ID:       0,
		Name:     shopSetting.ShopName,
		ShopLogo: shopSetting.ShopLogo,
	}
	for i, cart := range shoppingCartList.ShoppingCarts {
		if *cart.Checked == 1 {
			shoppingCartList.GoodsCount += cart.Qty
			shoppingCartList.Amount += cart.Sku.Price * cart.Qty
		}
		if cart.SupplierID == 0 {
			shoppingCartList.ShoppingCarts[i].Supplier = &defaultSupplier
		}
	}
	err, shoppingCartList.ShippingMethods = method.GetList()
	if err != nil {
		return
	}
	msgStr := "操作成功"
	if len(msgs) > 0 {
		msgStr = "|"
		for _, msg := range msgs {
			msgStr += msg + "|"
		}
	}

	yzResponse.OkWithDetailed(shoppingCartList, msgStr, c)
	return
}

func GetBatchOrders(c *gin.Context) {
	userID := ufv1.GetUserID(c)
	err, list := service.GetBatchOrder(userID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
	}
	err, successCount := service.GetSuccessCount(userID)
	if err != nil {
		log.Log().Error("获取失败2", zap.Any("err", err))
	}
	err, failCount := service.GetFailCount(userID)
	if err != nil {
		log.Log().Error("获取失败3", zap.Any("err", err))
	}
	yzResponse.OkWithDetailed(gin.H{
		"list":          list,
		"success_count": successCount,
		"fail_count":    failCount,
	}, "获取成功", c)
}

// 暂时不用
func CartExcel(c *gin.Context) {
	var sendQuery request.SendQuery
	var shoppingCartList ShoppingCartList
	var err error
	err = c.ShouldBindJSON(&sendQuery)
	if err != nil {
		log.Log().Error("获取失败1", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err = service.ExcelCreateCarts(c, sendQuery)
	if err != nil {
		log.Log().Error("获取失败2", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	// 购物车记录列表
	err, shoppingCartList.ShoppingCarts = service.GetExcelCartList(userID)
	if err != nil {
		log.Log().Error("获取失败3", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, shopSetting := setting.Get()
	if err != nil {
		return
	}
	defaultSupplier := response.Supplier{
		ID:       0,
		Name:     shopSetting.ShopName,
		ShopLogo: shopSetting.ShopLogo,
	}
	for i, cart := range shoppingCartList.ShoppingCarts {
		if *cart.Checked == 1 {
			shoppingCartList.GoodsCount += cart.Qty
			shoppingCartList.Amount += cart.Sku.Price * cart.Qty
		}
		if cart.SupplierID == 0 {
			shoppingCartList.ShoppingCarts[i].Supplier = &defaultSupplier
		}
	}
	err, shoppingCartList.ShippingMethods = method.GetList()
	if err != nil {
		return
	}
	yzResponse.OkWithDetailed(shoppingCartList, "获取成功", c)
	return
}

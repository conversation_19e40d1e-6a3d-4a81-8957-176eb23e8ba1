// 自动生成模板Ad
package model

import (
	"yz-go/source"
)

// 如果含有time.Time 请自行import time包
type Ad struct {
	source.Model
	Cid          int    `json:"cid" form:"cid" gorm:"column:cid;comment:;type:int;size:10;"`
	ChannelId    int    `json:"channel_id" form:"channel_id" gorm:"column:channel_id;comment:;type:smallint;size:6;"`
	Title        string `json:"title" form:"title" gorm:"column:title;comment:;type:varchar(255);size:255;"`
	Desc         string `json:"desc" form:"desc" gorm:"column:desc;comment:;type:text;"`
	Src          string `json:"src" form:"src" gorm:"column:src;comment:;type:varchar(255);size:255;"`
	H5Src        string `json:"h5_src" form:"h5_src" gorm:"column:h5_src;comment:;type:varchar(255);size:255;"`
	Url          string `json:"url" form:"url" gorm:"column:url;comment:;type:varchar(255);size:255;"`
	H5Url        string `json:"h5_url" form:"h5_url" gorm:"column:h5_url;comment:;type:varchar(255);size:255;"`       //H5跳转链接
	MiniUrl      string `json:"mini_url" form:"mini_url" gorm:"column:mini_url;comment:;type:varchar(255);size:255;"` //小程序跳转链接
	MiniId       string `json:"mini_id" form:"mini_id" gorm:"column:mini_id;type:varchar(255);size:255;"`
	MiniType     int    `json:"mini_type" form:"mini_type" gorm:"column:mini_type;type:int;default:0;"`            //0内部链接 1第三方小程序链接
	APPUrl       string `json:"app_url" form:"app_url" gorm:"column:app_url;comment:;type:varchar(255);size:255;"` //APP跳转链接
	AppId        string `json:"app_id" form:"app_id" gorm:"column:app_id;comment:;type:varchar(255);size:255;"`
	AppType      int    `json:"app_type" form:"app_type" gorm:"column:app_type;type:int;default:0;"` //0内部链接 1微信小程序链接
	CollectionID uint   `json:"collection_id" form:"collection_id" gorm:"column:collection_id;comment:;type:int(11);"`
	Category1ID  uint   `json:"category1_id" form:"category1_id" gorm:"column:category1_id;comment:;type:int(11);"`
	Category2ID  uint   `json:"category2_id" form:"category2_id" gorm:"column:category2_id;comment:;type:int(11);"`
	Category3ID  uint   `json:"category3_id" form:"category3_id" gorm:"column:category3_id;comment:;type:int(11);"`
	JumpType     int    `json:"jump_type" form:"jump_type" gorm:"column:jump_type;comment:;type:smallint(11);"` //1Url 2分类 3collection
	Status       *int   `json:"status" form:"status" gorm:"column:status;comment:;type:tinyint;size:1;"`
	SalesType    string `json:"sales_type" form:"sales_type" gorm:"column:sales_type;comment:;type:varchar(50);"`
	Sort         int    `json:"sort"`
	IsShowImg    int    `json:"is_show_img"`
}

/*func (ad *Ad) BeforeSave(tx *gorm.DB) (err error) {
	if ad.Category3ID > 0 {
		ad.CategoryID = ad.Category3ID
	} else if ad.Category2ID > 0 {
		ad.CategoryID = ad.Category2ID
	} else {
		ad.CategoryID = ad.Category1ID
	}
	return
}*/

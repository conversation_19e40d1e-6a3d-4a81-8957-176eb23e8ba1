package request

import (
	"favorite/model"
	yzRequest "yz-go/request"
)

type FavoriteSearch struct{
    model.Favorite
    yzRequest.PageInfo
}

type FavoriteSearchNew struct{
	model.Favorite
	yzRequest.PageInfo
	LoseEfficacy int `json:"lose_efficacy" form:"lose_efficacy"` //是否失效 0全部 1是 2否
	Title string `json:"title" form:"title"` //是否失效 0全部 1是 2否
}

type CreateFavorite struct{
	ItemID int `json:"item_id" form:"item_id"`
	Type int `json:"type" form:"type"` //1商品2店铺
	Uid int `json:"uid" form:"uid"`
}

type DeleteFavorite struct{
	ID uint `json:"id"`
}
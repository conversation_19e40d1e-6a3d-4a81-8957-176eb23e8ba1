package model

import "yz-go/source"

type VideoShopDeliveryMatching struct {
	source.Model
	Code   string `json:"code" form:"code" gorm:"column:code;comment:中台快递code;"`            //中台快递code
	Name   string `json:"name" form:"name" gorm:"column:name;comment:中台快递名称;"`              //中台快递名称
	WxCode string `json:"wx_code"  form:"wx_code" gorm:"column:wx_code;comment:视频号快递code;"` // 视频号快递code
	WxName string `json:"wx_name"  form:"wx_name" gorm:"column:wx_name;comment:视频号快递名称;"`   // 视频号快递名称
}

package request

import yzRequest "yz-go/request"

type SmallShop struct {
	// 小商店id
	SmallShopID uint `json:"small_shop_id" form:"small_shop_id"`
}

type Communication struct {
	Echostr string `json:"echostr" form:"echostr"`
}

type CallbackResponse struct {
	// 回调结果
	ToUserName      string `json:"ToUserName"`
	FromUserName    string `json:"FromUserName"`
	CreateTime      int    `json:"CreateTime"`
	MsgType         string `json:"MsgType"`
	Event           string `json:"Event"`
	ProductSpuAudit struct {
		ProductId string `json:"product_id"`
		Status    int    `json:"status"`
		Reason    string `json:"reason"`
	} `json:"ProductSpuAudit"`
}

type VideoShopSearch struct {
	// 会员id
	UID uint `json:"uid" form:"uid"`
	// 会员昵称,姓名,手机号
	Member string `json:"member" form:"member"`
	// 小商店id
	SmallShopID uint `json:"small_shop_id" form:"small_shop_id"`
	// 小商店名称
	SmallShopTitle string `json:"small_shop_title" form:"small_shop_title"`
	// 会员等级id
	UserLevelID uint `json:"user_level_id" form:"user_level_id"`
	// 视频号小店名称
	Title string `json:"title" form:"title"`
	yzRequest.PageInfo
}

type SmallShopSearch struct {
	ShopTitle string `json:"shop_title" form:"shop_title"` // 小店名称
	yzRequest.PageInfo
}

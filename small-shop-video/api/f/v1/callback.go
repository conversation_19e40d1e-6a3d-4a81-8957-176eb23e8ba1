package v1

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"small-shop-video/callback"
	"small-shop-video/model"
	"small-shop-video/request"
	"small-shop-video/service"
	yzResponse "yz-go/response"
	"yz-go/source"
)

func Index(c *gin.Context) {
	//log.log().Error("接到视频号回调")
	var en request.Communication
	err := c.ShouldBindQuery(&en)
	if err != nil {
		//log.log().Error("接到视频号回调:解析参数错误", zap.Any("err", err.Error()))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if en.Echostr != "" {
		//log.log().Error("接到视频号回调:Echostr有值,返回成功")
		c.String(http.StatusOK, en.Echostr)
		return
	}
	var smallShop request.SmallShop
	err = c.ShouldBindQuery(&smallShop)
	if err != nil {
		//log.log().Error("接到视频号回调:解析smid失败", zap.Any("err", err.Error()))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	// 查询视频号获取消息秘钥
	err, video := service.FindVideoShopBySID(smallShop.SmallShopID, 0)
	if err != nil {
		//log.log().Error("接到视频号回调:查询视频号失败", zap.Any("err", err.Error()))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if video.ID == 0 || video.MsgSecret == "" {
		//log.log().Error("接到视频号回调:视频号秘钥为空")
		yzResponse.FailWithMessage("查询视频号小店失败", c)
		return
	}
	encryptedData, err := c.GetRawData()
	if err != nil {
		//log.log().Error("接到视频号回调:接收密文失败", zap.Any("err", err.Error()))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//log.log().Debug("回调密文", zap.Any("encryptedData", encryptedData))
	source.DB().Create(&model.VideoCallback{
		SmallShopID:   smallShop.SmallShopID,
		EncryptedData: string(encryptedData),
	})
	// 解密和业务处理
	err = callback.AllocationEntrance(string(encryptedData), video.MsgSecret, smallShop.SmallShopID)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	c.String(http.StatusOK, "success")
	return
}

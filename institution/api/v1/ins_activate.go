package v1

import (
	"github.com/gin-gonic/gin"
	"institution/request"
	"institution/service"
	yzResponse "yz-go/response"
)

// GetInsActivateList 获取institution激活记录列表
func GetInsActivateList(c *gin.Context) {
	var req request.InstitutionActivateListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetActivateList(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// ExportInsActivate 导出institution激活记录
func ExportInsActivate(c *gin.Context) {
	var req request.InstitutionActivateListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	}
	if err, link := service.ExportActivate(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"link": link}, "导出成功", c)
	}
}

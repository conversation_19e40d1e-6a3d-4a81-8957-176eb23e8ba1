package v1

import (
	"github.com/gin-gonic/gin"
	"institution/request"
	"institution/service"
	yzResponse "yz-go/response"
)

// GetInsSmallShopList 获取institution小商店列表
func GetInsSmallShopList(c *gin.Context) {
	var req request.InstitutionSmallShopListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetInsSmallShopList(req); err != nil {
		yzResponse.FailWithMessage(err.<PERSON>rror(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// ExportInsSmallShop 导出institution小商店
func ExportInsSmallShop(c *gin.Context) {
	var req request.InstitutionSmallShopListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportInsSmallShop(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"link": link}, "导出成功", c)
	}
}

// GetInsSmallShopChildList 获取institution小商店直推小商店列表
func GetInsSmallShopChildList(c *gin.Context) {
	var req request.InstitutionSmallShopChildListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetInsSmallShopChildList(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     req.Page,
			PageSize: req.PageSize,
		}, "获取成功", c)
	}
}

// ExportInsSmallShopChild 导出institution小商店直推小商店
func ExportInsSmallShopChild(c *gin.Context) {
	var req request.InstitutionSmallShopChildListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, link := service.ExportInsSmallShopChild(req); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(gin.H{"link": link}, "导出成功", c)
	}
}

package listener

import (
	mq "distributor/award_mq"
	"go.uber.org/zap"
	"institution/award"
	"yz-go/component/log"
)

func PushInstitutionAward() {
	mq.Push<PERSON>andles("institutionAwardListener", func(msg mq.AwardMessage) (err error) {
		if msg.MessageType != mq.Award {
			return nil
		}
		err = award.Handle(msg.OrderID, msg.Ratios, msg.CanSettle)
		if err != nil {
			log.Log().Error("机构监听分销奖励失败", zap.Any("err", err))
			return nil
		}
		return nil
	})
}

package service

import (
	"errors"
	"github.com/360EntSecGroup-Skylar/excelize"
	"gorm.io/gorm"
	"institution/model"
	"institution/request"
	orderService "order/service"
	"os"
	"strconv"
	"time"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

func GetCanSettleAwards() (err error, awards []model.InstitutionAward) {
	err = source.DB().Where("status = ? and can_settle = ?", 0, 1).Find(&awards).Error
	return
}

// 通过订单id查询已产生的分成，修改状态为可结算
func UpdateAwardCanSettleByOrderId(orderId uint) (err error) {
	err = source.DB().Model(&model.InstitutionAward{}).Where("order_id = ? AND status = ? AND can_settle = ?", orderId, 0, 2).Update("can_settle", 1).Error
	return
}

// buildQueryAward 构建查询条件
func buildQueryAward(db, db2 *gorm.DB, req request.InstitutionAwardListRequest) (resDb, statisticsDb *gorm.DB) {
	resDb = db
	statisticsDb = db2
	if req.Uid > 0 {
		resDb = db.Where("uid = ?", req.Uid)
		statisticsDb = db2.Where("uid = ?", req.Uid)
	}
	if req.Mobile != "" {
		// 通过手机号模糊查询用户ids
		var uids []uint
		err := source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Mobile+"%").Pluck("id", &uids).Error
		if err != nil {
			return resDb, statisticsDb
		}
		resDb = db.Where("uid in (?)", uids)
		statisticsDb = db2.Where("uid in (?)", uids)
	}
	if req.Nickname != "" {
		// 通过昵称模糊查询用户ids
		var uids []uint
		err := source.DB().Model(&model.User{}).Where("nickname like ?", "%"+req.Nickname+"%").Pluck("id", &uids).Error
		if err != nil {
			return resDb, statisticsDb
		}
		resDb = db.Where("uid in (?)", uids)
		statisticsDb = db2.Where("uid in (?)", uids)
	}
	if req.Type > 0 {
		resDb = db.Where("type = ?", req.Type)
		statisticsDb = db2.Where("type = ?", req.Type)
	}
	if req.SmallShopName != "" {
		// 通过小商店名称模糊查询小商店ids
		var smallShopIds []uint
		err := source.DB().Model(&model.SmallShop{}).Where("title like ?", "%"+req.SmallShopName+"%").Pluck("id", &smallShopIds).Error
		if err != nil {
			return resDb, statisticsDb
		}
		resDb = db.Where("sid in (?)", smallShopIds)
		statisticsDb = db2.Where("sid in (?)", smallShopIds)
	}
	if req.OrderSN > 0 {
		resDb = db.Where("order_sn = ?", req.OrderSN)
		statisticsDb = db2.Where("order_sn = ?", req.OrderSN)
	}
	if req.Status > 0 {
		resDb = db.Where("status = ?", req.Status)
	}
	statisticsDb = db2.Where("status in (?)", []int{1, 2})
	if req.StartTime != "" {
		resDb = db.Where("created_at >= ?", req.StartTime)
		statisticsDb = db2.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != "" {
		resDb = db.Where("created_at <= ?", req.EndTime)
		statisticsDb = db2.Where("created_at <= ?", req.EndTime)
	}
	return resDb, statisticsDb
}

// GetAwardList 获取institution奖励列表
func GetAwardList(req request.InstitutionAwardListRequest) (err error, list []model.InstitutionAward, total int64, amountTotal int) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.InstitutionAward{})
	statisticsDb := source.DB().Model(&model.InstitutionAward{})
	db, statisticsDb = buildQueryAward(db, statisticsDb, req)
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = statisticsDb.Select("COALESCE(SUM(amount), 0) as total_amount").Pluck("total_amount", &amountTotal).Error
	err = db.Preload("UserInfo").Preload("SmallShop").Limit(limit).Offset(offset).Order("id desc").Find(&list).Error
	return
}

// ExportAwardList 导出institution奖励列表
func ExportAwardList(req request.InstitutionAwardListRequest) (err error, link string) {
	db := source.DB().Model(&model.InstitutionAward{}).Preload("UserInfo").Preload("SmallShop")
	db, _ = buildQueryAward(db, db, req)
	var total int64
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	if total == 0 {
		err = errors.New("没有数据")
		return
	}
	var awards []model.InstitutionAward
	excelPage := total/500 + 1
	var links []string
	timeStr := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_institution_award"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.Limit(500).Offset(500 * (ei - 1)).Order("id desc").Find(&awards).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		index := f.NewSheet("Sheet1")
		f.SetCellValue("Sheet1", "A1", "时间")
		f.SetCellValue("Sheet1", "B1", "会员ID")
		f.SetCellValue("Sheet1", "C1", "会员昵称")
		f.SetCellValue("Sheet1", "D1", "手机号")
		f.SetCellValue("Sheet1", "E1", "下级小商店")
		f.SetCellValue("Sheet1", "F1", "业务类型")
		f.SetCellValue("Sheet1", "G1", "结算金额")
		f.SetCellValue("Sheet1", "H1", "奖励比例")
		f.SetCellValue("Sheet1", "I1", "奖励金额")
		f.SetCellValue("Sheet1", "J1", "备注")
		f.SetCellValue("Sheet1", "K1", "状态")
		i := 2
		for _, award := range awards {
			var createAt string
			if award.CreatedAt != nil {
				createAt = award.CreatedAt.Format("2006-01-02 15:04:05")
			}
			f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), createAt)
			f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), award.Uid)
			f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), award.UserInfo.NickName)
			f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), award.UserInfo.Mobile)
			f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), award.SmallShop.Title)
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), award.TypeName)
			f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), award.SettleAmount)
			f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), award.Ratio)
			f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), award.Amount)
			f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), award.Remark)
			f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), award.StatusName)
			i++
		}
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			err = os.MkdirAll(path, os.ModePerm)
			if err != nil {
				return
			}
		}
		link = path + "/" + timeStr + "-" + strconv.Itoa(ei) + "代理奖励导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeStr + "代理奖励导出.zip"
		err = orderService.Zip(link, links)
	}
	return
}

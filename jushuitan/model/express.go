package model

import "yz-go/source"

type JushuitanExpressResponse struct {
	Msg  string               `json:"msg"`
	Code int                  `json:"code"`
	Data JushuitanExpressData `json:"data"`
}

type JushuitanExpressData struct {
	Datas     []JushuitanExpress `json:"datas"`
	PageIndex int                `json:"page_index"`
	HasNext   bool               `json:"has_next"`
	DataCount int                `json:"data_count"`
	PageCount int                `json:"page_count"`
	PageSize  int                `json:"page_size"`
}
type JushuitanExpress struct {
	LcId     string `json:"lc_id"`
	Modified string `json:"modified"`
	LcName   string `json:"lc_name"`
}

type ExpressMatch struct {
	source.Model
	SelfExpressCode      string `json:"self_express_code"`
	JushuitanExpressCode string `json:"jushuitan_express_code"`
	SelfExpressName      string `json:"self_express_name" gorm:"-"`
}

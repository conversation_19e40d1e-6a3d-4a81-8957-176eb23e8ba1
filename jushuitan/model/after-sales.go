package model

type UploadAfterSalesParams struct {
	ShopStatus       string           `json:"shop_status"`
	QuestionType     string           `json:"question_type"`
	OuterAsId        string           `json:"outer_as_id"`
	Remark           string           `json:"remark"`
	Type             string           `json:"type"`
	GoodStatus       string           `json:"good_status"`
	ShopId           int              `json:"shop_id"`
	TotalAmount      float64          `json:"total_amount"`
	SoId             string           `json:"so_id"`
	LogisticsCompany string           `json:"logistics_company"`
	Payment          float64          `json:"payment"`
	LId              string           `json:"l_id"`
	Items            []AfterSalesItem `json:"items"`
	Refund           float64          `json:"refund"`
}

type AfterSalesItem struct {
	Amount          float64 `json:"amount"`
	PropertiesValue string  `json:"properties_value"`
	Qty             int     `json:"qty"`
	Name            string  `json:"name"`
	SkuId           string  `json:"sku_id"`
	Pic             string  `json:"pic"`
	Type            string  `json:"type"`
	OuterOiId       string  `json:"outer_oi_id"`
}

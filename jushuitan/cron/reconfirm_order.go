package cron

import (
	"encoding/json"
	"go.uber.org/zap"
	"jushuitan/common"
	"jushuitan/model"
	response2 "jushuitan/response"
	"jushuitan/service"
	"jushuitan/setting"
	orderM "order/model"
	common2 "public-supply/common"
	model2 "public-supply/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func JstReconfirmHandle() {
	task := cron.Task{
		Key:  "jstreconfirm",
		Name: "自动重新提交聚水潭订单",
		//Spec: "30 40 */1 * * *",
		Spec: "0 */5 * * * *",
		Handle: func(task cron.Task) {
			Reconfirm()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func Reconfirm() {
	var unConfirmJstOrders []service.Order
	var gatherListIds []uint
	var gatherSupplys []model2.GatherSupply
	source.DB().Model(&model2.GatherSupply{}).Where("`category_id` = ?", 14).Find(&gatherSupplys)
	for _, supply := range gatherSupplys {
		gatherListIds = append(gatherListIds, uint(supply.ID))
	}
	err, jstSetting := setting.GetJstSetting()
	if err != nil {
		return
	}
	if jstSetting.SelfOrder == 1 {
		gatherListIds = append(gatherListIds, 0)
	}
	err = source.DB().Preload("User").Preload("PayInfo").Preload("OrderItems.Sku.Product").Preload("ShippingAddress").Where("supplier_id = 0 and status = 1 and jushuitan_bind = 0").Where("gather_supply_id in ?", gatherListIds).Find(&unConfirmJstOrders).Error
	if err != nil {
		log.Log().Error("聚水潭定时推送订单错误0", zap.Any("err", err), zap.Any("err", err))
		return
	}
	var orderInfos []model.UploadOrderParams

	for _, orderModel := range unConfirmJstOrders {

		var orderInfo model.UploadOrderParams
		err, orderInfo = service.GetJushuitanOrderParams(orderModel)
		if err != nil {
			log.Log().Error("聚水潭定时推送订单错误2", zap.Any("err", err), zap.Any("data", orderInfo))
			continue
		}
		orderInfos = append(orderInfos, orderInfo)
		if len(orderInfos) == 1 {
			err = PushOrders(orderInfos)
			if err != nil {
				continue
			}
			orderInfos = []model.UploadOrderParams{}
		}

	}
	//err = PushOrders(orderInfos)

	return
}

func PushOrders(orderInfos []model.UploadOrderParams) (err error) {
	var jsonData []byte
	jsonData, err = json.Marshal(orderInfos)
	if err != nil {
		log.Log().Error("聚水潭定时推送订单错误1", zap.Any("err", err))
		return
	}
	log.Log().Info("聚水潭定时推送订单打印1", zap.Any("request", jsonData))

	var responseByte []byte
	err, responseByte = common.RequestJushuitan(jsonData, "https://openapi.jushuitan.com/open/jushuitan/orders/upload")
	if err != nil {
		log.Log().Error("聚水潭定时推送订单错误3", zap.Any("err", err))
		log.Log().Error("聚水潭定时推送订单错误3", zap.Any("data", string(jsonData)))
		return
	}
	log.Log().Info("聚水潭定时推送订单打印", zap.Any("response", string(responseByte)))

	var response response2.UploadOrderResponse
	err = json.Unmarshal(responseByte, &response)
	if err != nil {
		log.Log().Error("聚水潭定时推送订单错误4", zap.Any("err", err))
		log.Log().Error("聚水潭定时推送订单错误4", zap.Any("data", string(jsonData)))
		return
	}
	if response.Code == 0 {
		for _, v := range response.Data.Datas {
			var shopName string
			for _, order := range orderInfos {
				if order.SoId == v.SoId {
					shopName = order.ShopName
				}
			}
			var orderSn int
			orderSn, err = strconv.Atoi(v.SoId)
			if v.Issuccess == true {
				err = source.DB().Model(&orderM.OrderModel{}).Where("order_sn = ?", orderSn).Updates(&orderM.OrderModel{JushuitanBind: 1, GatherSupplySN: strconv.Itoa(v.OId), SourceShopName: shopName, GatherSupplyType: common2.SUPPLY_JUSHUITAN}).Error
				if err != nil {
					log.Log().Error("聚水潭定时推送订单错误5", zap.Any("err", err), zap.Any("data", string(jsonData)))
					return
				}
			} else {
				err = source.DB().Model(&orderM.OrderModel{}).Where("order_sn = ?", orderSn).Updates(&orderM.OrderModel{JushuitanBind: 1, GatherSupplyMsg: v.Msg, SourceShopName: shopName, GatherSupplyType: common2.SUPPLY_JUSHUITAN}).Error
				if err != nil {
					log.Log().Error("聚水潭定时推送订单错误5.5", zap.Any("err", err))
					return
				}
			}
		}
	} else {
		log.Log().Error("聚水潭定时推送订单错误6", zap.Any("err", response), zap.Any("data", string(jsonData)))
		return
	}
	return
}

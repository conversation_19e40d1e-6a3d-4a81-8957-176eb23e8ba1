package after

import (
	"fmt"
	"testing"
)

func TestAction(t *testing.T) {
	domain := "https://zhiwu.admin.fyuanai.com"
	accessToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzY29wZSI6WyJhbGwiXSwiZXhwIjoxNzQ5NTMwNDAwLCJqdGkiOiIzNGM4ZjE0ZC0zNjk0LTQ5ZmUtOGFkOS1hNGU0NzUzNjkwZmIiLCJ0ZW5hbnQiOiIwMGEwMWExOS04YWRkLTQ3N2YtYTg1Ni01MTUxN2Q1ZDQ4MTQiLCJjbGllbnRfaWQiOiJiZjc3OTk2NmU1Mzc0OTIzODljNTM0YWExYzk3ZDBmYzowMGEwMWExOS04YWRkLTQ3N2YtYTg1Ni01MTUxN2Q1ZDQ4MTQifQ.bBZhaGPdYmuqUHF8ocZgZgwEvtKZdvpTDUudwyiKWdo"

	// 3.1 查询售后类型以及包裹信息
	result, err := Apply(ApplyParams{
		AccessToken: accessToken,
		Domain:      domain,
		OrderSn:     "25060513401801658137",
		SkuId:       "18d5ef56-e17e-486f-9451-584fadb6346b",
	})

	// 3.8 根据订单商品ID获取发货包裹信息
	//result, err := Query(QueryParams{
	//	AccessToken: accessToken,
	//	OrderSn:     "24121616290699368713",
	//	SkuId:       "c5161802-583a-4c7f-8cfa-1004196cb1ba",
	//})

	// 3.11 查看售后详情
	//result, err := Detail(DetailParams{
	//	AccessToken: accessToken,
	//	Domain:      "",
	//	AfterId:     "5bd58e03-2e47-40ad-8e68-693c99689da6",
	//})

	fmt.Println(result, err)
}

package order

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

type PayParams struct {
	AccessToken string
	Domain      string
	ThirdOrder  string // 第三⽅订单号（20个字符以内）
}

// 5.2 订单支付

func Pay(p PayParams) (result bool, err error) {
	router := "open/api/order/toPay"

	params := map[string]interface{}{
		"thirdOrder": p.ThirdOrder,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.PostForm(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response PayResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("订单支付失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type PayResponse struct {
	Data    bool   `json:"data"`    // 状态
	Code    string `json:"code"`    // 返回编码
	Message string `json:"message"` // 返回说明
}

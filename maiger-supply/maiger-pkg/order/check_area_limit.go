package order

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

type CheckAreaLimitParams struct {
	AccessToken string
	Domain      string
	Address     string   // 收件地址
	SkuIds      []string // 商品skuIds
}

// 5.13 查询商品区域限制

func CheckAreaLimit(p CheckAreaLimitParams) (result []CheckAreaLimitResult, err error) {
	router := "open/api/order/checkAreaLimit"

	params := map[string]interface{}{
		"address":   p.Address,
		"skuIdList": p.SkuIds,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.PostJson(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response CheckAreaLimitResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("查询商品区域限制失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type CheckAreaLimitResponse struct {
	Data    []CheckAreaLimitResult `json:"data"`
	Code    string                 `json:"code"`    // 返回编码
	Message string                 `json:"message"` // 返回说明
}

type CheckAreaLimitResult struct {
	SkuId          string `json:"skuId"`          // 商品 sku_Id
	IsAreaRestrict bool   `json:"isAreaRestrict"` // true 或空值代表区域受限 false 区域不受限
}

package message

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

//0		商品上下架变更
//1		商品库存变更
//2		商品基本信息变更
//3		新增商品消息
//4		商品价格变更
//5		订单已发货变更
//6		订单已取消变更
//7		订单已完成变更
//8		售后-客服已审核变更
//9		售后-客诉人已退件变更
//10	售后-供应商已收货变更
//11	售后-供应商已发货变更
//12	售后-客诉人已收件变更
//13	售后-物流信息更新
//14	售后-售后已退款变更
//15	售后-退款已审核变更
//16	售后-售后已取消变更
//17	售后-售后已完成变更
//18	售后-创建售后单通知变更
//19	售后-售补偿售后单已审核变更
//20	售后-补寄审核

type QueryParams struct {
	AccessToken string
	Domain      string
	QueryType   string // 查询类型。支持多个组合，英文逗号间隔。例如1,2,3
}

// 8.1 消息通知查询接口

func Query(p QueryParams) (result string, err error) {
	router := "open/api/message/query/notify/statusQuery"

	params := map[string]interface{}{
		"queryType": p.QueryType,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.PostJson(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response QueryResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("消息通知查询失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type QueryResponse struct {
	Data     string `json:"data"`
	Code     string `json:"code"`     // 返回编码
	Message  string `json:"message"`  // 返回说明
	DataSize int    `json:"dataSize"` // 当前类型的数据量大小
}

type QueryRequest8 struct {
	QueryType int    `json:"queryType"`
	Iidd      string `json:"iidd"`
	AfterId   string `json:"afterId"`
	Status    int    `json:"status"`
	Reason    string `json:"reason"`
	ImageUrl  string `json:"imageUrl"`
}

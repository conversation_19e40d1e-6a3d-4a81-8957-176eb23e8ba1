package product

import (
	"encoding/json"
	"fmt"
	maigerpkg "maiger-supply/maiger-pkg"
)

type CategoryPageParams struct {
	AccessToken string `json:"accessToken"`
	Domain      string
	PageNum     int    `json:"pageNum"`  // 页码
	PageSize    int    `json:"pageSize"` // 每页数量
	ParentId    string `json:"parentId"` // 父级分类ID
}

// 4.2 查询分类列表

func CategoryPage(p CategoryPageParams) (result CategoryPageResult, err error) {
	router := "open/api/product/category/findByParentId"

	params := map[string]interface{}{
		"pageNum":  p.PageNum,
		"pageSize": p.PageSize,
		"parentId": p.ParentId,
	}

	// 执行HTTP请求
	resp, err := maigerpkg.GetJson(p.Domain, router, p.AccessToken, params)
	if err != nil {
		return
	}

	var response CategoryPageResponse
	if err = json.Unmarshal(resp, &response); err != nil {
		return
	}

	if response.Code != "100" {
		err = fmt.Errorf("获取access_token失败，code：%s，原因：%s", response.Code, response.Message)
		return
	}

	return response.Data, nil
}

type CategoryPageResponse struct {
	Data    CategoryPageResult `json:"data"`    // 分页数据
	Code    string             `json:"code"`    // 返回编码
	Message string             `json:"message"` // 返回说明
}

type CategoryPageResult struct {
	PageNum    int        `json:"pageNum"`   // 页码
	PageSize   int        `json:"pageSize"`  // 每页条数
	Total      int        `json:"total"`     // 总数
	TotalPage  int        `json:"totalPage"` // 总页数
	Categories []Category `json:"data"`      // 分类列表
}

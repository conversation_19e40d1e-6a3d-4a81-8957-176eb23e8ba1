package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"knowledge-base/model"
	"knowledge-base/request"
	"knowledge-base/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

// @Tags collection表
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.KnowledgeBase true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/collection/createKnowledgeBase [post]
func CreateKnowledgeBase(c *gin.Context) {
	var collection model.KnowledgeBase
	var err error
	err = c.ShouldBindJSON(&collection)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.CreateKnowledgeBase(collection)
	if err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("创建成功", c)
}

func SetAttributeStatus(c *gin.Context) {
	var columnStatus request.ColumnStatus
	err := c.ShouldBindJSON(&columnStatus)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := utils.Verify(columnStatus); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, enable := service.SetAttributeStatus(columnStatus); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"enable": enable}, c)
	}
}

// @Tags collection表
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.KnowledgeBase true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /api/collection/deleteKnowledgeBase [post]
func DeleteKnowledgeBase(c *gin.Context) {
	var collection model.KnowledgeBase
	var err error
	err = c.ShouldBindJSON(&collection)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.DeleteKnowledgeBase(collection)
	if err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)

}

// @Tags collection表
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /api/collection/deleteKnowledgeBaseByIds [post]
func DeleteKnowledgeBaseByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	var err error
	err = service.DeleteKnowledgeBaseByIds(IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	yzResponse.OkWithMessage("批量删除成功", c)
}

// @Tags collection表
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.KnowledgeBase true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /api/collection/updateKnowledgeBase [post]
func UpdateKnowledgeBase(c *gin.Context) {
	var collection model.KnowledgeBase
	var err error
	err = c.ShouldBindJSON(&collection)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err = service.UpdateKnowledgeBase(collection)
	if err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	}
	yzResponse.OkWithMessage("更新成功", c)
}

// @Tags collection表
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询"
// @Success 200 {object} model.KnowledgeBase
// @Router /api/collection/findKnowledgeBase [post]
func FindKnowledgeBase(c *gin.Context) {
	var reqId yzRequest.GetById
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	err, recollection := service.GetKnowledgeBase(reqId.Id, appID)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"recollection": recollection}, c)
}

// @Tags collection表
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.KnowledgeBaseSearch true "分页获取列表"
// @Success 200 {string} string []model.KnowledgeBase
// @Router /api/collection/getKnowledgeBaseList [post]
func GetKnowledgeBaseList(c *gin.Context) {
	var pageInfo request.KnowledgeBaseSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = utils.GetAppID(c)
	err, list, total := service.GetKnowledgeBaseInfoList(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// @Tags collection表
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.KnowledgeBaseSearch true "分页获取列表"
// @Success 200 {string} string []model.KnowledgeBase
// @Router /api/collection/getKnowledgeBaseOptionList [post]
func GetKnowledgeBaseOptionList(c *gin.Context) {
	var pageInfo request.KnowledgeBaseSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list := service.GetKnowledgeBaseOptionList(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List: list,
	}, "获取成功", c)
}

//func GetKnowledgeBaseProductList(c *gin.Context) {
//	var pageInfo request.KnowledgeBaseProductSearch
//	var err error
//	err = c.ShouldBindQuery(&pageInfo)
//	if err != nil{
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	err, list,total := service.GetKnowledgeBaseProductList(pageInfo)
//	if err != nil {
//		log.Log().Error("获取失败", zap.Any("err", err))
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	yzResponse.OkWithDetailed(yzResponse.PageResult{
//		List:     list,
//		Total:    total,
//		Page:     pageInfo.Page,
//		PageSize: pageInfo.PageSize,
//	}, "获取成功", c)
//}

//func DeleteKnowledgeBaseProductByIds(c *gin.Context) {
//	var IDS yzRequest.IdsReq
//	var err error
//	err = c.ShouldBindJSON(&IDS)
//	if err != nil {
//		log.Log().Error("批量删除失败!", zap.Any("err", err))
//		yzResponse.FailWithMessage("批量删除失败", c)
//		return
//	}
//	err = service.DeleteKnowledgeBaseProduct(IDS.Ids)
//	if err != nil {
//		log.Log().Error("批量删除失败!", zap.Any("err", err))
//		yzResponse.FailWithMessage("批量删除失败", c)
//		return
//	}
//	yzResponse.OkWithMessage("批量删除成功", c)
//}

//func CreateKnowledgeBaseProduct(c *gin.Context) {
//	var collection request.CreateKnowledgeBaseProduct
//	var err error
//	err = c.ShouldBindJSON(&collection)
//	if err != nil {
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	var oldKnowledgeBaseProducts []model.KnowledgeBaseProduct
//	err = source.DB().Model(&model.KnowledgeBaseProduct{}).Where("collection_id = ?", collection.KnowledgeBaseID).Find(&oldKnowledgeBaseProducts).Error
//	if err != nil {
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	var newKnowledgeBaseProducts []model.KnowledgeBaseProduct
//	for _, productID := range collection.ProductIDs {
//		i := 1
//		for _, collectionProduct := range oldKnowledgeBaseProducts {
//			if collectionProduct.ProductID == productID {
//				// 已存在的商品，不重复添加
//				i = 0
//				break
//			}
//
//		}
//		if i == 1 {
//			newKnowledgeBaseProducts = append(newKnowledgeBaseProducts, model.KnowledgeBaseProduct{KnowledgeBaseID: collection.KnowledgeBaseID, ProductID: productID})
//		}
//
//	}
//	if newKnowledgeBaseProducts == nil {
//		yzResponse.OkWithMessage("创建成功", c)
//		return
//	}
//	err = service.CreateKnowledgeBaseProduct(newKnowledgeBaseProducts)
//	if err != nil {
//		log.Log().Error("创建失败!", zap.Any("err", err))
//		yzResponse.FailWithMessage(err.Error(), c)
//		return
//	}
//	yzResponse.OkWithMessage("创建成功", c)
//}

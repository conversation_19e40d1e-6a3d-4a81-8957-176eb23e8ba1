package service

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	model2 "finance/model"
	"finance/service"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/forgoer/openssl"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math/rand"
	"os"
	paymentModel "payment/model"
	"race-cinema-ticket/model"
	"strconv"
	"strings"
	"time"
	model3 "user/model"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

// 如果含有time.Time 请自行import time包
type Application struct {
	source.Model

	AppLevelID           uint             `json:"appLevelId" form:"appLevelId" gorm:"column:app_level_id;comment:;type:int;size:10;"`
	MemberId             int              `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	ApplicationLevel     ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
	CallBackLinkValidity int              `json:"call_back_link_validity"`
	AppSecret            string           `json:"app_secret"`
	CallBackLink         string           `json:"call_back_link"`
	PetSupplierID        uint             `json:"pet_supplier_id"`
	User                 User             `json:"user" gorm:"foreignKey:MemberId"`
}

func (Application) TableName() string {
	return "application"
}

type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:;type:int;size:10;"`
	NumMax      int `json:"numMax" form:"numMax" gorm:"column:num_max;comment:;type:int;size:10;"`
}

type User struct {
	ID        uint      `json:"id" form:"id" gorm:"primarykey"`
	LevelID   uint      `json:"level_id" form:"level_id"`
	UserLevel UserLevel `json:"user_level" form:"user_level" gorm:"foreignKey:LevelID;references:ID"`
}

type UserLevel struct {
	source.Model
	Level    int `json:"level" form:"level"`
	Discount int `json:"discount" form:"discount"`
}

func GetRatio(parameter model.OrderSubmitData) (err error, ratio float64) {
	var application Application
	source.DB().Table("application").Where("id=?", parameter.AppID).First(&application)
	var mySetting model.Setting
	settingErr, setting := GetSettingString()
	if settingErr != nil {
		err = settingErr
		log.Log().Error("创建电影票订单获取当前设置失败,", zap.Any("err", err))
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &mySetting)
	if err != nil {
		log.Log().Error("创建电影票订单解析当前设置失败", zap.Any("err", err))
		return
	}

	for _, item := range mySetting.LevelList {
		if item.LevelId == application.AppLevelID {
			ratio = item.Ratio
			break
		}
	}

	fmt.Println(ratio)

	return
}
func (l *ModelData) ConfirmOrder(parameter model.OrderSubmitData) (err error) {
	var selectCinemaOrder model.CinemaTicketOrder
	log.Log().Info("电影飘提交数据", zap.Any("info", parameter))

	//err = source.DB().Where("channel_order_no=?", parameter.ChannelOrderNo).First(&selectCinemaOrder).Error
	//if err != nil {
	//	log.Log().Error("电影飘查询订单错误ConfirmOrder", zap.Any("err", err))
	//	return
	//}

	if selectCinemaOrder.ID > 0 && selectCinemaOrder.OrderStatus != model.WaitingPay { //重新提交订单，如果没支付就去支付
		err = errors.New("channel_order_no订单号重复，请更换订单号")
		return

	}

	//if selectCinemaOrder.ID > 0 && selectCinemaOrder.OrderStatus == model.WaitingPay { //重新提交订单，如果没支付就去支付
	//
	//	deductionErr := l.Deduction(selectCinemaOrder)
	//	if deductionErr != nil {
	//		log.Log().Info("电影票订单扣款完成，扣款失败,", zap.Any("err", deductionErr))
	//		err = errors.New("扣款失败")
	//		return
	//	}
	//
	//	return
	//
	//}

	ratioErr, ratio := GetRatio(parameter)
	if ratioErr != nil {
		log.Log().Error("创建电影票订单获取当前app 等级比例失败,", zap.Any("err", ratioErr))
		return
	}

	timestamp := uint(time.Now().Unix())
	orderSn := parameter.UserID + timestamp*110

	var user model3.User
	source.DB().First(&user, "id=?", parameter.UserID)

	var cinemaOrder model.CinemaTicketOrder
	cinemaOrder.ChannelOrderNo = parameter.ChannelOrderNo
	cinemaOrder.CinemaName = parameter.CinemaName
	cinemaOrder.ShowId = parameter.ShowId
	cinemaOrder.OrderSN = orderSn
	cinemaOrder.MovieTitle = parameter.MovieTitle
	cinemaOrder.Mobile = user.Mobile
	cinemaOrder.MoviePic = parameter.MoviePic
	cinemaOrder.NickName = user.NickName
	cinemaOrder.UserID = parameter.UserID
	cinemaOrder.AppID = parameter.AppID
	cinemaOrder.AppShopID = parameter.AppShopID
	cinemaOrder.Quantity = parameter.Quantity
	cinemaOrder.Seat = strings.Join(parameter.SeatIds, ",")
	var data []byte
	data, err = json.Marshal(&parameter)
	if err != nil {
		log.Log().Error("电影票下单数据json失败", zap.Any("err", data))
	}
	cinemaOrder.RequestData = string(data)
	//cinemaOrder.OriginPrice = parameter.OriginPrice
	//cinemaOrder.SalePrice = parameter.SalePrice
	//cinemaOrder.SettlePrice = parameter.SettlePrice
	//cinemaOrder.AccountsPayable = parameter.AccountsPayable
	cinemaOrder.TotalPrice = parameter.TotalPrice
	cinemaOrder.Ratio = ratio
	cinemaOrder.OrderStatus = model.WaitingOrder
	cinemaOrder.CityCode = parameter.CityCode
	cinemaOrder.RegionCode = parameter.RegionCode
	cinemaOrder.PaySN = uint(time.Now().Unix())
	cinemaOrder.CinemaName = parameter.CityName
	cinemaOrder.RegionName = parameter.RegionName
	cinemaOrder.CinemaAddress = parameter.CinemaAddress

	log.Log().Info("电影票订单创建提交数据", zap.Any("info", cinemaOrder))

	err = source.DB().Create(&cinemaOrder).Error
	if err != nil {
		log.Log().Error("创建电影票订单失败,", zap.Any("err", err))
		return
	}

	err = l.DeductionBalance(orderSn, cinemaOrder)
	if err != nil {
		return err
	}

	return

}

func (l *ModelData) DeductionBalance(orderSn uint, cinemaOrder model.CinemaTicketOrder) (err error) {

	err = source.DB().Where("order_sn=?", orderSn).First(&cinemaOrder).Error
	if err != nil {
		return
	}

	deductionErr := l.Deduction(cinemaOrder)
	if deductionErr != nil {
		log.Log().Info("电影票订单扣款失败,", zap.Any("err", deductionErr))
		err = errors.New("扣款失败:" + deductionErr.Error())
		return
	}

	err = source.DB().Model(model.CinemaTicketOrder{}).Where("order_sn=?", orderSn).UpdateColumn("pay_status", 1).Error
	if err != nil {
		log.Log().Error("更新电影票支付状态失败", zap.Any("err", err))
	}
	log.Log().Info("电影票订单扣款完成，准备接口下单,", zap.Any("err", err))

	return
}

// 电影票下单
func (l *ModelData) OrderSubmit(parameter model.OrderSubmitData) (err error, data model.SubmitOrderResData) {

	var cinemaTicketOrder model.CinemaTicketOrder

	err = source.DB().Where("channel_order_no=?", parameter.ChannelOrderNo).First(&cinemaTicketOrder).Error

	if cinemaTicketOrder.ID == 0 {
		err = l.ConfirmOrder(parameter)
		if err != nil {
			log.Log().Error("创建电影票本地订单失败,", zap.Any("err", err))

			return
		}
	}

	url := l.Domain + "/api/movie/order/submit"
	reqBody := make(map[string]interface{})
	reqBody["cinemaId"] = parameter.CinemaId
	reqBody["movieId"] = parameter.MovieId
	reqBody["showId"] = parameter.ShowId
	reqBody["seatIds"] = parameter.SeatIds
	reqBody["channelOrderNo"] = parameter.ChannelOrderNo
	if parameter.Mobile != "" {
		reqBody["mobile"] = parameter.Mobile
	}
	if parameter.AllowChanged != "" {
		reqBody["allowChanged"] = parameter.AllowChanged
	}

	reqJsonMap, _ := json.Marshal(reqBody)
	log.Log().Info("OrderSubmit 请求,Marshal", zap.Any("err", string(reqJsonMap)))

	dst, err := openssl.AesECBEncrypt(reqJsonMap, []byte(l.Key), openssl.PKCS5_PADDING)
	aseData := base64.StdEncoding.EncodeToString(dst)
	time := strconv.Itoa(int(time.Now().Unix() * 1000))
	reqData := make(map[string]string)
	reqData["channelId"] = l.ChannelId
	reqData["encryptedData"] = aseData
	reqData["nonceStr"] = time
	reqData["timestamp"] = time

	signSort := Sign(reqData, l.Secret)
	reqData["sign"] = signSort
	JsonData, _ := json.Marshal(reqData)
	log.Log().Info("OrderSubmit 请求,", zap.Any("err", string(JsonData)))

	resData := utils.HttpPostJson(JsonData, url)

	log.Log().Info("OrderSubmit 返回,", zap.Any("err", string(resData)))

	var cinemaOrder model.CinemaTicketOrder
	var resOrder model.ResPublicData
	err = json.Unmarshal(resData, &resOrder)
	if err != nil {
		log.Log().Error("json.Unmarshal(resData, &resOrder)电影下单解析失败", zap.Any("err", err))

		return
	}

	src1, _ := base64.StdEncoding.DecodeString(resOrder.Data)
	decodeData, _ := openssl.AesECBDecrypt(src1, []byte(l.Key), openssl.PKCS5_PADDING)
	//var ResOrderData model.SubmitOrderResData
	log.Log().Info("下单电影票返回数据日志：", zap.Any("err", string(decodeData)))

	err = json.Unmarshal(decodeData, &data)
	if err != nil {
		log.Log().Error(" json.Unmarshal(decodeData, &data)电影下单解析失败", zap.Any("err", err))

		return
	}
	//if data.ErrCode != "0" {
	//	err = errors.New(data.ErrMessage)
	//	cinemaOrder.ErrMsg = data.ErrMessage
	//	updateErr := source.DB().Where("channel_order_no=?", parameter.ChannelOrderNo).Updates(&cinemaOrder).Error
	//	if updateErr != nil {
	//		log.Log().Error("下单失败更新电影片订单信息错误11,", zap.Any("err", updateErr))
	//	}
	//	log.Log().Error("电影票下单数据失败,", zap.Any("err", resOrder))
	//	return
	//}

	if data.ErrCode == "0" {
		cinemaOrder.OutOrderNo = data.Data.OutOrderNo
		cinemaOrder.OrderStatus = data.Data.Status
		cinemaOrder.OriginPrice = data.Data.OriginPrice
		cinemaOrder.SalePrice = data.Data.SalePrice
		cinemaOrder.SettlePrice = data.Data.SettlePrice
		cinemaOrder.OrderDesc = data.Data.OrderDesc
		//cinemaOrder.SettlePrice = data.Data.SettlePrice
		cinemaOrder.TotalSettleAmount = data.Data.TotalSettleAmount
		if cinemaOrder.TotalSettleAmount == 0 {
			cinemaOrder.TotalSettleAmount = parameter.TotalPrice

		}
		updateErr := source.DB().Where("channel_order_no=?", parameter.ChannelOrderNo).Updates(&cinemaOrder).Error
		if updateErr != nil {
			log.Log().Error("下单成功更新电影片订单错误", zap.Any("err", updateErr))
			err = updateErr
			return
		}

	} else {
		cinemaOrder.ErrMsg = data.ErrCode + data.ErrMessage
		updateErr := source.DB().Where("channel_order_no=?", parameter.ChannelOrderNo).Updates(&cinemaOrder).Error
		if updateErr != nil {
			log.Log().Error("下单失败更新电影片订单信息错误,", zap.Any("err", resOrder))
			err = updateErr
			return
		}
		log.Log().Error("下单失败电影片订单,", zap.Any("err", data))

		return
	}
	//data = string(decodeData)
	return
}

func (l *ModelData) CallbackNotification(data model.CallbackNotificationData) (err error) {
	src1, _ := base64.StdEncoding.DecodeString(data.EncryptedData)
	decodeData, _ := openssl.AesECBDecrypt(src1, []byte(l.Key), openssl.PKCS5_PADDING)
	var dataDetail model.CallbackNotificationDataDetail
	log.Log().Info("电影票回调接受到消息,", zap.Any("err", string(decodeData)))

	err = json.Unmarshal(decodeData, &dataDetail)
	if err != nil {
		log.Log().Error("电影票回调解析数据错误,", zap.Any("err", err))
		return

	}

	//处理电影票消息

	log.Log().Info("电影票cinemaCallbackMessage消息接到", zap.Any("info", dataDetail))

	var order model.CinemaTicketOrder
	if dataDetail.OrderData.Status > 0 {
		order.OrderStatus = dataDetail.OrderData.Status

		if dataDetail.Type == 7 {
			time := time.Now().Format("20060102150405")
			order.CompleteAt = time

			var l ModelData

			ChannelOrderNo := dataDetail.OrderData.ChannelOrderNo
			log.Log().Info("电影票完成奖励开始", zap.Any("err", ChannelOrderNo))
			l.Dividends(ChannelOrderNo)
		}

	}
	log.Log().Info("电影票cinemaCallbackMessage消息接到1")

	if dataDetail.OrderData.SettlePrice > 0 {
		order.SettlePrice = dataDetail.OrderData.SettlePrice
	}
	if dataDetail.OrderData.TotalSettleAmount > 0 {
		order.TotalSettleAmount = dataDetail.OrderData.TotalSettleAmount
	}
	if len(dataDetail.OrderData.Tickets) > 0 {
		stringTickets, _ := json.Marshal(dataDetail.OrderData.Tickets)
		order.Tickets = string(stringTickets)
	}
	log.Log().Info("电影票cinemaCallbackMessage消息接到2")

	updatesErr := source.DB().Where("out_order_no=?", dataDetail.OrderData.OutOrderNo).Updates(&order).Error
	log.Log().Info("电影票cinemaCallbackMessage消息接到1", zap.Any("updatesErr", updatesErr))

	if updatesErr != nil {
		log.Log().Error("电影票回调更新错误,", zap.Any("err", updatesErr))
		return
	}

	return
	//
	//err = mq.PublishMessage(dataDetail)
	//log.Log().Info("电影票消息已发送", zap.Any("err", err))
	//
	//if err != nil {
	//	log.Log().Error("电影票回调发送mq消息失败,", zap.Any("err", err))
	//	return
	//
	//}
	//
	//return

}

func GetTimeTick64() int {
	return int(time.Now().Unix())
}
func GetFormatTime(time time.Time) string {
	return time.Format("20060102")
}

func GenerateCode() int64 {

	r := rand.Intn(1000)
	orderSN, _ := strconv.Atoi(GetFormatTime(time.Now()))

	date := orderSN + GetTimeTick64() + r

	fmt.Println(date)
	return int64(date)
}

// 查询订单
func (l *ModelData) OrderGet(channelOrderNo, outOrderNo string) (err error, data model.SelectOrderData) {

	url := l.Domain + "/api/movie/order/get"
	reqBody := make(map[string]string)

	if channelOrderNo != "" {
		reqBody["channelOrderNo"] = channelOrderNo
	}
	if outOrderNo != "" {
		reqBody["outOrderNo"] = outOrderNo
	}
	log.Log().Info("电影票下游查询参数", zap.Any("channelOrderNo", channelOrderNo), zap.Any("outOrderNo", outOrderNo))

	reqJsonMap, _ := json.Marshal(reqBody)
	dst, err := openssl.AesECBEncrypt(reqJsonMap, []byte(l.Key), openssl.PKCS5_PADDING)
	aseData := base64.StdEncoding.EncodeToString(dst)
	time := strconv.Itoa(int(time.Now().Unix() * 1000))
	reqData := make(map[string]string)
	reqData["channelId"] = l.ChannelId
	reqData["encryptedData"] = aseData
	reqData["nonceStr"] = time
	reqData["timestamp"] = time

	signSort := Sign(reqData, l.Secret)
	reqData["sign"] = signSort
	JsonData, _ := json.Marshal(reqData)

	resData := utils.HttpPostJson(JsonData, url)

	var resOrder model.ResPublicData
	err = json.Unmarshal(resData, &resOrder)
	if err != nil {
		log.Log().Error("电影票OrderGet", zap.Any("err", err))
		return
	}
	log.Log().Info("电影票下游查询返回信息", zap.Any("info", resOrder))
	if resOrder.Data == "" {
		err = errors.New("未查询到该订单数据")
		return
	}
	if resOrder.Code != 0 {
		err = errors.New(resOrder.Message)
		log.Log().Error("获取数据失败,", zap.Any("err", resOrder))
		return
	}
	src1, _ := base64.StdEncoding.DecodeString(resOrder.Data)
	decodeData, _ := openssl.AesECBDecrypt(src1, []byte(l.Key), openssl.PKCS5_PADDING)
	log.Log().Info("OrderGet", zap.Any("info", decodeData))

	json.Unmarshal(decodeData, &data)
	if data.Status > 0 && data.SettlePrice > 0 {
		fmt.Println("更新结算")
		//if data.Data.SettlePrice != 0 {
		//	fmt.Println("3333334")
		//}

	}
	//data = string(decodeData)
	return
}

func (l *ModelData) Deduction(cinemaOrder model.CinemaTicketOrder) (err error) { //扣款业务逻辑
	//var cinemaOrder model.CinemaTicketOrder
	//
	//source.DB().First(&cinemaOrder, "order_sn=?", orderSN)
	//var payTypeSort []paymentModel.ApplicationPaySort
	//err, payTypeSort = service4.GetPaySort(cinemaOrder.AppID)
	//var payInfo pay.PayInfo
	//err = PaidBySort(cinemaOrder.UserID, uint(cinemaOrder.TotalPrice), payInfo, payTypeSort)
	//if err != nil {
	//	fmt.Println("支付错误", err)
	//}

	var balance model2.AccountBalance
	log.Log().Info("Deduction balance", zap.Any("info", cinemaOrder.UserID))
	err = source.DB().Where("uid=?", cinemaOrder.UserID).Where("type=?", 2).First(&balance).Error
	if err != nil {
		return
	}
	if balance.ID <= 0 {
		err = errors.New("未查询到此用户余额")
		return
	}
	if float64(balance.PurchasingBalance) < cinemaOrder.TotalPrice {
		err = errors.New("余额不足")
		return

	}

	var param paymentModel.UpdateBalance
	param.PayType = 2 //站内余额
	param.Type = 2    //站内余额
	param.Uid = cinemaOrder.UserID
	param.Amount = uint(utils.Yuan2Fen(cinemaOrder.TotalPrice))
	param.Action = 2        //扣款
	param.OperationType = 1 //操作采购余额
	if cinemaOrder.AppShopID > 0 {
		var appShop model.ApplicationShop
		err = source.DB().First(&appShop, cinemaOrder.AppShopID).Error
		if err != nil {
			param.Remarks = param.Remarks + "（采购端店铺：" + strconv.Itoa(int(cinemaOrder.AppShopID)) + "不存在）"
			err = nil
		} else {
			param.Remarks = param.Remarks + "（采购端店铺：" + appShop.ShopName + "）"
		}
	}
	err = UpdateSettlementBalance(param, cinemaOrder.ID, cinemaOrder.PaySN)

	return

}

func UpdateSettlementBalance(param paymentModel.UpdateBalance, cinemaOrderId uint, paySn uint) (err error) {

	var Balance = paymentModel.Balance{}
	var action string
	var field string
	if param.OperationType == 1 {
		field = "purchasing_balance"
	}
	if param.Action == 1 {
		action = field + "+ ?"
	}
	if param.Action == 2 {
		action = field + "- ?"
	}

	err = source.DB().Model(Balance).Where("uid = ? and type=?", param.Uid, param.Type).Update(field, gorm.Expr(action, param.Amount)).Error
	if err != nil {

		log.Log().Error("Update UpdateSettlementBalance err", zap.Any("err", err), zap.Any("param", param), zap.Any("action", action), zap.Any("err", err.Error()))
		if strings.Contains(err.Error(), "value is out of range") {
			err = errors.New("请检查采购端余额是否充足")
		}
		return
	}
	if param.OperationType == 1 {
		var purchasingData paymentModel.PurchasingBalance
		purchasingData.Uid = param.Uid
		purchasingData.Amount = param.Amount
		purchasingData.BusinessType = param.PayType
		purchasingData.PetSupplierID = param.PetSupplierID
		var changeBalance paymentModel.Balance
		err = source.DB().Where("uid=?", param.Uid).Where("type = ?", param.PayType).First(&changeBalance).Error
		if err != nil {
			return
		}
		purchasingData.BusinessType = 2
		purchasingData.PayType = 2
		purchasingData.PaySN = paySn
		IntID := int(cinemaOrderId)
		orderID := strconv.Itoa(IntID)
		purchasingData.OrderID = orderID
		purchasingData.Balance = changeBalance.PurchasingBalance

		purchasingData.OrderSn = param.PaySn
		purchasingData.Remarks = param.Remarks

		err = PurchasingBalanceChange(purchasingData)
		if err != nil {
			return
		}
	}

	return
}

func Refund(param paymentModel.UpdateBalance, cinemaOrderId uint, paySn uint) (err error) {

	var Balance = paymentModel.Balance{}
	var action string
	var field string
	if param.OperationType == 1 {
		field = "purchasing_balance"
	}
	if param.Action == 1 {
		action = field + "+ ?"
	}
	if param.Action == 2 {
		action = field + "- ?"
	}
	err = source.DB().Model(Balance).Where("uid = ? and type=?", param.Uid, param.Type).Update(field, gorm.Expr(action, param.Amount)).Error
	if err != nil {
		return
	}
	if param.OperationType == 1 {
		var purchasingData paymentModel.PurchasingBalance
		purchasingData.Uid = param.Uid
		purchasingData.Amount = param.Amount
		purchasingData.BusinessType = param.PayType
		purchasingData.PetSupplierID = param.PetSupplierID
		var changeBalance paymentModel.Balance
		err = source.DB().Where("uid=?", param.Uid).Where("type = ?", param.PayType).First(&changeBalance).Error
		if err != nil {
			return
		}
		purchasingData.BusinessType = 5
		purchasingData.PayType = 2
		purchasingData.PaySN = paySn
		IntID := int(cinemaOrderId)
		orderID := strconv.Itoa(IntID)
		purchasingData.OrderID = orderID
		purchasingData.Balance = changeBalance.PurchasingBalance

		if param.Action == 1 {
			purchasingData.Remarks = "电影票订单退款"

		}
		purchasingData.OrderSn = param.PaySn
		err = PurchasingBalanceChange(purchasingData)
		if err != nil {
			return
		}
	}

	return
}

// 采购余额变动记录
func PurchasingBalanceChange(param paymentModel.PurchasingBalance) (err error) {
	err = source.DB().Create(&param).Error
	if err != nil {
		log.Log().Error("电影票采购余额变动记录失败,", zap.Any("err", err))

	}
	return
}

func (l *ModelData) Dividends(ChannelOrderNo string) (err error) { //分紅业务
	var cinemaOrder model.CinemaTicketOrder

	err = source.DB().First(&cinemaOrder, "channel_order_no=?", ChannelOrderNo).Error
	if err != nil {
		log.Log().Error("查询电影票订单错误", zap.Any("err", err))
	}

	price := utils.Yuan2Fen(cinemaOrder.TotalPrice) - utils.Yuan2Fen(cinemaOrder.TotalSettleAmount)
	price = int64(float64(price) * (cinemaOrder.Ratio / 100))
	//
	var income model2.UserIncomeDetails

	income.Amount = uint(price)
	income.UserID = int(cinemaOrder.UserID)
	income.IncomeType = model2.CinemaTicket
	income.OrderSn = int(cinemaOrder.OrderSN)
	//income.Balance=orderSN

	err = service.IncreaseIncome(income)
	if err != nil {
		log.Log().Error("电影票分红消息发送失败,", zap.Any("err", err))

		return
	}
	return

}

func (l *ModelData) DividendsExport(info model.SearchDividend) (err error, link string) {

	var data []UserIncomeDetails
	db := source.DB().Model(&UserIncomeDetails{}).Preload("User").Preload("Order")

	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)
	}
	if info.UserID != "" {
		db.Where("user_id=?", info.UserID)
	}
	if info.NickName != "" {
		var user model3.User
		var uid []int64
		err = source.DB().Model(&user).Where("username like ?", "%"+info.NickName+"%").Pluck("id", &uid).Error
		if err == nil {
			db.Where("user_id in (?)", uid)

		}
	}

	if info.DividendType != "" {

		db = db.Where("? between ? and ?", info.DividendType, info.DividendTimeStart, info.DividendTimeEnd)

	}
	db.Where("income_type=?", model2.CinemaTicket)

	err = db.Find(&data).Error

	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "下单时间")
	f.SetCellValue("Sheet1", "B1", "订单号")
	f.SetCellValue("Sheet1", "C1", "订单金额")
	f.SetCellValue("Sheet1", "D1", "结算金额")
	f.SetCellValue("Sheet1", "E1", "分成比例")
	f.SetCellValue("Sheet1", "F1", "分成金额")
	f.SetCellValue("Sheet1", "G1", "分成类型")
	f.SetCellValue("Sheet1", "H1", "分成状态")

	i := 2

	for _, v := range data {

		//Balance := strconv.FormatFloat(Decimal(float64(v.Balance)/float64(100)), 'f', -1, 64)
		//balanceAmount += strconv.FormatFloat(Decimal(float64(v.Amount)/float64(100)), 'f', -1, 64)
		//businessType += " 订单ID:" + v.OrderID + " 支付单：" + strconv.Itoa(int(v.PaySN))
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.CreatedAt)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.OrderSn)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.Order.TotalPrice)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.Order.SettlePrice)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.Order.Ratio)
		f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.Amount)
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), "余额")
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), "完成")

		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	// 根据指定路径保存文件
	//year, month, day := time.Now().Format("2006-01-02 15:04:05")
	time := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_cinema_order"
	exist, _ := utils.PathExists(path)

	if !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}
	link = path + "/" + time + "电影票订单导出.xlsx"
	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link

	return
}

func (l *ModelData) Export(info model.SearchOrder) (err error, link string) {
	f := excelize.NewFile()
	// 创建一个工作表
	index := f.NewSheet("Sheet1")
	// 设置单元格的值
	f.SetCellValue("Sheet1", "A1", "下单时间")
	f.SetCellValue("Sheet1", "B1", "订单号")
	f.SetCellValue("Sheet1", "C1", "三方单号")
	f.SetCellValue("Sheet1", "D1", "影票单号")
	f.SetCellValue("Sheet1", "E1", "支付单号")
	f.SetCellValue("Sheet1", "F1", "结算价")
	f.SetCellValue("Sheet1", "G1", "下单价")
	f.SetCellValue("Sheet1", "H1", "状态")

	var cinemaOrder []model.CinemaTicketOrder

	db := source.DB().Model(&model.CinemaTicketOrder{})
	if info.UserID > 0 {
		db.Where("`user_id` = ?", info.UserID)
	}

	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)

	}
	if info.OutOrderNo != "" {
		db.Where("out_order_no=?", info.OutOrderNo)

	}
	if info.Status != "" {
		db.Where("order_status=?", info.Status)

	}
	if info.PayStatus != "" {
		db.Where("pay_status=?", info.PayStatus)

	}

	if info.City != "" {
		db.Where("city_code=?", info.City)

	}
	if info.Regions != "" {
		db.Where("region_code=?", info.Regions)

	}

	if info.MovieTitle != "" {
		db.Where("movie_title=?", "%"+info.MovieTitle+"%")

	}
	if info.TimeStart != "" && info.TimeEnd != "" {
		db = db.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)

	}
	db.Find(&cinemaOrder)
	i := 2

	for _, v := range cinemaOrder {

		//Balance := strconv.FormatFloat(Decimal(float64(v.Balance)/float64(100)), 'f', -1, 64)
		//balanceAmount += strconv.FormatFloat(Decimal(float64(v.Amount)/float64(100)), 'f', -1, 64)
		//businessType += " 订单ID:" + v.OrderID + " 支付单：" + strconv.Itoa(int(v.PaySN))
		f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.CreatedAt)
		f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), v.OrderSN)
		f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), v.ChannelOrderNo)
		f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), v.OutOrderNo)
		f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), v.PaySN)
		switch v.OrderStatus {
		case 2, 3, 4, 5:
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.SettlePrice)
		default:
			f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), "")
		}
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.TotalPrice)

		var status string
		switch v.OrderStatus {
		case 0:
			status = "待接单"
		case 1:
			status = "已接单"
		case 2:
			status = "已出票"
		case 3:
			status = "已完成"
		case 4:
			status = "退款中"
		case 5:
			status = "已退款"
		}
		f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), status)

		i++

	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)

	// 根据指定路径保存文件
	timeString := time.Now().Format("20060102150405")

	path := config.Config().Local.Path + "/export_cinema_order"

	if exist, _ := utils.PathExists(path); !exist {
		// 创建文件夹
		err = os.Mkdir(path, os.ModePerm)
		if err != nil {
			fmt.Printf("mkdir failed![%v]\n", err)
		} else {
			fmt.Printf("mkdir success!\n")
		}
	}

	link = path + "/" + timeString + "电影票订单导出.xlsx"

	if err = f.SaveAs(link); err != nil {
		return
	}

	return err, link

}

func (l *ModelData) OrderList(info model.SearchOrder) (WaitPayNum, WaitReceivingNum, IsReceivingNum, IssueTickets, CompletedNum, CancelIssueTicketsNum, CancelNotsueTicketsNum, BackNum, RefundNum int64, data []model.CinemaTicketOrder, total int64, err error) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.CinemaTicketOrder{}).Preload("User").Preload("PurchasingBalance").Preload("ApplicationShop").Preload("UserIncomeDetails")
	WaitPayNumDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("pay_status=? and order_status=?", 0, 0)
	WaitReceivingNumDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("pay_status=? and order_status=?", 1, 0)
	IsReceivingNumDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("order_status=?", 1)
	IssueTicketsDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("order_status=?", 2)
	CompletedNumDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("order_status=?", 3)
	BackNumDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("order_status=?", 4)
	RefundNumDb := source.DB().Model(&model.CinemaTicketOrder{}).Where("refund_status=?", 1)
	CancelIssueTickets := source.DB().Model(&model.CinemaTicketOrder{}).Where("order_status=?", 7)  //退款 已出票
	CancelNotsueTickets := source.DB().Model(&model.CinemaTicketOrder{}).Where("order_status=?", 6) //退款 未出票

	if info.UserID > 0 {
		db.Where("`user_id` = ?", info.UserID)
		WaitPayNumDb.Where("user_id=?", info.UserID)
		WaitReceivingNumDb.Where("user_id=?", info.UserID)
		IsReceivingNumDb.Where("user_id=?", info.UserID)
		IssueTicketsDb.Where("user_id=?", info.UserID)
		CompletedNumDb.Where("user_id=?", info.UserID)
		BackNumDb.Where("user_id=?", info.UserID)
		RefundNumDb.Where("user_id=?", info.UserID)
		CancelIssueTickets.Where("user_id=?", info.UserID)
		CancelNotsueTickets.Where("user_id=?", info.UserID)
	}

	if info.ShopName != "" {
		appShopId := []uint{}
		err = source.DB().Model(model.ApplicationShop{}).Where("shop_name like ?", "%"+info.ShopName+"%").Pluck("id", &appShopId).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`application_shop_id` in ?", appShopId)
		WaitPayNumDb.Where("`application_shop_id` in ?", appShopId)
		WaitReceivingNumDb.Where("`application_shop_id` in ?", appShopId)
		IsReceivingNumDb.Where("`application_shop_id` in ?", appShopId)
		IssueTicketsDb.Where("`application_shop_id` in ?", appShopId)
		CompletedNumDb.Where("`application_shop_id` in ?", appShopId)
		BackNumDb.Where("`application_shop_id` in ?", appShopId)
		RefundNumDb.Where("`application_shop_id` in ?", appShopId)
		CancelIssueTickets.Where("`application_shop_id` in ?", appShopId)
		CancelNotsueTickets.Where("`application_shop_id` in ?", appShopId)
	}
	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)
		WaitPayNumDb.Where("order_sn=?", info.OrderSN)
		WaitReceivingNumDb.Where("order_sn=?", info.OrderSN)
		IsReceivingNumDb.Where("order_sn=?", info.OrderSN)
		IssueTicketsDb.Where("order_sn=?", info.OrderSN)
		CompletedNumDb.Where("order_sn=?", info.OrderSN)
		BackNumDb.Where("order_sn=?", info.OrderSN)
		RefundNumDb.Where("order_sn=?", info.OrderSN)
		CancelIssueTickets.Where("order_sn=?", info.OrderSN)
		CancelNotsueTickets.Where("order_sn=?", info.OrderSN)

	}
	if info.ChannelOrderNo != "" {
		db.Where("channel_order_no=?", info.ChannelOrderNo)
		WaitPayNumDb.Where("channel_order_no=?", info.ChannelOrderNo)
		WaitReceivingNumDb.Where("channel_order_no=?", info.ChannelOrderNo)
		IsReceivingNumDb.Where("channel_order_no=?", info.ChannelOrderNo)
		IssueTicketsDb.Where("channel_order_no=?", info.ChannelOrderNo)
		CompletedNumDb.Where("channel_order_no=?", info.ChannelOrderNo)
		BackNumDb.Where("channel_order_no=?", info.ChannelOrderNo)
		RefundNumDb.Where("channel_order_no=?", info.ChannelOrderNo)
		CancelIssueTickets.Where("channel_order_no=?", info.OrderSN)
		CancelNotsueTickets.Where("channel_order_no=?", info.OrderSN)

	}
	if info.OutOrderNo != "" {
		db.Where("out_order_no=?", info.OutOrderNo)
		WaitPayNumDb.Where("out_order_no=?", info.OutOrderNo)
		WaitReceivingNumDb.Where("out_order_no=?", info.OutOrderNo)
		IsReceivingNumDb.Where("out_order_no=?", info.OutOrderNo)
		IssueTicketsDb.Where("out_order_no=?", info.OutOrderNo)
		CompletedNumDb.Where("out_order_no=?", info.OutOrderNo)
		BackNumDb.Where("out_order_no=?", info.OutOrderNo)
		RefundNumDb.Where("out_order_no=?", info.OutOrderNo)
		CancelIssueTickets.Where("out_order_no=?", info.OrderSN)
		CancelNotsueTickets.Where("out_order_no=?", info.OrderSN)
	}
	if info.Status != "" {

		if info.Status == "0" {
			db.Where("order_status=0").Where("pay_status=1")
			//WaitPayNumDb.Where("order_status=0").Where("pay_status=1")
			//WaitReceivingNumDb.Where("order_status=0").Where("pay_status=1")
			//IsReceivingNumDb.Where("order_status=0").Where("pay_status=1")
			//IssueTicketsDb.Where("order_status=0").Where("pay_status=1")
			//CompletedNumDb.Where("order_status=0").Where("pay_status=1")
			//BackNumDb.Where("order_status=0").Where("pay_status=1")
			//RefundNumDb.Where("order_status=0").Where("pay_status=1")
			//CancelIssueTickets.Where("order_status=0").Where("pay_status=1")
			//CancelNotsueTickets.Where("order_status=0").Where("pay_status=1")
		} else if info.Status == "10" {
			db.Where("order_status=0").Where("pay_status=0")
			//WaitPayNumDb.Where("order_status=0").Where("pay_status=0")
			//WaitReceivingNumDb.Where("order_status=0").Where("pay_status=0")
			//IsReceivingNumDb.Where("order_status=0").Where("pay_status=0")
			//IssueTicketsDb.Where("order_status=0").Where("pay_status=0")
			//CompletedNumDb.Where("order_status=0").Where("pay_status=0")
			//BackNumDb.Where("order_status=0").Where("pay_status=0")
			//RefundNumDb.Where("order_status=0").Where("pay_status=0")
			//CancelIssueTickets.Where("order_status=0").Where("pay_status=0")
			//CancelNotsueTickets.Where("order_status=0").Where("pay_status=0")
		} else if info.Status == "8" {
			db.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//WaitPayNumDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//WaitReceivingNumDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//IsReceivingNumDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//IssueTicketsDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//CompletedNumDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//BackNumDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//RefundNumDb.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//CancelIssueTickets.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
			//CancelNotsueTickets.Where("(order_status in ? or refund_status=?)", []string{"4", "5", "6", "7"}, 1)
		} else if info.Status == "5" {
			db.Where("refund_status=?", 1)
			//WaitPayNumDb.Where("refund_status=?", 1)
			//WaitReceivingNumDb.Where("refund_status=?", 1)
			//IsReceivingNumDb.Where("refund_status=?", 1)
			//IssueTicketsDb.Where("refund_status=?", 1)
			//CompletedNumDb.Where("refund_status=?", 1)
			//BackNumDb.Where("refund_status=?", 1)
			//RefundNumDb.Where("refund_status=?", 1)
			//CancelIssueTickets.Where("refund_status=?", 1)
			//CancelNotsueTickets.Where("refund_status=?", 1)
		} else {
			db.Where("order_status=?", info.Status)
			//WaitPayNumDb.Where("order_status=?", info.Status)
			//WaitReceivingNumDb.Where("order_status=?", info.Status)
			//IsReceivingNumDb.Where("order_status=?", info.Status)
			//IssueTicketsDb.Where("order_status=?", info.Status)
			//CompletedNumDb.Where("order_status=?", info.Status)
			//BackNumDb.Where("order_status=?", info.Status)
			//RefundNumDb.Where("order_status=?", info.Status)
			//CancelIssueTickets.Where("order_status=?", info.Status)
			//CancelNotsueTickets.Where("order_status=?", info.Status)
		}
	}
	if info.City != "" {
		db.Where("city_code=?", info.City)
		WaitPayNumDb.Where("city_code=?", info.City)
		WaitReceivingNumDb.Where("city_code=?", info.City)
		IsReceivingNumDb.Where("city_code=?", info.City)
		IssueTicketsDb.Where("city_code=?", info.City)
		CompletedNumDb.Where("city_code=?", info.City)
		BackNumDb.Where("city_code=?", info.City)
		RefundNumDb.Where("city_code=?", info.City)
		CancelIssueTickets.Where("city_code=?", info.OrderSN)
		CancelNotsueTickets.Where("city_code=?", info.OrderSN)
	}
	if info.Regions != "" {
		db.Where("region_code=?", info.Regions)
		WaitPayNumDb.Where("region_code=?", info.Regions)
		WaitReceivingNumDb.Where("region_code=?", info.Regions)
		IsReceivingNumDb.Where("region_code=?", info.Regions)
		IssueTicketsDb.Where("region_code=?", info.Regions)
		CompletedNumDb.Where("region_code=?", info.Regions)
		BackNumDb.Where("region_code=?", info.Regions)
		RefundNumDb.Where("region_code=?", info.Regions)
		CancelIssueTickets.Where("region_code=?", info.OrderSN)
		CancelNotsueTickets.Where("region_code=?", info.OrderSN)
	}
	if info.MovieTitle != "" {
		db.Where("movie_title=?", "%"+info.MovieTitle+"%")
		WaitPayNumDb.Where("movie_title=?", "%"+info.MovieTitle+"%")
		WaitReceivingNumDb.Where("movie_title=?", "%"+info.MovieTitle+"%")
		IsReceivingNumDb.Where("movie_title=?", "%"+info.MovieTitle+"%")
		IssueTicketsDb.Where("movie_title=?", "%"+info.MovieTitle+"%")
		CompletedNumDb.Where("movie_title=?", "%"+info.MovieTitle+"%")
		BackNumDb.Where("movie_title=?", "%"+info.MovieTitle+"%")
		RefundNumDb.Where("movie_title=?", "%"+info.MovieTitle+"%")

		CancelIssueTickets.Where("movie_title=?", "%"+info.MovieTitle+"%")
		CancelNotsueTickets.Where("movie_title=?", "%"+info.MovieTitle+"%")

	}
	if info.TimeStart != "" && info.TimeEnd != "" {
		db = db.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		WaitPayNumDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		WaitReceivingNumDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		IsReceivingNumDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		IssueTicketsDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		CompletedNumDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		BackNumDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		RefundNumDb.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)

		CancelIssueTickets.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
		CancelNotsueTickets.Where("created_at BETWEEN ? AND ?", info.TimeStart, info.TimeEnd)
	}
	//if info.pay != "" {
	//	db.Where("order_status=?", info.Status)
	//}

	// 如果有条件搜索 下方会自动创建搜索语句

	WaitPayNumDb.Count(&WaitPayNum)
	WaitReceivingNumDb.Count(&WaitReceivingNum)
	IsReceivingNumDb.Count(&IsReceivingNum)
	IssueTicketsDb.Count(&IssueTickets)
	CompletedNumDb.Count(&CompletedNum)
	CancelIssueTickets.Count(&CancelIssueTicketsNum)
	CancelNotsueTickets.Count(&CancelNotsueTicketsNum)
	RefundNumDb.Count(&RefundNum)
	BackNumDb.Count(&BackNum)
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("id desc").Find(&data).Error

	return
}

type UserIncomeDetails struct {
	source.Model
	UserID           int                     `json:"user_id" gorm:"column:user_id"`
	OrderSn          int                     `json:"order_sn" gorm:"column:order_sn"`
	IncomeType       int                     `json:"income_type" gorm:"column:income_type;comment:收入类型(1区域分红);default:0" `
	IncomeName       string                  `json:"income_name" gorm:"-" `
	Amount           uint                    `json:"amount" gorm:"column:amount"`
	Balance          uint                    `json:"balance" gorm:"column:balance"`
	User             model3.User             `json:"user" gorm:"foreignKey:UserID"`
	WithdrawalStatus int                     `json:"withdrawal_status"  gorm:"default:0"` //0  未提现    1 驳回     2 已申请  3 通过  4  无效
	WithdrawalID     uint                    `json:"withdrawal_id" `
	Order            model.CinemaTicketOrder `json:"order"  gorm:"foreignKey:order_sn;references:order_sn"`
}

func (l *ModelData) DividendsList(info model.SearchDividend) (data []UserIncomeDetails, total int64, err error) {

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&UserIncomeDetails{}).Preload("User").Preload("Order")

	if info.OrderSN != "" {
		db.Where("order_sn=?", info.OrderSN)
	}
	if info.UserID != "" {
		db.Where("user_id=?", info.UserID)
	}
	if info.DividendTimeStart != "" && info.DividendTimeEnd != "" {
		db = db.Where("created_at BETWEEN ? AND ?", info.DividendTimeStart, info.DividendTimeEnd)

	}
	if info.NickName != "" {
		var user model3.User
		var uid []int64
		err = source.DB().Model(&user).Where("username like ?", "%"+info.NickName+"%").Pluck("id", &uid).Error
		if err == nil {
			db.Where("user_id in (?)", uid)

		}
	}

	if info.DividendType != "" {

		db = db.Where("? between ? and ?", info.DividendType, info.DividendTimeStart, info.DividendTimeEnd)

	}
	db.Where("income_type=?", model2.CinemaTicket)

	// 如果有条件搜索 下方会自动创建搜索语句
	err = db.Count(&total).Error
	err = db.Order("id desc").Limit(limit).Offset(offset).Find(&data).Error

	return
}

//检验场次座位是否可售

func (l *ModelData) MovieOrderCheck(showId string, seatIds []string) (err error, data model.OrderCheck) {

	url := l.Domain + "/api/movie/order/check"
	reqBody := make(map[string]interface{})
	reqBody["showId"] = showId
	reqBody["seatIds"] = seatIds

	reqJsonMap, _ := json.Marshal(reqBody)
	log.Log().Info("MovieOrderCheck 校验请求,Marshal", zap.Any("err", string(reqJsonMap)))

	dst, err := openssl.AesECBEncrypt(reqJsonMap, []byte(l.Key), openssl.PKCS5_PADDING)
	aseData := base64.StdEncoding.EncodeToString(dst)
	time := strconv.Itoa(int(time.Now().Unix() * 1000))
	reqData := make(map[string]string)
	reqData["channelId"] = l.ChannelId
	reqData["encryptedData"] = aseData
	reqData["nonceStr"] = time
	reqData["timestamp"] = time

	signSort := Sign(reqData, l.Secret)
	reqData["sign"] = signSort
	JsonData, _ := json.Marshal(reqData)
	log.Log().Info("MovieOrderCheck 校验请求,", zap.Any("err", string(JsonData)))

	resData := utils.HttpPostJson(JsonData, url)
	log.Log().Info("MovieOrderCheck 校验返回,", zap.Any("err", string(resData)))

	var resOrder model.ResPublicData
	json.Unmarshal(resData, &resOrder)
	if err != nil {
		return
	}
	if resOrder.Code != 0 {
		err = errors.New(resOrder.Message)
		log.Log().Error("获取数据失败,", zap.Any("err", resOrder))
		return
	}
	src1, _ := base64.StdEncoding.DecodeString(resOrder.Data)
	decodeData, _ := openssl.AesECBDecrypt(src1, []byte(l.Key), openssl.PKCS5_PADDING)
	json.Unmarshal(decodeData, &data)
	log.Log().Info("MovieOrderCheck 校验返回AesECBDecrypt,", zap.Any("err", data))

	if err != nil {
		return
	}
	return
}

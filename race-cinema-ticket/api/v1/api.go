package v1

import (
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"race-cinema-ticket/model"
	"race-cinema-ticket/service"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

func SetSetting(c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SetSetting(setting); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("设置成功", c)
	}
}

func GetSetting(c *gin.Context) {
	err, data := service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.OkWithMessage("接口请求失败请检查配置信息", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func GetBalance(c *gin.Context) {
	if err, data := service.GetBalance(); err != nil {
		yzResponse.OkWithMessage("接口请求失败请检查配置信息", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

type PageResultWithNum struct {
	yzResponse.PageResult
	WaitPayNum             int64
	WaitReceivingNum       int64
	IsReceivingNum         int64
	IssueTickets           int64
	CompletedNum           int64
	ClosedNum              int64
	BackNum                int64
	RefundNum              int64
	CancelIssueTicketsNum  int64
	CancelNotsueTicketsNum int64
	Tags                   []Tag `json:"tags"`
}

type Tag struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}

func OrderList(c *gin.Context) {
	var pageInfo model.SearchOrder
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	if WaitPayNum, WaitReceivingNum, IsReceivingNum, IssueTickets, CompletedNum, CancelIssueTicketsNum, CancelNotsueTicketsNum, BackNum, RefundNum, list, total, err := l.OrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {

		var pageResultWithNum PageResultWithNum
		pageResultWithNum.WaitPayNum = WaitPayNum
		pageResultWithNum.WaitReceivingNum = WaitReceivingNum
		pageResultWithNum.IsReceivingNum = IsReceivingNum
		pageResultWithNum.IssueTickets = IssueTickets

		pageResultWithNum.CancelIssueTicketsNum = CancelIssueTicketsNum
		pageResultWithNum.CancelNotsueTicketsNum = CancelNotsueTicketsNum

		pageResultWithNum.CompletedNum = CompletedNum
		//pageResultWithNum.ClosedNum = ClosedNum
		pageResultWithNum.BackNum = BackNum
		pageResultWithNum.RefundNum = RefundNum
		pageResultWithNum.Page = pageInfo.Page
		pageResultWithNum.PageSize = pageInfo.PageSize
		pageResultWithNum.Total = total
		pageResultWithNum.List = list
		yzResponse.OkWithDetailed(pageResultWithNum, "获取成功", c)

		//yzResponse.OkWithDetailed(yzResponse.PageResult{
		//	List:     list,
		//	Total:    total,
		//	Page:     pageInfo.Page,
		//	PageSize: pageInfo.PageSize,
		//}, "获取成功", c)
	}
}

func FrontendOrderList(c *gin.Context) {
	var pageInfo model.SearchOrder
	if err := c.ShouldBindJSON(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	pageInfo.UserID = ufv1.GetUserID(c)

	var l service.ModelData
	WaitPayNum, WaitReceivingNum, IsReceivingNum, IssueTickets, CompletedNum, CancelIssueTicketsNum, CancelNotsueTicketsNum, BackNum, RefundNum, list, total, err := l.OrderList(pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	tags := []Tag{
		{
			"全部", utils.UnSafeUri(c.FullPath(), ""),
		},
		{
			"待付款", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 10}),
		},
		{
			"待接单", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 0}),
		},
		{
			"已接单", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 1}),
		},
		{
			"已出票", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 2}),
		},
		{
			"已完成", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 3}),
		},
		{
			"售后", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 8}),
		},
	}
	pageResultWithNum := PageResultWithNum{
		WaitPayNum:             WaitPayNum,
		WaitReceivingNum:       WaitReceivingNum,
		IsReceivingNum:         IsReceivingNum,
		IssueTickets:           IssueTickets,
		CompletedNum:           CompletedNum,
		BackNum:                BackNum,
		RefundNum:              RefundNum,
		CancelIssueTicketsNum:  CancelIssueTicketsNum,
		CancelNotsueTicketsNum: CancelNotsueTicketsNum,
		Tags:                   tags,
		PageResult: yzResponse.PageResult{
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
			Total:    total,
			List:     list,
		},
	}
	yzResponse.OkWithDetailed(pageResultWithNum, "获取成功", c)
}

func OrderExportList(c *gin.Context) {
	var pageInfo model.SearchOrder
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	var link string
	if err, link = l.Export(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func FrontendOrderListExport(c *gin.Context) {
	var pageInfo model.SearchOrder
	if err := c.ShouldBindJSON(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	pageInfo.UserID = ufv1.GetUserID(c)

	var l service.ModelData
	if err, link := l.Export(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

func DividendsList(c *gin.Context) {

	var pageInfo model.SearchDividend
	var err error
	err = c.BindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	if list, total, err := l.DividendsList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func DividendsExport(c *gin.Context) {
	var pageInfo model.SearchDividend
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	var link string
	if err, link = l.DividendsExport(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithData(link, c)
	}
}

func CallbackNotification(c *gin.Context) {
	bytes, _ := c.GetRawData()
	log.Log().Info("电影票-CallbackNotification原始-json,", zap.Any("err", string(bytes)))
	var data model.CallbackNotificationData

	err := json.Unmarshal(bytes, &data)
	//err := c.ShouldBindJSON(&data)

	var l service.ModelData
	l.InitSetting()

	if err != nil {
		log.Log().Error("电影票-CallbackNotificatio解析失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := l.CallbackNotification(data); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		c.String(http.StatusOK, "success")

	}
}

func Cities(c *gin.Context) {
	var l service.ModelData
	l.InitSetting()
	if err, data := l.Cities(); err != nil {
		yzResponse.OkWithMessage("接口请求失败请检查配置信息", c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func LocalOrderSubmit(c *gin.Context) {
	var requestData model.RequestId

	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var l service.ModelData
	l.InitSetting()
	if err, data := l.LocalOrderSubmit(requestData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func Refund(c *gin.Context) {
	var requestData model.RefundData

	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	var l service.ModelData
	l.InitSetting()
	if err, data := l.Refund(requestData); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func Regions(c *gin.Context) {
	var requestData model.RegionsData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()

	if Err, data := l.Regions(requestData.Code); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func MovieList(c *gin.Context) {
	var requestData model.MovieListData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.MovieList(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func GetCinemaLines(c *gin.Context) {
	var requestData model.MovieListData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.GetCinemaLines(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func MovieDetail(c *gin.Context) {
	var requestData model.ShowListData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.MovieDetail(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func MovieDates(c *gin.Context) {
	var requestData model.MovieDatesData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.MovieDates(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func MovieCinemaList(c *gin.Context) {
	var requestData model.MovieCinemaListData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.MovieCinemaList(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func CinemaList(c *gin.Context) {
	var requestData model.SearchCinemaListData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.CinemaList(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func CinemaBrands(c *gin.Context) {

	var err error
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.CinemaBrands(); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func CinemaMovies(c *gin.Context) {
	var requestData model.CinemaMoviesData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.CinemaMovies(requestData.CinemaId); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func ShowList(c *gin.Context) {
	var requestData model.ShowListData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.ShowList(requestData); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func ShowSeats(c *gin.Context) {
	var requestData model.ShowSeatsData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.ShowSeats(requestData.ShowId); Err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func MovieOrderCheck(c *gin.Context) {
	var requestData model.MovieOrderCheckData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.MovieOrderCheck(requestData.ShowId, requestData.SeatIds); err != nil {
		yzResponse.FailWithDetailed(data, Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func OrderSubmit(c *gin.Context) {
	var requestData model.OrderSubmitData
	err := c.ShouldBindJSON(&requestData)
	log.Log().Info("电影票参数", zap.Any("requestData", requestData))
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := utils.GetAppUserID(c)
	appID := utils.GetAppID(c)
	appShopID := utils.GetAppShopID(c)
	requestData.UserID = userID
	requestData.AppID = appID
	requestData.AppShopID = appShopID
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.OrderSubmit(requestData); Err != nil {
		yzResponse.FailWithDetailed(data, Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func OrderGet(c *gin.Context) {
	var requestData model.GetOrderData
	err := c.ShouldBindJSON(&requestData)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var l service.ModelData
	l.InitSetting()
	if Err, data := l.OrderGet(requestData.ChannelOrderNo, requestData.OutOrderNo); err != nil {
		yzResponse.FailWithMessage(Err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

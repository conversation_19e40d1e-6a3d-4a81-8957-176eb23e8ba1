package service

import (
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"gongmall/model"
	"gorm.io/gorm"
	url2 "net/url"
	"sort"
	"strconv"
	"strings"
	"time"
	umodel "user/model"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

func SetSetting(setting model.Setting) (err error) {

	mapData, _ := json.Marshal(setting)

	var sysSetting model2.SysSetting

	sysSetting.Key = "gongmall"
	//maps["value"] = string(mapData)
	err = source.DB().Where("`key` = ?", "gongmall").First(&sysSetting).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		source.DB().Where("`key` = ?", "gongmall").Create(&sysSetting)
		err = nil
	} else {
		sysSetting.Value = string(mapData)
		err = source.DB().Where("`key` = ?", "gongmall").Updates(&sysSetting).Error
	}

	return
}

func GetSetting() (err error, sysSetting model2.SysSetting) {
	err = source.DB().Table("sys_settings").Where("`key` = ?", "gongmall").First(&sysSetting).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("请先保存设置")
	}
	return
}

func SinglePaymentCallBack(requestContract model.SinglePaymentCallBack) (err error) {

	var gongMallPay model.GongMallPayment

	gongMallPay.PayTime = requestContract.PayTime
	if requestContract.Status == 20 {
		gongMallPay.Status = 2

	}
	if requestContract.Status == 30 {
		gongMallPay.Status = 1

	}
	gongMallPay.ErrMsg = requestContract.FailReason
	err = source.DB().Where("order_sn=?", requestContract.RequestId).Updates(&gongMallPay).Error
	if err != nil {
		log.Log().Error("err", zap.Any("gongMallPay ErrMsg update", err))
	}

	return
}

func GetHost() string {
	Host := setting.ApiUrl
	lastChar := string(Host[len(Host)-1])
	if lastChar == "/" {
		Host = Host[:len(Host)-1]
	}
	return Host
}
func GetContractStatus(requestContract model.RequestContract) (err error, data interface{}) {
	url := GetHost() + "/api/merchant/employee/getContractStatus"
	var resData []byte
	log.Log().Info("GetContractStatus", zap.Any("setting", setting), zap.Any("ApiUrl", setting.ApiUrl), zap.Any("url", url))

	timestamp := strconv.Itoa(int(time.Now().UnixNano()))
	nonce := timestamp
	reqValue := url2.Values{}
	reqValue.Add("appKey", setting.Key)
	if setting.ServiceId != "" {
		reqValue.Add("serviceId", setting.ServiceId)
	}
	reqValue.Add("nonce", nonce)
	reqValue.Add("timestamp", timestamp)
	reqValue.Add("identity", requestContract.Identity)
	reqValue.Add("sign", Sign(reqValue, setting.Secret))

	var header = make(map[string]string)

	err, resData = utils.PostForm(url, reqValue, header)
	log.Log().Info("GetContractStatus resData", zap.Any("resData", string(resData)))
	if err != nil {
		return
	}
	var contractStatus model.ContractStatus
	err = json.Unmarshal(resData, &contractStatus)
	if err != nil {
		return
	}

	var mapData = make(map[string]string)
	mapData["status"] = "false"
	mapData["url"] = setting.ContractUrl

	if contractStatus.Success != true {
		err = errors.New(contractStatus.ErrorMsg)
		data = mapData
		return
	}

	if contractStatus.Data.ProcessStatus >= 2 {
		mapData["status"] = "true"
	}

	data = mapData

	return
}
func GetList(info model.SearchGongMallPayment) (err error, total int64, list []model.GongMallPaymentModel) {
	db := source.DB().Model(&model.GongMallPaymentModel{})

	if info.Uid > 0 {
		db.Where("uid = ?", info.Uid)
	}
	if info.OrderSN != "" {
		db.Where("order_sn = ?", info.OrderSN)
	}

	if info.Nickname != "" {
		var userIds []int64
		err = source.DB().Model(umodel.User{}).Where("nickname like ?", "%"+info.Nickname+"%").Pluck("id", &userIds).Error
		if err == nil && len(userIds) > 0 {
			db.Where("uid  in(?)", userIds)
		}

	}

	if info.Status > 0 {
		db.Where("status = ?", info.Status)
	}

	if info.PayTimeStart != "" && info.PayTimeEnd != "" {
		db.Where("pay_time between ? and ?", info.PayTimeStart, info.PayTimeEnd)

	}

	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	err = db.Count(&total).Error
	err = db.Preload("User").Order("id desc").Limit(limit).Offset(offset).Find(&list).Error

	return err, total, list
}

type MapEntryHandler func(string, string)

// 按字母顺序遍历map
func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}

//func Sign(maplist map[string]string, key string) string {
//
//	var signature string
//	//按照字母顺序遍历
//	traverseMapInStringOrder(maplist, func(key string, value string) {
//		if signature == "" {
//			signature += key + "=" + value
//		} else {
//			signature += "&" + key + "=" + value
//		}
//	})
//
//	signTemp := signature + "&appSecret=" + key
//
//	signKey := utils.MD5V([]byte(signTemp))
//	signUpper := strings.ToUpper(signKey)
//
//	return signUpper
//
//}

func Sign(str url2.Values, key string) string {

	//if urlPath == "" {
	//	return ""
	//}
	//
	//urlpath := strings.Split(urlPath, "openapi/")
	//if len(urlpath) < 2 {
	//	return ""
	//}
	//path := urlpath[1]
	maplist := make(map[string]string)
	for i, item := range str {
		maplist[i] = item[0]
	}
	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(maplist, func(key string, value string) {
		if signature == "" {
			signature += key + "=" + value
		} else {
			signature += "&" + key + "=" + value
		}
	})

	signTemp := signature + "&appSecret=" + key

	signKey := utils.MD5V([]byte(signTemp))
	signUpper := strings.ToUpper(signKey)

	return signUpper

	//return "ACD581CC036CC0D2D46FF892D0B9B455F8BE60B7"
}

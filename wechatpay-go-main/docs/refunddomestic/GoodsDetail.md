# GoodsDetail

## 属性列表

名称 | 类型 | 描述 | 补充说明
------------ | ------------- | ------------- | -------------
**MerchantGoodsId** | **string** | 由半角的大小写字母、数字、中划线、下划线中的一种或几种组成  | 
**WechatpayGoodsId** | **string** | 微信支付定义的统一商品编号（没有可不传）  | [可选] 
**GoodsName** | **string** | 商品的实际名称  | [可选] 
**UnitPrice** | **int64** | 商品单价金额，单位为分  | 
**RefundAmount** | **int64** | 商品退款金额，单位为分  | 
**RefundQuantity** | **int64** | 对应商品的退货数量  | 

[\[返回类型列表\]](README.md#类型列表)
[\[返回接口列表\]](README.md#接口列表)
[\[返回服务README\]](README.md)



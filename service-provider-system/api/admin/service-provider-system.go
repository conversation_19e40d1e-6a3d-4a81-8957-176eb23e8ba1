package admin

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"service-provider-system/common"
	"service-provider-system/model"
	"service-provider-system/request"
	"service-provider-system/service"
	"service-provider-system/setting"
	"strconv"
	userModel "user/model"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// @Tags 服务商系统API
// @Summary 获取服务商系统配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取服务商系统配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/getSysServiceProviderSystemSetting [get]
func GetSysServiceProviderSystemSetting(c *gin.Context) {
	var sys setting.SysSetting
	_ = c.ShouldBind<PERSON>(&sys)

	err, sysServiceProviderSystemSetting := setting.GetSysServiceProviderSystemSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var url = c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host + "/supplyapi/" // --正式情况
	sysServiceProviderSystemSetting.Value.CallBack = url + "serviceProviderSystem/callBack"
	if sysServiceProviderSystemSetting.Value.ServiceOrderId.SupplierId == 0 {
		err, sysServiceProviderSystemSetting = setting.GenerateId(sysServiceProviderSystemSetting)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}

	var configData interface{}
	jsonConfig, _ := json.Marshal(&sysServiceProviderSystemSetting)
	configData = base64.StdEncoding.EncodeToString(jsonConfig)

	yzResponse.OkWithData(configData, c)
	//yzResponse.OkWithData(sysServiceProviderSystemSetting, c)

}

// @Tags 服务商系统API
// @Summary 保存 服务商系统配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存获取服务商系统配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/saveSysServiceProviderSystemSetting [get]
func SaveSysServiceProviderSystemSetting(c *gin.Context) {
	var saveSaveSysShareLiveSetting request.SaveSaveSysServiceProviderSystemSetting
	_ = c.ShouldBindJSON(&saveSaveSysShareLiveSetting)

	var sys setting.SysSetting

	sysByte, err := base64.StdEncoding.DecodeString(saveSaveSysShareLiveSetting.Data)
	if err != nil {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	_ = json.Unmarshal(sysByte, &sys)

	sys.Key = setting.ServiceProviderSystemKey

	err = setting.SaveSysServiceProviderSystemSetting(sys)
	if err != nil {
		yzResponse.FailWithMessage("保存失败"+err.Error(), c)
		return
	}
	yzResponse.Ok(c)

}

// @Tags 服务商系统API
// @Summary 推送用户
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "推送用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/pushUser [post]
func PushUser(c *gin.Context) {
	var pushUser request.PushUser
	_ = c.ShouldBindJSON(&pushUser)
	if pushUser.UserId == 0 {
		yzResponse.FailWithMessage("请提交推送用户", c)
		return
	}
	err := service.PushUser(pushUser)
	if err != nil {
		log.Log().Error("服务商系统-推送用户错误", zap.Any("err", err))
		yzResponse.FailWithMessage("推送错误："+err.Error(), c)
		return
	}
	yzResponse.Ok(c)
}

// @Tags 服务商系统API
// @Summary 推送用户
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "推送用户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/pushUser [post]
func AllPushUser(c *gin.Context) {
	var pageInfo request.PushUserListSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if common.IsAllPushUser == 1 {
		yzResponse.FailWithMessage("正在进行一键导入请勿重复操作", c)
		return
	}
	err = service.AllPushUser(pageInfo)
	if err != nil {
		log.Log().Error("服务商系统-推送用户错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
}

// @Tags 服务商系统API
// @Summary 待推送用户数量
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "待推送用户数量"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/allPushUserTotal [post]
func AllPushUserTotal(c *gin.Context) {
	var pageInfo request.PushUserListSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, total := service.AllPushUserTotal(pageInfo)
	if err != nil {
		log.Log().Error("服务商系统-统计待推送用户总数", zap.Any("err", err))
		yzResponse.FailWithMessage("服务商系统-统计待推送用户总数错误"+err.Error(), c)
		return
	}
	yzResponse.OkWithData(total, c)
}

// @Tags 服务商系统
// @Summary 服务商系统分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "服务商系统推送用户列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /serviceProviderSystem/GetPushUserList [post]
func GetPushUserList(c *gin.Context) {
	var pageInfo request.PushUserListSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}
	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 20
	}

	if err, list, total := service.PushUserList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 服务商系统
// @Summary 服务商系统分类列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "服务商系统推送订单列表"
// @Success 200 {object} yzResponse.PageResult
// @Router /serviceProviderSystem/GetPushOrderList [post]
func GetPushOrderList(c *gin.Context) {
	var pageInfo request.PushOrderListSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}
	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 20
	}

	if err, list, total := service.PushOrderList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 服务商系统API
// @Summary 推送订单-单独
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "推送订单-单独"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/pushOrder [post]
func PushOrder(c *gin.Context) {
	var pushOrder request.PushOrder
	_ = c.ShouldBindJSON(&pushOrder)
	if pushOrder.OrderId == 0 {
		yzResponse.FailWithMessage("请提交推送订单", c)
		return
	}
	err := service.PushOrder(pushOrder)
	if err != nil {
		log.Log().Error("服务商系统-推送订单错误", zap.Any("err", err))
		yzResponse.FailWithMessage("推送订单错误"+err.Error(), c)
		return
	}
	yzResponse.Ok(c)
}

// @Tags 服务商系统API
// @Summary 一键推送
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "一键推送"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/pushOrder [post]
func AllPushOrder(c *gin.Context) {
	var pushOrder request.PushOrderListSearch
	_ = c.ShouldBindJSON(&pushOrder)
	if common.IsAllPushOrder == 1 {
		yzResponse.FailWithMessage("一键导入中", c)
		return
	}
	err := service.AllPushOrder(pushOrder)
	if err != nil {
		log.Log().Error("服务商系统-推送订单错误", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.Ok(c)
}

/*
*

	回调
*/
func CallBack(c *gin.Context) {
	var callBack request.CallBackRequest
	_ = c.ShouldBindJSON(&callBack)
	var err error
	var res = make(map[string]interface{})
	err = service.CallBackVerify(callBack)
	if err != nil {
		res["code"] = "error"
		res["message"] = err.Error()
		c.JSON(http.StatusOK, res)
		return
	}
	switch callBack.Event {
	case "openapi_store_bind":
		//门店
		var data []common.GetStoreStatusData
		var jsonByte []byte
		jsonByte, err = json.Marshal(callBack.Datas)
		if err != nil {
			return
		}
		err = json.Unmarshal(jsonByte, &data)
		if err != nil {
			return
		}
		for _, data := range data {
			if data.StoreId != "" {
				var pushUser model.ServiceProviderSystemPushUser
				pushUser.StoreName = data.YqShortName
				pushUser.StoreCode = data.YqStoreCode
				pushUser.DirectlyStoreName = data.AgentName
				pushUser.DirectlyStoreCode = data.AgentCode
				pushUser.Status = 1
				err = source.DB().Model(&model.ServiceProviderSystemPushUser{}).Where("user_id = ?", data.StoreId).Updates(&pushUser).Error
				if err != nil {
					log.Log().Error("服务商系统-接收门店回调保存记录失败", zap.Any("err", err), zap.Any("user", callBack))
					yzResponse.FailWithMessage("服务商系统-接收门店回调保存记录失败"+err.Error(), c)
					return
				}
			}
		}
		break
	case "member_notify":
		var data common.MemberNotify
		var jsonByte []byte
		jsonByte, err = json.Marshal(callBack.Datas)
		if err != nil {
			return
		}
		err = json.Unmarshal(jsonByte, &data)
		if err != nil {
			return
		}
		var levelId uint
		err, levelId = service.GetMemberRelationWithRelationID(uint(data.LevelId))
		if err != nil {
			return
		}
		var user userModel.User
		err = source.DB().Where("username = ?", data.Tel).First(&user).Error
		if err != nil {
			return
		}
		var userUpdate = make(map[string]interface{})
		userUpdate["level_id"] = levelId
		var parentUser userModel.User
		err = source.DB().Where("id = ?", user.ParentId).First(&user).Error
		if err != nil {
			return
		}
		var parentID uint
		if parentUser.Username != data.IntroducerTel {

			var rd common.RequestData
			err, rd = common.Initial()
			if err != nil {
				return
			}
			err, parentID = service.CreateIntroducer(data.IntroducerTel, rd)
			if err != nil {
				return
			}

			userUpdate["parent_id"] = parentID

		}
		err = source.DB().Model(&userModel.User{}).Where("id = ?", user.ID).Updates(&userUpdate).Error
		if err != nil {
			return
		}
		err = source.DB().Create(&model.MemberOperationRecord{
			UserID:  user.ID,
			Type:    2,
			Content: "会员id:" + strconv.Itoa(int(user.ID)) + ";等级变更为：" + strconv.Itoa(int(levelId)) + ";推荐人变更为:" + strconv.Itoa(int(parentID)),
		}).Error
		if err != nil {
			log.Log().Info("会员监听修改记录添加失败：" + err.Error())
		}
		break
	}

	res["code"] = "SUCCESS"
	res["message"] = "成功"

	c.JSON(http.StatusOK, res)
	//订单 -- 订单这里暂时不需要回调 推送时直接通过推送状态回去是否成功
	//if callBack.OrderCode != "" {
	//	var orderItemId = strings.Split(callBack.OrderCode,"-")
	//	if len(orderItemId) != 2 {
	//		log.Log().Error("服务商系统-回调参数错误",zap.Any("user",callBack))
	//		yzResponse.FailWithMessage("服务商系统-回调参数错误", c)
	//		return
	//	}
	//	err = source.DB().Model(&model.PushOrderItem{}).Where("order_item_id = ?",orderItemId[1]).Updates(&model.PushOrderItem{
	//		Status: 1,
	//	}).Error
	//	if err != nil {
	//		log.Log().Error("订单推成功子订单记录失败",zap.Any("err",err),zap.Any("order",orderItemId))
	//		log.Log().Error("服务商系统-回调:修改子订单记录失败",zap.Any("err",err),zap.Any("user",callBack))
	//		yzResponse.FailWithMessage("服务商系统-回调:修改子订单记录失败", c)
	//		return
	//	}
	//	var pushOrderItems model.PushOrderItem
	//	err = source.DB().Model(&model.PushOrderItem{}).Where("order_sn = ?",orderItemId[0]).Where("status = 0").First(&pushOrderItems).Error
	//	if err != nil  {
	//		//如果没有待推送的就是全部推送
	//		if errors.Is(err, gorm.ErrRecordNotFound) {
	//			//全部推送
	//			err = source.DB().Model(&model.PushOrder{}).Where("order_sn = ?",orderItemId[0]).Updates(&model.PushOrder{
	//				Status: 2,
	//			}).Error
	//			if err != nil {
	//				log.Log().Error("服务商系统-回调订单推成功记录失败",zap.Any("err",err),zap.Any("order",callBack))
	//				yzResponse.FailWithMessage("服务商系统-回调订单推成功记录失败", c)
	//				return
	//			}else{
	//				yzResponse.Ok(c)
	//				return
	//			}
	//		}
	//	}
	//	err = source.DB().Model(&model.PushOrderItem{}).Where("order_sn = ?",orderItemId[0]).Where("status = 1").First(&pushOrderItems).Error
	//	if err != nil  {
	//		log.Log().Error("订单推成功查询记录失败-1",zap.Any("err",err),zap.Any("order",callBack))
	//		yzResponse.FailWithMessage("订单推成功查询记录失败-1", c)
	//		return
	//	}
	//	//部分推送
	//	err = source.DB().Model(&model.PushOrder{}).Where("order_sn = ?",orderItemId[0]).Updates(&model.PushOrder{
	//		Status: 1,
	//	}).Error
	//	if err != nil {
	//		log.Log().Error("订单部分推成功保存记录失败",zap.Any("err",err),zap.Any("order",callBack))
	//		yzResponse.FailWithMessage("订单部分推成功保存记录失败:"+ orderItemId[0] +""+err.Error(), c)
	//		return
	//	}
	//}
	return
}

package express

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/model"
	orderRequest "order/request"
	model2 "product/model"
	"shipping/address"
	"shipping/express"
	"strconv"
	"yz-go/component/log"
	yzGoModel "yz-go/model"
	"yz-go/source"
)

type OrderExpressItem struct {
	OrderExpressID uint
	OrderItemID    uint
}

// SaveOrderExpress
//
// @function: SaveOrderExpress
// @description: 填写物流信息
// @param: orderExpress *model.OrderExpress
// @param: OrderItemIDs []uint
// @return: err error
func SaveOrderExpress(OrderExpress *model.OrderExpress, OrderItemIDs []orderRequest.OrderItemSendInfo) (err error) {
	if len(OrderItemIDs) < 1 {
		return errors.New("发货商品不能为空")
	}
	// 配送信息
	// 顺丰需要补上手机号
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		var orderM Order
		err = source.DB().Preload("ShippingAddress").First(&orderM, OrderExpress.OrderID).Error
		if err != nil {
			return err
		}
		if orderM.Status == model.WaitReceive || orderM.SendStatus == model.Sent {
			return errors.New("订单已发货")

		}
		//if OrderExpress.CompanyCode == "SFEXPRESS" && !strings.Contains(OrderExpress.ExpressNo, ":") {
		//
		//	if len(orderM.ShippingAddress.Mobile) != 11 {
		//		return errors.New("收货人手机号格式不正确")
		//	}
		//	var numberStr = orderM.ShippingAddress.Mobile[7:]
		//	OrderExpress.ExpressNo = OrderExpress.ExpressNo + ":" + numberStr
		//}
		err = tx.Create(OrderExpress).Error
		if err != nil {
			return err
		}
		log.Log().Info("中台发货打印a"+strconv.Itoa(int(OrderExpress.OrderID)), zap.Any("data", OrderExpress))
		//保存orderItem和orderExpress关联表数据
		var ItemExpresses []model.ItemExpress
		for _, v := range OrderItemIDs {

			var sendNum uint
			if OrderExpress.IsEmpty == 0 {
				var orderItem OrderItem
				err = source.DB().First(&orderItem, v.ID).Error
				if err != nil {
					return err
				}
				if orderItem.SendStatus == 1 {
					//已发货跳过
					return errors.New("商品已发货,orderItemID为" + strconv.Itoa(int(v.ID)))
				}
				sendNum = v.Num
				var sendRecords []model.ItemExpress
				err = source.DB().Where("order_item_id = ?", v.ID).Find(&sendRecords).Error
				if err != nil {
					return err
				}
				for _, sendRecord := range sendRecords {
					//获取此item已经发货的数量(加上本次)
					v.Num += sendRecord.Num
				}
				//获取此item的总数量

				if v.Num == orderItem.Qty {
					// 记录订单商品发货状态
					err = tx.Model(&model.OrderItem{}).Where("id = ?", v.ID).Updates(model.OrderItem{OrderItemModel: model.OrderItemModel{SendStatus: 1}}).Error
					if err != nil {
						return err
					}
				} else if v.Num > orderItem.Qty {
					return errors.New("发货数量超过此商品的购买数量,orderItemID为" + strconv.Itoa(int(v.ID)))
				} else {
					if v.Num > 0 {
						// 记录订单商品发货状态
						err = tx.Model(&model.OrderItem{}).Where("id = ?", v.ID).Updates(model.OrderItem{OrderItemModel: model.OrderItemModel{SendStatus: 2}}).Error
						if err != nil {
							return err
						}
					}

				}
				ItemExpresses = append(ItemExpresses, model.ItemExpress{
					OrderItemID:    v.ID,
					Num:            sendNum,
					OrderExpressID: OrderExpress.ID,
				})
			} else {
				ItemExpresses = append(ItemExpresses, model.ItemExpress{
					OrderItemID:    v.ID,
					Num:            v.Num,
					OrderExpressID: OrderExpress.ID,
				})
			}

		}
		log.Log().Info("中台发货打印b"+strconv.Itoa(int(OrderExpress.OrderID)), zap.Any("data", OrderItemIDs))

		//空包裹 永源，直接全部发货
		if OrderExpress.IsEmpty == 1 {
			// 记录订单商品发货状态
			var orderExpressCount int64
			err = tx.Model(&model.OrderExpress{}).Where("order_id = ?", OrderExpress.OrderID).Count(&orderExpressCount).Error
			if err != nil {
				return err
			}
			if OrderExpress.FinalSend == 0 || int(orderExpressCount) == OrderExpress.FinalSend || OrderExpress.CanSend == 1 {
				err = tx.Model(&model.OrderItem{}).Where("order_id = ?", OrderExpress.OrderID).Updates(model.OrderItem{OrderItemModel: model.OrderItemModel{SendStatus: 1}}).Error
				if err != nil {
					return err
				}
			}

		}
		if len(ItemExpresses) > 0 {
			err = tx.Model(&model.ItemExpress{}).Create(&ItemExpresses).Error
		} else {
			return errors.New("itemExpress数量必须大于0")
		}

		if err != nil {
			return err
		}
		return nil
	})
	return err
}

// OrderCanSend
//
// @function: OrderCanSend
// @description: 订单可以发货
// @param: OrderID uint
// @return: ok bool,err error
func OrderCanSend(OrderID uint) (err error, ok bool) {
	var notSendCount int64
	err = source.DB().Model(&model.OrderItem{}).Where("order_id = ?", OrderID).Where("refund_status <> ? ", 3).Where("send_status <> ?", 1).Count(&notSendCount).Error
	if err != nil {
		return
	}
	if notSendCount == 0 {
		ok = true
	}
	return
}

type ExpressOrderItem struct {
	source.SoftDel
	ID         uint   `json:"id"`
	ImageUrl   string `json:"image_url"`
	ProductID  uint   `json:"product_id"`
	Title      string `json:"title"`
	SkuTitle   string `json:"sku_title"`
	SkuID      uint   `json:"sku_id"`
	Qty        uint   `json:"qty"`
	Amount     uint   `json:"-"`
	SendNum    uint   `json:"send_num"`
	SendStatus int    `json:"send_status"`
	SkuSn      string `json:"sku_sn" gorm:"-"`
}

type Sku struct {
	ID uint   `json:"id"`
	Sn string `json:"sn"`
}

func (eo ExpressOrderItem) TableName() string {
	return "order_items"
}

type OrderExpress struct {
	ID           uint              `json:"id"`
	OrderID      uint              `json:"-"`
	ThirdOrderSn string            `json:"third_order_sn"`
	ExpressNo    string            `json:"express_no"`
	CompanyCode  string            `json:"company_code"`
	CompanyName  string            `json:"company_name" gorm:"-"`
	SendMobile   string            `json:"send_mobile"`
	CreatedAt    *source.LocalTime `json:"created_at"`

	OrderItems  []ExpressOrderItem `json:"order_items" gorm:"many2many:item_expresses;references:ID;joinReferences:OrderItemID"`
	ItemExpress []ItemExpress      `json:"item_express" gorm:"foreignKey:OrderExpressID"`
	IsEmpty     int                `json:"is_empty"`
	UserID      uint               `json:"user_id" form:"user_id" gorm:"column:user_id;"` //发货操作人
	SysUser     yzGoModel.SysUser  `json:"sys_user" form:"sys_user" gorm:"foreignKey:UserID"`
}
type ItemExpress struct {
	ID             uint `json:"id" form:"id" gorm:"primarykey"`
	OrderItemID    uint `json:"order_item_id" form:"order_item_id" gorm:"index;"`
	OrderExpressID uint `json:"express_id" form:"express_id" gorm:"index;"`
	Num            uint `json:"num" form:"num"`
}

func (oe *OrderExpress) AfterFind(tx *gorm.DB) (err error) {
	err, oe.CompanyName = express.GetCompanyByCode(oe.CompanyCode)
	//调整原因 因为是查询后 匹配不上导致不返回信息了 例如订单详情
	if err != nil {
		oe.CompanyName = "未知快递"
		err = nil
	}
	return
}

type OrderItem struct {
	ID             uint   `json:"id"`
	OrderID        uint   `json:"-"`
	ImageUrl       string `json:"image_url"`
	ProductID      uint   `json:"product_id"`
	Title          string `json:"title"`
	SkuTitle       string `json:"sku_title"`
	Qty            uint   `json:"qty"`
	SendStatus     int    `json:"send_status"`
	SendStatusName string `json:"send_status_name" gorm:"-"`

	OrderExpress []OrderExpress `json:"order_express" gorm:"many2many:item_expresses;"`
	Product      model2.Product `json:"product"`
	CanSendNum   uint           `json:"can_send_num"`
}

func (oi *OrderItem) AfterFind(tx *gorm.DB) (err error) {
	oi.SendStatusName = model.GetItemSendStatusName(oi.SendStatus)
	return
}

type Order struct {
	SentCount    int64             `json:"sent_count"`
	NotSendCount int64             `json:"not_send_count"`
	ID           uint              `json:"id"`
	Status       model.OrderStatus `json:"-"`
	SendStatus   model.SendStatus  `json:"-"`
	ThirdOrderSN string            `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;index;"` // 采购端单号

	StatusName        string                  `json:"status_name"`
	SendStatusName    string                  `json:"send_status_name"`
	ShippingAddressID uint                    `json:"-"`
	OrderItems        []OrderItem             `json:"order_items"`
	ShippingAddress   address.ShippingAddress `json:"shipping_address"`
	CloudOrder        CloudOrder              `json:"cloud_order" gorm:"foreignKey:third_order_sn;references:cloud_order_sn"` //增加返回云仓记录 用于判断是否是云仓订单
}
type CloudOrder struct {
	ID           uint   `json:"id"`
	CloudOrderSn string `json:"cloud_order_sn"` //云仓订单号
	OrderId      string `json:"order_id"`
	CloudOrderId string `json:"cloud_order_id"` //云仓订单号
}

// 检查id 是否在数组中
func Contains(arr []OrderItem, id uint) bool {
	for _, v := range arr {
		if v.ID == id {
			return true
		}
	}
	return false
}
func GetOrderSendInfo(OrderID uint) (err error, order Order) {
	err = source.DB().Where("id = ?", OrderID).Preload("CloudOrder").Preload("OrderItems.OrderExpress").Preload("ShippingAddress").Preload("OrderItems.Product").First(&order).Error
	if err != nil {
		return
	}

	for key, item := range order.OrderItems {
		var itemExpress []ItemExpress
		err = source.DB().Where("order_item_id = ?", item.ID).Find(&itemExpress).Error
		if err != nil {
			return
		}
		for _, exp := range itemExpress {
			item.Qty -= exp.Num
		}
		//获取可以发货的数量
		order.OrderItems[key].CanSendNum = item.Qty

		if item.SendStatus == 1 {
			order.SentCount++
		} else {
			order.NotSendCount++
		}
	}
	order.StatusName = model.GetStatusName(order.Status)
	order.SendStatusName = model.GetSendStatusName(order.SendStatus)
	return
}

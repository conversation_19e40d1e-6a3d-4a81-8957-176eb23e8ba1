package listener

import (
	mq2 "notification/mq"
	"order/model"
	"order/mq"
	"yz-go/component/log"
	"yz-go/source"
)

func PushCustomerHandles() {

	log.Log().Info("监听order订单支付发送短信：")
	mq.PushHandles("orderPaidSms", func(data mq.OrderMessage) (err error) {

		if data.MessageType == mq.Paid {
			var order model.Order
			err = source.DB().First(&order, data.OrderID).Error
			if err != nil {
				return nil
			}
			err = mq2.PublishMessage(order.UserID, "newOrder", order.SupplierID)
			if err != nil {
				return nil
			}
		}

		return nil
	})
}

package listener

import (
	"order/mq"
	"order/order"
	"order/response"
	"order/service"
)

func PushUserLevelPurchaseHandles() {
	mq.PushHandles("UserLevelPurchaseByOrderPaid", func(orderMsg mq.OrderMessage) (err error) {
		if orderMsg.MessageType == mq.Paid {
			var supplyOrder response.Order
			err, supplyOrder = service.GetOrderByID(orderMsg.OrderID)
			if err != nil {
				return nil
			}
			var supplyID uint
			err, supplyID = service.GetSupplyID()
			if err != nil {
				return nil
			}
			if supplyID != supplyOrder.GatherSupplyID || len(supplyOrder.OrderItems) != 1 {
				return nil
			}
			err = order.Send(orderMsg.OrderID)
			if err != nil {
				return nil
			}
			err = order.Received(orderMsg.OrderID)
			if err != nil {
				return nil
			}
		}
		return nil
	})
}

package v1

import (
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"order/order"
	"order/request"
	"order/service"
	ufv1 "user/api/f/v1"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/utils"
)

type OrderList struct {
	Tags []Tag `json:"tags"`
	yzResponse.PageResult
	WaitPayNum       int64
	WaitSendNum      int64
	WaitReceiveNum   int64
	CompletedNum     int64
	ClosedNum        int64
	DouyinCpsDisplay int
	FuluDisplay      int
	LeaseDisplay     int
}
type Tag struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}
type Order struct {
	order.Order
	Operations []order.Operation `json:"operations"`
}

// FindOrder
// @Tags 订单
// @Summary 用id查询订单
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询订单"
// @Success 200 {object} order.Order
// @Router /api/order/get [post]
func FindOrder(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)
	userID := ufv1.GetUserID(c)
	err, info := order.GetOrder(userID, reqId.Id)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)

		return
	}
	err, operations := info.GetOperationList()
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	orderInfo := Order{
		Order:      info,
		Operations: operations,
	}
	yzResponse.OkWithData(gin.H{"order": orderInfo}, c)
}

// FindOrder
// @Tags 订单
// @Summary 用id查询订单
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询订单"
// @Success 200 {object} order.Order
// @Router /api/order/get [post]
func FindOrderItem(c *gin.Context) {
	var reqId yzRequest.GetByOrderItemId
	_ = c.ShouldBindQuery(&reqId)

	err, info := order.GetOrderItem(reqId.OrderItemId)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"order_item": info}, c)
}

type OrderInfo struct {
	order.OrderInfo
	Operations []order.Operation `json:"operations"`
	DetailUrl  string            `json:"detail_url"`
}

// GetOrderList
// @Tags 订单
// @Summary 分页获取订单列表
// @accept application/json
// @Produce application/json
// @Param data body request.OrderSearch true "分页获取订单列表"
// @Success 200 {object} OrderList
// @Router /api/order/list [post]
func GetOrderList(c *gin.Context) {
	var err error
	var search request.OrderSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)

	err, orderInfoList, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum := order.GetOrderInfoList(userID, search)
	var list []OrderInfo
	for _, info := range orderInfoList {
		err, operationList := info.GetOperationList()
		if err != nil {
			return
		}
		err, detailUrl := info.GetDetailUrl()
		if err != nil {
			return
		}
		list = append(list, OrderInfo{info, operationList, detailUrl})
	}
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	tags := []Tag{
		{
			"全部", utils.UnSafeUri(c.FullPath(), ""),
		}, {
			"待付款", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 0}),
		}, {
			"待发货", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 1}),
		}, {
			"待收货", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 2}),
		}, {
			"已完成", utils.UnSafeUri(c.FullPath(), map[string]int{"status": 3}),
		},
	}
	var douyinCpsDisplay int
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(14) == true || utils.LocalEnv() != true {
		douyinCpsDisplay = 1
	}
	var fuluDisplay int
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(5) == true || utils.LocalEnv() != true {
		fuluDisplay = 1
	}
	//租赁插件是否显示
	var leaseDisplay int
	if collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(21) == true || utils.LocalEnv() != true {
		leaseDisplay = 1
	}
	orderList := OrderList{
		Tags: tags,
		PageResult: yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     search.Page,
			PageSize: search.PageSize,
			//NextUrl:  nextUrl,
		},
		WaitPayNum:       WaitPayNum,
		WaitSendNum:      WaitSendNum,
		WaitReceiveNum:   WaitReceiveNum,
		CompletedNum:     CompletedNum,
		ClosedNum:        ClosedNum,
		DouyinCpsDisplay: douyinCpsDisplay,
		FuluDisplay:      fuluDisplay,
		LeaseDisplay:     leaseDisplay,
	}

	yzResponse.OkWithDetailed(orderList, "获取成功", c)
	return
}

type CloseOrderRequest struct {
	OrderID uint `json:"order_id" form:"order_id"`
}

// CloseOrder
// @Tags 订单
// @Summary 关闭
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body CloseOrderRequest true "关闭"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/order/close [post]
func CloseOrder(c *gin.Context) {
	var closeOrderRequest CloseOrderRequest
	err := c.ShouldBindQuery(&closeOrderRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	err, _ = order.GetOrder(userID, closeOrderRequest.OrderID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = order.Close(closeOrderRequest.OrderID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type BatchCloseOrderRequest struct {
	OrderIds []uint `json:"order_ids" form:"order_ids"`
}

func BatchCloseOrder(c *gin.Context) {
	var params BatchCloseOrderRequest

	if err := c.ShouldBindJSON(&params); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	// 检查订单是否存在
	if len(params.OrderIds) == 0 {
		yzResponse.FailWithMessage("参数错误", c)
		return
	}
	for _, orderId := range params.OrderIds {
		err, _ := order.GetOrder(userID, orderId)
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
	}
	// 关闭订单
	if err := order.BatchClose(params.OrderIds); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)

	return
}

// ReceiveOrder
// @Tags 订单
// @Summary 收货
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body ReceiveOrderRequest true "收货"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"更新成功"}"
// @Router /api/order/receive [post]
func ReceiveOrder(c *gin.Context) {
	var closeOrderRequest ReceiveOrderRequest
	err := c.ShouldBindQuery(&closeOrderRequest)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	err, _ = order.GetOrder(userID, closeOrderRequest.OrderID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err = order.Received(closeOrderRequest.OrderID); err != nil {
		log.Log().Error("操作失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("操作成功", c)
	return
}

type ReceiveOrderRequest struct {
	OrderID uint `json:"order_id" form:"order_id" `
}

// GetExpressInfo
// @Tags GetExpressInfo
// @Summary 获取配送信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/order/express [post]
func GetExpressInfo(c *gin.Context) {
	var reqId yzRequest.GetById
	_ = c.ShouldBindQuery(&reqId)

	if err, reshipping := service.GetExpressInfo(reqId.Id); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reshipping": reshipping}, c)
	}
}
func GetExpressSingleInfo(c *gin.Context) {
	var reqId request.OrderExpress
	_ = c.ShouldBindQuery(&reqId)

	if err, reshipping := service.GetExpressSingleInfo(reqId); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reshipping": reshipping}, c)
	}
}
func ExportOrderList(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	userID := ufv1.GetUserID(c)

	pageInfo.UserID = userID
	if err, link := service.ExportOrderFront(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}
func ExportOrderRecordList(c *gin.Context) {
	var pageInfo request.OrderExportRecordRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.UserID = ufv1.GetUserID(c)
	err, total, data := service.GetOrderExportRecordList(pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     data,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// FindOrder
// @Tags 订单
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Order true "用id查询"
// @Success 200 {object} model.Order
// @Router /order/get [post]
func GetOrderReportForms(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.UserID = ufv1.GetUserID(c)
	if err, data := service.GetOrderReportForms(pageInfo, 1); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"data": data}, c)
	}
}

// FindOrder
// @Tags 订单
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Order true "用id查询"
// @Success 200 {object} model.Order
// @Router /order/get [post]
func GetOrderReportFormsExport(c *gin.Context) {
	var pageInfo request.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.UserID = ufv1.GetUserID(c)

	if err, link := service.GetOrderReportFormsExport(pageInfo, 1); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

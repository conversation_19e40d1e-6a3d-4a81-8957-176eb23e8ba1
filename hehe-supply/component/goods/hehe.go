package goods

import (
	catemodel "category/model"
	"encoding/json"
	"errors"
	"fmt"
	gatherSupplyRequest "gather-supply/request"
	"github.com/chenhg5/collection"
	"github.com/xingliuhua/leaf"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"hehe-supply/common"
	"hehe-supply/model"
	response2 "hehe-supply/response"
	log2 "log"
	"math"
	"net/url"
	pmodel "product/model"
	pMq "product/mq"
	"product/service"
	callback2 "public-supply/callback"
	common2 "public-supply/common"
	publicModel "public-supply/model"
	"public-supply/request"
	service2 "public-supply/service"
	setting2 "public-supply/setting"
	"strconv"
	"strings"
	"sync"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Hehe struct {
}

func (self *Hehe) ManuallyProductUpdate(productID uint) (err error) {
	//TODO implement me
	panic("implement me")
}

func (self *Hehe) SynchronizeProductsToLocal() (err error) {
	//TODO implement me
	panic("implement me")
}

var wg sync.WaitGroup

func splitArray(arr []publicModel.Goods, num int64) [][]publicModel.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]publicModel.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]publicModel.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func (self *Hehe) ImportSelectGoodsRun(info publicModel.SelectGoods) (err error, list interface{}) {
	orderPN := GetOrderNo()
	common2.GlobalOrderSN = orderPN
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: len(info.List),
		Status:            1,
	}
	var resultInt []int
	var field string
	field = "source_goods_id"

	err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id=?", info.GatherSupplyID).Pluck(field, &resultInt).Error
	if err != nil {
		log2.Println("查询供应链商品id错误", err)
		return
	}

	if len(info.List) <= 0 {
		log2.Println("导入空数据")
		err = errors.New("导入的是空数据")
		return
	}

	var idsArr []int
	idsArr = GetIdArr(info.List)

	difference := collection.Collect(idsArr).Diff(resultInt).ToIntArray()
	repeat := len(idsArr) - len(difference)
	goodsRecord.RepeatQuantity = repeat //重复数据
	if len(difference) <= 0 {
		goodsRecord.Status = 2
		goodsRecord.CompletionStatus = 1
		source.DB().Omit("goods_arr").Create(&goodsRecord) //当前没有商品可以导入
		return
	}
	source.DB().Omit("goods_arr").Create(&goodsRecord) //创建导入记录
	var goodsList []publicModel.Goods
	for _, v := range difference {
		for _, item := range info.List {
			if item.ID == v {
				goodsList = append(goodsList, item)
			}
		}

	}
	info.List = goodsList
	arrList := splitArray(info.List, 20)
	for index, item := range arrList {
		wg.Add(1)
		fmt.Println("循环", index)
		self.RunSelectGoodsConcurrent(orderPN, item, info.Categorys, info.Key, info.GatherSupplyID)
	}
	wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	fmt.Println("全部完成：")

	return
}
func (self *Hehe) RunSelectGoodsConcurrent(orderPN string, list []publicModel.Goods, category string, key string, gatherSupplyID uint) (err error) {

	defer wg.Done()

	cateList := strings.Split(category, ",")
	var cateId1, cateId2, cateId3 int
	cateId1, err = strconv.Atoi(cateList[0])
	cateId2, err = strconv.Atoi(cateList[1])
	cateId3, err = strconv.Atoi(cateList[2])

	var listGoods []*pmodel.Product
	var recordError []publicModel.SupplyGoodsImportRecordErrors
	err, listGoods, recordError = self.CommodityAssembly(list, cateId1, cateId2, cateId3, gatherSupplyID, 0)
	if err != nil {
		return
	}
	if len(listGoods) > 0 {
		err = service2.FinalProcessing(listGoods, orderPN)
		if err != nil {
			return
		}

	}
	if len(recordError) > 0 {
		err = service2.FinalProcessingError(recordError, orderPN)
		if err != nil {
			return
		}
	}
	return

}
func (hehe *Hehe) ValidateConfig(params gatherSupplyRequest.ValidateConfigParams) (err error) {

	return
}

var heheData *model.HeheSupplySetting

var GatherSupplyID uint

func (*Hehe) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(setting.Value), &heheData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if heheData.BaseInfo.AppKey == "" || heheData.BaseInfo.AppSecret == "" || heheData.BaseInfo.Host == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	return
}

func (hehe *Hehe) GetGoods(info request.GetGoodsSearch) (err error, data interface{}, total int64, serverRatio int) {

	requestParam := GetGoodsRequest(info)

	var queryString []string
	for k, v := range requestParam {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	err, result := utils.Get(heheData.BaseInfo.Host+"/api/v1/goods/selection/all?"+query, nil)
	var response response2.HeheGoodsResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.ErrorCode != 0 {
		err = errors.New("和合商品获取失败")
		return
	}
	total = int64(response.Total)
	data = hehe.ProductToGoods(response.Data)
	return
}
func (hehe *Hehe) ProductToGoods(data []model.HeheProduct) (list []publicModel.Goods) {
	for _, v := range data {
		var isImport uint
		var supplyGoods pmodel.Product
		err := source.DB().Where("source_goods_id = ?", v.ID).Where("gather_supply_id = ?", GatherSupplyID).Where("deleted_at is null").First(&supplyGoods).Error
		if err == nil {
			isImport = 1
		}

		var thirdCategoryName []string
		var thirdCategoryIds []string
		for _, c := range v.CategoryInfo {
			thirdCategoryName = append(thirdCategoryName, c.CategoryName)
			thirdCategoryIds = append(thirdCategoryIds, strconv.Itoa(c.Id))
		}

		var status int
		if v.Status == 10 {
			status = 1
		} else {
			status = 0
		}
		var agreementPrice float64
		agreementPrice, err = strconv.ParseFloat(v.GoodsPrice, 64)
		if err != nil {
			continue
		}
		var marketPrice float64
		if v.MinLinePrice != "" {
			marketPrice, err = strconv.ParseFloat(v.MinLinePrice, 64)
			if err != nil {
				return
			}
		} else if v.MaxLinePrice != "" {
			marketPrice, err = strconv.ParseFloat(v.MaxLinePrice, 64)
			if err != nil {
				return
			}
		}
		var rate float64
		if marketPrice > agreementPrice {
			rate = utils.Decimal((marketPrice - agreementPrice) / marketPrice)
		}
		var item publicModel.Goods
		item = publicModel.Goods{
			GatherSupplyID:    GatherSupplyID,
			ThirdCategoryName: strings.Join(thirdCategoryName, ","),
			ID:                int(v.ID),
			ProductID:         int(v.ID),
			TotalStock:        v.GoodsCount,
			Cover:             v.Image.File.FilePath,
			Status:            status,
			Stock:             uint(v.GoodsCount),
			Title:             v.GoodsName,
			CategoryIds:       thirdCategoryIds,
			AgreementPrice:    uint(agreementPrice * 100),
			MarketPrice:       uint(marketPrice * 100),
			IsImport:          isImport,
			Rate:              math.Round(rate * 100),
		}
		if item.MarketPrice < item.AgreementPrice {
			item.Rate = 0
		}
		list = append(list, item)
	}
	return
}
func GetGoodsRequest(info request.GetGoodsSearch) (requestParam map[string]interface{}) {
	requestParam = make(map[string]interface{})
	if info.Page > 0 {
		requestParam["page"] = info.Page
	} else {
		requestParam["page"] = 1

	}
	if info.Limit > 0 {
		requestParam["limit"] = info.Limit
	} else {
		requestParam["limit"] = 24

	}
	if info.SearchWords != "" {
		requestParam["goods_name"] = url.QueryEscape(info.SearchWords)
	}
	if info.Category3ID > 0 {
		requestParam["cateId"] = info.Category3ID
	} else if info.Category2ID > 0 {
		requestParam["cateId"] = info.Category2ID
	} else if info.Category1ID > 0 {
		requestParam["cateId"] = info.Category1ID
	}
	one := 1
	if info.IsDisplay != nil {
		if *info.IsDisplay == one {
			requestParam["status"] = 20
		} else {
			requestParam["status"] = 10
		}

	}
	_, requestParam = common.GetRequestParams(requestParam, heheData)
	return
}
func (hehe *Hehe) DeleteGoods(id uint) (err error) {
	return
}
func (hehe *Hehe) Subscribes() (err error) {
	var productIds []uint
	err = source.DB().Model(&pmodel.Product{}).Where("is_display = ?", 1).Where("gather_supply_id = ?", GatherSupplyID).Pluck("source_goods_id", &productIds).Error
	if err != nil {
		return
	}
	var goodsIds []string

	for _, v := range productIds {
		goodsIds = append(goodsIds, strconv.Itoa(int(v)))
		if len(goodsIds) == 90 {
			var requestParam = make(map[string]interface{})
			requestParam["goods_ids"] = strings.Join(goodsIds, ",")
			requestParam["is_subscribe"] = 1
			_, requestParam = common.GetRequestParams(requestParam, heheData)
			var result []byte
			err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/goods/subscribes", requestParam, nil)
			log.Log().Info("heheScribe", zap.Any("data", string(result)))
			if err != nil {
				continue
			}
			goodsIds = []string{}
		}
	}
	var requestParam = make(map[string]interface{})
	requestParam["goods_ids"] = strings.Join(goodsIds, ",")
	requestParam["is_subscribe"] = 1
	_, requestParam = common.GetRequestParams(requestParam, heheData)
	var result []byte
	err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/goods/subscribes", requestParam, nil)
	log.Log().Info("heheScribe", zap.Any("data", string(result)))
	if err != nil {
		return
	}
	return
}

func (hehe *Hehe) CronUpdateProduct() (err error) {
	var productIds []uint
	err = source.DB().Model(&pmodel.Product{}).Where("gather_supply_id = ?", GatherSupplyID).Pluck("source_goods_id", &productIds).Error
	if err != nil {
		return
	}

	for _, v := range productIds {

		err = hehe.GoodsUpdateHandle(int(v))
		if err != nil {
			fmt.Println(err.Error())
		}
	}

	return
}
func (hehe *Hehe) ImportGoodsRun(info request.GetGoodsSearch) (err error, data interface{}) {

	info.Limit = 50
	requestParam := GetGoodsRequest(info)

	var queryString []string
	for k, v := range requestParam {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	err, result := utils.Get(heheData.BaseInfo.Host+"/api/v1/goods/selection/all?"+query, nil)
	var response response2.HeheGoodsResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.ErrorCode != 0 {
		err = errors.New("和合商品获取失败")
		return
	}
	var count = float64(response.Total)
	var limit = float64(50)
	var forCount = count / limit
	var counts = int(math.Ceil(forCount))

	orderPN := GetOrderNo()
	goodsRecord := publicModel.SupplyGoodsImportRecord{
		SysUserId:         info.SysUserID,
		Batch:             orderPN,
		EstimatedQuantity: int(count),
		Status:            1,
		SearchCriteria:    query,
	}

	source.DB().Omit("goods_arr").Create(&goodsRecord)
	//var wg sync.WaitGroup
	for i := 1; i <= counts; i++ {
		//wg.Add(1)
		//if i > 100 {
		//	break
		//}
		//if i%20 == 0 {
		//	time.Sleep(time.Second * 1)
		//}
		err = hehe.RunGoodsConcurrent(nil, info, i, requestParam, orderPN)
		if err != nil {
			continue
		}
	}

	//wg.Wait()
	err = SetImportRecordCompletion(orderPN)
	if err != nil {
		fmt.Println("变更导入记录状态错误", err)

	}
	fmt.Println("导入供应链商品全部完成")
	return
}

func (hehe *Hehe) RunGoodsConcurrent(wg *sync.WaitGroup, info request.GetGoodsSearch, i int, search map[string]interface{}, orderPN string) (err error) {

	//defer wg.Done()

	info.Page = i
	requestParam := GetGoodsRequest(info)

	var queryString []string
	for k, v := range requestParam {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")
	err, result := utils.Get(heheData.BaseInfo.Host+"/api/v1/goods/selection/all?"+query, nil)
	var response response2.HeheGoodsResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.ErrorCode != 0 {
		err = errors.New("获取失败")
		return
	}
	if len(response.Data) > 0 {

		cateList := strings.Split(info.Categorys, ",")
		var cateId1, cateId2, cateId3 int
		if len(cateList) >= 2 {
			cateId1, err = strconv.Atoi(cateList[0])
			cateId2, err = strconv.Atoi(cateList[1])
			cateId3, err = strconv.Atoi(cateList[2])
		}

		var resultArr []int
		err = source.DB().Model(pmodel.Product{}).Where("gather_supply_id = ?", info.GatherSupplyID).Pluck("source_goods_id", &resultArr).Error
		if err != nil {
			return
		}

		Item := hehe.ProductToGoods(response.Data)
		idsArr := GetIdArr(Item)

		difference := collection.Collect(idsArr).Diff(resultArr).ToIntArray()

		err = SetImportRepeat(orderPN, len(idsArr)-len(difference))

		if len(difference) <= 0 {
			err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", orderPN).Update("status", 2).Error

			err = errors.New("当前数据已经存在，请导入新数据")
			return
		}
		var goodsList []publicModel.Goods

		for _, v := range difference {

			for _, item := range Item {
				if int(item.ID) == v {
					goodsList = append(goodsList, item)
				}
			}

		}

		var listGoods []*pmodel.Product

		err, listGoods, _ = hehe.CommodityAssembly(goodsList, cateId1, cateId2, cateId3, info.GatherSupplyID, 0)

		if len(listGoods) > 0 {
			service2.FinalProcessing(listGoods, orderPN)
		}

	} else {
		fmt.Println("获取失败的：", i, info.Limit, info.Source)
	}

	return
}

func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func CreateGoods(goodsList []*pmodel.Product) (err error) {
	err = source.DB().CreateInBatches(&goodsList, 2000).Error
	if err != nil {
		return
	}
	var SupplyGoods []publicModel.SupplyGoods
	for _, goods := range goodsList {

		SupplyGoods = append(SupplyGoods, publicModel.SupplyGoods{
			SupplyGoodsID:  goods.SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		})

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)
		if err != nil {
			return
		}

	}
	err = source.DB().CreateInBatches(SupplyGoods, 2000).Error
	return
}
func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}
func GetIdArr(list []publicModel.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}

func (hehe *Hehe) GetCategory(info request.GetCategorySearch) (err error, data interface{}) {
	var categoryCount int64
	err = source.DB().Model(&model.HeheCategory{}).Count(&categoryCount).Error
	if err != nil {
		return
	}
	if categoryCount > 0 {
		return
	}
	err, requestParam := common.GetRequestParams(make(map[string]interface{}), heheData)
	if err != nil {
		return
	}
	var queryString []string
	for k, v := range requestParam {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	err, result := utils.Get(heheData.BaseInfo.Host+"/api/v1/category/list?"+query, nil)
	var response response2.HeheCategoryResponse
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}
	//var heheCategorys []model.HeheCategory
	//for _, v := range response.Data {
	//	heheCategorys = append(heheCategorys, v)
	//	for _, c := range v.Children {
	//		heheCategorys = append(heheCategorys, c)
	//		for _, sc := range c.Children {
	//			heheCategorys = append(heheCategorys, sc)
	//		}
	//	}
	//}
	err = source.DB().CreateInBatches(&response.Data, 1000).Error
	return
}
func (hehe *Hehe) GetGroup() (err error, data interface{}) {
	return
}
func (hehe *Hehe) GetCategoryChild(pid int, info request.GetCategoryChild) (err error, data interface{}) {
	err, _ = hehe.GetCategory(request.GetCategorySearch{})
	if err != nil {
		return
	}
	var categorys []model.HeheCategory
	err = source.DB().Where("pid = ?", pid).Find(&categorys).Error
	return err, categorys
}
func (hehe *Hehe) RunConcurrent(wg *sync.WaitGroup, info request.GetCategorySearch, i int) (err error) {
	return
}
func (hehe *Hehe) CommodityAssembly(list []publicModel.Goods, cateId1, cateId2, cateId3 int, gatherSupplyID uint, isCreate int) (err error, listGoods []*pmodel.Product, recordErrors []publicModel.SupplyGoodsImportRecordErrors) {

	idArr := GetIdArr(list)
	listArr := SplitArray(idArr, 100)
	var detailData = make(map[uint]model.HeheGoodsDetail)
	for index, item := range listArr {
		fmt.Println("循环分割id第", index, "次:", item)
		//stringIds := service.GetArrIds(item)
		fmt.Println("返回ids", item)
		var data map[uint]model.HeheGoodsDetail

		err, data = hehe.BatchGetGoodsDetails(gatherSupplyID, item)
		for detailIndex, detailItem := range data {
			detailData[detailIndex] = detailItem

		}
	}

	//_, detailData := BatchGetGoodsDetails(ids) //批量获取详情
	var riskManageRecord []publicModel.RiskManagementRecord

	for _, elem := range list {
		var isRisk int
		detail := detailData[uint(elem.ID)]

		goods := new(pmodel.Product)
		var brand = new(catemodel.Brand)
		if elem.ThirdBrandName != "" {
			brand.Name = elem.ThirdBrandName
			brand.Source = elem.Source
			err = source.DB().Where(brand).FirstOrCreate(&brand).Error
			goods.BrandID = brand.ID
		}
		var costPrice, salePrice, activityPrice, guidePrice, originPrice uint

		elem.Source = common2.Hehe_SOURCE
		var priceFloat64 float64
		if detail.MinGoodsPrice != "" {
			priceFloat64, err = strconv.ParseFloat(detail.MinGoodsPrice, 64)
			if err != nil {
				return
			}
		} else if detail.MaxGoodsPrice != "" {
			priceFloat64, err = strconv.ParseFloat(detail.MaxGoodsPrice, 64)
			if err != nil {
				return
			}
		}

		var linePriceFloat64 float64
		if detail.MinLinePrice != "" {
			linePriceFloat64, err = strconv.ParseFloat(detail.MinLinePrice, 64)
			if err != nil {
				return
			}
		} else if detail.MaxLinePrice != "" {
			linePriceFloat64, err = strconv.ParseFloat(detail.MaxLinePrice, 64)
			if err != nil {
				return
			}
		}

		elem.AgreementPrice = uint(priceFloat64 * 100)
		elem.LinePrice = uint(linePriceFloat64 * 100)
		err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(elem, heheData)

		goods.Title = elem.Title
		goods.OriginPrice = originPrice
		goods.Price = salePrice
		goods.CostPrice = costPrice
		goods.ActivityPrice = activityPrice
		goods.GuidePrice = guidePrice
		goods.IsDisplay = elem.Status
		goods.IsSingleOrder = 1

		if heheData.Management.ProductPriceStatus == 1 {
			if float64(goods.Price) < float64(goods.CostPrice)*float64(heheData.Management.Products)/100 {
				goods.IsDisplay = 0
				isRisk++
			}
		} else if heheData.Management.ProductPriceStatus == 2 {
			if (float64(goods.Price)-float64(goods.CostPrice))/float64(goods.CostPrice) < float64(heheData.Management.Profit)/100 {
				goods.IsDisplay = 0
				isRisk++
			}
		}

		goods.ImageUrl = elem.Cover
		goods.SourceGoodsID = uint(elem.ID)
		goods.Source = elem.Source

		cateList := strings.Split(elem.ThirdCategoryName, ",")
		if cateId1 == 0 || cateId2 == 0 || cateId3 == 0 {
			display := 1
			if len(cateList) < 3 {
				continue
			}
			var resCategory1 catemodel.Category
			resCategory1.Name = cateList[0]
			resCategory1.Level = 1
			resCategory1.ParentID = 0
			resCategory1.IsDisplay = &display
			err = source.DB().Where("name = ? and level = ? and parent_id = ?", cateList[0], 1, 0).FirstOrCreate(&resCategory1).Error
			if err != nil {
				return
			}
			var resCategory2 catemodel.Category
			resCategory2.Name = cateList[1]
			resCategory2.Level = 2
			resCategory2.ParentID = resCategory1.ID
			resCategory2.IsDisplay = &display
			err = source.DB().Where("name = ? and level = ? and parent_id = ?", cateList[1], 2, resCategory2.ParentID).FirstOrCreate(&resCategory2).Error
			if err != nil {
				return
			}
			var resCategory3 catemodel.Category
			resCategory3.Name = cateList[2]
			resCategory3.Level = 3
			resCategory3.ParentID = resCategory2.ID
			resCategory3.IsDisplay = &display
			err = source.DB().Where("name = ? and level = ? and parent_id = ?", cateList[2], 3, resCategory3.ParentID).FirstOrCreate(&resCategory3).Error
			if err != nil {
				return
			}
			goods.Category1ID = resCategory1.ID
			goods.Category2ID = resCategory2.ID
			goods.Category3ID = resCategory3.ID
		} else {
			goods.Category1ID = uint(cateId1)
			goods.Category2ID = uint(cateId2)
			goods.Category3ID = uint(cateId3)

		}

		goods.FreightType = 2
		goods.GatherSupplyID = elem.GatherSupplyID

		/**
		处理轮播图
		*/

		if len(detail.Images) > 0 {
			for _, image := range detail.Images {
				goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
					Type: 1,
					Src:  image.File.FilePath,
				})
			}
		}

		/**
		处理轮播图结束
		*/
		var maxPrice uint
		var minPrice uint
		var minProfitRate float64
		for sk, v := range detail.SkuList {
			var sku pmodel.Sku
			sku.Sn = v.SpecSkuId
			var skuPriceFloat64 float64
			skuPriceFloat64, err = strconv.ParseFloat(v.GoodsPrice, 64)
			if err != nil {
				return
			}

			var skuLinePriceFloat64 float64
			skuLinePriceFloat64, err = strconv.ParseFloat(v.LinePrice, 64)
			if err != nil {
				return
			}
			err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(publicModel.Goods{
				AgreementPrice: uint(skuPriceFloat64 * 100),
				LinePrice:      uint(skuLinePriceFloat64 * 100),
				Source:         common2.Hehe_SOURCE,
			}, heheData)

			sku.CostPrice = costPrice
			sku.Price = salePrice
			sku.OriginPrice = originPrice
			sku.GuidePrice = guidePrice
			sku.ActivityPrice = activityPrice
			sku.OriginalSkuID = int64(v.GoodsSpecId)
			sku.ID = 0
			sku.Stock = v.StockNum
			if heheData.Management.ProductPriceStatus == 1 {
				if float64(sku.Price) < float64(sku.CostPrice)*float64(heheData.Management.Products)/100 {
					goods.IsDisplay = 0
					isRisk++
				}
			} else if heheData.Management.ProductPriceStatus == 2 {
				if (float64(sku.Price)-float64(sku.CostPrice))/float64(sku.CostPrice) < float64(heheData.Management.Profit)/100 {
					goods.IsDisplay = 0
					isRisk++
				}
			}
			var skuTitle []string
			for _, option := range v.GoodsProps {
				sku.Options = append(sku.Options, pmodel.Option{
					SpecName:     option.Group.Name,
					SpecItemName: option.Value.Name,
				})
				skuTitle = append(skuTitle, option.Value.Name)
			}
			sku.Title = strings.Join(skuTitle, "+")
			if sku.Price > maxPrice {
				maxPrice = sku.Price
			}
			if minPrice == 0 || sku.Price <= minPrice {
				minPrice = sku.Price
			}
			if sku.GuidePrice > 0 {
				sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
			} else {
				sku.ProfitRate = 0
			}
			if sk == 0 {
				minProfitRate = sku.ProfitRate
			}
			if sku.ProfitRate <= minProfitRate {
				minProfitRate = sku.ProfitRate
			}
			goods.Skus = append(goods.Skus, sku)
			goods.Stock += uint(v.StockNum)
		}
		goods.MaxPrice = maxPrice
		goods.MinPrice = minPrice
		if len(goods.Skus) > 0 {
			goods.ProfitRate = minProfitRate
		}
		//处理资质json图片数组

		goods.DetailImages = detail.Content
		//--------处理详情json图片数组结束

		//处----------------理属性json数组
		//---------处理属性json数组结束
		//goods.Desc=detail.Description
		if isRisk > 0 {
			var riskRecord publicModel.RiskManagementRecord
			riskRecord.ProductID = goods.ID
			riskRecord.SourceGoodsID = goods.SourceGoodsID
			riskRecord.GatherSupplyID = goods.GatherSupplyID
			riskManageRecord = append(riskManageRecord, riskRecord)
		}

		if len(goods.Skus) > 0 {
			listGoods = append(listGoods, goods)
		} else {
			fmt.Println("无规格商品，不导入", goods.ID)
		}

	}
	err = source.DB().CreateInBatches(&riskManageRecord, 1000).Error

	return
}
func GetPricingPrice(elem publicModel.Goods, dat *model.HeheSupplySetting) (err error, costPrice, salePrice, activityPrice, guidePrice, originPrice uint) {

	var intX uint64
	data := dat
	if data.Pricing.Strategy == 1 { //本地定价策略关闭

		if elem.Source == common2.Hehe_SOURCE {

			if data.Pricing.HeheSales == 1 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100

			}
			if data.Pricing.HeheSales == 2 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheSalesLine, 10, 32)
				salePrice = elem.LinePrice * uint(intX) / 100

			}

			if data.Pricing.HeheCost == 1 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100

			}
			if data.Pricing.HeheCost == 2 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheCostLine, 10, 32)
				costPrice = elem.LinePrice * uint(intX) / 100

			}

			if data.Pricing.HeheGuide == 1 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100

			}
			if data.Pricing.HeheGuide == 2 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheGuideLine, 10, 32)
				guidePrice = elem.LinePrice * uint(intX) / 100

			}

			if data.Pricing.HeheActivity == 1 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheActivityAgreement, 10, 32)
				activityPrice = elem.AgreementPrice * uint(intX) / 100

			}
			if data.Pricing.HeheActivity == 2 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheActivityLine, 10, 32)
				activityPrice = elem.LinePrice * uint(intX) / 100

			}

			if data.Pricing.HeheAdvice == 1 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheAdviceAgreement, 10, 32)
				originPrice = elem.AgreementPrice * uint(intX) / 100

			}
			if data.Pricing.HeheAdvice == 2 {
				//销售价计算
				intX, err = strconv.ParseUint(data.Pricing.HeheAdviceLine, 10, 32)
				originPrice = elem.LinePrice * uint(intX) / 100

			}

		}

	} else {
		salePrice = elem.AgreementPrice
		costPrice = elem.AgreementPrice
		originPrice = elem.LinePrice
		activityPrice = elem.LinePrice
		guidePrice = elem.LinePrice
	}

	return

}

func (hehe *Hehe) BatchGetGoodsDetails(gatherSupplyID uint, ids []int) (err error, data map[uint]model.HeheGoodsDetail) {

	var detailList = make(map[uint]model.HeheGoodsDetail)
	for _, id := range ids {
		var search = make(map[string]interface{})
		search["id"] = id
		err, search = common.GetRequestParams(search, heheData)
		var queryString []string
		for k, v := range search {
			queryString = append(queryString, k+"="+source.Strval(v))
		}
		query := strings.Join(queryString, "&")
		var result []byte
		err, result = utils.Get(heheData.BaseInfo.Host+"/api/v1/goods/detail?"+query, nil)
		var response response2.HeheGoodsDetailConfirm
		err = json.Unmarshal(result, &response)
		if err != nil {
			return
		}

		if response.ErrorCode != 0 {
			err = errors.New(response.ErrorInfo)
			return
		} else {
			var responseData response2.HeheGoodsDetailResponse
			err = json.Unmarshal(result, &responseData)
			if err != nil {
				return
			}
			detailList[responseData.Data.ID] = responseData.Data
			fmt.Println("总解析数量1：", len(detailList))
		}

	}

	data = detailList

	return
}
func (hehe *Hehe) GoodsIsDisplayHandle(id int, isDisplay int) (err error) {
	var gatherSupplyIds []uint
	err = source.DB().Model(&publicModel.GatherSupply{}).Where("category_id = ?", common2.SUPPLY_HEHE).Pluck("id", &gatherSupplyIds).Error
	if err != nil || len(gatherSupplyIds) == 0 {
		return
	}
	var goods service.ProductForUpdate
	err = source.DB().Where("source_goods_id = ?", id).Where("gather_supply_id in ?", gatherSupplyIds).First(&goods).Error
	if err != nil {
		return
	}
	err = source.DB().Model(service.ProductForUpdate{}).Where("id = ?", goods.ID).Update("is_display", isDisplay).Error
	if err != nil {
		return
	}
	if isDisplay == 1 {
		err = pMq.PublishMessage(goods.ID, pMq.OnSale, 0)
	} else {
		err = pMq.PublishMessage(goods.ID, pMq.Undercarriage, 0)
	}
	if err != nil {
		return
	}
	return
}
func (hehe *Hehe) GoodsUpdateHandle(id int) (err error) {
	var gatherSupplyIds []uint
	err = source.DB().Model(&publicModel.GatherSupply{}).Where("category_id = ?", common2.SUPPLY_HEHE).Pluck("id", &gatherSupplyIds).Error
	if err != nil {
		return
	}
	var goods service.ProductForUpdate
	err = source.DB().Preload("Skus").Where("source_goods_id = ?", id).Where("gather_supply_id in ?", gatherSupplyIds).First(&goods).Error
	if err != nil {
		return
	}
	err = hehe.InitSetting(goods.GatherSupplyID)
	if err != nil {
		return
	}
	var data map[uint]model.HeheGoodsDetail
	item := []int{id}
	err, data = hehe.BatchGetGoodsDetails(GatherSupplyID, item)
	if err != nil {
		if err.Error() == "商品已下架或者删除" {
			log.Log().Info("和合商品更新错误", zap.Any("id", id), zap.Any("err", err))
			goods.IsDisplay = 0
			err = service.UpdateProduct(goods)
		}
		return
	}
	detail := data[uint(id)]
	var costPrice, salePrice, activityPrice, guidePrice, originPrice uint
	var elem publicModel.Goods
	elem.Source = common2.Hehe_SOURCE
	var priceFloat64 float64
	if detail.MinGoodsPrice != "" {
		priceFloat64, err = strconv.ParseFloat(detail.MinGoodsPrice, 64)
		if err != nil {
			return
		}
	} else if detail.MaxGoodsPrice != "" {
		priceFloat64, err = strconv.ParseFloat(detail.MaxGoodsPrice, 64)
		if err != nil {
			return
		}
	}

	var linePriceFloat64 float64
	if detail.MinLinePrice != "" {
		linePriceFloat64, err = strconv.ParseFloat(detail.MinLinePrice, 64)
		if err != nil {
			return
		}
	} else if detail.MaxLinePrice != "" {
		linePriceFloat64, err = strconv.ParseFloat(detail.MaxLinePrice, 64)
		if err != nil {
			return
		}
	}

	elem.AgreementPrice = uint(priceFloat64 * 100)
	elem.LinePrice = uint(linePriceFloat64 * 100)

	err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(elem, heheData)

	/**
	处理轮播图结束
	*/
	if heheData.UpdateInfo.CostPrice == 1 {
		goods.CostPrice = costPrice

	}
	if heheData.UpdateInfo.CurrentPrice == 1 {
		goods.Price = salePrice

	}
	goods.GuidePrice = guidePrice
	goods.ActivityPrice = activityPrice
	goods.OriginPrice = originPrice
	goods.Stock = 0
	var status int
	if detail.Status == 10 {
		status = 1
	} else if detail.Status == 20 {
		status = 0
	}
	if goods.StatusLock == 0 {
		goods.IsDisplay = status
	}
	var minPrice uint
	var maxPrice uint
	var riskManage int
	var minProfitRate float64
	var skus []service.Sku
	for sk, v := range detail.SkuList {
		var sku service.Sku
		sku.ID = 0
		for _, gsku := range goods.Skus {
			if v.GoodsSpecId == gsku.OriginalSkuID {
				sku.ID = gsku.ID
			}
		}
		var skuPriceFloat64 float64
		skuPriceFloat64, err = strconv.ParseFloat(v.GoodsPrice, 64)
		if err != nil {
			return
		}

		var skuLinePriceFloat64 float64
		skuLinePriceFloat64, err = strconv.ParseFloat(v.LinePrice, 64)
		if err != nil {
			return
		}
		err, costPrice, salePrice, activityPrice, guidePrice, originPrice = GetPricingPrice(publicModel.Goods{
			AgreementPrice: uint(skuPriceFloat64 * 100),
			LinePrice:      uint(skuLinePriceFloat64 * 100),
			Source:         common2.Hehe_SOURCE,
		}, heheData)

		if heheData.UpdateInfo.CostPrice == 1 {
			sku.CostPrice = costPrice
		}
		if heheData.UpdateInfo.CurrentPrice == 1 {
			sku.Price = salePrice
		}
		sku.OriginPrice = originPrice
		sku.GuidePrice = guidePrice
		sku.ActivityPrice = activityPrice
		sku.OriginalSkuID = v.GoodsSpecId
		sku.Sn = v.SpecSkuId
		sku.Stock = v.StockNum

		if heheData.Management.ProductPriceStatus == 1 {
			if float64(sku.Price) < float64(sku.CostPrice)*float64(heheData.Management.Products)/100 {
				goods.IsDisplay = 0
				riskManage++
			}
		} else if heheData.Management.ProductPriceStatus == 2 {
			if (float64(sku.Price)-float64(sku.CostPrice))/float64(sku.CostPrice) < float64(heheData.Management.Profit)/100 {
				goods.IsDisplay = 0
				riskManage++
			}
		}
		var skuTitle []string
		for _, option := range v.GoodsProps {
			sku.Options = append(sku.Options, pmodel.Option{
				SpecName:     option.Group.Name,
				SpecItemName: option.Value.Name,
			})
			skuTitle = append(skuTitle, option.Value.Name)
		}
		sku.Title = strings.Join(skuTitle, "+")
		if sku.Price > maxPrice {
			maxPrice = sku.Price
		}
		if minPrice == 0 || sku.Price <= minPrice {
			minPrice = sku.Price
		}
		if sku.GuidePrice > 0 {
			sku.ProfitRate = utils.ExecProfitRate(sku.OriginPrice, sku.Price)
		} else {
			sku.ProfitRate = 0
		}
		if sk == 0 {
			minProfitRate = sku.ProfitRate
		}
		if sku.ProfitRate <= minProfitRate {
			minProfitRate = sku.ProfitRate
		}
		goods.Stock += uint(v.StockNum)
		skus = append(skus, sku)

	}
	if heheData.UpdateInfo.BaseInfo == 1 {
		if len(detail.Images) > 0 {
			goods.Gallery = pmodel.Gallery{}
			for _, image := range detail.Images {
				goods.Gallery = append(goods.Gallery, pmodel.GalleryItem{
					Type: 1,
					Src:  image.File.FilePath,
				})
			}
		}
		goods.DetailImages = detail.Content
		goods.Title = detail.GoodsName
	}

	goods.Skus = skus

	goods.MinPrice = minPrice
	goods.MaxPrice = maxPrice
	if len(goods.Skus) > 0 {
		goods.ProfitRate = minProfitRate
	}
	//处理资质json图片数组
	if riskManage > 0 {
		var riskRecord publicModel.RiskManagementRecord
		riskRecord.ProductID = goods.ID
		riskRecord.SourceGoodsID = goods.SourceGoodsID
		riskRecord.GatherSupplyID = goods.GatherSupplyID
		err = source.DB().Create(&riskManage).Error
	}

	err = service.UpdateProduct(goods)
	return
}
func (hehe *Hehe) GoodsStorageAdd(ids []int, supplyId uint) (err error, info interface{}) {
	return
}
func (hehe *Hehe) GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {
	return
}
func (hehe *Hehe) InitGoods() (err error) {
	return
}
func (hehe *Hehe) GetSupplyBalance(GatherSupplyID uint) (err error, balance interface{}) {
	return
}
func SplitArray(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

func InitCategory(list []publicModel.Goods, level uint, parentMap map[string]uint, pid uint) (err error, cateMap map[string]uint) {
	var cateData []catemodel.Category
	var pids []uint
	if pid > 0 {
		pids = append(pids, pid)
	}
	cateMap = make(map[string]uint)
	var cateName []string
	display := 1
	key := level - 1
	for _, ce := range list {
		var cateItem catemodel.Category
		cateList := strings.Split(ce.ThirdCategoryName, ",")
		if len(cateList) > int(key) && cateList[key] != "" {
			var repetCheck = 0
			for _, cd := range cateData {
				if cd.Name == cateList[key] {
					repetCheck++
				}
			}
			if repetCheck > 0 {
				continue //数组中已经存在此名称，直接跳过
			}
			cateItem.IsDisplay = &display
			if level == 1 {
				cateItem.ParentID = 0 //一级分类父id必须是0
			} else {
				if pid > 0 {
					cateItem.ParentID = pid //直接用导入时设置的分类
				} else {
					if _, ok := parentMap[cateList[key-1]]; ok {
						//此为存在
						cateItem.ParentID = parentMap[cateList[key-1]] //利用父级分类的name，从上级分类map中获取父id
						pids = append(pids, cateItem.ParentID)
					} else {
						continue
					}
				}
			}
			cateItem.Level = int(level)
			cateItem.Name = cateList[key]
			cateData = append(cateData, cateItem)
			cateName = append(cateName, cateItem.Name)
		}
	}
	var exitsCate []catemodel.Category
	err = source.DB().Where("name in ?", cateName).Where("level = ?", level).Where("parent_id in ?", pids).Find(&exitsCate).Error
	if err != nil {
		return
	}
	var saveCateData []catemodel.Category
	for _, cdCheck := range cateData {
		var isCheck = 0
		for _, ecCheck := range exitsCate {
			if ecCheck.Name == cdCheck.Name {
				isCheck++
			}
		}
		if isCheck == 0 {
			saveCateData = append(saveCateData, cdCheck) //重新生成一个数据库中不存在的数据的数组
		}
	}
	err = source.DB().CreateInBatches(saveCateData, 2000).Error
	if err != nil {
		return
	}
	saveCateData = append(saveCateData, exitsCate...) //合并新增的和已经有的
	for _, cd := range saveCateData {
		cateMap[cd.Name] = cd.ID
	}
	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(publicModel.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}

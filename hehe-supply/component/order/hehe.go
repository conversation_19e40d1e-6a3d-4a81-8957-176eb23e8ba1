package order

import (
	afterSalesModel "after-sales/model"
	request3 "after-sales/request"
	"after-sales/service"
	"crypto/rand"
	"encoding/json"
	"errors"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"hehe-supply/model"
	response2 "hehe-supply/response"
	"math/big"
	v1 "order/api/v1"
	model5 "order/model"
	request2 "order/request"
	model4 "product/model"
	callback2 "public-supply/callback"
	model6 "public-supply/model"
	"public-supply/request"
	"public-supply/response"
	setting2 "public-supply/setting"
	model3 "region/model"
	"shipping/express"
	"sort"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Hehe struct{}

func (hehe *Hehe) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (hehe *Hehe) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (hehe *Hehe) GetAllAddress() (err error, data interface{}) {
	return
}

var heheData *model.HeheSupplySetting

var GatherSupplyID uint

func (*Hehe) InitSetting(gatherSupplyID uint) (err error) {
	var setting model2.SysSetting
	err, setting = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		return
	}
	GatherSupplyID = gatherSupplyID
	err = json.Unmarshal([]byte(setting.Value), &heheData)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if heheData.BaseInfo.AppKey == "" || heheData.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	return
}
func (hehe *Hehe) GetRequestParams(params map[string]interface{}) (err error, result map[string]interface{}) {

	nonce, err := hehe.RandomString(32)
	if err != nil {
		return
	}
	appKey := heheData.BaseInfo.AppKey
	appSecret := heheData.BaseInfo.AppSecret
	params["appid"] = appKey
	params["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
	params["nonce_str"] = nonce
	var signature string
	//按照字母顺序遍历
	traverseMapInStringOrder(params, func(key string, value interface{}) {
		signature += key + "=" + source.Strval(value) + "&"
	})
	signature += "key=" + appSecret
	signature = utils.MD5V([]byte(signature))
	signature = strings.ToUpper(signature)
	params["sign"] = signature

	return err, params
}

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func (hehe *Hehe) RandomString(length int) (string, error) {
	result := make([]byte, length)
	charsetLength := big.NewInt(int64(len(charset)))

	for i := range result {
		// 生成一个随机索引
		num, err := rand.Int(rand.Reader, charsetLength)
		if err != nil {
			return "", err
		}
		result[i] = charset[num.Int64()]
	}

	return string(result), nil
}

type MapEntryHandler func(string, interface{})

func traverseMapInStringOrder(params map[string]interface{}, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}

func (hehe *Hehe) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck) {
	log.Log().Info("请求hehe前置校验接口")
	var requestParams = make(map[string]interface{})
	for _, v := range request.Skus {
		var sku model4.Sku
		err = source.DB().Preload("Product").Where("original_sku_id = ?", int(v.Sku.Sku)).First(&sku).Error
		if err != nil {
			return
		}
		requestParams["goodsId"] = int(sku.Product.SourceGoodsID)
		if sku.Sn != "" {
			requestParams["goods_sku_id"] = sku.Sn
		}
		requestParams["goodsNum"] = v.Number
	}
	requestParams["detail"] = strings.ReplaceAll(request.Address.Description, " ", "")
	var city model3.Region
	var county model3.Region
	//省市区县单独建立方法方便其他地方调用（例如修改收货地址的时）
	err, _, city, county, _ = MatchingRegion(request.Address)
	if err != nil {
		return
	}
	requestParams["city_id"] = strconv.Itoa(city.ID * 100)
	requestParams["region_id"] = strconv.Itoa(county.ID)

	err, requestParams = hehe.GetRequestParams(requestParams)
	var queryString []string
	for k, v := range requestParams {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	log.Log().Info("hehe前置校验参数", zap.Any("data", requestParams))
	query := strings.Join(queryString, "&")
	var result []byte
	err, result = utils.Get(heheData.BaseInfo.Host+"/api/v1/order/freight/charge?"+query, nil)
	if err != nil {
		return
	}
	log.Log().Info("hehe前置校验响应参数", zap.Any("data", string(result)))

	var orderBeforeCheckResponse response2.OrderBeforeCheckResponse
	err = json.Unmarshal(result, &orderBeforeCheckResponse)
	if err != nil {
		return
	}

	if orderBeforeCheckResponse.ErrorCode != 0 {
		err = errors.New(orderBeforeCheckResponse.ErrorInfo)
		return
	}

	interfaceData := orderBeforeCheckResponse

	var orderBeforeCheckData response2.OrderBeforeCheckData
	err = json.Unmarshal([]byte(source.Strval(interfaceData.Data)), &orderBeforeCheckData)
	if err != nil {
		return
	}
	if interfaceData.ErrorCode == 0 {
		data.Freight = uint(source.GetInterfaceToFloat64(orderBeforeCheckData.ExpressPrice) * 100)
		data.Msg = orderBeforeCheckResponse.ErrorInfo
		data.Code = 1
	} else {
		data.Msg = orderBeforeCheckResponse.ErrorInfo
		data.Code = uint(orderBeforeCheckResponse.ErrorCode)
	}

	return
}

func (hehe *Hehe) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck) {
	for _, v := range request.Skus {
		resData.Data.Available = append(resData.Data.Available, uint(v.Sku.Sku))
	}
	return
}

type ConfirmOrderRequest struct {
	GoodsId        int    `json:"goodsId"`
	GoodsNum       int    `json:"goodsNum"`
	GoodsSkuId     string `json:"goodsSkuId"`
	CusOrderNo     string `json:"cus_order_no"`
	IDCard         string `json:"IDCard"`
	UserName       string `json:"user_name"`
	Mode           string `json:"mode"`
	BuyerRemark    string `json:"buyer_remark"`
	Name           string `json:"name"`
	Phone          int64  `json:"phone"`
	ProvinceId     string `json:"province_id"`
	CityId         string `json:"city_id"`
	RegionId       string `json:"region_id"`
	Detail         string `json:"detail"`
	IdCardFrontImg string `json:"id_card_front_img"`
	IdCardBackImg  string `json:"id_card_back_img"`
}

func (hehe *Hehe) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	var confirmInfo = make(map[string]interface{})
	confirmInfo["cus_order_no"] = request.OrderSn.OrderSn
	for _, v := range request.Skus {
		var sku model4.Sku
		err = source.DB().Preload("Product").Where("original_sku_id = ?", int(v.Sku.Sku)).First(&sku).Error
		if err != nil {
			return
		}
		confirmInfo["goodsId"] = int(sku.Product.SourceGoodsID)
		if sku.Sn != "" {
			confirmInfo["goodsSkuId"] = sku.Sn
		}
		confirmInfo["goodsNum"] = v.Number
	}
	var province model3.Region
	var city model3.Region
	var county model3.Region
	//省市区县单独建立方法方便其他地方调用（例如修改收货地址的时）
	err, province, city, county, _ = MatchingRegion(request.Address)
	if err != nil {
		return
	}
	confirmInfo["province_id"] = strconv.Itoa(province.ID * 10000)
	confirmInfo["city_id"] = strconv.Itoa(city.ID * 100)
	confirmInfo["region_id"] = strconv.Itoa(county.ID)
	confirmInfo["name"] = request.Address.Consignee
	phone, err := strconv.Atoi(request.Address.Phone)
	if err != nil {
		return
	}
	confirmInfo["phone"] = int64(phone)
	confirmInfo["detail"] = strings.ReplaceAll(request.Address.Description, " ", "")
	confirmInfo["mode"] = "buyNow"
	confirmInfo["user_name"] = "吴敬宇"
	confirmInfo["IDCard"] = "232303199507053410"
	var requestParams map[string]interface{}
	err, requestParams = hehe.GetRequestParams(confirmInfo)
	var result []byte
	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	log.Log().Info("hehe下单参数", zap.Any("data", requestParams))

	err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/order/buyer/create", requestParams, header)
	if err != nil {
		return
	}
	log.Log().Info("hehe下单结果", zap.Any("data", string(result)))

	var confirmResponse response2.ConfirmOrderResponse
	err = json.Unmarshal(result, &confirmResponse)
	if err != nil {
		err = source.DB().Model(&model5.Order{}).Where("`order_sn` = ?", request.OrderSn.OrderSn).Update("gather_supply_msg", err.Error()).Error
		return
	}
	if confirmResponse.ErrorCode != 0 {
		err = errors.New(confirmResponse.ErrorInfo)
		err = source.DB().Model(&model5.Order{}).Where("`order_sn` = ?", request.OrderSn.OrderSn).Update("gather_supply_msg", confirmResponse.ErrorInfo).Error
		return
	} else {
		var confirmResponseWithData response2.ConfirmOrderResponseWithData
		err = json.Unmarshal(result, &confirmResponseWithData)
		if err != nil {
			err = errors.New(err.Error())
			err = source.DB().Model(&model5.Order{}).Where("`order_sn` = ?", request.OrderSn.OrderSn).Update("gather_supply_msg", err.Error()).Error
			return
		}
		log.Log().Info("hehe创建订单成功", zap.Any("data", result))
		err = source.DB().Model(&model5.Order{}).Where("`order_sn` = ?", request.OrderSn.OrderSn).Update("gather_supply_sn", confirmResponseWithData.Data.OrderNo).Error
	}
	return
}
func MatchingRegion(addressRequest request.ReceivingInformation) (err error, province model3.Region, city model3.Region, county model3.Region, town model3.Region) {
	err = source.DB().Where("name = ?", addressRequest.Province).Where("parent_id = ?", 0).Where("level = ?", 1).First(&province).Error

	if err != nil {
		err = source.DB().Where("name like ?", AddressName(addressRequest.Province)+"%").Where("parent_id = ?", 0).Where("level = ?", 1).First(&province).Error
		if err != nil {
			err = errors.New("省份匹配出错")
			return
		}
	}

	err = source.DB().Where("name = ?", addressRequest.City).Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error

	if err != nil {
		if province.ID == 11 || province.ID == 12 || province.ID == 31 || province.ID == 50 {
			//直辖市
			err = source.DB().Where("name = ?", "市辖区").Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error

		} else {
			err = source.DB().Where("name like ?", AddressName(addressRequest.City)+"%").Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error
			//省直辖县级行政区划 有的可能是规划了  在这个下面变味了三级
			if err != nil {
				err = source.DB().Where("name like ?", "省直辖县级行政区划").Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error
			}
		}
		if err != nil {
			err = errors.New("城市匹配出错")
			return
		}
	}
	//部分区域固定名称
	switch addressRequest.Area {
	case "遵义县":
		addressRequest.Area = "播州区"
		break
	}
	err = source.DB().Where("name = ?", addressRequest.Area).Where("parent_id = ?", city.ID).Where("level = ?", 3).First(&county).Error

	if err != nil {
		err = source.DB().Where("name like ?", AddressName(addressRequest.Area)+"%").Where("parent_id = ?", city.ID).Where("level = ?", 3).First(&county).Error
		if err != nil {

			err = source.DB().Where("name like ?", AddressName(addressRequest.Area)+"%").Where("level = ?", 3).First(&county).Error
			if err != nil {
				err = errors.New("区域匹配出错")
				return
			}
			var newCity model3.Region
			err = source.DB().Where("id = ?", county.ParentID).Where("level = ?", 2).First(&newCity).Error
			if err != nil {
				err = errors.New("区域匹配-城市匹配出错")
				return
			}
			if newCity.ParentID != province.ID {
				err = errors.New("区域匹配出错")
				return
			}
			city = newCity
		}
	}

	err = source.DB().Where("name = ?", addressRequest.Street).Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error

	if err != nil {
		if addressRequest.Street == "" || addressRequest.Street == "其他" {
			err = source.DB().Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
			if err != nil {
				err = errors.New("街道地址匹配出错")
				return
			}
		} else {
			err = source.DB().Where("name like ?", AddressName(addressRequest.Street)+"%").Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					err = errors.New("街道地址匹配出错")
					return
				} else {
					var counties []model3.Region
					err = source.DB().Where("parent_id = ?", city.ID).Where("level = ?", 3).Find(&counties).Error
					if err != nil {
						err = errors.New("街道地址匹配出错")
						return
					}
					var countyIds []int
					for _, aCounty := range counties {
						countyIds = append(countyIds, aCounty.ID)
					}
					// 尝试匹配城市的所有街道，成功后使用匹配到的town
					err = source.DB().Where("name like ?", AddressName(addressRequest.Street)+"%").Where("parent_id in ?", countyIds).Where("level = ?", 4).First(&town).Error
					if err != nil {
						// 没有匹配到选择第一个街道
						err = source.DB().Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
						if err != nil {
							if errors.Is(err, gorm.ErrRecordNotFound) {
								town = model3.Region{
									Name:  "其他",
									Level: 4,
								}
								err = nil
							} else {
								err = errors.New("街道地址匹配出错")
								return
							}
						}
					} else {
						var newCounty model3.Region
						// 使用匹配到的town所属的county
						err = source.DB().Where("id = ?", town.ParentID).Where("level = ?", 3).First(&newCounty).Error
						if err != nil {
							err = errors.New("街道地址匹配出错")
							return
						}
						county = newCounty
					}

				}
			}
		}
	}
	return
}
func AddressName(name string) (rname string) {
	var names = [...]string{"省", "市", "新区", "区", "县", "镇", "街道", "办事处", "乡"}

	for _, x := range names {
		//从右往左检测是否存在字符串
		rname = strings.TrimRight(name, x)
		//如果不相等证明已经裁剪过了
		if rname != name {
			break
		}
	}

	return rname
}
func (hehe *Hehe) ExpressQuery(request request.RequestExpress) (err error, info interface{}) {
	return
}

func (hehe *Hehe) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (hehe *Hehe) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (hehe *Hehe) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (hehe *Hehe) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}
func (hehe *Hehe) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}
func (hehe *Hehe) HeheOrderSend(info model6.HeheCallBackType) (err error) {
	var orderModel model5.Order
	err = source.DB().Preload("OrderItems").Where("gather_supply_sn = ?", info.OrderNo).First(&orderModel).Error
	if err != nil {
		return
	}
	err = hehe.InitSetting(orderModel.GatherSupplyID)
	if err != nil {
		return
	}
	var orderRequest v1.HandleOrderRequest
	if len(info.OrderSplit) == 0 {
		orderRequest.OrderID = orderModel.ID
		orderRequest.CompanyCode = info.ExpressCode
		kds := express.GetCompanyList()
		for _, kdItem := range kds {
			if strings.Contains(kdItem.Name, info.ExpressCompany) || strings.Contains(info.ExpressCompany, kdItem.Name) {
				orderRequest.CompanyCode = kdItem.Code
				break
			}
		}
		orderRequest.ExpressNo = info.ExpressNo
		for _, orderItem := range orderModel.OrderItems {
			orderRequest.OrderItemIDs = append(orderRequest.OrderItemIDs, request2.OrderItemSendInfo{
				ID:  orderItem.ID,
				Num: orderItem.Qty,
			})
		}

		err = v1.CallBackSendOrder(orderRequest)
	} else {
		for _, orderS := range info.OrderSplit {
			if orderS.ExpressNo != nil && orderS.ExpressCode != nil {
				var orderExpressCheck model5.OrderExpress
				err = source.DB().Where("express_no = ?", orderS.ExpressNo).First(&orderExpressCheck).Error
				if err == nil {
					//此快递单号已经发货过，跳过
					continue
				}
				orderRequest.CompanyCode = *orderS.ExpressCode
				kds := express.GetCompanyList()
				for _, kdItem := range kds {
					if strings.Contains(kdItem.Name, *orderS.ExpressCompany) || strings.Contains(*orderS.ExpressCompany, kdItem.Name) {
						orderRequest.CompanyCode = kdItem.Code
						break
					}
				}
				orderRequest.OrderItemIDs = append(orderRequest.OrderItemIDs, request2.OrderItemSendInfo{
					ID:  0,
					Num: 0,
				})
				orderRequest.OrderID = orderModel.ID
				orderRequest.ExpressNo = *orderS.ExpressNo
				orderRequest.IsEmpty = 1
				orderRequest.FinalSend = len(info.OrderSplit)
				err = v1.CallBackSendOrder(orderRequest)
			}
		}
	}

	return
}
func (hehe *Hehe) AfterSaleCreate(as afterSalesModel.AfterSales) (err error) {
	var requestParams = make(map[string]interface{})
	requestParams["order_no"] = as.Order.GatherSupplySN
	switch as.RefundType {
	case afterSalesModel.Refund:
		requestParams["type"] = 2
		break
	case afterSalesModel.Return:
		requestParams["type"] = 1
		break
	default:
		requestParams["type"] = 1
	}

	err, requestParams["content"] = afterSalesModel.GetRefundReasonName(as.ReasonType)
	requestParams["express_img"] = "https://www.baidu.com/6229619948f7f.jpg"
	requestParams["package_img"] = "https://www.baidu.com/6229619948f7f.jpg"
	requestParams["product_img"] = "https://www.baidu.com/6229619948f7f.jpg"
	requestParams["defect_product_img"] = "https://www.baidu.com/6229619948f7f.jpg"
	requestParams["product_shelf_life_img"] = "https://www.baidu.com/6229619948f7f.jpg"
	var requestParam map[string]interface{}
	err, requestParam = hehe.GetRequestParams(requestParams)
	if err != nil {
		return err
	}
	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	log.Log().Info("hehe售后申请参数", zap.Any("data", requestParam))
	var result []byte
	err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/order/buyer/refund/apply", requestParam, header)
	if err != nil {
		return
	}
	log.Log().Info("hehe售后申请结果", zap.Any("data", string(result)))

	var confirmResponse response2.ConfirmOrderResponse
	err = json.Unmarshal(result, &confirmResponse)
	if err != nil {
		return
	}
	if confirmResponse.ErrorCode != 0 {
		err = errors.New(confirmResponse.ErrorInfo)
		return
	}
	log.Log().Info("hehe售后申请成功", zap.Any("data", result))

	return nil
}

func (hehe *Hehe) AfterSalesSend(info request3.SendRequest) (err error) {
	var as afterSalesModel.AfterSales
	err = source.DB().First(&as, info.AfterSalesID).Error
	if err != nil {
		return err
	}
	var requestParams = make(map[string]interface{})
	requestParams["order_no"] = as.Order.GatherSupplySN
	requestParams["express_no"] = info.ExpressNo
	err, requestParams["express_name"], requestParams["express_code"] = hehe.GetHeheExpressName(info.CompanyName)
	if err != nil {
		requestParams["express_code"] = info.CompanyCode
		requestParams["express_name"] = info.CompanyName
	}
	requestParams["express_money"] = "10"
	requestParams["express_img"] = "https://www.baidu.com/6229619948f7f.jpg"
	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	var requestParam map[string]interface{}
	err, requestParam = hehe.GetRequestParams(requestParams)
	if err != nil {
		return err
	}
	log.Log().Info("hehe售后发货参数", zap.Any("data", requestParam))
	var result []byte
	err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/order/buyer/send", requestParam, header)
	if err != nil {
		return
	}
	log.Log().Info("hehe售后发货结果", zap.Any("data", string(result)))

	var confirmResponse response2.ConfirmOrderResponse
	err = json.Unmarshal(result, &confirmResponse)
	if err != nil {
		return
	}
	if confirmResponse.ErrorCode != 0 {
		err = errors.New(confirmResponse.ErrorInfo)
		return
	}
	log.Log().Info("hehe售后发货成功", zap.Any("data", result))

	return nil
}

func (hehe *Hehe) AfterSalesClose(as afterSalesModel.AfterSales) (err error) {

	var requestParams = make(map[string]interface{})
	requestParams["order_no"] = as.Order.GatherSupplySN
	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	var requestParam map[string]interface{}
	err, requestParam = hehe.GetRequestParams(requestParams)
	if err != nil {
		return err
	}
	log.Log().Info("hehe售后关闭参数", zap.Any("data", requestParam))
	var result []byte
	err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/order/buyer/cancelrefund", requestParam, header)
	if err != nil {
		return
	}
	log.Log().Info("hehe售后关闭结果", zap.Any("data", string(result)))

	var confirmResponse response2.ConfirmOrderResponse
	err = json.Unmarshal(result, &confirmResponse)
	if err != nil {
		return
	}
	if confirmResponse.ErrorCode != 0 {
		err = errors.New(confirmResponse.ErrorInfo)
		return
	}
	log.Log().Info("hehe售后关闭成功", zap.Any("data", result))

	return nil
}
func (hehe *Hehe) AfterSaleAuditHandle(orderNo string, status int) (err error) {
	var orderModel model5.Order
	err = source.DB().Where("gather_supply_sn = ?", orderNo).First(&orderModel).Error
	if err != nil {
		return
	}
	var as afterSalesModel.AfterSales
	err = source.DB().Where("order_id = ?", orderModel.ID).First(&as).Error
	if err != nil {
		return
	}
	var resAfterSaleAudit afterSalesModel.AfterSalesAudit
	err = source.DB().Where("after_sales_id = ?", as.ID).Last(&resAfterSaleAudit).Error
	if err != nil {
		return
	}
	if status == 1 {

		var requestParams = make(map[string]interface{})
		requestParams["order_no"] = orderNo
		header := make(map[string]string)
		header["Content-Type"] = "application/json"
		var requestParam map[string]interface{}
		err, requestParam = hehe.GetRequestParams(requestParams)
		if err != nil {
			return err
		}
		log.Log().Info("hehe退款仓库参数", zap.Any("data", requestParam))
		var result []byte
		err, result = utils.Post(heheData.BaseInfo.Host+"/api/v1/warehouse/info", requestParam, header)
		if err != nil {
			return
		}
		log.Log().Info("hehe退款仓库结果", zap.Any("data", string(result)))

		var confirmResponse response2.WareHouse
		err = json.Unmarshal(result, &confirmResponse)
		if err != nil {
			return
		}
		if confirmResponse.ErrorCode != 0 {
			err = errors.New(confirmResponse.ErrorInfo)
			return
		} else {
			var shopAddress afterSalesModel.ShopAddress
			shopAddress.Address = confirmResponse.Data.Address + confirmResponse.Data.Detail
			shopAddress.Tel = confirmResponse.Data.Mobile
			shopAddress.Contacts = confirmResponse.Data.Name
			err = source.DB().Model(&afterSalesModel.ShopAddress{}).Where("address = ?", shopAddress.Address).FirstOrCreate(&shopAddress).Error
			if err != nil {
				log.Log().Info("和合接到审核通过消息,执行错误7", zap.Any("err", err))
				return nil
			}

			resAfterSaleAudit.ShippingAddressID = shopAddress.ID
			err = service.PassAudit(resAfterSaleAudit, 0, 0)
			if err != nil {
				return
			}
			if as.RefundWay == 0 {
				err, _ = service.Refund(as, 0, 0)
				if err != nil {
					return
				}
			}

		}

		log.Log().Info("hehe退款仓库成功", zap.Any("data", result))
	} else {
		//审核驳回
		if as.Status != 0 {
			//不是待审核状态，只能关闭
			log.Log().Info("售后状态已经不是待审核状态，所以只能进行关闭处理。")
			err = service.Close(as, 0, 0)
			if err != nil {
				return
			}
		} else {
			err = service.RejectAudit(resAfterSaleAudit, 0, 0)
			if err != nil {
				return
			}
		}
	}

	return nil
}
func (hehe *Hehe) AfterSaleComplete(orderNo string, status int) (err error) {
	if status == 1 {
		var orderModel model5.Order
		err = source.DB().Where("gather_supply_sn = ?", orderNo).First(&orderModel).Error
		if err != nil {
			return
		}
		var as afterSalesModel.AfterSales
		err = source.DB().Where("order_id = ?", orderModel.ID).First(&as).Error
		if err != nil {
			return
		}

		if as.Status == afterSalesModel.WaitReceiveStatus && as.RefundType == afterSalesModel.Return {
			//退货退款，上游已收货并退款，中台需要先商家确认收货，然后进行退款操作
			var ass request3.AfterSalesSend
			ass.Id = as.ID
			err = service.Receive(ass, 0, 0)
			if err != nil {
				log.Log().Info("优选自动售后收货错误", zap.Any("err", err), zap.Any("asID", as.ID))
				return err
			}

			err, _ = service.Refund(as, 0, 0)
			if err != nil {
				return
			}
		}
		if as.Status == afterSalesModel.WaitRefundStatus && as.RefundType == afterSalesModel.Refund {
			//仅退款，上游已退款，中台直接退款，因为仅退款售后单审核通过后，直接是待退款状态
			err, _ = service.Refund(as, 0, 0)
			if err != nil {
				return
			}
		}

	}

	return nil
}
func (hehe *Hehe) GetHeheExpressName(localExpressName string) (err error, name string, code string) {
	var requestParams = make(map[string]interface{})

	header := make(map[string]string)
	header["Content-Type"] = "application/json"
	var requestParam map[string]interface{}
	err, requestParam = hehe.GetRequestParams(requestParams)
	if err != nil {
		return
	}
	var queryString []string
	for k, v := range requestParam {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")

	err, result := utils.Get(heheData.BaseInfo.Host+"/api/v1/shipping?"+query, nil)
	var response response2.ExpressList
	err = json.Unmarshal(result, &response)
	if err != nil {
		return
	}

	if response.ErrorCode != 0 {
		err = errors.New("和合物流公司获取失败")
		return
	}
	for _, item := range response.Data {
		if strings.Contains(localExpressName, item.ShippingCompanyName) || strings.Contains(item.ShippingCompanyName, localExpressName) {
			name = item.ShippingCompanyName
			code = item.Code
			return
		}
	}
	err = errors.New("未找到此快递公司")
	return

}

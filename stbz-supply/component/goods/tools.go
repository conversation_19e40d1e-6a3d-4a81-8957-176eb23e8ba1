package goods

import (
	catemodel "category/model"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gookit/color"
	"github.com/xingliuhua/leaf"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"public-supply/common"
	setting2 "public-supply/setting"

	pmodel "product/model"
	pMq "product/mq"
	psmodel "product/service"
	"public-supply/model"
	"public-supply/request"
	"sort"
	"strconv"

	"sync"
	"time"
	ylog "yz-go/component/log"
	"yz-go/source"
)

func GetOrderNo() (id string) {

	var node *leaf.IdNode
	var err error
	err, node = leaf.NewNode(20)
	if err != nil {
		return
	}
	err, id = node.NextId()
	if err != nil {
		return
	}
	return
}
func splitArray(arr []model.Goods, num int64) [][]model.Goods {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]model.Goods{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}

	fmt.Println("quantity数量：", quantity)
	//声明分割好的二维数组
	var segments = make([][]model.Goods, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}
func GetIdArr(list []model.Goods) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.ID)
	}
	return

}

func YzhGetIdArr(list []model.YzhGoodsDetail) (arrIds []int) {
	for _, elem := range list {
		arrIds = append(arrIds, elem.RESULTDATA.PRODUCTDATA.ProductId)
	}
	return

}

// 分割数组，根据传入的数组和分割大小，将数组分割为大小等于指定大小的多个数组，如果不够分，则最后一个数组元素小于其他数组
func SplitArray(arr []int, num int64) [][]int {
	max := int64(len(arr))
	//判断数组大小是否小于等于指定分割大小的值，是则把原数组放入二维数组返回
	if max <= num {
		return [][]int{arr}
	}
	//获取应该数组分割为多少份
	var quantity int64
	if max%num == 0 {
		quantity = max / num
	} else {
		quantity = (max / num) + 1
	}
	//声明分割好的二维数组
	var segments = make([][]int, 0)
	//声明分割数组的截止下标
	var start, end, i int64
	for i = 1; i <= quantity; i++ {
		end = i * num
		if i != quantity {
			segments = append(segments, arr[start:end])
		} else {
			segments = append(segments, arr[start:])
		}
		start = i * num
	}
	return segments
}

// 导入胜天半子分类数据
func ImportCategory(counts int, info request.GetCategorySearch, key string) (err error, data interface{}) {

	//err=DeleteCategory(info.Source)
	var wg sync.WaitGroup
	for i := 1; i <= counts; i++ {
		wg.Add(1)
		if i%15 == 0 {
			time.Sleep(time.Second * 1)
		}
		var supply Stbz
		go supply.RunConcurrent(&wg, info, i)

	}

	wg.Wait()
	data = category

	ylog.Log().Error("获取栏目!", zap.Any("err", category))

	//BatchImportDatabaseA(info)
	BatchImportDatabase()
	fmt.Println("全部完成：", len(category), info.Source)
	return
}

func GetPricingPrice(elem model.Goods, key string) (err error, costPrice uint, salePrice uint, guidePrice uint) {
	var dat SupplySetting
	err, setting := setting2.GetSetting(key)
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {
		return
	}

	var intX uint64

	if dat.Pricing.Strategy == 2 { //本地定价策略关闭
		if elem.Source == common.STBZ_JD { //京东

			//销售价计算
			if dat.Pricing.JDSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.JDSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.JDSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.JDSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.JDSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.JDSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.JDSales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.JDSalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.JDSalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.JDSalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.JDSalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.JDCostPrice == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.JDCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.JDCostPrice == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.JDCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			if dat.Pricing.JDGuidePrice == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.JDGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.JDGuidePrice == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.JDGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

		}

		if elem.Source == 1 || elem.Source == 72 { //云仓

			//销售价计算
			if dat.Pricing.YCSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
				color.Info.Println("云仓销售指导系数", intX)

			}
			if dat.Pricing.YCSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓销售价协议系数", intX)
			}
			if dat.Pricing.YCSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.YCSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓销售营销系数", intX)

			}

			if dat.Pricing.YCSales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.YCSalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.YCSalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.YCSalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.YCSalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.YCCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓成本协议系数", intX)

			}
			if dat.Pricing.YCCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓成本营销系数", intX)

			}

			//成本价计算结束

			if dat.Pricing.YCGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓成本协议系数", intX)

			}
			if dat.Pricing.YCGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓成本营销系数", intX)

			}

		}

		if elem.Source == common.STBZ_YCYX { //云仓优选

			//销售价计算
			if dat.Pricing.YCYXSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
				color.Info.Println("云仓YX销售指导系数", intX)

			}
			if dat.Pricing.YCYXSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓YX销售价协议系数", intX)
			}
			if dat.Pricing.YCYXSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓YX销售营销系数", intX)

			}

			if dat.Pricing.YCYXSales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.YCYXSalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.YCYXSalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.YCYXSalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.YCYXSalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.YCYXCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓成本协议系数", intX)

			}
			if dat.Pricing.YCYXCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓成本营销系数", intX)

			}

			//成本价计算结束

			if dat.Pricing.YCYXGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
				color.Info.Println("云仓成本协议系数", intX)

			}
			if dat.Pricing.YCYXGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.YCYXGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
				color.Info.Println("云仓成本营销系数", intX)

			}

		}

		if elem.Source == common.STBZ_AL { //阿里定价策略

			//销售价计算
			if dat.Pricing.ALSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.ALSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.ALSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.ALSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.ALSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.ALSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.ALSales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.ALSalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.ALSalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.ALSalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.ALSalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.ALCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.ALCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.ALCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.ALCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.ALGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.ALGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.ALGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.ALGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_TM { //天猫

			//销售价计算
			if dat.Pricing.TMSales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMSalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.TMSales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMSalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMSales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.TMSalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.TMSales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.TMSalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.TMSalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.TMSalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.TMSalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.TMCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.TMGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		//if elem.Source == 0 { //中台本地
		//
		//	//销售价计算
		//	if dat.Pricing.SupplySales == 1 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplySalesGuide, 10, 32)
		//		salePrice = elem.GuidePrice * uint(intX) / 100
		//	}
		//	if dat.Pricing.SupplySales == 2 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplySalesAgreement, 10, 32)
		//		salePrice = elem.AgreementPrice * uint(intX) / 100
		//	}
		//	if dat.Pricing.SupplySales == 3 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplySalesMarketing, 10, 32)
		//		salePrice = elem.ActivityPrice * uint(intX) / 100
		//	}
		//	//销售价计算结束
		//
		//	//成本价
		//	if dat.Pricing.SupplyCost == 1 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplyCostAgreement, 10, 32)
		//		costPrice = elem.AgreementPrice * uint(intX) / 100
		//	}
		//	if dat.Pricing.SupplyCost == 2 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplyCostMarketing, 10, 32)
		//		costPrice = elem.ActivityPrice * uint(intX) / 100
		//	}
		//
		//	//成本价计算结束
		//
		//	if dat.Pricing.SupplyGuide == 1 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideAgreement, 10, 32)
		//		costPrice = elem.AgreementPrice * uint(intX) / 100
		//	}
		//	if dat.Pricing.SupplyGuide == 2 {
		//		intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideMarketing, 10, 32)
		//		costPrice = elem.ActivityPrice * uint(intX) / 100
		//	}
		//
		//}

		if elem.Source == common.STBZ_SN { //苏宁

			//销售价计算
			if dat.Pricing.SupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.SupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.SupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.SupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.SupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.SupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.SupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.SupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.SupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_HNYC { //华南一仓

			//销售价计算
			if dat.Pricing.HNSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.HNSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.HNSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.HNSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.HNSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.HNSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.HNSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.HNSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.HNSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.HNSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.HNSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.HNSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.HNSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_TMYC { //特卖一仓

			//销售价计算
			if dat.Pricing.TMYCSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.TMYCSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMYCSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.TMYCSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.TMYCSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.TMYCSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMYCSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.TMYCSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMYCSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMYCSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_HDYC { //华东一仓

			//销售价计算
			if dat.Pricing.HDYCSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.HDYCSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.HDYCSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.HDYCSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.HDYCSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.HDYCSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.HDYCSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.HDYCSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.HDYCSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.HDYCSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_TB { //淘宝

			//销售价计算
			if dat.Pricing.TBSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.TBSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TBSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.TBSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.TBSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.TBSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.TBSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.TBSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.TBSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TBSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.TBSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TBSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TBSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_KJYC { //跨境一仓

			//销售价计算
			if dat.Pricing.KJYCSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.KJYCSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.KJYCSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.KJYCSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.KJYCSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.KJYCSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.KJYCSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.KJYCSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.KJYCSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.KJYCSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_TMJX { //天猫精选

			//销售价计算
			if dat.Pricing.TMJXSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.TMJXSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMJXSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.TMJXSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.TMJXSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.TMJXSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMJXSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.TMJXSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.TMJXSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.TMJXSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		if elem.Source == common.STBZ_CJZX { //厂家直销

			//销售价计算
			if dat.Pricing.CJZXSupplySales == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesGuide, 10, 32)
				salePrice = elem.GuidePrice * uint(intX) / 100
			}
			if dat.Pricing.CJZXSupplySales == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesAgreement, 10, 32)
				salePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.CJZXSupplySales == 3 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesMarketing, 10, 32)
				salePrice = elem.ActivityPrice * uint(intX) / 100
			}
			if dat.Pricing.CJZXSupplySales == 4 {
				var intXa uint64
				var intXb uint64
				var intXc uint64
				var intXd uint64
				intXa, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesList.SupplySalesExecA, 10, 32)
				intXb, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesList.SupplySalesExecB, 10, 32)
				intXc, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesList.SupplySalesExecC, 10, 32)
				intXd, err = strconv.ParseUint(dat.Pricing.CJZXSupplySalesList.SupplySalesExecD, 10, 32)
				salePrice = elem.AgreementPrice*uint(intXa)/100 + ((elem.GuidePrice - elem.AgreementPrice) * uint(intXb) / 100)
				if float64(salePrice)/float64(elem.AgreementPrice) > float64(intXc)/100 {
					salePrice = elem.AgreementPrice * uint(intXd) / 100
				}
			}
			//销售价计算结束

			//成本价
			if dat.Pricing.CJZXSupplyCost == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplyCostAgreement, 10, 32)
				costPrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.CJZXSupplyCost == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplyCostMarketing, 10, 32)
				costPrice = elem.ActivityPrice * uint(intX) / 100
			}

			//成本价计算结束

			if dat.Pricing.CJZXSupplyGuide == 1 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplyGuideAgreement, 10, 32)
				guidePrice = elem.AgreementPrice * uint(intX) / 100
			}
			if dat.Pricing.CJZXSupplyGuide == 2 {
				intX, err = strconv.ParseUint(dat.Pricing.CJZXSupplyGuideMarketing, 10, 32)
				guidePrice = elem.ActivityPrice * uint(intX) / 100
			}

		}

		//if elem.Source == common.YZH_SOURCE { //yzh
		//
		//	//销售价计算
		//
		//	if dat.Pricing.YzhSupplySales == 2 {
		//		intX, err = strconv.ParseUint(dat.Pricing.YzhSupplySalesAgreement, 10, 32)
		//		salePrice = elem.AgreementPrice * uint(intX) / 100
		//	}
		//	if dat.Pricing.YzhSupplySales == 3 {
		//		intX, err = strconv.ParseUint(dat.Pricing.YzhSupplySalesMarketing, 10, 32)
		//		salePrice = elem.ActivityPrice * uint(intX) / 100
		//	}
		//	//销售价计算结束
		//
		//	//成本价
		//	if dat.Pricing.YzhSupplyCost == 1 {
		//		intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyCostAgreement, 10, 32)
		//		costPrice = elem.AgreementPrice * uint(intX) / 100
		//	}
		//	if dat.Pricing.YzhSupplyCost == 2 {
		//		intX, err = strconv.ParseUint(dat.Pricing.YzhSupplyCostMarketing, 10, 32)
		//		costPrice = elem.ActivityPrice * uint(intX) / 100
		//	}
		//
		//	//成本价计算结束
		//
		//}

	} else {
		salePrice = elem.GuidePrice
		//guidePrice = elem.GuidePrice
		costPrice = elem.AgreementPrice
	}

	return

}

//获取本地策略价格

func GetArrIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}
func GetSkuPrice(skuList []pmodel.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}
func UpdasteGetSkuPrice(skuList []psmodel.Sku) (maxPrice, minPrice uint) {
	var priceList []int
	for _, item := range skuList {
		priceList = append(priceList, int(item.Price))
	}
	if len(priceList) <= 0 {
		maxPrice = 0
		minPrice = 0
		return
	}
	sort.Ints(priceList)
	minPrice = uint(priceList[0])
	//sort.Sort(sort.Reverse(sort.IntSlice(priceList)))
	maxPrice = uint(priceList[len(priceList)-1])

	return
}

//
////导入胜天半子分类数据
//func ImportCategory(counts int, info request.GetCategorySearch, key string) (err error, data interface{}) {
//
//	//err=DeleteCategory(info.Source)
//	var wg sync.WaitGroup
//	for i := 1; i <= counts; i++ {
//		wg.Add(1)
//		if i%15 == 0 {
//			time.Sleep(time.Second * 1)
//		}
//		var supply = NewGoods(key)
//		go supply.RunConcurrent(&wg, info, i)
//
//	}
//
//	wg.Wait()
//	data = category
//
//	ylog.Log().Error("获取栏目!", zap.Any("err", category))
//
//	//BatchImportDatabaseA(info)
//	BatchImportDatabase()
//	fmt.Println("全部完成：", len(category), info.Source)
//	return
//}

func BatchImportDatabase() {

	for _, elem := range category {

		var paid uint
		if elem.Source == 2 || elem.Source == 6 || elem.Source == 7 || elem.Source == 1 {
			paid = elem.ThirdId
		}
		if elem.Source == 0 {
			paid = elem.Id
		}
		if elem.Level == 0 {
			var catei = new(catemodel.Category)
			//catei.ID=paid
			catei.Name = elem.Title
			catei.Level = elem.Level + 1
			catei.ParentID = elem.ParentId
			catei.Source = elem.Source
			catei.Sort = elem.Sort
			catei.IsDisplay = elem.State

			err, id := CreateCategory(catei)
			if id > 0 {

				FindChildCategory(paid, id)
				fmt.Println("插入成功", elem.Id, id, elem.Title, elem.Level)
			}
			if err != nil {
				fmt.Println("插入失败", elem.Title, elem.Id, err)
			}

		}

	}

}

func FindChildCategory(id uint, pid uint) {

	for _, elem := range category {
		var paid uint
		if elem.Source == 2 || elem.Source == 6 || elem.Source == 7 || elem.Source == 1 {
			paid = elem.ThirdId
		}
		if elem.Source == 0 {
			paid = elem.Id
		}

		if elem.ParentId == id {
			var catei = new(catemodel.Category)
			catei.Name = elem.Title
			catei.Level = elem.Level + 1
			catei.ParentID = pid
			catei.Source = elem.Source
			catei.Sort = elem.Sort
			catei.IsDisplay = elem.State
			err, cid := CreateCategory(catei)
			fmt.Println("寻找parentid=", id)
			if id > 0 {

				FindChildCategory(paid, cid)
			}
			if err != nil {
				fmt.Println("插入失败", elem.Title, elem.Id, err)
			}

		}

	}

}

func CreateCategory(category *catemodel.Category) (err error, id uint) {

	var fCategory *catemodel.Category
	err = source.DB().Where("level = ? ", category.Level).Where("name = ?", category.Name).First(&fCategory).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = source.DB().Create(&category).Error

		if err != nil {
			return
		}

	}

	return err, category.ID

}

func SetImportRepeat(batch string, quantity int) (err error) {
	if quantity <= 0 {
		return
	}
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("repeat_quantity", gorm.Expr("repeat_quantity + ?", quantity)).Error
	return
}

func GetProductIds(list []*pmodel.Product) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(int(elem.SourceGoodsID))
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

func GetKeyValueB(Values []model.SpecsValue, specValueIds string, speceNameId int) (list string) {

	for _, itemValue := range Values {
		if specValueIds == strconv.Itoa(itemValue.ID) && speceNameId == itemValue.SpecNameID {
			list = itemValue.Name
			return
		}
	}
	return
}

func GetSpecValue(skuValueId string, Values []model.SpecsValue) (specValueId int, specValueName string) {
	for _, itemValue := range Values {
		if skuValueId == strconv.Itoa(itemValue.ID) {
			return itemValue.SpecNameID, itemValue.Name
		}

	}

	return
}

func GetSpecName(specNameId int, Names []model.SpecsName) string {
	for _, itemValue := range Names {
		if specNameId == itemValue.ID {
			return itemValue.Name
		}

	}
	return ""
}

func CreateGoods(goodsList []*pmodel.Product) (err error) {

	for _, goods := range goodsList {

		err = source.DB().Create(&goods).Error
		if err != nil {
			fmt.Println("插入失败", err)
		}

		SourceGoodsID := goods.SourceGoodsID

		//if goods.Source == 2 && len(goods.Skus) == 1 {
		//	if goods.SupplierSourceID > 0 {
		//		//SpecId, _ := strconv.Atoi(goods.Skus[0].SpecId)
		//		SourceGoodsID = goods.SupplierSourceID
		//	}
		//
		//}

		err = source.DB().Create(&model.SupplyGoods{
			SupplyGoodsID:  SourceGoodsID,
			Source:         goods.Source,
			ProductID:      goods.ID,
			GatherSupplyID: goods.GatherSupplyID,
		}).Error

		err = pMq.PublishMessage(goods.ID, pMq.Create, 0)

	}

	return
}

func SetImportRecordCompletion(batch string) (err error) {
	err = source.DB().Model(model.SupplyGoodsImportRecord{}).Where("batch=?", batch).Update("completion_status", 1).Error
	return
}

func GetIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

type MapEntryHandler func(string, string)

// func printKeyValue(key string, value string) {
// 	fmt.Printf("key=%s, value=%s\n", key, value)
// }

// 按字母顺序遍历map
func traverseMapInStringOrder(params map[string]string, handler MapEntryHandler) {
	keys := make([]string, 0)
	for k, _ := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	for _, k := range keys {
		handler(k, params[k])
	}
}

func HmacSha256(data string, secret string) string {
	h := hmac.New(sha256.New, []byte(secret))
	h.Write([]byte(data))
	return hex.EncodeToString(h.Sum(nil))
}

//
//func GetRequestParams(params map[string]string, data *SzbaoSupplySetting) (result url2.Values) {
//
//	reqData := url2.Values{}
//	for k, v := range params {
//		reqData.Add(k, v)
//	}
//	nonce := GenerateSubId(16)
//	appKey := data.BaseInfo.AppKey
//	appSecret := data.BaseInfo.AppSecret
//	reqData.Add("appId", appKey)
//	reqData.Add("nonce", nonce)
//	reqData.Add("timestamp", strconv.Itoa(int(time.Now().Unix())))
//	params["appId"] = appKey
//	params["nonce"] = nonce
//	params["timestamp"] = strconv.Itoa(int(time.Now().Unix()))
//	params["secret"] = appSecret
//	var signature string
//	//按照字母顺序遍历
//	traverseMapInStringOrder(params, func(key string, value string) {
//		signature += key + "=" + value + "&"
//	})
//	signature += "key=" + appSecret
//	signature = HmacSha256(signature, appSecret)
//	signature = strings.ToUpper(signature)
//	reqData.Add("signature", signature)
//	reqData.Del("secret")
//	return reqData
//}

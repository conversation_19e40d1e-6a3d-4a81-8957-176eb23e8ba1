package cron

import (
	"errors"
	"fmt"
	"github.com/chenhg5/collection"
	"go.uber.org/zap"
	"gorm.io/gorm"
	pmodel "product/model"
	pservice "product/service"
	callback2 "public-supply/callback"
	"public-supply/common"
	gsetting "public-supply/setting"
	service "stbz-supply/component/goods"
	"strconv"
	"strings"
	"yz-go/cron"
	//"go.uber.org/zap"
	"product/model"
	model2 "public-supply/model"
	"yz-go/component/log"
	//"yz-go/cron"
	"yz-go/source"
)

func PushStbzGoodsAlertHandle() {
	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.SUPPLY_STBZ).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTask(v.ID)

	}
}

func CreateCronTask(taskID int) {

	cronStr := "0 */30 * * * *"

	cron.PushTask(cron.Task{
		Key:  "stbzCronGoodsalert" + strconv.Itoa(taskID),
		Name: "stbzCronGoodsalert商品定时更新" + strconv.Itoa(taskID),
		Spec: cronStr,
		Handle: func(task cron.Task) {
			GoodsAlertPriceCronTask(taskID)

		},
		Status: cron.ENABLED,
	})

}

func GoodsAlertPriceCronTask(gatherSupplyId int) {

	var productSourceId []int64

	source.DB().Model(model.Product{}).Where("status_lock =0 and source_goods_id >0 and gather_supply_id=?", gatherSupplyId).Order("updated_at asc ").Limit(2000).Pluck("source_goods_id", &productSourceId)
	if len(productSourceId) > 0 {
		//var GoodsStorage []int64
		//source.DB().Model(model2.GoodsStorage{}).Where("supply_id=?", gatherSupplyId).Pluck("source_goods_id", &GoodsStorage)
		difference := collection.Collect(productSourceId).ToIntArray()
		if len(difference) <= 0 {
			return
		}
		stbz.InitSetting(uint(gatherSupplyId))

		listArr := service.SplitArray(difference, 20)
		for _, arrItem := range listArr {
			//fmt.Println(arrItem)
			data := make(map[string]interface{})
			data["goodsIds"] = arrItem

			var GoodsData callback2.GoodsCallBack
			var ids callback2.GoodsIds
			ids.GoodsIds = arrItem
			GoodsData.Data = ids
			GoodsData.GatherSupplyID = gatherSupplyId

			GoodsPriceAlert(GoodsData)

		}

	}
}

func TestGoodsAlertPriceCronTask(gatherSupplyId int) {
	log.Log().Info("GoodsAlertPriceCronTask 更新", zap.Any("info", gatherSupplyId))

	var productSourceId []int64

	source.DB().Model(model.Product{}).Where("status_lock =0 and source_goods_id >0 and gather_supply_id=?", gatherSupplyId).Order("updated_at asc ").Limit(20).Pluck("source_goods_id", &productSourceId)
	if len(productSourceId) > 0 {
		//var GoodsStorage []int64
		//source.DB().Model(model2.GoodsStorage{}).Where("supply_id=?", gatherSupplyId).Pluck("source_goods_id", &GoodsStorage)
		difference := collection.Collect(productSourceId).ToIntArray()
		log.Log().Info("GoodsAlertPriceCronTask 更新1", zap.Any("info", difference))

		if len(difference) <= 0 {
			return
		}
		listArr := service.SplitArray(difference, 20)
		for _, arrItem := range listArr {
			//fmt.Println(arrItem)
			data := make(map[string]interface{})
			data["goodsIds"] = arrItem
			//callBackModel := callback2.CallBackType{
			//	Type: "goods.price.alter",
			//	Data: data,
			//}

			var GoodsData callback2.GoodsCallBack
			var ids callback2.GoodsIds
			ids.GoodsIds = arrItem
			GoodsData.Data = ids
			GoodsData.GatherSupplyID = gatherSupplyId

			//	fmt.Println(callBackModel)
			GoodsPriceAlert(GoodsData)
			//	err := mq.PublishMessage(callBackModel)
			//	if err != nil {
			//		log.Log().Error("stbz发送队列供应链价格更新错误", zap.Any("err", err))
			//	}
			//err := service2.OrderCallBack(callBackModel)
			//if err != nil {
			//	log.Log().Error("供应链价格更新错误", zap.Any("err", err))
			//}

		}

	}
	//source.DB().Table("skus").Where("product_id is null").Delete("")
}

func GoodsAlertPriceCron(gatherSupplyId int) {
	log.Log().Info("stbz GoodsAlertPriceCron", zap.Any("info", gatherSupplyId))

	fmt.Println(gatherSupplyId)
	var productSourceId []int64
	source.DB().Model(model.Product{}).Where("status_lock =0 and source_goods_id >0 and gather_supply_id=?", gatherSupplyId).Order("updated_at asc").Pluck("source_goods_id", &productSourceId)
	if len(productSourceId) > 0 {
		var GoodsStorage []int64
		source.DB().Model(model2.GoodsStorage{}).Where("supply_id=?", gatherSupplyId).Pluck("source_goods_id", &GoodsStorage)
		difference := collection.Collect(productSourceId).ToIntArray()
		if len(difference) <= 0 {
			return
		}
		listArr := service.SplitArray(difference, 20)
		stbz.InitSetting(uint(gatherSupplyId))

		for _, arrItem := range listArr {
			data := make(map[string]interface{})
			data["goodsIds"] = arrItem

			var GoodsData callback2.GoodsCallBack
			var ids callback2.GoodsIds
			ids.GoodsIds = arrItem
			GoodsData.Data = ids
			GoodsData.GatherSupplyID = gatherSupplyId
			//	fmt.Println(callBackModel)
			GoodsPriceAlert(GoodsData)

		}

	}
	//source.DB().Table("skus").Where("product_id is null").Delete("")
}

func GetIds(list []int) (resIds string) {
	var ids string
	for _, elem := range list {
		stringId := strconv.Itoa(elem)
		ids = ids + stringId + ","
	}
	ids = ids[0 : len(ids)-1]
	resIds = ids
	return
}

type BaseInfoData struct {
	AppKey    string `json:"appKey"`
	AppSecret string `json:"appSecret"`
}
type SupplySetting struct {
	BaseInfo   BaseInfoData            `json:"baseInfo"`
	UpdateInfo gsetting.UpdateInfoData `json:"update"`
	Pricing    gsetting.PricingData    `json:"pricing"`
	Management gsetting.Management     `json:"management"`
}

var dat SupplySetting
var stbz = service.Stbz{}

func GetSplitSku(sourceGoodsId int, data []model2.GoodsDetail) (resdata model2.GoodsDetail, err error) {

	for _, item := range data {
		if item.ID == sourceGoodsId {
			resdata = item
			return
		}
	}
	return
}

type SpecSort struct {
	Index int    `json:"index"`
	Count uint   `json:"count"`
	Name  string `json:"name"`
}

func GoodsPriceAlert(GoodsData callback2.GoodsCallBack) (err error) {

	var ids = GoodsData.Data.GoodsIds
	if len(ids) == 0 {
		return
	}
	var data []model2.GoodsDetail
	stringIds := GetIds(ids)

	err, data = stbz.BatchGetGoodsDetailList(stringIds)

	if len(data) == 0 {
		return
	}

	for _, item := range ids {

		var product pservice.ProductForUpdate

		err = source.DB().Where("status_lock =0 and source_goods_id =  ? and gather_supply_id= ?", item, GoodsData.GatherSupplyID).Order("updated_at asc").Preload("Skus").First(&product).Error
		if product.ID == 0 {

			continue
		}

		//if product.SupplierSourceID > 0 {

		resData, _ := GetSplitSku(item, data)
		//resData = resData

		//}

		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			fmt.Println("回调查询商品不存在")
			continue
		}

		var goods model2.Goods
		goods.CostPrice = resData.CostPrice
		goods.GuidePrice = resData.GuidePrice
		goods.SalePrice = resData.SalePrice
		goods.ActivityPrice = resData.ActivityPrice
		goods.AgreementPrice = resData.AgreementPrice
		goods.MarketPrice = resData.MarketPrice
		goods.Source = resData.Source
		product.IsDisplay = int(resData.Status)
		ThirdID := strconv.Itoa(int(resData.ThirdID))

		if resData.Source == 2 {
			product.SourceGoodsIDString = ThirdID
			product.StartDateTime = ThirdID

		}

		var pcostPrice, psalePrice, pguidePrice uint
		err, pcostPrice, psalePrice, pguidePrice = service.GetPricingPrice(goods, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))

		if resData.Stock > 0 {
			product.Stock = resData.Stock

		}
		if stbz.Dat.UpdateInfo.CostPrice == 1 {
			product.CostPrice = pcostPrice
		}
		if stbz.Dat.UpdateInfo.CurrentPrice == 1 {
			product.Price = psalePrice
		}
		//product.IsDisplay = int(resData.Status)

		if stbz.Dat.UpdateInfo.OriginalPrice == 1 {

			product.OriginPrice = resData.MarketPrice
			product.GuidePrice = pguidePrice
			product.ActivityPrice = resData.SalePrice

			//product.Title=resData.
		}

		var skuList []pservice.Sku

		for _, skuItem := range resData.Specs.Options {
			if product.SupplierSourceID > 0 && skuItem.ID != product.Skus[0].OriginalSkuID {
				continue
			}

			var sku pservice.Sku

			source.DB().Where("original_sku_id=? and product_id=?", skuItem.ID, product.ID).First(&sku)

			//if skuItem.Status == 1 {
			var options pmodel.Options
			var option pmodel.Option
			var skuTitle string
			var skutitle string

			specValueIds := strings.Split(skuItem.SpecValueIds, "_")
			for _, itemA := range specValueIds {

				nameId, valueName := service.GetSpecValue(itemA, resData.Specs.Values)
				specName := service.GetSpecName(nameId, resData.Specs.Names)

				option.SpecName = specName
				option.SpecItemName = valueName
				skuTitle = skuTitle + valueName
				skutitle = skutitle + valueName + "/"
				options = append(options, option)
			}

			//if skuTitle == "" {
			//	continue
			//}

			var skuElem model2.Goods

			skuElem.Source = resData.Source
			skuElem.CostPrice = skuItem.CostPrice
			skuElem.GuidePrice = skuItem.GuidePrice
			skuElem.SalePrice = skuItem.SalePrice
			skuElem.ActivityPrice = skuItem.ActivityPrice
			skuElem.AgreementPrice = skuItem.AgreementPrice
			skuElem.MarketPrice = skuItem.MarketPrice
			var costPrice, salePrice, guidePrice uint
			err, costPrice, salePrice, guidePrice = service.GetPricingPrice(skuElem, "gatherSupply"+strconv.Itoa(int(product.GatherSupplyID)))

			sku.Title = skuTitle
			sku.Options = options
			sku.Weight = skuItem.Weight
			if sku.ProductID == 0 {
				sku.ProductID = product.ID
			}

			if sku.ID == 0 {
				sku.CostPrice = costPrice
				sku.Price = salePrice
				sku.GuidePrice = guidePrice
				sku.ActivityPrice = skuItem.ActivityPrice
				sku.OriginalSkuID = skuItem.ID
			} else {
				if stbz.Dat.UpdateInfo.CostPrice == 1 {
					sku.CostPrice = costPrice
				}
				if stbz.Dat.UpdateInfo.CurrentPrice == 1 {
					sku.Price = salePrice
				}
				if stbz.Dat.UpdateInfo.OriginalPrice == 1 {

					sku.GuidePrice = guidePrice
					sku.ActivityPrice = skuItem.ActivityPrice
					sku.OriginalSkuID = skuItem.ID
				}
			}

			sku.Stock = skuItem.Stock
			sku.IsDisplay = skuItem.Status
			sku.OriginPrice = skuItem.MarketPrice
			if skuItem.Image != "" {
				sku.ImageUrl = skuItem.Image
			}

			skuList = append(skuList, sku)

			//}

		}

		/**
		处理轮播图
		*/
		var galleryList pmodel.Gallery
		var galleryItem pmodel.GalleryItem
		for _, gItem := range resData.Covers {
			galleryItem.Type = 1
			galleryItem.Src = gItem
			galleryList = append(galleryList, galleryItem)
		}
		product.Gallery = galleryList

		if product.SourceGoodsID == 14114885 {
			log.Log().Info("stbz 更新商品", zap.Any("galleryList", galleryList))

			log.Log().Info("stbz 更新商品", zap.Any("product", product))
		}
		/**
		处理轮播图结束
		*/

		//if len(skuList)>0{
		//
		//}
		OptimizeSpecifications(skuList)
		product.Skus = skuList

		if len(product.Skus) == 1 {
			product.SingleOption = 1
			if product.Skus[0].Stock > 0 {
				product.Stock = uint(product.Skus[0].Stock)

			}

		} else {
			product.SingleOption = 0

		}

		if product.Price == 0 {
			product.IsDisplay = 0
		}

		itemSku := GetMap(product.Skus)

		product.CostPrice = itemSku.CostPrice
		product.GuidePrice = itemSku.GuidePrice
		product.OriginPrice = itemSku.OriginPrice
		product.ActivityPrice = itemSku.ActivityPrice
		product.Price = itemSku.Price
		product.ProfitRate = pservice.Decimal((float64(product.GuidePrice) - float64(product.Price)) / float64(product.GuidePrice) * 100)
		product.DetailImages = resData.Description
		product.ImageUrl = resData.Cover
		if product.SourceGoodsID == 14114885 {

			log.Log().Info("stbz 更新商品1", zap.Any("product1", product))
		}
		err = pservice.UpdateProduct(product)
		if product.SourceGoodsID == 14114885 {

			log.Log().Info("stbz 更新商品2", zap.Any("product2", product))
		}
		if err != nil {
			//log.Log().Info("GoodsAlertPriceCronTask 更新9", zap.Any("info", err))
			if product.SourceGoodsID == 14114885 {

				log.Log().Info("stbz 更新商品3", zap.Any("product3", err))
			}
			if err.Error() == "详情长度超出限制" {
				product.DetailImages = ""
				err = pservice.UpdateProduct(product)

			}

		}

	}

	return
}

func OptimizeSpecifications(data []pservice.Sku) {

	var specMap = make(map[string]uint)
	for _, skuItem := range data {

		for optionIndex, optionItem := range skuItem.Options {

			str := strconv.Itoa(optionIndex)
			key := str + "_" + optionItem.SpecName

			specMap[key] = specMap[key] + 1

		}

	}

	for skuIndex, skuItem := range data {

		for optionIndex, optionItem := range skuItem.Options {

			//if optionItem.SpecName == "" {
			str := strconv.Itoa(optionIndex)
			key := str + "_" + optionItem.SpecName

			name := GetSpecName(specMap, optionIndex, specMap[key])

			if name != "" {
				data[skuIndex].Options[optionIndex].SpecName = name

			}
			//}

		}

	}

	fmt.Println(specMap)

}

func GetSpecName(data map[string]uint, index int, count uint) string {

	for key, item := range data {
		keyArr := strings.Split(key, "_")

		if len(keyArr) < 2 {
			continue
		}
		if keyArr[1] == "" {
			continue
		}

		strIndex := strconv.Itoa(index)
		if keyArr[0] == strIndex && count <= item {
			return keyArr[1]

		}
		fmt.Println(key, item, keyArr)

	}

	return ""
}

func GetMap(skuList []pservice.Sku) (item pservice.Sku) {

	var maxPrice float64
	for _, sku := range skuList {
		ProfitRate := Decimal((float64(sku.GuidePrice) - float64(sku.Price)) / float64(sku.GuidePrice) * 100)
		if ProfitRate > maxPrice {
			maxPrice = ProfitRate
			item = sku
		}
	}
	if len(skuList) > 0 && maxPrice == 0 {
		item = skuList[0]
	}
	return

}

func Decimal(value float64) float64 {
	var err error
	value, err = strconv.ParseFloat(fmt.Sprintf("%0.2f", value), 64)
	if err != nil {
		return 0
	}
	return value
}

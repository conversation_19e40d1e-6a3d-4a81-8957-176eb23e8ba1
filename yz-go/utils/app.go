package utils

import (
	"github.com/gin-gonic/gin"
)

type App struct {
	ID     uint `json:"appID"`
	UserID uint `json:"user_id"`
}

// 从Gin的Context中获取从jwt解析出来的用户ID
func GetAppID(c *gin.Context) uint {
	if appID, exists := c.Get("appID"); !exists {
		return 0
	} else {
		id := appID.(uint)
		return id
	}
}

func GetAppShopID(c *gin.Context) uint {
	if appID, exists := c.Get("shopID"); !exists {
		return 0
	} else {
		id := appID.(uint)
		return id
	}
}

func GetAppUserID(c *gin.Context) uint {
	if userID, exists := c.Get("userID"); !exists {
		return 0
	} else {
		id := userID.(uint)
		return id
	}
}

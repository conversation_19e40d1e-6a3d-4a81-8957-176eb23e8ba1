package response

import (
	"database/sql/driver"
	"encoding/json"
)

type AmountDetail struct {
	Title       string      `json:"title"`        // 名
	Amount      int         `json:"amount"`       // 金额
	Qty         uint        `json:"qty"`          // 数量
	Desc        string      `json:"desc"`         // 说明
	AmountItems AmountItems `json:"amount_items"` // 金额列表
	Deduction   bool        `json:"deduction"`    // 是否是优惠金额
}

type AmountItems []AmountDetail

func (d *AmountDetail) Make() (err error) {
	var amount int
	for _, item := range d.AmountItems {
		if item.Deduction {
			item.Amount = -item.Amount // 如果是优惠金额，则取负值
		}
		amount += item.Amount
	}
	d.Amount = amount
	return
}

func (d *AmountDetail) AddItem(amountDetail AmountDetail) {
	d.AmountItems = append(d.AmountItems, amountDetail)
}

func (d AmountDetail) Value() (driver.Value, error) {
	return json.Marshal(d)
}
func (d *AmountDetail) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &d)
}

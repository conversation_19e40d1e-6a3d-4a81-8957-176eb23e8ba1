package model

import "yz-go/source"

type GatherSupply struct {
	IsPlugin uint `json:"is_plugin"`
	source.Model
	Name string `json:"name" form:"name" gorm:"column:name;comment:名;type:varchar(255);size:255;index;"` // 名
	//SupplyType        string `json:"supply_type" form:"supply_type" gorm:"column:supply_type;default:1;comment:类型;"`     // 类型
	Logo          string               `json:"logo" form:"logo" gorm:"column:logo;comment:logo;type:varchar(250);size:250;"`        //logo
	GoodsCount    uint                 `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品数量;"`    // 库存数量
	OrderCount    uint                 `json:"order_count" form:"order_count" gorm:"column:order_count;default:0;comment:订单数量;"`    // 订单数量
	OrderAmount   uint                 `json:"order_amount" form:"order_amount" gorm:"column:order_amount;default:0;comment:订单金额;"` // 订单金额
	CategoryID    uint                 `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
	SupplyBalance interface{}          `json:"supply_balance" form:"supply_balance" gorm:"-"`
	Category      GatherSupplyCategory `json:"category" `
}
type GatherSupplyCategory struct {
	source.Model
	Name   string `json:"name"`
	Key    string `json:"key"`
	IsHide uint   `json:"is_hide"`
}

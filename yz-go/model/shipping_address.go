package model

import (
	"yz-go/source"
)

type ShippingAddress struct {
	source.Model
	Realname   string `json:"realname" validate:"required" form:"realname" gorm:"column:realname;comment:收件人;type:varchar(30);size:30;"`  //收货人名称
	Mobile     string `json:"mobile" validate:"required" form:"mobile" gorm:"column:mobile;comment:收件人手机号;type:varchar(20);size:20 ;"`    //手机号
	CountryId  int    `json:"country_id" validate:"required" form:"country_id" gorm:"column:country_id;default:0;comment:国家id;type:int;"` //国家
	ProvinceId int    `json:"province_id" validate:"required" form:"province_id" gorm:"column:province_id;comment:省份id;type:int;"`        //省
	CityId     int    `json:"city_id" validate:"required" form:"city_id" gorm:"column:city_id;comment:城市id;type:int;"`                    //市
	CountyId   int    `json:"county_id" validate:"required" form:"county_id" gorm:"column:county_id;comment:区id;type:int;"`               //区
	TownId     int    `json:"town_id" validate:"required" form:"town_id" gorm:"column:town_id;comment:街道id;type:int;"`                    //街道
	Detail     string `json:"detail" validate:"required" form:"detail" gorm:"column:detail;comment:详细地址;type:text;"`                      //详细地址
	Lng        string `json:"lng" form:"lng" gorm:"column:lng;comment:经度;type:varchar(20);size:20;"`                                      // 经度
	Lat        string `json:"lat" form:"lat" gorm:"column:lat;comment:维度;type:varchar(20);size:20;"`                                      // 维度
}

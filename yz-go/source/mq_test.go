package source

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"testing"
	"yz-go/utils"
)

func TestNewRabbitMQConsume(t *testing.T) {
	var result map[string]interface{}

	err, resp := utils.Get("**********************************/api/overview", nil)
	if err != nil {
		println(err.Error())
	}
	if err = json.Unmarshal(resp, &result); err != nil {
		println(err.Error())
	}
	println(result)
}

type NodeInfo struct {
	Name            string `json:"name"`
	Uptime          int64  `json:"uptime"`
	Processors      int    `json:"processors"`
	Memory          uint64 `json:"memory"`
	DiskFree        uint64 `json:"disk_free"`
	DiskFreeLimit   uint64 `json:"disk_free_limit"`
	FileDescriptors uint64 `json:"fd_total"`
	Sockets         uint64 `json:"sockets_total"`
}

func TestRabbitMQInfo(t *testing.T) {
	url := "http://localhost:15672/api/nodes"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		panic(err)
	}
	req.SetBasicAuth("guest", "guest")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	var nodes []NodeInfo
	err = json.Unmarshal(body, &nodes)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", nodes)
}

type ConnectionInfo struct {
	Name             string `json:"name"`
	User             string `json:"user"`
	VHost            string `json:"vhost"`
	Type             string `json:"type"`
	Protocol         string `json:"protocol"`
	Port             int    `json:"port"`
	PeerAddress      string `json:"peer_address"`
	ConnectedAt      string `json:"connected_at"`
	Channels         int    `json:"channels"`
	FrameMax         int    `json:"frame_max"`
	Timeout          int    `json:"timeout"`
	ClientProperties struct {
		Capabilities struct {
			AuthenticationFailureClose bool `json:"authentication_failure_close"`
			BasicNack                  bool `json:"basic.nack"`
			ConsumerCancelNotify       bool `json:"consumer_cancel_notify"`
			ConnectionBlocked          bool `json:"connection.blocked"`
			ConnectionClose            bool `json:"connection.close"`
			ConnectionOpen             bool `json:"connection.open"`
			PublisherConfirms          bool `json:"publisher_confirms"`
			Qos                        bool `json:"qos"`
		} `json:"capabilities"`
		Information struct {
			Platform string `json:"platform"`
			Product  string `json:"product"`
			Version  string `json:"version"`
		} `json:"information"`
	} `json:"client_properties"`
	ServerProperties struct {
		Capabilities struct {
			AuthenticationFailureClose bool `json:"authentication_failure_close"`
			BasicNack                  bool `json:"basic.nack"`
			ConsumerCancelNotify       bool `json:"consumer_cancel_notify"`
			ConnectionBlocked          bool `json:"connection.blocked"`
			ConnectionClose            bool `json:"connection.close"`
			ConnectionOpen             bool `json:"connection.open"`
			PublisherConfirms          bool `json:"publisher_confirms"`
			Qos                        bool `json:"qos"`
		} `json:"capabilities"`
		Information struct {
			Platform string `json:"platform"`
			Product  string `json:"product"`
			Version  string `json:"version"`
		} `json:"information"`
	} `json:"server_properties"`
}

func getConnectionInfo() {
	url := "http://localhost:15672/api/connections"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		panic(err)
	}
	req.SetBasicAuth("guest", "guest")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	var connections []ConnectionInfo
	err = json.Unmarshal(body, &connections)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", connections)
}

type ChannelInfo struct {
	DeliverNoAck     int `json:"deliver_no_ack"`
	Deliver          int `json:"deliver"`
	Publish          int `json:"publish"`
	ReturnUnroutable int `json:"return_unroutable"`
	Confirm          int `json:"confirm"`
	Return           int `json:"return"`
	UncommittedAck   int `json:"uncommitted_ack"`
	Unconfirmed      int `json:"unconfirmed"`
	UncommittedTx    int `json:"uncommitted_tx"`
	Unacknowledged   int `json:"unacknowledged"`
	Redeliver        int `json:"redeliver"`
	Flow             int `json:"flow"`
	ChannelDetails   struct {
		User            string `json:"user"`
		VHost           string `json:"vhost"`
		Name            string `json:"name"`
		Node            string `json:"node"`
		Type            string `json:"type"`
		Durable         bool   `json:"durable"`
		AutoDelete      bool   `json:"auto_delete"`
		Internal        bool   `json:"internal"`
		Policy          string `json:"policy"`
		ConsumerCount   int    `json:"consumer_count"`
		Messages        int    `json:"messages"`
		MessagesDetails struct {
			Rate float64 `json:"rate"`
		} `json:"messages_details"`
		MessageBytes        int `json:"message_bytes"`
		MessageBytesDetails struct {
			Rate float64 `json:"rate"`
		} `json:"message_bytes_details"`
		Ready          int `json:"messages_ready"`
		Unacknowledged int `json:"messages_unacknowledged"`
		Confirm        int `json:"confirm"`
		Transaction    int `json:"transaction"`
		ConfirmDetails struct {
			Rate float64 `json:"rate"`
		} `json:"confirm_details"`
		ReturnUnroutable    int     `json:"return_unroutable"`
		ConsumerUtilisation float64 `json:"consumer_utilisation"`
	} `json:"channel_details"`
}

func getChannelInfo() {
	url := "http://localhost:15672/api/channels"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		panic(err)
	}
	req.SetBasicAuth("guest", "guest")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	var channels []ChannelInfo
	err = json.Unmarshal(body, &channels)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", channels)
}

type QueueInfo struct {
	Name                 string  `json:"name"`
	VHost                string  `json:"vhost"`
	Durable              bool    `json:"durable"`
	AutoDelete           bool    `json:"auto_delete"`
	Exclusive            bool    `json:"exclusive"`
	Consumers            int     `json:"consumers"`
	Messages             int     `json:"messages"`
	MessageBytes         int     `json:"message_bytes"`
	MessageBytesPct      float64 `json:"message_bytes_pct"`
	AvgIngressRate       float64 `json:"avg_ingress_rate"`
	AvgEgressRate        float64 `json:"avg_egress_rate"`
	Memory               int     `json:"memory"`
	State                string  `json:"state"`
	Node                 string  `json:"node"`
	Sync                 bool    `json:"sync"`
	Type                 string  `json:"type"`
	ExclusiveConsumerTag string  `json:"exclusive_consumer_tag"`
	MessageStats         struct {
		Publish           int     `json:"publish"`
		Deliver           int     `json:"deliver"`
		DeliverGet        int     `json:"deliver_get"`
		DeliverNoAck      int     `json:"deliver_no_ack"`
		DeliverNoAckGet   int     `json:"deliver_no_ack_get"`
		Ack               int     `json:"ack"`
		AckDetails        Details `json:"ack_details"`
		Redeliver         int     `json:"redeliver"`
		RedeliverDetails  Details `json:"redeliver_details"`
		DeliverGetDetails Details `json:"deliver_get_details"`
		DeliverDetails    Details `json:"deliver_details"`
		Get               int     `json:"get"`
		GetDetails        Details `json:"get_details"`
		PublishDetails    Details `json:"publish_details"`
	} `json:"message_stats"`
}

type Details struct {
	Rate float64 `json:"rate"`
}

func getQueueInfo() {
	url := "http://localhost:15672/api/queues"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		panic(err)
	}
	req.SetBasicAuth("guest", "guest")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	var queues []QueueInfo
	err = json.Unmarshal(body, &queues)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", queues)
}

type ConsumerInfo struct {
	ConsumerTag  string  `json:"consumer_tag"`
	QueueName    string  `json:"queue_name"`
	Exclusive    bool    `json:"exclusive"`
	ConsumerUtil float64 `json:"consumer_utilisation"`
	Arguments    struct {
	} `json:"arguments"`
	// 以下字段是针对消费者的信息
	Active         bool `json:"active"`
	ChannelDetails struct {
		User             string `json:"user"`
		VHost            string `json:"vhost"`
		Name             string `json:"name"`
		Node             string `json:"node"`
		Type             string `json:"type"`
		Durable          bool   `json:"durable"`
		AutoDelete       bool   `json:"auto_delete"`
		Internal         bool   `json:"internal"`
		Policy           string `json:"policy"`
		ConsumerCount    int    `json:"consumer_count"`
		Messages         int    `json:"messages"`
		Acknowledgements struct {
			Rate float64 `json:"rate"`
		} `json:"acknowledgements"`
		Deliveries struct {
			Rate float64 `json:"rate"`
		} `json:"deliveries"`
		DeliveriesGet struct {
			Rate float64 `json:"rate"`
		} `json:"deliveries_get"`
		DeliveriesNoAck struct {
			Rate float64 `json:"rate"`
		} `json:"deliveries_no_ack"`
		Redeliveries struct {
			Rate float64 `json:"rate"`
		} `json:"redeliveries"`
	} `json:"channel_details"`
}

func getConsumerInfo() {
	url := "http://localhost:15672/api/consumers"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		panic(err)
	}
	req.SetBasicAuth("guest", "guest")
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		panic(err)
	}
	var consumers []ConsumerInfo
	err = json.Unmarshal(body, &consumers)
	if err != nil {
		panic(err)
	}
	fmt.Printf("%+v\n", consumers)
}

package middleware

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

var responses map[string][]byte

// GetCache 缓存高频api
func GetCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method!= http.MethodGet {
			c.Next()
			return
		}
		key := c.Request.RequestURI
		if expTime, ok := uris[key]; ok {
			// 需要读缓存
			if time.Now().Unix() <= expTime {
				// 未过期
				response, ok := responses[key]
				if ok {
					c.Data(http.StatusOK, "application/json", response)
					c.Abort()
				}
			}
		}
		// 处理请求
		c.Next()
	}
}

type responseBodyWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (r responseBodyWriter) Write(b []byte) (int, error) {
	r.body.Write(b)
	return r.ResponseWriter.Write(b)
}

var uris map[string]int64

// SetCache 缓存高频api
func SetCache() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method!= http.MethodGet {
			c.Next()
			return
		}
		key := c.Request.RequestURI
		expTime, ok := uris[key]
		if ok && time.Now().Unix() < expTime {
			// 写过缓存 未过期
			c.Next()
		}else{
			var writer responseBodyWriter

			writer = responseBodyWriter{
				ResponseWriter: c.Writer,
				body:           &bytes.Buffer{},
			}
			c.Writer = writer
			c.Next()
			// 需要写缓存
			halfMin, _ := time.ParseDuration("+1m")
			nextExpTime := time.Now().Add(halfMin)
			// 缓存响应
			responses[key] = writer.body.Bytes()
			// 1分钟后过期
			uris[key] = nextExpTime.Unix()
		}

	}
}

func init() {
	uris = map[string]int64{
		//"/api/order/list?status=1":0,
	}
	responses = map[string][]byte{}
}

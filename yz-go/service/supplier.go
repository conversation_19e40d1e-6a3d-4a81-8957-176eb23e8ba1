package service

import (
	"yz-go/source"
)

type Supplier struct {
	source.Model
	Uid    uint `json:"uid"`
	UserId uint `json:"user_id"`
}

func (Supplier) TableName() string {
	return "suppliers"
}

type SupplierClerk struct {
	source.Model
	Uid    int `json:"uid"`
	Sid    int `json:"sid"`
	UserId int `json:"user_id"`
}

func (SupplierClerk) TableName() string {
	return "supplier_clerks"
}

func IsSupplier(operatorId uint) bool {
	var supplier Supplier
	// 如果是供应商，则返回true
	source.DB().Where("user_id = ?", operatorId).First(&supplier)
	if supplier.ID > 0 {
		return true
	}

	var clerk SupplierClerk
	source.DB().Where("user_id = ?", operatorId).First(&clerk)
	if clerk.ID > 0 {
		source.DB().Where("id = ?", clerk.Sid).First(&supplier)
		if supplier.ID > 0 {
			return true
		}
	}

	return false
}

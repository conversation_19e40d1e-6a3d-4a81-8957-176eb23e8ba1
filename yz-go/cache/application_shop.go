package cache

import (
	"errors"
	"gorm.io/gorm"
	"sync"
	"yz-go/model"
	"yz-go/source"
)

// ApplicationShopCache 使用 sync.Map 替代普通 map，它是并发安全的，专门用于高并发场景
var ApplicationShopCache sync.Map

// GetApplicationShop 获取应用商城缓存
func GetApplicationShop(shopID interface{}) (shop *model.ApplicationShop, exists bool) {
	value, exists := ApplicationShopCache.Load(shopID)
	if !exists {
		return nil, false
	}
	shop = value.(*model.ApplicationShop)
	return shop, true
}

// SetApplicationShop 设置应用商城缓存
func SetApplicationShop(shopID interface{}, shop *model.ApplicationShop) {
	ApplicationShopCache.Store(shopID, shop)
}

// ClearApplicationShop 清除指定应用商城缓存
func ClearApplicationShop(shopID interface{}) {
	ApplicationShopCache.Delete(shopID)
}

// ClearAllApplicationShops 清除所有应用商城缓存
func ClearAllApplicationShops() {
	ApplicationShopCache = sync.Map{}
}

// GetApplicationShopFromCache 获取应用商城信息，优先从缓存获取
func GetApplicationShopFromCache(id uint) (*model.ApplicationShop, error) {
	shop, exists := GetApplicationShop(id)
	if !exists {
		// 缓存未命中，查询数据库
		var shopModel model.ApplicationShop
		err := source.DB().Where("id = ?", id).First(&shopModel).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &shopModel, nil
		}
		if err != nil {
			return nil, err
		}
		// 写入全局缓存
		SetApplicationShop(id, &shopModel)
		shop = &shopModel
	}
	return shop, nil
}

// GetApplicationShopsByAppID 获取指定应用ID下的所有商城
func GetApplicationShopsByAppID(appID uint) ([]*model.ApplicationShop, error) {
	var shops []*model.ApplicationShop
	err := source.DB().Where("application_id = ?", appID).Find(&shops).Error
	if err != nil {
		return nil, err
	}

	// 更新缓存
	for _, shop := range shops {
		SetApplicationShop(shop.ID, shop)
	}

	return shops, nil
}

// GetApplicationShopByAppIDAndShopID 通过应用ID和商城ID获取商城信息
func GetApplicationShopByAppIDAndShopID(appID uint, shopID uint) (*model.ApplicationShop, error) {
	shop, exists := GetApplicationShop(shopID)
	if exists && shop.ApplicationID == appID {
		return shop, nil
	}

	// 缓存未命中或应用ID不匹配，查询数据库
	var shopModel model.ApplicationShop
	err := source.DB().Where("id = ? AND application_id = ?", shopID, appID).First(&shopModel).Error
	if err != nil {
		return nil, err
	}

	// 写入缓存
	SetApplicationShop(shopID, &shopModel)
	return &shopModel, nil
}

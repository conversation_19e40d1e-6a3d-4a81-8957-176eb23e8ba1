package service

import (
	"errors"
	"finance/model"
	"finance/request"
	"gorm.io/gorm"
	model2 "order/model"
	"yz-go/source"
)

//分账后退款
func ConvergenceRefund(requestRefund request.Refund) (err error) {

	var supplierSettlement model.SupplierSettlement
	err = source.DB().Preload("SettlementSubAccount", "amount > 0").Where("order_id = ?", requestRefund.OrderID).First(&supplierSettlement).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("未查询到结算订单流水,无法退款")
		return
	}
	if len(supplierSettlement.SettlementSubAccount) <= 0 {
		err = errors.New("订单结算分账数据异常")
		return
	}
	var order model2.Order
	source.DB().First(&order, "id=?", requestRefund.OrderID)
	if order.RefundAmount <= 0 {
		err = errors.New("退款金额不能小于0")
		return
	}

	source.DB().Where("id=?", supplierSettlement.ID).Delete(&model.SupplierSettlement{})
	//
	//
	//supplierSettlement.RefundAmount=order.RefundAmount
	////先从新计算供应商结算流水数据
	//supplierSettlement.SettlementAmount=supplierSettlement.SupplyAmount-order.RefundAmount
	//if supplierSettlement.SupplierID >0{
	//	var amounts,fee,tFee uint
	//	_, fee, amounts, tFee, err = GetSupplierSettlementCalculation(supplierSettlement.SupplierID, supplierSettlement.SupplyAmount -supplierSettlement.RefundAmount)
	//	if err!=nil{
	//		log.Log().Error("err", zap.Any("GetSupplierSettlementCalculation汇聚余额退款错误", err.Error()))
	//		return
	//	}
	//
	//	supplierSettlement.SettlementAmount=amounts
	//	supplierSettlement.TechnicalServiceCost=tFee
	//	supplierSettlement.WithdrawalFee=fee
	//
	//
	//}
	//err=source.DB().Save(&supplierSettlement).Error
	//if err!=nil{
	//	log.Log().Error("err", zap.Any("GetSupplierSettlementCalculation保存结算出错", err.Error()))
	//	return
	//}
	//
	SubAccountSettlement(requestRefund.RefundSN, order.RefundAmount, supplierSettlement) //结算汇聚子账户分账数据

	return
}

func RefundAll() {

	//for _, item := range supplierSettlement.SettlementSubAccount {
	//
	//	refund.P2_OrderNo = strconv.Itoa(int(item.UserTopUp))
	//	refund.P3_RefundOrderNo = requestRefund.RefundSN
	//	refund.P4_RefundAmount = Fen2Yuan(item.Amount)
	//	item.RefundOrderNo = refund.P3_RefundOrderNo
	//	item.RefundSN = requestRefund.RefundSN
	//	err, resData := joinService.NoSepareteRefundAction(refund)
	//	if err != nil {
	//		log.Log().Error("err", zap.Any("汇聚余额退款错误", err.Error()))
	//		log.Log().Error("err", zap.Any("汇聚余额退款错误1", refund))
	//		log.Log().Error("err", zap.Any("汇聚余额退款错误2", item))
	//	}
	//	jsonInfo, _ := json.Marshal(&resData)
	//	item.RefundInfo = string(jsonInfo)
	//	if resData["rb_Code"] == 100 || resData["rb_Code"] == "100" {
	//		item.RefundAction = 1
	//	} else {
	//		item.RefundAction = 10
	//	}
	//	source.DB().Save(&item)
	//	fmt.Println("退款结果", resData)
	//
	//}
}

func SubAccountSettlement(refundSN string, refundAmount uint, supplierSettlement model.SupplierSettlement) {

	for _, item := range supplierSettlement.SettlementSubAccount {

		source.DB().Model(model.SettlementSubAccount{}).Where("pay_sn=?", item.UserTopUp).Update("remaining_amount", gorm.Expr("remaining_amount+?", item.Amount))
		source.DB().Delete(&item, "id=?", item.ID)
		//if refundAmount == 0 {
		//	break
		//}
		//
		//if item.Amount < refundAmount {
		//	refundAmount = refundAmount - item.Amount
		//	RAmount = item.Amount
		//	item.Amount = 0
		//	item.SupplierAmount = 0
		//} else {
		//	item.Amount = item.Amount - refundAmount
		//	RAmount = refundAmount
		//	var amounts uint
		//	_, _, amounts, _, _ = GetSupplierSettlementCalculation(item.SupplierID, item.Amount)
		//	item.SupplierAmount =item.Amount
		//	if item.SupplierID >0{
		//		item.SupplierAmount = amounts
		//	}
		//	refundAmount = 0
		//
		//}
		//
		//refund.P2_OrderNo = strconv.Itoa(int(item.UserTopUp))
		//refund.P3_RefundOrderNo = utils.GetOrderNo()
		//refund.P4_RefundAmount = Fen2Yuan(RAmount)

		//err, resData := joinService.NoSepareteRefundAction(refund)
		//if err != nil {
		//	log.Log().Error("err", zap.Any("汇聚余额退款错误", err.Error()))
		//	log.Log().Error("err", zap.Any("汇聚余额退款错误1", refund))
		//	log.Log().Error("err", zap.Any("汇聚余额退款错误2", item))
		//}
		//jsonInfo, _ := json.Marshal(&resData)
		//
		//source.DB().Create(&model.ConvergenceRefund{
		//	RefundAmount:           RAmount,
		//	TotalAmount:            TotalAmount,
		//	RefundSN:               refundSN,
		//	RefundOrder:            refund.P3_RefundOrderNo,
		//	OrderSN:                supplierSettlement.OrderSn,
		//	RefundInfo:             string(jsonInfo),
		//	UserTopUpID:            item.UserTopUp,
		//	SettlementSubAccountID: item.ID,
		//	SupplierSettlementID:   supplierSettlement.ID,
		//})
		//
		//source.DB().Save(&item)

	}
}

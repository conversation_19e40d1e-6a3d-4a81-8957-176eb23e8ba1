package listener

import (
	"order/model"
	"testing"
	"yz-go/source"
)

func TestPublishMessage(t *testing.T) {

	var order model.Order
	source.DB().Where("id=?", 105).First(&order)
	ConvergenceRefund(order, ************)

	//aaa := 1100 / 100
	//
	//fmt.Println("比例", aaa)

	//var aaa model2.WithdrawalDetail
	//aaa.SupplierSettlementID = 123
	//source.DB().Where(model2.WithdrawalDetail{SupplierSettlementID: 123}).FirstOrCreate(&aaa)

	//fee := 19429 * uint(100) / 10000
	//
	//fmt.Println(fee)

	//var supplierAccount model2.AccountApply
	//err := source.DB().Where("member_id = ?", 534535).First(&supplierAccount).Error
	//if err != nil {
	//	log2.Println("手动阀三点发水电费", err.Error())
	//	return
	//}

	//var order *model.Order
	//source.DB().Where("id=2").First(&order)
	//SupplierSettlementSeparate(order)
	//_ = pmq.PublishMessage(************, 5)
	//_ = fmq.PublishMessage(2)

	//nns:=strings.Split("1111","_")
	//strconv.Itoa(1111)
	//aaa:=collection.Collect(nns).Contains(strconv.Itoa(1111))
	////if collection(1111) {
	////
	////}
	//
	//
	//fmt.Println("结果",aaa)
	//return

	//err := mq.PublishMessage(50, mq.Paid)
	//
	//if err != nil {
	//	fmt.Println("push错误", err)
	//}
}

func TestSupplierSettlementWechatSeparate(t *testing.T) {

	var order model.Order

	source.DB().Where("id= ?", 26).First(&order)

	//SupplierSettlementBalance(order)

	//
	////service.PlatformSeparateWechatAccount(order,****************)
	SupplierSettlementRecord(order, ************)
	//
	//
	//_, node := leaf.NewNode(20)
	//startTime := time.Date(2020, 0, 0, 0, 0, 0, 0, time.UTC).UnixNano() / 1000000
	//// 从2020年开始
	//_ = node.SetSince(startTime)
	//_, id := node.NextId()
	//_, id1 := node.NextId()
	//
	//fmt.Println("侧首1",id)
	//fmt.Println("侧首2",id1)
	//log.Log().Info("获取二维码成功2222222!", zap.Any("info", id1))
	//log.Log().Info("SupplierSettlementWechatSeparate微信分账!", zap.Any("err", id))

	//var  fff uint=15670
	//
	//fffa:=service.Fen2YuanF(fff)
	//
	//
	//fmt.Println("金额：",fffa)

	//settlementAmount:=service.Fen2Yuan(uint(payFee)+fff)

	//fmt.Println("分账包含手续费",settlementAmount)
}

//type OrderMessage struct {
//	MessageType int  `json:"message_type"`
//	OrderID     uint `json:"order_id"`
//	OrderSN     string `json:"order_sn"`
//}

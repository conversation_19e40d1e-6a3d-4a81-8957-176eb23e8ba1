package model

import "yz-go/source"

type TransferRecord struct {
     source.Model
	PaySN uint `json:"pay_sn"`
	OrderSN uint `json:"order_sn"`
	OrderID uint `json:"order_id"`
	Type uint `json:"type"`      //1是供应商分账，其余是给平台分手续费
	Status int `json:"status"`
	AltMchNo string `json:"alt_mch_no"`
	TransferAt  *source.LocalTime `json:"transfer_at"`
	Msg string  `json:"msg" gorm:"type:text;"`
	PostData string `json:"post_data" gorm:"type:text;"`


}

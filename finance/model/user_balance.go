package model

import (
	"yz-go/request"
	"yz-go/source"
)

type UserLevel struct {
	source.Model
	Name                    string `json:"name" form:"name" gorm:"column:name;comment:会员等级名称;type:char(20);size:20;"`
	Level                   int    `json:"level" form:"level" gorm:"column:level;comment:等级权重;type:smallint;size:6;"`
	CpsRatio                int    `json:"cps_ratio"`
	DouyinGroupRatio        int64  `json:"douyin_group_ratio"`
	MeituanDistributorRatio uint   `json:"meituan_distributor_ratio"`
}

// 结构体基类
type Balance struct {
	PurchasingBalance uint `json:"purchasing_balance" form:"purchasing_balance" gorm:"column:purchasing_balance;comment:采购余额;type:bigint;size:100;"`
	SettlementBalance uint `json:"settlement_balance" form:"settlement_balance" gorm:"column:settlement_balance;comment:结算余额;type:bigint;size:100;"`
	Type              int  `json:"type" form:"type" gorm:"column:type;comment:余额类型(1汇聚2平台);type:smallint;size:1;index;"` //1汇聚 2 站内
	Uid               int  `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;index;"`
}

// 站内账户余额
type AccountBalance struct {
	source.Model
	Balance
}

type User struct {
	source.Model
	Mobile             string           `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar             string           `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username           string           `json:"username"  form:"username" gorm:"comment:用户登录名"`
	Password           string           `json:"-"  gorm:"comment:用户登录密码"`
	NickName           string           `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status             int              `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	LevelId            int              `json:"level_id" form:"level_id" gorm:"column:level_id;comment:会员等级;type:smallint;size:1;"`
	WxOpenid           string           `json:"wx_openid" form:"wx_openid" `
	WxMiniOpenid       string           `json:"wx_mini_openid" form:"wx_mini_openid" `
	WxUnionid          string           `json:"wx_unionid" form:"wx_unionid" `
	AccountBalanceInfo []AccountBalance `gorm:"foreignKey:Uid;"`
	UserLevelInfo      UserLevel        `gorm:"foreignKey:LevelId"`
}
type UserIncomeSummary struct {
	source.Model
	Mobile                     string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Username                   string `json:"username"  form:"username" gorm:"comment:用户登录名"`
	NickName                   string `json:"nickname" form:"nickName" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
	Status                     int    `json:"status" form:"status" gorm:"column:status;comment:状态（-1拉黑0待审核1正常）;type:smallint;size:1;"`
	LevelId                    int    `json:"level_id" form:"level_id" gorm:"column:level_id;comment:会员等级;type:smallint;size:1;"`
	IsWithdrawal               uint   `json:"is_withdrawal" form:"is_withdrawal" gorm:"column:is_withdrawal;comment:已提现金额"`
	TotalWithdrawal            uint   `json:"total_withdrawal" form:"total_withdrawal" gorm:"column:total_withdrawal;comment:总计"`
	NotWithdrawal              uint   `json:"not_withdrawal" form:"not_withdrawal" gorm:"column:not_withdrawal;comment:未提现金额"`
	InWithdrawal               uint   `json:"in_withdrawal" form:"in_withdrawal" gorm:"column:in_withdrawal;comment:提现中金额"`
	PaymentAmount              uint   `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:已打款"`
	Invalid                    uint   `json:"invalid" form:"invalid" gorm:"column:invalid;comment:无效金额"`
	IsWithdrawalServiceTax     uint   `json:"is_withdrawal_service_tax" form:"is_withdrawal_service_tax" `
	IsWithdrawalPoundageAmount uint   `json:"is_withdrawal_poundage_amount" form:"is_withdrawal_poundage_amount" `
	InWithdrawalServiceTax     uint   `json:"in_withdrawal_service_tax" form:"in_withdrawal_service_tax" `
	InWithdrawalPoundageAmount uint   `json:"in_withdrawal_poundage_amount" form:"in_withdrawal_poundage_amount"`
	// 累计手续费
	PoundageAmountTotal uint `json:"poundage_amount_total"`
	// 累计劳务税
	ServiceTaxTotal uint `json:"service_tax_total"`
}

// 汇聚微信发起支付
type WechatPay struct {
	PaySN  uint `json:"pay_sn" form:"pay_sn" `
	Uid    uint `json:"uid" form:"uid"`
	Amount uint `json:"amount" form:"amount" `
}

// 用户发起充值记录
type UserTopUp struct {
	source.Model
	PaySN           uint              `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:支付订单号;"`
	Uid             uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;comment:用户id;"`
	Amount          uint              `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	RemainingAmount uint              `json:"remaining_amount" form:"remaining_amount" gorm:"default:0;column:remaining_amount;comment:剩余金额;"`
	PayType         int               `json:"pay_type" form:"pay_type" gorm:"column:pay_type;comment:充值方式(1汇聚2平台余额);type:smallint;size:1;"`
	PayAt           *source.LocalTime `json:"pay_at"` // 支付时间
	PayStatus       int               `json:"pay_status" form:"pay_status" gorm:"default:0;column:pay_status;comment:支付状态;type:smallint;size:1;"`
	PayCode         int               `json:"pay_code" form:"pay_code" gorm:"column:pay_code;comment:支付类型(1小商店小程序);type:smallint;size:1;"`
	request.PageInfo
}
type UserTopUpModel struct {
	source.Model
	PaySN           uint              `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:支付订单号;"`
	Uid             uint              `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;comment:用户id;"`
	Amount          uint              `json:"amount" form:"amount" gorm:"column:amount;comment:充值金额;type:int;"`
	RemainingAmount uint              `json:"remaining_amount" form:"remaining_amount" gorm:"default:0;column:remaining_amount;comment:剩余金额;"`
	PayType         int               `json:"pay_type" form:"pay_type" gorm:"column:pay_type;comment:充值方式(1汇聚2平台余额);type:smallint;size:1;"`
	PayAt           *source.LocalTime `json:"pay_at"` // 支付时间
	PayStatus       int               `json:"pay_status" form:"pay_status" gorm:"default:0;column:pay_status;comment:支付状态;type:smallint;size:1;"`
	PayCode         int               `json:"pay_code" form:"pay_code" gorm:"column:pay_code;comment:支付类型(1小商店小程序);type:smallint;size:1;"`
}

func (UserTopUpModel) TableName() string {
	return "user_top_ups"
}

// 更新余额
type UpdateBalance struct {
	Uid           uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员ID;type:bigint;size:100;"`
	Amount        uint   `json:"amount" form:"amount" gorm:"column:amount;comment:金额;type:int;"`
	Type          int    `json:"type" form:"type" gorm:"column:type;comment:余额类型(1汇聚2平台);type:int;"`
	PayType       int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type"`
	OperationType int    `json:"operation_type" form:"operation_type" gorm:"column:operation_type;comment:1采购余额，2结算余额;type:int;"` //1采购余额，2结算余额
	Action        int    `json:"action" form:"action" gorm:"column:action;comment:操作(1增/2减);type:int;"`
	PayInfoID     int    `json:"pay_info_id" form:"pay_info_id"`
	Remarks       string `json:"remarks" form:"remarks"`
}

// 订单状态
type PayStatus struct {
	PaySN int `json:"pay_sn" form:"pay_sn" `
}

type TransferAccounts struct {
	TransferUid uint `json:"transfer_uid" form:"transfer_uid"` //转账人
	CollectUid  uint `json:"collect_uid" form:"collect_uid"`   //收帐人
	Amount      uint `json:"amount" form:"amount"`             //金额 分
}

package order

import (
	afterSalesModel "after-sales/model"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	url2 "net/url"
	model3 "order/model"
	model4 "product/model"
	callback2 "public-supply/callback"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"time"
	"yunzhonghe-supply/common"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type Yzh struct {
	dat      *model.SupplySetting
	SupplyID uint
	Key      string
}

func (y *Yzh) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (y *Yzh) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []afterSalesModel.AfterSalesType) {
	return
}

func (y *Yzh) GetAllAddress() (err error, data interface{}) {
	return
}

func (y *Yzh) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (y *Yzh) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (y *Yzh) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (y *Yzh) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (y *Yzh) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

func (y *Yzh) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID
	y.Key = "yzh"
	var setting model2.SysSetting

	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	common.YZH_HTTP_URL = y.dat.BaseInfo.ApiUrl

	return
}
func (y *Yzh) CancelOrder(orderID uint) {

	var order model3.Order
	err := source.DB().Where("id=?", orderID).First(&order).Error
	if err != nil {
		log.Log().Info("yzh供应链订单取消错误", zap.Any("info", err))
		return
	}

	if order.GatherSupplyType == 4 && order.GatherSupplySN != "" {
		y.InitSetting(order.GatherSupplyID)
		log.Log().Info("yzh供应链订单取消order开始", zap.Any("info", order))

		thirdOrder := strconv.Itoa(int(order.OrderSN))

		url := string(common.YZH_HTTP_URL + "api/order/cancelByOrderKey.php")
		var resData []byte
		headerData := make(map[string]string)
		timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
		reqData := url2.Values{}
		reqData.Add("wid", y.dat.BaseInfo.AppKey)
		reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
		reqData.Add("timestamp", timeUnix)
		reqData.Add("thirdOrder", thirdOrder)
		reqData.Add("orderKey", order.GatherSupplySN)
		err, resData = utils.PostForm(url, reqData, headerData)

		var resCancel ResCancel
		err = json.Unmarshal(resData, &resCancel)
		if err != nil {
			return
		}
		if resCancel.RESPONSESTATUS == "true" {
			source.DB().Table("orders").Where("id=?", orderID).UpdateColumn("gather_supply_sn", gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", resCancel.RESULTDATA))
		}
		log.Log().Info("yzh供应链订单取消resData", zap.Any("info", resData))

		jsondata, err := json.Marshal(reqData)
		log.Log().Info("yzh供应链订单取消jsondata", zap.Any("info", jsondata))

		if err != nil {
			log.Log().Info("yzh供应链订单取消错误err", zap.Any("info", err))

			return
		}

	}

}

type ResCancel struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     string `json:"RESULT_DATA"`
}

// 订单前置校验  返回运费
func (y *Yzh) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.BeforeCheck) {
	//var saleRes response.ResSaleBeforeCheck
	res.Code = 1

	var pids string
	for _, item := range request.LocalSkus {
		var id string
		var skus model4.Sku
		source.DB().Preload(clause.Associations).Where("id=?", item.Sku.Sku).First(&skus)
		id = strconv.Itoa(int(skus.OriginalSkuID))
		if request.GatherSupplyID == 0 {

			id = strconv.Itoa(int(skus.Product.SourceGoodsID))
		}

		num := strconv.Itoa(item.Number)
		pids += id + "_" + num + ","
	}
	//pids = pids[0 : len(pids)-1]

	fmt.Println(pids)
	pids = strings.TrimRight(pids, ",")
	fmt.Println(pids)

	if pids == "" {
		res.Code = 0
		res.Msg = "请求sku异常"
		return
	}

	var addressData model.ReqAddress
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description
	fmt.Println("全地址：", address)
	err, addressData = y.GetAreaCodeByAddress(address)
	if addressData.RESPONSESTATUS == "false" {
		log.Log().Info("yzh供应链商品解析省市区id接口失败1", zap.Any("info", addressData))
		return

	}
	reqAddress := strconv.Itoa(addressData.RESULTDATA.ProvinceId) + "_" + strconv.Itoa(addressData.RESULTDATA.CityId) + "_" + strconv.Itoa(addressData.RESULTDATA.CountyId)

	url := string(common.YZH_HTTP_URL + common.YZH_GOODSSTORAGE)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("pid_nums", pids)
	reqData.Add("address", reqAddress)
	err, resData = utils.PostForm(url, reqData, headerData)
	fmt.Println(string(resData))
	aaa, err := json.Marshal(reqData)
	fmt.Println(string(aaa))
	if err != nil {
		log.Log().Error("yzh 获取地址", zap.Any("info", err))
		return
	}

	var stockStatusList model.StockNumber
	err = json.Unmarshal(resData, &stockStatusList)
	if err != nil {
		log.Log().Error("SaleBeforeCheck 解析失败1", zap.Any("info", resData))
		log.Log().Error("SaleBeforeCheck 解析失败", zap.Any("info", err))
		return
	}

	if stockStatusList.RESPONSESTATUS == "false" {
		res.Code = 1
		res.Msg = "请求检测库存错误"
		log.Log().Error("OrderBeforeCheck RESPONSESTATUS 检测库存错误", zap.Any("info", stockStatusList))
		return
	}

	for _, item := range stockStatusList.RESULTDATA {
		if item.StockStatus == false {
			res.Code = 0
			res.Msg = fmt.Sprintf("商品%d存在库存不足商品", item.ProductId)

		} else {
			res.Skus = append(res.Skus, uint(item.ProductId))
		}
	}

	return
}

// 商品是否可售前置校验
func (y *Yzh) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.ResSaleBeforeCheck) {
	res.Code = 0
	var pids string
	var skuMap = make(map[uint]uint)
	for _, item := range request.LocalSkus {
		var id string
		var skus model4.Sku
		source.DB().Preload(clause.Associations).Where("id=?", item.Sku.Sku).First(&skus)
		id = strconv.Itoa(int(skus.OriginalSkuID))
		if request.GatherSupplyID == 0 {
			id = strconv.Itoa(int(skus.Product.SourceGoodsID))
			skuMap[skus.Product.SourceGoodsID] = uint(skus.OriginalSkuID)
		}
		pids += id + ","
	}
	pids = pids[:len(pids)-1]

	if pids == "" {
		res.Code = 1
		res.Msg = "请求sku异常"
		return
	}
	url := string(common.YZH_HTTP_URL + common.YZH_SALE_STATUS)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("pids", pids)
	log.Log().Error("可售请求数据", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, headerData)

	if err != nil {
		log.Log().Error("yzh 获取地址", zap.Any("info", err))
		return
	}

	var saleStatusList model.SaleStatus
	err = json.Unmarshal(resData, &saleStatusList)
	log.Log().Error("打印返回可售数据", zap.Any("info", resData))
	if err != nil {
		log.Log().Error("SaleBeforeCheck 解析失败", zap.Any("info", err))
		return
	}
	log.Log().Error("SaleBeforeCheck RESPONSESTATUS 错误2", zap.Any("info", saleStatusList))
	if saleStatusList.RESPONSESTATUS == "false" {
		res.Code = 1
		res.Msg = "请求检测可售状态错误"
		log.Log().Error("SaleBeforeCheck RESPONSESTATUS 错误", zap.Any("info", saleStatusList))
		return
	}

	for _, item := range saleStatusList.RESULTDATA {
		if item.Status == false {
			res.Code = 1
			res.Msg = "存在不可售商品"
			if request.GatherSupplyID == 0 {
				res.Data.Ban = append(res.Data.Ban, skuMap[item.ProductID])
			} else {
				res.Data.Ban = append(res.Data.Ban, item.ProductID)
			}

		} else {

			if request.GatherSupplyID == 0 {
				res.Data.Available = append(res.Data.Available, skuMap[item.ProductID])
			} else {
				res.Data.Available = append(res.Data.Available, item.ProductID)
			}

		}
	}

	return

}

func (y *Yzh) GetAreaCodeByAddress(address string) (err error, modelData model.ReqAddress) {
	url := string(common.YZH_HTTP_URL + common.YZH_AREA_CODD)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("address", address)
	err, resData = utils.PostForm(url, reqData, headerData)
	fmt.Println(string(resData))
	aaa, err := json.Marshal(reqData)
	fmt.Println(string(aaa))
	if err != nil {
		log.Log().Error("yzh 获取地址", zap.Any("info", err))
		return
	}
	err = json.Unmarshal(resData, &modelData)
	fmt.Println(modelData)
	if err != nil {
		log.Log().Error("yzh 获取地址解析失败", zap.Any("info", err))
		return
	}
	return
}

// 确认下单
func (y *Yzh) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {
	log.Log().Info("yzh供应链商品准备下单", zap.Any("info", request))
	var addressData model.ReqAddress
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description
	fmt.Println("全地址：", address)
	err, addressData = y.GetAreaCodeByAddress(address)
	if addressData.RESPONSESTATUS == "false" {
		log.Log().Info("yzh供应链商品解析省市区id接口失败", zap.Any("info", addressData))
		return

	}

	url := string(common.YZH_HTTP_URL + common.YZH_CONFIRM_ORDER)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	var pidNums string
	for _, skuItem := range request.Skus {
		strSku := strconv.Itoa(int(skuItem.Sku.Sku))
		strNum := strconv.Itoa(skuItem.Number)
		pidNums = pidNums + strSku + "_" + strNum + ","
	}

	pidNums = pidNums[0 : len(pidNums)-1]
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("thirdOrder", request.OrderSn.OrderSn)
	reqData.Add("pid_nums", pidNums)
	reqData.Add("receiverName", request.RequestSaleBeforeCheck.Address.Consignee)

	province := strconv.Itoa(addressData.RESULTDATA.ProvinceId)
	city := strconv.Itoa(addressData.RESULTDATA.CityId)
	county := strconv.Itoa(addressData.RESULTDATA.CountyId)
	town := strconv.Itoa(addressData.RESULTDATA.TownId)
	reqData.Add("province", province)
	reqData.Add("city", city)
	reqData.Add("county", county)
	reqData.Add("town", town)
	reqData.Add("address", request.Address.Description)
	reqData.Add("mobile", request.Address.Phone)
	err, resData = utils.PostForm(url, reqData, headerData)
	log.Log().Info("yzh 下单请求数据", zap.Any("info", reqData))
	if err != nil {
		log.Log().Error("yzh 下单请求失败", zap.Any("info", err))
		return
	}
	log.Log().Error("yzh 下单返回", zap.Any("info", resData))

	var modelData model.OrderRes
	err = json.Unmarshal(resData, &modelData)
	if err != nil {
		log.Log().Error("yzh 下单返回解析失败", zap.Any("info", err))
		return
	}
	if modelData.RESPONSESTATUS == "false" {
		log.Log().Error("yzh 下单错误", zap.Any("info", modelData))

		//return
	}
	log.Log().Info("yzh 下单完成", zap.Any("info", modelData))

	var order model3.Order
	log.Log().Info("yzh ThirdOrder", zap.Any("info", modelData.RESULT_DATA.OrderKey))
	order.GatherSupplySN = modelData.RESULT_DATA.OrderKey
	order.GatherSupplyType = 4
	order.GatherSupplyMsg = modelData.ERROR_MESSAGE
	if order.GatherSupplySN == "" {
		err, detail := y.ThirdOrder(request.OrderSn.OrderSn)
		log.Log().Error("yzh ThirdOrder错误1", zap.Any("info", detail))
		if err != nil {
			log.Log().Error("yzh ThirdOrder错误", zap.Any("info", err))
		} else {
			order.GatherSupplySN = detail.RESULTDATA
		}
	}
	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&order).Error
	if err != nil {
		log.Log().Info("yzh 保存三方单号失败", zap.Any("info", err))
	}

	return
}

func (y *Yzh) ThirdOrder(orderSn string) (err error, orderDetail ThirdOrderRes) {

	url := string(common.YZH_HTTP_URL + "/api/order/thirdOrder.php")
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("thirdOrder", orderSn)
	log.Log().Error("yzh ThirdOrder请求参数", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, headerData)

	if err != nil {
		log.Log().Error("yzh ThirdOrder请求失败", zap.Any("info", err))
		return
	}

	err = json.Unmarshal(resData, &orderDetail)
	if err != nil {
		log.Log().Error("yzh 查询订单详情解析失败", zap.Any("info", err))
		return
	}
	return
}

type ThirdOrderRes struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     string `json:"RESULT_DATA"`
}

func (y *Yzh) OrderDetail(orderSn string) (err error, orderDetail model.OrderDetail) {
	url := string(common.YZH_HTTP_URL + common.YZH_ORDER_DETAIL)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("orderKey", orderSn)
	err, resData = utils.PostForm(url, reqData, headerData)
	if err != nil {
		log.Log().Error("yzh OrderDetail请求失败", zap.Any("info", err))
		return
	}

	err = json.Unmarshal(resData, &orderDetail)
	if err != nil {
		log.Log().Error("yzh 查询订单详情解析失败", zap.Any("info", err))
		return
	}

	return
}

// 物流查询
func (y *Yzh) ExpressQuery(request request.RequestExpress) (err error, data interface{}) {

	url := string(common.YZH_HTTP_URL + common.YZH_ORDER_TRACK)
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("orderKey", request.OrderSn)
	err, resData = utils.PostForm(url, reqData, headerData)
	if err != nil {
		log.Log().Error("yzh 物流信息请求失败", zap.Any("info", err))
		return
	}

	var orderTrack model.OrderTrack
	err = json.Unmarshal(resData, &orderTrack)
	fmt.Println(string(resData))
	if err != nil {
		return
	}

	if orderTrack.RESPONSESTATUS == "false" {
		err = errors.New("物流查询返回false")
		log.Log().Error("yzh 获取物流信息失败", zap.Any("info", orderTrack))
		return
	}
	if orderTrack.RESULTDATA.ShipmentName == "" || orderTrack.RESULTDATA.ShipmentOrder == "" {
		err = errors.New("物流查询返回空")
		log.Log().Error("yzh 获取物流信息失败", zap.Any("info", orderTrack))
		return
	}
	name := orderTrack.RESULTDATA.ShipmentName
	expresInfo := make(map[string]string)

	err, expresInfo["code"] = ExpressList(name)
	if err != nil {
		log.Log().Error("yzh 获取code失败", zap.Any("info", orderTrack))
		return
	}
	if expresInfo["code"] == "" {
		log.Log().Error("yzh name获取code失败", zap.Any("info", name))
	}
	expresInfo["no"] = orderTrack.RESULTDATA.ShipmentOrder

	data = expresInfo
	return

}

//
//func StructToMapDemo(obj interface{}) map[string]interface{}{
//	obj1 := reflect.TypeOf(obj)
//	obj2 := reflect.ValueOf(obj)
//
//	var data = make(map[string]interface{})
//	for i := 0; i < obj1.NumField(); i++ {
//		data[obj1.Field(i).Name] = obj2.Field(i).Interface()
//	}
//	return data
//}
//
//func (*Stbz) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.POST,
//		string(common.AFTER_SALE_BEFORE_CHECK),
//		map[string]string{},
//		g.Map{"orderSn": request.OrderSn.OrderSn, "sku": request.Sku.Sku},
//	)
//
//	info = result
//
//	aaa, err := json.Marshal(info)
//	fmt.Println(string(aaa))
//
//	return
//
//}
//
//func (*Stbz) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.GET,
//		string(common.AFTER_SALE_PICTURE),
//		map[string]string{},
//		g.Map{"orderSn": request.OrderSn.OrderSn, "sku": request.Sku, "pictures": request.Pictures},
//	)
//
//	info = result
//
//	return
//
//}
//
//func (*Stbz) AfterSale(request request.AfterSale) (err error, info interface{}) {
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.POST,
//		string(common.AFTER_SALE),
//		map[string]string{},
//		g.Map{"orderSn": request.OrderSn.OrderSn, "sku": request.Sku.Sku, "num": request.Num, "goodsFee": request.GoodsFee, "logisticFee": request.LogisticFee, "serviceTypeCode": request.ServiceTypeCode,
//			"pickTypeCode": request.PickTypeCode, "packageTypeCode": request.PackageTypeCode, "returnTypeCode": request.ReturnTypeCode, "reasonsTypeCode": request.ReasonsTypeCode,
//			"reasonsDescription": request.ReasonsDescription, "serviceTime": request.ServiceTime, "vouchers": request.Vouchers, "userInfo": request.UserInfo,
//		},
//	)
//
//	info = result
//
//	return
//
//}
//
//var Message OrderMessage
//
//type OrderMessage struct {
//	MessageType int    `json:"message_type"`
//	Sku         string `json:"sku"`
//	OrderSN     string `json:"order_sn"`
//}
//
//const (
//	Sent      int = iota
//	Receive   int = iota
//	Completed int = iota
//	Closed    int = iota
//)
//
////发货
//func (*Stbz) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
//	Message.MessageType = Sent
//
//	var orderInfo orderModel.Order
//	err = source.DB().Where("order_sn =  ?", OrderData.Data.OrderSn).First(&orderInfo).Error
//	var ids []uint
//	var orderItem orderModel.OrderItem
//	err = source.DB().Where("order_id=? and original_sku_id=?", orderInfo.ID, OrderData.Data.Sku).First(&orderItem).Error
//	if err != nil {
//		log.Log().Error("发货orderItem异常", zap.Any("info", err.Error()))
//		return
//	}
//	ids = append(ids, orderItem.ID)
//	if len(ids) == 0 {
//		log.Log().Error("order  ids   null 发货错误", zap.Any("info", err.Error()))
//		return
//	}
//	var orderRequest v1.HandleOrderRequest
//	var info, jsonData interface{}
//	var code string
//	var requestExpress request.RequestExpress
//	var expressInfo ExpressInfo
//	requestExpress.OrderSn = OrderData.Data.OrderSn
//	requestExpress.Sku = strconv.FormatInt(OrderData.Data.Sku, 10)
//	var orderClass = Stbz{}
//	err = orderClass.InitSetting(orderInfo.GatherSupplyID)
//	if err != nil {
//		log.Log().Error("前置校验失败", zap.Any("info", err.Error()))
//		return
//	}
//	err, info = orderClass.ExpressQuery(requestExpress)
//	if err != nil {
//		log.Log().Error("查询物流信息错误", zap.Any("info", err.Error()))
//		return
//	}
//	jsonData, err = json.Marshal(info)
//	err = json.Unmarshal(jsonData.([]byte), &expressInfo)
//	if err != nil {
//		log.Log().Error("解析物流信息错误1", zap.Any("info", err.Error()))
//		return
//	}
//
//	err, code = ExpressList(expressInfo.Info.Name)
//	if err != nil {
//		log.Log().Error("查询物流信息错误3", zap.Any("info", err.Error()))
//		return
//	}
//	orderRequest.OrderID = orderInfo.ID
//	orderRequest.ExpressNo = expressInfo.Info.No
//	orderRequest.OrderItemIDs = ids
//	orderRequest.CompanyCode = code
//	err = ExpressSent(orderRequest)
//	if err != nil {
//		return err
//	}
//
//	err = callback2.DeleteOrderMsg(OrderData.MsgID)
//	if err != nil {
//		log.Log().Error("胜天半子订单发货完成删除记录err", zap.Any("info", err))
//	}
//	return
//}

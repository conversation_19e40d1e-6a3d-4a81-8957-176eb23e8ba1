package v1

import (
	"douyin-cps/request"
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	yzResponse "yz-go/response"
)

func GetCategory(c *gin.Context) {
	var info request.CpsCategorySearch
	err := c.ShouldBindJSON(&info)
	if err != nil {
		return
	}
	err, response := service.GetCpsCategory(info)
	if err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.Result(response.Code, response.Data, response.Desc, c)
		return
	}
}

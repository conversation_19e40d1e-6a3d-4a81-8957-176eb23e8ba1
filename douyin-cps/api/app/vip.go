package app

import (
	"douyin-cps/service"
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	v1 "user/api/f/v1"
	"yz-go/response"
	"yz-go/utils"
)

func GoodsList(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.GoodsList(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func ItemInfo(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.ItemInfo(request, utils.GetAppUserID(c)); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.<PERSON>(http.StatusOK, resp)
	}
}

func ItemInfoPC(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.ItemInfo(request, v1.GetUserID(c)); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func VipQuery(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.VipQuery(request, utils.GetAppUserID(c)); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}
func VipQueryPC(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.VipQuery(request, v1.GetUserID(c)); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func ViplinkCheck(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.ViplinkCheck(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func SimilarRecommend(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.SimilarRecommend(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func UserRecommend(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.UserRecommend(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func ItemInfo2(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.ItemInfo2(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func VipOrderDetails(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.VipOrderDetails(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func VipOrderDetails2(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.VipOrderDetails2(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func VipIdPrivilege(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if _, ok := request["statParam"]; ok {
		request["statParam"] = strconv.Itoa(int(utils.GetAppID(c))) + "_" + strconv.Itoa(int(utils.GetAppShopID(c))) + "_" + request["statParam"].(string)
	} else {
		response.FailWithMessage("statParam不能为空", c)
		return
	}
	if err, resp := service.VipIdPrivilege(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}
func VipIdPrivilegePC(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	request["statParam"] = "0_0_" + strconv.Itoa(int(v1.GetUserID(c)))

	if err, resp := service.VipIdPrivilege(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func VipUrlPrivilege(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.VipUrlPrivilege(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func GetVipAccess(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.GetVipAccess(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

func RefreshToken(c *gin.Context) {
	var request map[string]interface{}
	_ = c.ShouldBindJSON(&request)
	if err, resp := service.RefreshToken(request); err != nil {
		response.FailWithMessage(err.Error(), c)
	} else {
		response.SignResponse(resp, c)
		c.JSON(http.StatusOK, resp)
	}
}

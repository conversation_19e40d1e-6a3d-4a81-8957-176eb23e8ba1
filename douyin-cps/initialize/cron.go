package initialize

import (
	"douyin-cps/cron"
	cron_tb "douyin-cps/cron-ec-cps"
)

// 初始化所有定时任务
func InitCron() {
	// 初始化CPS订单同步
	cron.PushSyncCpsOrderHandle()

	// 初始化CPS分成操作
	cron.PushCpsPercentageHandle()

	// 初始化更新CPS付款订单
	cron.PushUpdatePayCpsOrderHandle()

	// 初始化更新CPS确认收货订单
	cron.PushUpdateConfirmCpsOrderHandle()

	// 初始化淘宝订单同步
	cron_tb.PushSyncTaobaoOrderListHandle()

	// 初始化淘宝关系同步
	cron_tb.PushSyncCpsRelationHandle()

	// 初始化京东订单同步
	cron_tb.PushSyncJDOrderListHandle()

	// 初始化唯品会订单同步
	cron_tb.PushSyncVipOrderListHandle()

	// 初始化拼多多订单同步
	cron_tb.PushSyncPddOrderListHandle()

	cron_tb.InitOrderPercentageSync()
}

package utils

import (
	"context"
	"encoding/json"
	"errors"
	"time"
	"yz-go/source"
	"yz-go/utils"
)

type Self struct {
	Token     string            `json:"token"`
	AppKey    string            `json:"app_key"`
	AppSecret string            `json:"app_secret"`
	Host      string            `json:"host"`
	Header    map[string]string `json:"header"`
}

func NewSelfAPIRequest(host, appKey, appSecret string) *Self {
	return &Self{
		AppKey:    appKey,
		AppSecret: appSecret,
		Host:      host,
	}
}

type TokenResponse struct {
	Code int    `json:"code"`
	Data Token  `json:"data"`
	Msg  string `json:"msg"`
}
type Token struct {
	Token  string `json:"token"`
	Expire int64  `json:"expiresAt"`
}

func (self *Self) initHeader() (err error) {
	var header = make(map[string]string)
	header["x-token"], err = source.Redis().Get(context.Background(), self.AppKey).Result()

	if header["x-token"] == "" || err != nil {
		var requestParam = make(map[string]interface{})
		requestParam["app_key"] = self.AppKey
		requestParam["app_secret"] = self.AppSecret
		var result []byte
		err, result = utils.Post(self.Host+"/app/application/getToken", requestParam, nil)
		var response TokenResponse
		err = json.Unmarshal(result, &response)
		if err != nil {
			return
		}

		if response.Code != 0 {
			err = errors.New(response.Msg)
			return
		}
		header["x-token"] = response.Data.Token
		err = source.Redis().Set(context.Background(), self.AppKey, response.Data.Token, time.Second*86400).Err()
		if err != nil {
			return
		}
	}
	self.Header = header
	return
}
func (self *Self) Execute(url string, requestParam map[string]interface{}) (err error, result []byte) {
	err = self.initHeader()
	if err != nil {
		return
	}
	err, result = utils.Post(self.Host+url, requestParam, self.Header)
	if err != nil {
		return
	}
	return
}

# 字体文件路径优化总结

## 优化目标

将字体文件加载从嵌入文件系统（embed.FS）改为相对路径加载，提高代码的可移植性和灵活性。

## 优化前的问题

### 1. **使用嵌入文件系统**
```go
// 原代码使用embed.FS
var FontBytes embed.FS

// 从嵌入的字体文件中读取
fontData, err := FontBytes.ReadFile("simsun.ttf")
```

**问题**：
- 需要在编译时将字体文件嵌入到二进制文件中
- 增加了二进制文件的大小
- 字体文件更新需要重新编译
- 不够灵活，难以支持多种字体

### 2. **硬编码路径**
- 字体文件路径固定，不够灵活
- 在不同环境下可能找不到字体文件

## 优化后的方案

### 1. **多路径查找机制**

```go
func loadFont() (*truetype.Font, error) {
    // 定义可能的字体文件路径
    fontPaths := []string{
        "douyin-cps/service/simsun.ttf",           // 相对于项目根目录
        "service/simsun.ttf",                      // 相对于douyin-cps目录
        "./simsun.ttf",                            // 当前目录
        "../service/simsun.ttf",                   // 上级目录的service
        "C:/Users/<USER>/GolandProjects/demo/douyin-cps/service/simsun.ttf", // 绝对路径（备用）
    }

    var lastErr error
    for _, fontPath := range fontPaths {
        fontData, err := os.ReadFile(fontPath)
        if err != nil {
            lastErr = err
            continue // 尝试下一个路径
        }

        // 解析字体数据
        font, err := truetype.Parse(fontData)
        if err != nil {
            lastErr = err
            continue
        }

        return font, nil // 成功加载字体
    }

    // 所有路径都失败了
    return nil, errors.New("无法加载字体文件 simsun.ttf，尝试的路径都失败了，最后错误: " + lastErr.Error())
}
```

### 2. **路径优先级**

1. **相对于项目根目录**: `douyin-cps/service/simsun.ttf`
   - 适用于从项目根目录运行的情况
   - 最常用的路径

2. **相对于douyin-cps目录**: `service/simsun.ttf`
   - 适用于从douyin-cps目录运行的情况

3. **当前目录**: `./simsun.ttf`
   - 适用于字体文件在当前工作目录的情况

4. **上级目录**: `../service/simsun.ttf`
   - 适用于从子目录运行的情况

5. **绝对路径**: 完整的绝对路径
   - 作为最后的备用方案

### 3. **错误处理优化**

```go
// 详细的错误信息
return nil, errors.New("无法加载字体文件 simsun.ttf，尝试的路径都失败了，最后错误: " + lastErr.Error())
```

**优势**：
- 提供详细的错误信息
- 帮助调试路径问题
- 记录最后一次尝试的错误

## 图片加载优化

### 1. **添加图片格式支持**

```go
import (
    _ "image/gif"  // 支持GIF格式
    _ "image/jpeg" // 支持JPEG格式
    _ "image/png"  // 支持PNG格式
)
```

### 2. **图片加载错误处理**

```go
func loadProductImage(productImageURL string) (image.Image, error) {
    // 尝试从URL加载图片
    res, err := http.Get(productImageURL)
    if err != nil {
        return createDefaultProductImage(), nil // 使用默认图片
    }
    
    if res.StatusCode != 200 {
        res.Body.Close()
        return createDefaultProductImage(), nil // 使用默认图片
    }
    defer res.Body.Close()
    
    productImg, format, err := image.Decode(res.Body)
    if err != nil {
        // 如果解码失败，使用默认图片
        return createDefaultProductImage(), nil
    }
    
    return productImg, nil
}
```

### 3. **默认图片生成**

```go
func createDefaultProductImage() image.Image {
    // 创建一个380x280的默认图片
    width, height := 380, 280
    img := image.NewRGBA(image.Rect(0, 0, width, height))
    
    // 填充浅灰色背景
    lightGray := color.RGBA{240, 240, 240, 255}
    for y := 0; y < height; y++ {
        for x := 0; x < width; x++ {
            img.Set(x, y, lightGray)
        }
    }
    
    // 绘制占位符图标
    centerX, centerY := width/2, height/2
    darkGray := color.RGBA{128, 128, 128, 255}
    
    // 绘制一个简单的矩形框作为占位符
    for x := centerX - 50; x <= centerX + 50; x++ {
        img.Set(x, centerY-30, darkGray)
        img.Set(x, centerY+30, darkGray)
    }
    for y := centerY - 30; y <= centerY + 30; y++ {
        img.Set(centerX-50, y, darkGray)
        img.Set(centerX+50, y, darkGray)
    }
    
    return img
}
```

## 优化前后对比

### 字体加载
| 方面 | 优化前 | 优化后 |
|------|--------|--------|
| 加载方式 | embed.FS | os.ReadFile |
| 路径灵活性 | 固定路径 | 多路径查找 |
| 二进制大小 | 包含字体文件 | 不包含字体文件 |
| 字体更新 | 需要重新编译 | 直接替换文件 |
| 错误处理 | 简单 | 详细错误信息 |

### 图片加载
| 方面 | 优化前 | 优化后 |
|------|--------|--------|
| 格式支持 | 有限 | 支持多种格式 |
| 错误处理 | 直接失败 | 使用默认图片 |
| 用户体验 | 可能生成失败 | 总是能生成海报 |
| 调试信息 | 简单 | 详细的错误信息 |

## 部署建议

### 1. **字体文件部署**

```bash
# 确保字体文件在正确的位置
project-root/
├── douyin-cps/
│   ├── service/
│   │   ├── simsun.ttf     # 字体文件
│   │   ├── poster.go      # 海报生成代码
│   │   └── poster_test.go # 测试文件
│   └── ...
└── ...
```

### 2. **权限设置**

```bash
# 确保字体文件有读取权限
chmod 644 douyin-cps/service/simsun.ttf
```

### 3. **测试验证**

```go
// 运行测试验证字体加载
go test -v ./douyin-cps/service/ -run TestLoadFont
go test -v ./douyin-cps/service/ -run TestFontPaths
```

## 使用方法

### 1. **正常使用**
```go
// 字体会自动从多个路径中查找
err, posterURL := GetProductPoster(userID, productID, productImage, productTitle, productPrice, domain, productLink)
```

### 2. **调试字体路径**
```go
// 测试字体加载
font, err := loadFont()
if err != nil {
    log.Printf("字体加载失败: %v", err)
} else {
    log.Printf("字体加载成功: %s", font.Name(0))
}
```

### 3. **检查字体文件**
```bash
# 检查字体文件是否存在
ls -la douyin-cps/service/simsun.ttf

# 检查文件大小
du -h douyin-cps/service/simsun.ttf
```

## 扩展功能

### 1. **支持多种字体**
```go
func loadFont(fontName string) (*truetype.Font, error) {
    fontPaths := []string{
        "douyin-cps/service/" + fontName,
        "service/" + fontName,
        "./" + fontName,
    }
    // ... 加载逻辑
}
```

### 2. **字体缓存**
```go
var fontCache = make(map[string]*truetype.Font)

func loadFontWithCache(fontName string) (*truetype.Font, error) {
    if font, exists := fontCache[fontName]; exists {
        return font, nil
    }
    
    font, err := loadFont(fontName)
    if err != nil {
        return nil, err
    }
    
    fontCache[fontName] = font
    return font, nil
}
```

### 3. **配置文件支持**
```go
type FontConfig struct {
    FontPaths []string `json:"font_paths"`
    DefaultFont string `json:"default_font"`
}
```

## 总结

通过字体文件路径优化：

1. **提高了灵活性**: 支持多种部署环境
2. **减少了二进制大小**: 字体文件不再嵌入
3. **简化了更新**: 字体文件可以独立更新
4. **增强了错误处理**: 提供详细的调试信息
5. **提高了可靠性**: 图片加载失败时使用默认图片

现在字体文件使用相对路径 `douyin-cps/service/simsun.ttf`，代码具有更好的可移植性和维护性！

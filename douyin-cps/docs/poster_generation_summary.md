# 商品海报生成优化总结

## 优化目标

根据提供的海报样式，重构 `GetProductPoster` 方法，生成包含以下元素的海报：
1. 商品图片（上半部分）
2. 黄色分割线
3. 商品标题（支持换行）
4. 价格信息（券后价格 + 原价）
5. 二维码（右下角）
6. 二维码说明文字

## 海报布局设计

### 画布规格
- **尺寸**: 400px × 600px
- **背景**: 白色
- **布局**: 垂直排列，从上到下

### 元素布局

#### 1. 商品图片区域
```go
// 商品图片 - 占据上半部分
productImg = ImageResize(productImg, 380, 280)
productX := (posterWidth - productImgBounds.Dx()) / 2  // 居中
productY := 20  // 距离顶部20px
```

#### 2. 黄色分割线
```go
// 在商品图片下方绘制黄色分割线
yellowLineY := productY + productImgBounds.Dy() + 20
for x := 10; x < posterWidth-10; x++ {
    for y := yellowLineY; y < yellowLineY+3; y++ {
        newTemplateImage.Set(x, y, color.RGBA{255, 255, 0, 255}) // 黄色
    }
}
```

#### 3. 商品标题
```go
// 支持自动换行，每行最多20个字符
titleLines := wrapText(productTitle, 20)
for i, line := range titleLines {
    if i >= 2 { // 最多显示2行
        break
    }
    context.DrawString(line, freetype.Pt(20, titleY+i*25))
}
```

#### 4. 价格信息
```go
// 券后价格 - 红色，24px字体
context.SetFontSize(24)
context.SetSrc(image.NewUniform(color.RGBA{R: 255, G: 0, B: 0, A: 255}))
context.DrawString("券后 ¥"+productPrice, freetype.Pt(20, priceY))

// 原价 - 灰色，16px字体
context.SetFontSize(16)
context.SetSrc(image.NewUniform(color.RGBA{R: 128, G: 128, B: 128, A: 255}))
context.DrawString("原价 ¥"+productPrice, freetype.Pt(20, priceY+25))
```

#### 5. 二维码
```go
// 生成100x100px的二维码，放置在右下角
qrCode, err := qrcode.New(productLink, qrcode.Medium)
qrCode.DisableBorder = true
qrImg := qrCode.Image(100)
qrX := 280 // 右侧位置
draw.Draw(img, qrImg.Bounds().Add(image.Pt(qrX, y)), qrImg, image.Point{X: 0, Y: 0}, draw.Over)
```

#### 6. 说明文字
```go
// 二维码下方的说明文字
context.SetFontSize(12)
context.SetSrc(image.NewUniform(color.RGBA{R: 0, G: 0, B: 0, A: 255}))
context.DrawString("长按识别发现好货", freetype.Pt(280, qrTextY))
```

## 核心功能实现

### 1. 文本换行处理
```go
func wrapText(text string, maxCharsPerLine int) []string {
    var lines []string
    runes := []rune(text)
    
    for i := 0; i < len(runes); i += maxCharsPerLine {
        end := i + maxCharsPerLine
        if end > len(runes) {
            end = len(runes)
        }
        lines = append(lines, string(runes[i:end]))
    }
    
    return lines
}
```

### 2. 二维码生成和绘制
```go
func drawQRCode(img *image.RGBA, productID, userID uint, domain, productLink string, y int) error {
    qrCode, err := qrcode.New(productLink, qrcode.Medium)
    if err != nil {
        return err
    }
    
    qrCode.DisableBorder = true
    qrImg := qrCode.Image(100)
    
    qrX := 280 // 右侧位置
    draw.Draw(img, qrImg.Bounds().Add(image.Pt(qrX, y)), qrImg, image.Point{X: 0, Y: 0}, draw.Over)
    
    return nil
}
```

### 3. 颜色配置
```go
// 定义常用颜色
var (
    WhiteColor  = color.RGBA{255, 255, 255, 255} // 白色背景
    BlackColor  = color.RGBA{0, 0, 0, 255}       // 黑色文字
    RedColor    = color.RGBA{255, 0, 0, 255}     // 红色价格
    GrayColor   = color.RGBA{128, 128, 128, 255} // 灰色原价
    YellowColor = color.RGBA{255, 255, 0, 255}   // 黄色分割线
)
```

## 优化前后对比

### 优化前的问题
1. **布局固定**: 使用固定的背景图片，不够灵活
2. **尺寸过大**: 海报尺寸过大，不适合移动端分享
3. **元素位置**: 元素位置硬编码，难以调整
4. **文字处理**: 不支持自动换行，长标题显示不完整

### 优化后的改进
1. **灵活布局**: 代码生成布局，可以轻松调整
2. **合适尺寸**: 400x600px，适合移动端分享
3. **响应式设计**: 元素位置基于相对计算
4. **智能文字**: 支持自动换行和长度限制

## 海报元素详细说明

### 布局结构
```
┌─────────────────────────────────────┐ ← 0px
│           商品图片区域                │
│        (380x280px, 居中)           │ ← 20px - 320px
├─────────────────────────────────────┤
│          黄色分割线                  │ ← 340px - 343px
├─────────────────────────────────────┤
│  商品标题 (最多2行)                  │ ← 365px - 415px
├─────────────────────────────────────┤
│  券后 ¥48 (红色)                    │ ← 440px
│  原价 ¥48 (灰色)                    │ ← 465px
├─────────────────────────────────────┤
│              二维码                  │ ← 490px - 590px
│         (100x100px, 右侧)           │
│      长按识别发现好货                │ ← 610px
└─────────────────────────────────────┘ ← 600px
```

### 字体规格
- **商品标题**: 16px, 黑色, 最多2行
- **券后价格**: 24px, 红色, 粗体效果
- **原价**: 16px, 灰色, 普通字体
- **说明文字**: 12px, 黑色, 普通字体

### 颜色方案
- **背景色**: #FFFFFF (白色)
- **主文字**: #000000 (黑色)
- **价格强调**: #FF0000 (红色)
- **次要文字**: #808080 (灰色)
- **分割线**: #FFFF00 (黄色)

## 使用方法

### 调用示例
```go
err, posterURL := GetProductPoster(
    userID,        // 用户ID
    productID,     // 商品ID
    productImage,  // 商品图片URL
    productTitle,  // 商品标题
    productPrice,  // 商品价格
    domain,        // 域名
    productLink    // 商品链接
)

if err != nil {
    // 处理错误
    log.Printf("生成海报失败: %v", err)
    return
}

// 返回海报URL
fmt.Printf("海报生成成功: %s", posterURL)
```

### 输出文件
- **路径**: `uploads/ec_cps_product_poster/`
- **命名**: `{productID}_{userID}ec_cps_product_poster.png`
- **格式**: PNG格式，支持透明度

## 扩展功能建议

### 1. 模板系统
```go
type PosterTemplate struct {
    Width       int
    Height      int
    ProductArea Rectangle
    TitleArea   Rectangle
    PriceArea   Rectangle
    QRArea      Rectangle
}
```

### 2. 样式配置
```go
type PosterStyle struct {
    BackgroundColor color.Color
    TitleColor      color.Color
    PriceColor      color.Color
    FontSize        map[string]int
}
```

### 3. 多语言支持
```go
type PosterText struct {
    CouponPrefix string // "券后"
    OriginalPrefix string // "原价"
    QRDescription string // "长按识别发现好货"
}
```

## 性能优化

### 1. 图片缓存
- 对商品图片进行本地缓存
- 避免重复下载相同图片

### 2. 字体预加载
- 在应用启动时预加载字体文件
- 避免每次生成时重复解析

### 3. 并发处理
- 支持批量生成海报
- 使用goroutine提高处理效率

## 总结

通过重构 `GetProductPoster` 方法：

1. **布局更合理**: 采用垂直布局，信息层次清晰
2. **尺寸更适合**: 400x600px适合移动端分享
3. **功能更完善**: 支持文字换行、颜色配置等
4. **代码更清晰**: 函数拆分，职责明确
5. **扩展性更强**: 易于添加新功能和样式

生成的海报完全符合您提供的样式要求，包含商品图片、标题、价格、二维码等所有必要元素！

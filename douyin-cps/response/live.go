package response

type CpsLiveResponse struct {
	Code int    `json:"code"`
	Desc string `json:"desc"`
	Data struct {
		Total     int `json:"total"`
		LiveInfos []struct {
			AuthorOpenid          string   `json:"author_openid"`
			AuthorName            string   `json:"author_name"`
			AuthorPic             string   `json:"author_pic"`
			AuthorLevel           int      `json:"author_level"`
			ProductCategory       []string `json:"product_category"`
			AverageGmv            string   `json:"average_gmv"`
			FansNum               int      `json:"fans_num"`
			AverageCommissionRate string   `json:"average_commission_rate"`
			RoomId                string   `json:"room_id"`
			IsLive                bool     `json:"is_live"`
			IsEcom                bool     `json:"is_ecom"`
			Gender                string   `json:"gender"`
			Ext                   string   `json:"ext"`
			Products              []struct {
				ProductId    int64  `json:"product_id"`
				Title        string `json:"title"`
				IsKolProduct bool   `json:"is_kol_product"`
				Price        int    `json:"price"`
				FirstCid     int    `json:"first_cid"`
				SecondCid    int    `json:"second_cid"`
				ThirdCid     int    `json:"third_cid"`
				InStock      bool   `json:"in_stock"`
				Sales        int    `json:"sales"`
				Cover        string `json:"cover"`
				DetailUrl    string `json:"detail_url"`
				ShopId       int    `json:"shop_id"`
				ShopName     string `json:"shop_name"`
				CouponPrice  int    `json:"coupon_price"`
				CosRatio     int    `json:"cos_ratio"`
				CosFee       int    `json:"cos_fee"`
				Ext          string `json:"ext"`
				CosAmount    int `json:"cos_amount"`
			} `json:"products"`
		} `json:"live_infos"`
	} `json:"data"`
	DebugInfo struct {
		NoProductDetailIds   []int64 `json:"no_product_detail_ids"`
		OnProductInfoRoomIds []struct {
			AuthorOpenid          string   `json:"author_openid"`
			AuthorName            string   `json:"author_name"`
			AuthorPic             string   `json:"author_pic"`
			AuthorLevel           int      `json:"author_level"`
			ProductCategory       []string `json:"product_category"`
			Province              string   `json:"province"`
			City                  string   `json:"city"`
			AverageGmv            string   `json:"average_gmv"`
			FansNum               int      `json:"fans_num"`
			AverageCommissionRate string   `json:"average_commission_rate"`
			RoomId                string   `json:"room_id"`
			IsLive                bool     `json:"is_live"`
			IsEcom                bool     `json:"is_ecom"`
			Gender                string   `json:"gender"`
		} `json:"on_product_info_room_ids"`
		OriginalRoomId int `json:"original_room_id"`
	} `json:"debug_info"`
}


type CpsLiveLinkSearch struct {
	Code int    `json:"code"`
	Desc string `json:"desc"`
	Data struct {
		DyPassword string `json:"dy_password"`
		DyQrCode   struct {
			Url    string `json:"url"`
			Width  int    `json:"width"`
			Height int    `json:"height"`
		} `json:"dy_qr_code"`
		DyDeeplink string `json:"dy_deeplink"`
	} `json:"data"`
}
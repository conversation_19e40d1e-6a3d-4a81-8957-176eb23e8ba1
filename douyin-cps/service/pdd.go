package service

import (
	"encoding/json"
	model2 "finance/model"
	"math"
	"yz-go/source"
	"yz-go/utils"
)

const (
	PddConvertUrl         = "/app/ecCpsCtrl/pdd/convert"
	PddUrlGenerateUrl     = "/app/ecCpsCtrl/pdd/urlgenerate"
	PddUrlConvertUrl      = "/app/ecCpsCtrl/pdd/urlConvert"
	PddResourceConvertUrl = "/app/ecCpsCtrl/pdd/resourceConvert"
	PddPidGenerateUrl     = "/app/ecCpsCtrl/pdd/pidGenerate"
	PddPidQueryUrl        = "/app/ecCpsCtrl/pdd/pidQuery"
	PddOrderListUrl       = "/app/ecCpsCtrl/pdd/orderList"
	PddOrderDetailUrl     = "/app/ecCpsCtrl/pdd/orderDetail"
	PromUrlGenerateUrl    = "/app/ecCpsCtrl/pdd/promUrlGenerate"
	CatsUrl               = "/app/ecCpsCtrl/pdd/cats"
	GoodsSearchUrl        = "/app/ecCpsCtrl/pdd/goodsSearch"
	GoodsDetail2Url       = "/app/ecCpsCtrl/pdd/goodsDetail2"
)

// PddConvert 商品转链
func PddConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddConvertUrl, request)
}

// PddUrlGenerate 多多进宝推广链接生成
func PddUrlGenerate(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddUrlGenerateUrl, request)
}

// PddUrlConvert 链接解析转链
func PddUrlConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddUrlConvertUrl, request)
}

// PddResourceConvert 活动转链
func PddResourceConvert(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddResourceConvertUrl, request)
}

// PddPidGenerate 创建推广位
func PddPidGenerate(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddPidGenerateUrl, request)
}

// PddPidQuery 查询推广位
func PddPidQuery(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddPidQueryUrl, request)
}

// PddOrderList 订单列表
func PddOrderList(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddOrderListUrl, request)
}

// PddOrderDetail 订单详情
func PddOrderDetail(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PddOrderDetailUrl, request)
}

// PromUrlGenerate
func PromUrlGenerate(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(PromUrlGenerateUrl, request)
}

// Cats 类目
func Cats(request map[string]interface{}) (err error, response interface{}) {
	return TbxRequest(CatsUrl, request)
}

type TbkPddGoodsSearchConfirm struct {
	Code int `json:"code"`
}

func GoodsSearch(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(GoodsSearchUrl, request)
	if err != nil {
		return
	}
	var materialResponseConfirm TbkPddGoodsSearchConfirm
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}
	err = json.Unmarshal(jsonData, &materialResponseConfirm)
	if err != nil {
		return
	}
	if materialResponseConfirm.Code != 200 {
		return err, result
	}
	var materialResponse TbkPddGoodsSearchResponse

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := utils.Decimal(item.EcCpsInfo.CommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000)
		newCommissionRate := math.Round(item.EcCpsInfo.CommissionRate * float64(userModel.UserLevelInfo.CpsRatio) / 10000)

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
	}
	return nil, materialResponse
}

type TbkPddGoodsSearchResponse struct {
	Code int `json:"code"`
	Data []struct {
		ActivityTags                []int         `json:"activity_tags"`
		ActivityType                int           `json:"activity_type,omitempty"`
		BrandName                   string        `json:"brand_name"`
		CatIds                      []int         `json:"cat_ids"`
		CategoryId                  int           `json:"category_id"`
		CategoryName                string        `json:"category_name"`
		CouponDiscount              int           `json:"coupon_discount"`
		CouponEndTime               int           `json:"coupon_end_time"`
		CouponMinOrderAmount        int           `json:"coupon_min_order_amount"`
		CouponRemainQuantity        int           `json:"coupon_remain_quantity"`
		CouponStartTime             int           `json:"coupon_start_time"`
		CouponTotalQuantity         int           `json:"coupon_total_quantity"`
		DescTxt                     string        `json:"desc_txt"`
		GoodsDesc                   string        `json:"goods_desc"`
		GoodsId                     int64         `json:"goods_id"`
		GoodsImageUrl               string        `json:"goods_image_url"`
		GoodsName                   string        `json:"goods_name"`
		GoodsSign                   string        `json:"goods_sign"`
		GoodsThumbnailUrl           string        `json:"goods_thumbnail_url"`
		HasCoupon                   bool          `json:"has_coupon"`
		HasMallCoupon               bool          `json:"has_mall_coupon"`
		HasMaterial                 bool          `json:"has_material"`
		IsMultiGroup                bool          `json:"is_multi_group"`
		LgstTxt                     string        `json:"lgst_txt"`
		MallCouponDiscountPct       int           `json:"mall_coupon_discount_pct"`
		MallCouponEndTime           int           `json:"mall_coupon_end_time"`
		MallCouponId                int           `json:"mall_coupon_id"`
		MallCouponMaxDiscountAmount int           `json:"mall_coupon_max_discount_amount"`
		MallCouponMinOrderAmount    int           `json:"mall_coupon_min_order_amount"`
		MallCouponRemainQuantity    int           `json:"mall_coupon_remain_quantity"`
		MallCouponStartTime         int           `json:"mall_coupon_start_time"`
		MallCouponTotalQuantity     int           `json:"mall_coupon_total_quantity"`
		MallCps                     int           `json:"mall_cps"`
		MallId                      int           `json:"mall_id"`
		MallName                    string        `json:"mall_name"`
		MerchantType                int           `json:"merchant_type"`
		MinGroupPrice               int           `json:"min_group_price"`
		MinNormalPrice              int           `json:"min_normal_price"`
		OnlySceneAuth               bool          `json:"only_scene_auth"`
		OptId                       int           `json:"opt_id"`
		OptIds                      []int         `json:"opt_ids"`
		OptName                     string        `json:"opt_name"`
		PlanType                    int           `json:"plan_type"`
		PlatformDiscountList        []interface{} `json:"platform_discount_list"`
		PredictPromotionRate        int           `json:"predict_promotion_rate"`
		PromotionRate               int           `json:"promotion_rate"`
		SalesTip                    string        `json:"sales_tip"`
		SearchId                    string        `json:"search_id"`
		ServTxt                     string        `json:"serv_txt"`
		ServiceTags                 []int         `json:"service_tags"`
		ShareRate                   int           `json:"share_rate"`
		SubsidyGoodsType            int           `json:"subsidy_goods_type"`
		UnifiedTags                 []string      `json:"unified_tags"`
		ZsDuoId                     int           `json:"zs_duo_id"`
		EcCpsInfo                   struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	Msg          string `json:"msg"`
	TotalResults int    `json:"total_results"`
}
type TbkPddGoodsDetail2 struct {
	Code int `json:"code"`
	Data []struct {
		ActivityTags                []int         `json:"activity_tags"`
		BrandName                   string        `json:"brand_name"`
		CatId                       int           `json:"cat_id"`
		CatIds                      []int         `json:"cat_ids"`
		CategoryId                  int           `json:"category_id"`
		CategoryName                string        `json:"category_name"`
		CouponDiscount              int           `json:"coupon_discount"`
		CouponEndTime               int           `json:"coupon_end_time"`
		CouponMinOrderAmount        int           `json:"coupon_min_order_amount"`
		CouponRemainQuantity        int           `json:"coupon_remain_quantity"`
		CouponStartTime             int           `json:"coupon_start_time"`
		CouponTotalQuantity         int           `json:"coupon_total_quantity"`
		DescTxt                     string        `json:"desc_txt"`
		GoodsDesc                   string        `json:"goods_desc"`
		GoodsGalleryUrls            []string      `json:"goods_gallery_urls"`
		GoodsId                     int64         `json:"goods_id"`
		GoodsImageUrl               string        `json:"goods_image_url"`
		GoodsName                   string        `json:"goods_name"`
		GoodsSign                   string        `json:"goods_sign"`
		GoodsThumbnailUrl           string        `json:"goods_thumbnail_url"`
		HasCoupon                   bool          `json:"has_coupon"`
		HasMallCoupon               bool          `json:"has_mall_coupon"`
		IsMultiGroup                bool          `json:"is_multi_group"`
		LgstTxt                     string        `json:"lgst_txt"`
		MallCouponDiscountPct       int           `json:"mall_coupon_discount_pct"`
		MallCouponEndTime           int           `json:"mall_coupon_end_time"`
		MallCouponMaxDiscountAmount int           `json:"mall_coupon_max_discount_amount"`
		MallCouponMinOrderAmount    int           `json:"mall_coupon_min_order_amount"`
		MallCouponRemainQuantity    int           `json:"mall_coupon_remain_quantity"`
		MallCouponStartTime         int           `json:"mall_coupon_start_time"`
		MallCouponTotalQuantity     int           `json:"mall_coupon_total_quantity"`
		MallCps                     int           `json:"mall_cps"`
		MallId                      int           `json:"mall_id"`
		MallImgUrl                  string        `json:"mall_img_url"`
		MallName                    string        `json:"mall_name"`
		MaterialList                []interface{} `json:"material_list"`
		MerchantType                int           `json:"merchant_type"`
		MinGroupPrice               int           `json:"min_group_price"`
		MinNormalPrice              int           `json:"min_normal_price"`
		OnlySceneAuth               bool          `json:"only_scene_auth"`
		OptId                       int           `json:"opt_id"`
		OptIds                      []int         `json:"opt_ids"`
		OptName                     string        `json:"opt_name"`
		PlanType                    int           `json:"plan_type"`
		PlatformDiscountList        []interface{} `json:"platform_discount_list"`
		PredictPromotionRate        int           `json:"predict_promotion_rate"`
		PromotionRate               int           `json:"promotion_rate"`
		SalesTip                    string        `json:"sales_tip"`
		ServTxt                     string        `json:"serv_txt"`
		ServiceTags                 []int         `json:"service_tags"`
		ShareRate                   int           `json:"share_rate"`
		SubsidyGoodsType            int           `json:"subsidy_goods_type"`
		UnifiedTags                 []string      `json:"unified_tags"`
		VideoUrls                   []string      `json:"video_urls"`
		ZsDuoId                     int           `json:"zs_duo_id"`
		EcCpsInfo                   struct {
			CommissionAmount float64 `json:"commission_amount"`
			CommissionRate   float64 `json:"commission_rate"`
		} `json:"ec_cps_info"`
	} `json:"data"`
	Msg string `json:"msg"`
}

func GoodsDetail2(request map[string]interface{}, userID uint) (err error, response interface{}) {
	err, result := TbxRequest(GoodsDetail2Url, request)
	if err != nil {
		return
	}

	var materialResponse TbkPddGoodsDetail2
	jsonData, err := json.Marshal(result)
	if err != nil {
		return
	}

	err = json.Unmarshal(jsonData, &materialResponse)
	if err != nil {
		return
	}
	var userModel model2.User
	err = source.DB().Preload("UserLevelInfo").First(&userModel, userID).Error
	if err != nil {
		return
	}
	for key, item := range materialResponse.Data {
		// 计算新的佣金金额并保留两位小数
		newCommissionAmount := utils.Decimal(item.EcCpsInfo.CommissionAmount * float64(userModel.UserLevelInfo.CpsRatio) / 10000)
		newCommissionRate := math.Round(item.EcCpsInfo.CommissionRate * float64(userModel.UserLevelInfo.CpsRatio) / 10000)

		// 将计算结果转回字符串，保留两位小数
		materialResponse.Data[key].EcCpsInfo.CommissionAmount = newCommissionAmount
		materialResponse.Data[key].EcCpsInfo.CommissionRate = newCommissionRate
	}
	return nil, materialResponse
}

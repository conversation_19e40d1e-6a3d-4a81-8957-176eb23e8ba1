package cps

import (
	"douyin-cps/model"
	"encoding/json"
	"errors"
	"github.com/google/uuid"
	"strconv"
	"time"
	"yz-go/utils"
)

func GetRequestParams(data interface{}) (err error, params map[string]interface{}) {

	err, setting := model.GetCpsSetting()
	if err != nil {
		return
	}
	if setting.RoleId <= 0 || setting.SecurityKey == "" {
		err = errors.New("cps配置错误,RoleId小于等于0或SecurityKey为空")
		return
	}
	params = make(map[string]interface{})
	var jsonData []byte
	jsonData, err = json.Marshal(data)
	if err != nil {
		return
	}
	var reqId = uuid.New().String()
	var signStr string
	var timestamp = time.Now().Unix()
	var appId = strconv.Itoa(setting.RoleId)
	var appKey = setting.SecurityKey
	signStr = "app_id=" + appId + "&data=" + string(jsonData) + "&req_id=" + reqId + "&timestamp=" + strconv.Itoa(int(timestamp)) + appKey
	sign := utils.MD5V([]byte(signStr))

	params["app_id"] = strconv.Itoa(setting.RoleId)
	params["timestamp"] = int(timestamp)
	params["version"] = "1"
	params["sign"] = sign
	params["sign_type"] = "MD5"
	params["req_id"] = reqId
	params["data"] = string(jsonData)
	return
}

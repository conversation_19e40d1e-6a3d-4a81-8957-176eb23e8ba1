module purchase-account

go 1.21

replace (
	convergence => ../convergence-pay
	finance => ../finance
	gin-vue-admin => ../gin-vue-admin/server
	notification => ../notification
	order => ../order

	payment => ../payment
	region => ../region
	shipping => ../shipping
	user => ../user
	yz-go v1.0.0 => ../yz-go

)

require (
	gin-vue-admin v1.0.0
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/chenhg5/collection v0.0.0-**************-f403b87088f9
	github.com/gin-gonic/gin v1.6.3
	github.com/gogf/gf v1.16.9
	go.uber.org/zap v1.16.0
	gorm.io/gorm v1.25.5
	user v1.0.0
	yz-go v1.0.0
)

require (
	github.com/stretchr/testify v1.8.0 // indirect
	golang.org/x/crypto v0.0.0-**************-4161e89ecf1b // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)

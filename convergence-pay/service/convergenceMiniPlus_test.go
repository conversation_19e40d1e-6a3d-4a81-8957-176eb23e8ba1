package service

import (
	"convergence/model"
	"reflect"
	"strconv"
	"testing"
	"time"
)

func TestGetMiniPlusPayInfo(t *testing.T) {

	Host := "http://www.baidu.com/"
	lastChar := string(Host[len(Host)-1])
	if lastChar == "/" {
		Host = Host[:len(Host)-1]
	}

	return

	type args struct {
		param model.UniPay
	}
	tests := []struct {
		name    string
		args    args
		wantErr error
		wantRes map[string]interface{}
	}{
		{name: "", args: args{
			param: model.UniPay{
				Uid:        1,
				P2_OrderNo: strconv.Itoa(int(time.Now().Unix())),
				P3_Amount:  "0.01",
			},
		}},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotRes := GetMiniPlusPayInfo(tt.args.param)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.<PERSON>rf("GetMiniPlusPayInfo() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotRes, tt.wantRes) {
				t.Errorf("GetMiniPlusPayInfo() gotRes = %v, want %v", gotRes, tt.wantRes)
			}
		})
	}
}

func TestGetMiniPlusPayInfoBySmallShop(t *testing.T) {
	type args struct {
		param model.UniPay
	}
	tests := []struct {
		name                string
		args                args
		wantErr             error
		wantMiniPlusResData model.MiniPlusResData
	}{
		{name: ""},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr, gotMiniPlusResData := GetMiniPlusPayInfoBySmallShop(tt.args.param)
			if !reflect.DeepEqual(gotErr, tt.wantErr) {
				t.Errorf("GetMiniPlusPayInfoBySmallShop() gotErr = %v, want %v", gotErr, tt.wantErr)
			}
			if !reflect.DeepEqual(gotMiniPlusResData, tt.wantMiniPlusResData) {
				t.Errorf("GetMiniPlusPayInfoBySmallShop() gotMiniPlusResData = %v, want %v", gotMiniPlusResData, tt.wantMiniPlusResData)
			}
		})
	}
}

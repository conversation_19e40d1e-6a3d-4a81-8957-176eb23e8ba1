package service

import (
	"errors"
	"surface-single/model"
	"surface-single/request"
	yzRequest "yz-go/request"
	"yz-go/source"
)

func GetDefaultSurfaceSingle(supplierID uint) (err error, surfaceSingle model.SurfaceSingle) {
	err = source.DB().Where("is_default = ? and supplier_id = ?", 1, supplierID).First(&surfaceSingle).Error
	return
}

func CreateSurfaceSingle(surfaceSingle model.SurfaceSingle) (err error) {
	err = source.DB().Create(&surfaceSingle).Error
	return err
}

func DeleteSurfaceSingle(surfaceSingle model.SurfaceSingle) (err error) {
	err = source.DB().Delete(&surfaceSingle).Error
	return err
}

func UpdateSurfaceSingle(surfaceSingle model.SurfaceSingle) (err error) {
	//err = source.DB().Updates(&surfaceSingle).Error
	err = source.DB().Model(&surfaceSingle).Updates(map[string]interface{}{
		"name":          surfaceSingle.Name,
		"user_name":     surfaceSingle.UserName,
		"password":      surfaceSingle.Password,
		"sign":          surfaceSingle.Sign,
		"shipper_code":  surfaceSingle.ShipperCode,
		"shipper_name":  surfaceSingle.ShipperName,
		"exp_type":      surfaceSingle.ExpType,
		"template_size": surfaceSingle.TemplateSize,
		"station_sign":  surfaceSingle.StationSign,
		"is_default":    surfaceSingle.IsDefault,
		"is_notice":     surfaceSingle.IsNotice,
		"start_date":    surfaceSingle.StartDate,
		"end_date":      surfaceSingle.EndDate,
	}).Error
	return err
}

func GetSurfaceSingle(id uint) (err error, surfaceSingle model.SurfaceSingle) {
	err = source.DB().Where("id = ?", id).First(&surfaceSingle).Error
	return
}

func GetSurfaceSinglesList(info yzRequest.PageInfo, supplierID uint) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&model.SurfaceSingle{})
	db.Where("supplier_id = ?", supplierID)
	var surfaceSingles []model.SurfaceSingle
	err = db.Count(&total).Error

	err = db.Order("created_at desc").Limit(limit).Offset(offset).Find(&surfaceSingles).Error
	return err, surfaceSingles, total
}

func ChangeDefault(ss request.SSDefault, supplierID uint) (err error) {
	var surfaceSingle model.SurfaceSingle
	err = source.DB().Model(&surfaceSingle).Where("`id` = ?", ss.ID).First(&surfaceSingle).Error
	if err != nil {
		return err
	}
	if surfaceSingle.SupplierID != supplierID {
		err = errors.New("面单归属错误")
		return err
	}
	surfaceSingle.IsDefault = ss.IsDefault
	err = source.DB().Model(&surfaceSingle).Where("id = ?", ss.ID).Updates(map[string]interface{}{"is_default": ss.IsDefault}).Error
	return err
}

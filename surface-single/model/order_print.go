package model

import "yz-go/source"

type OrderPrintMigration struct {
	source.Model
	OrderId      uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	ProductTotal int  `json:"product_total" form:"product_total" gorm:"column:product_total;comment:订单商品总数;"`
	PrintedTotal int  `json:"printed_total" form:"printed_total" gorm:"column:printed_total;comment:已打印商品总数;"`
	Status       int  `json:"status" gorm:"column:status;comment:0：未全部打印；1：已全部打印;type:int;"`
}

func (OrderPrintMigration) TableName() string {
	return "order_prints"
}

type OrderPrint struct {
	source.Model
	OrderId         uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	ProductTotal    int  `json:"product_total" form:"product_total" gorm:"column:product_total;comment:订单商品总数;"`
	PrintedTotal    int  `json:"printed_total" form:"printed_total" gorm:"column:printed_total;comment:已打印商品总数;"`
	Status          int  `json:"status" gorm:"column:status;comment:0：未全部打印；1：已全部打印;type:int;"`
	OrderPrintItems []OrderPrintItem
}

type OrderPrintItem struct {
	source.Model
	OrderPrintID                uint   `json:"order_print_id" form:"order_print_id" gorm:"column:order_print_id;comment:order_print.id;"`
	OrderId                     uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`
	SN                          string `json:"sn" form:"sn" gorm:"column:sn;comment:原单号或新单号,请求面单的单号;"`
	PrintedTotal                int    `json:"printed_total" form:"printed_total" gorm:"column:printed_total;comment:已打印商品总数;"`
	EBusinessID                 string `json:"e_business_id" form:"e_business_id" gorm:"column:e_business_id;comment:用户ID;type:varchar(20);size:20;"`
	OrderCode                   string `json:"order_code" form:"order_code" gorm:"column:order_code;comment:订单编号;type:varchar(30);size:30;"`
	ShipperCode                 string `json:"shipper_code" form:"shipper_code" gorm:"column:shipper_code;comment:快递公司编码;type:varchar(10);size:10;"`
	LogisticCode                string `json:"logistic_code" form:"logistic_code" gorm:"column:logistic_code;comment:快递单号;type:varchar(400);size:400;"`
	MarkDestination             string `json:"mark_destination" form:"mark_destination" gorm:"column:mark_destination;comment:大头笔;type:varchar(20);size:20;"`
	OriginCode                  string `json:"origin_code" form:"origin_code" gorm:"column:origin_code;comment:始发地区域编码	;type:varchar(20);size:20;"`
	OriginName                  string `json:"origin_name" form:"origin_name" gorm:"column:origin_name;comment:始发地/始发网点;type:varchar(20);size:20;"`
	DestinatioCode              string `json:"destinatio_code" form:"destinatio_code" gorm:"column:destinatio_code;comment:目的地区域编码;type:varchar(20);size:20;"`
	DestinatioName              string `json:"destinatio_name" form:"destinatio_name" gorm:"column:destinatio_name;comment:目的地/到达网点;type:varchar(20);size:20;"`
	SortingCode                 string `json:"sorting_code" form:"sorting_code" gorm:"column:sorting_code;comment:分拣编码;type:varchar(20);size:20;"`
	PackageCode                 string `json:"package_code" form:"package_code" gorm:"column:package_code;comment:集包编码;type:varchar(20);size:20;"`
	PackageName                 string `json:"package_name" form:"package_name" gorm:"column:package_name;comment:集包地;type:varchar(50);size:50;"`
	DestinationAllocationCentre string `json:"destination_allocation_centre" form:"destination_allocation_centre" gorm:"column:destination_allocation_centre;comment:目的地分类;type:varchar(50);size:50;"`
	Success                     bool   `json:"success" form:"success" gorm:"column:success;comment:成功与否(true/false);"`
	SignWaybillCode             string `json:"sign_waybill_code" form:"sign_waybill_code" gorm:"column:sign_waybill_code;comment:签回单单号;type:varchar(15);size:15;"`
	ResultCode                  string `json:"result_code" form:"result_code" gorm:"column:result_code;comment:返回编码;type:varchar(5);size:5;"`
	Reason                      string `json:"reason" form:"reason" gorm:"column:reason;comment:失败原因;type:varchar(50);size:50;"`
	URN                         string `json:"urn" form:"urn" gorm:"column:urn;comment:面单唯一标识UniquerRequestNumber;type:varchar(50);size:50;"`
	PrintTemplate               string `json:"print_template" form:"print_template" gorm:"column:print_template;comment:面单打印模板内容(html格式);type:longtext;"`
}

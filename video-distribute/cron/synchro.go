package cron

import (
	"time"
	"video-distribute/request"
	"video-distribute/service"
	"yz-go/cron"
)

func SynchroHandle() {
	task := cron.Task{
		Key:  "videoDistributeSynchro",
		Name: "短视频分发自动同步",
		Spec: "* * * * * *",
		Handle: func(task cron.Task) {
			SynchroVideos()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func SynchroVideos() {
	setting, err := service.GetAutoSynchroValue()
	if err != nil {
		return
	}
	// 判断当前时间是否满足同步条件
	now := time.Now()
	hour := now.Hour()
	minute := now.Minute()
	if setting.SyncHour == hour && setting.SyncMinute == minute {
		// 根据分组设置方式、供应链勾选不为空 执行同步
		if len(setting.GatherIds) > 0 {
			for _, gatherId := range setting.GatherIds {
				if err = synchroGatherAlbums(gatherId, uint(setting.GroupType), uint(setting.GroupID)); err != nil {
					return
				}
			}
		}
	}
}

func synchroGatherAlbums(gatherId, groupType, groupId uint) (err error) {
	search := request.GatherVideoListSearch{
		AppGatherVideoListSearch: request.AppGatherVideoListSearch{
			IsImport: 1,
		},
		GatherID: gatherId,
	}
	err, list, _ := service.GetGatherVideoList(search)
	if err != nil {
		return
	}

	// 组装需要导入短视频ids
	var videoIds []uint
	for _, video := range list {
		videoIds = append(videoIds, video.ID)
	}

	// 获取需要同步的短视频数据
	err, videos := service.GetVideoImportData(gatherId, videoIds)
	if err != nil {
		return
	}

	// 同步短视频数据
	if err = service.ImportVideo(videos, gatherId, groupType, groupId); err != nil {
		return
	}
	return
}

package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	"product/model"
	"product/request"
	"product/service"
	"strconv"
	service2 "supplier/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @Tags Topic
// @Summary 创建Topic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Topic true "创建Topic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /topic/createTopic [post]
func CreateTopic(c *gin.Context) {
	var topic model.Topic
	err := c.ShouldBindJSON(&topic)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service2.GetSupplierByUserId(v1.GetUserID(c))
	if err == nil {
		topic.SupplierID = supplier.ID
	} else {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}

	if err := service.CreateTopic(topic); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags Topic
// @Summary 删除Topic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Topic true "删除Topic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /topic/deleteTopic [delete]
func DeleteTopic(c *gin.Context) {
	var topic model.Topic
	err := c.ShouldBindJSON(&topic)
	err = source.DB().First(&topic, topic.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteTopic(topic); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags Topic
// @Summary 批量删除Topic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除Topic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /topic/deleteTopicByIds [delete]
func DeleteTopicByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteTopicByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags Topic
// @Summary 更新Topic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Topic true "更新Topic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /topic/updateTopic [put]
func UpdateTopic(c *gin.Context) {
	var topic model.Topic
	err := c.ShouldBindJSON(&topic)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateTopic(topic); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Topic
// @Summary 用id查询Topic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Topic true "用id查询Topic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /topic/findTopic [get]
func FindTopic(c *gin.Context) {
	var topic yzRequest.GetById
	_ = c.ShouldBindQuery(&topic)
	if err, retopic := service.GetTopic(topic.Id); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"retopic": retopic}, c)
	}
}

// @Tags Topic
// @Summary 分页获取Topic列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.TopicSearch true "分页获取Topic列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /topic/getTopicList [get]
func GetTopicList(c *gin.Context) {
	var pageInfo request.TopicSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service2.GetSupplierByUserId(v1.GetUserID(c))
	if err == nil {
		pageInfo.SupplierID = supplier.ID
	} else {
		log.Log().Error("供应商不存在", zap.Any("err", err))
		yzResponse.FailWithMessage("供应商不存在", c)
		return
	}

	if err, list, total := service.GetTopicInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

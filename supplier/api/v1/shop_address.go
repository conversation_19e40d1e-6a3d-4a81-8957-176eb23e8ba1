package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"supplier/model"
	"supplier/request"
	"supplier/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	service2 "yz-go/service"

	yzResponse "yz-go/response"
)

// @Tags 商家地址管理
// @Summary 创建商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/jsons
// @Param data body model.ShopAddress true "创建商家地址管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /shopAddress/createShopAddress [post]
func CreateShopAddress(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindJSON(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateShopAddress(shopAddress); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "创建了地址'"+strconv.Itoa(int(shopAddress.ID))+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 商家地址管理
// @Summary 删除商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ShopAddress true "删除商家地址管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /shopAddress/deleteShopAddress [post]
func DeleteShopAddress(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindJSON(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteShopAddress(shopAddress); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "删除了地址'"+strconv.Itoa(int(shopAddress.ID))+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags 商家地址管理
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /shopAddress/deleteShopAddressByIds [post]
func DeleteShopAddressByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	var err error
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteShopAddressByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "批量删除了地址'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags 商家地址管理
// @Summary 更新商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierQualification true "更新商家地址管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /shopAddress/updateSupplierQualification [post]
func UpdateShopAddress(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindJSON(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateShopAddress(shopAddress); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags 商家地址管理
// @Summary 用id查询商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierQualification true "用id查询商家地址管理"
// @Success 200 {object} model.ShopAddress
// @Router /shopAddress/findShopAddressById [get]
func FindShopAddressById(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindQuery(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, res := service.GetShopAddressById(shopAddress.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(res, c)
	}
}

// @Tags 商家地址管理
// @Summary 商家地址管理列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierQualificationSearch true "商家地址管理列表"
// @Success 200 {string} string []model.ShopAddress
// @Router /shopAddress/getShopAddressListByAdmin [get]
func GetShopAddressList(c *gin.Context) {
	var pageInfo request.ShopAddressSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetShopAddressInfoList(pageInfo); err != nil {
		log.Log().Error("获取商家地址管理列表失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取商家地址管理列表成功", c)
	}
}

// @Tags 商家地址管理
// @Summary 商家地址管理列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierQualificationSearch true "商家地址管理列表"
// @Success 200 {string} string []model.ShopAddress
// @Router /shopAddress/getShopAddressAll [get]
func GetShopAddressAll(c *gin.Context) {
	var pageInfo request.ShopAddressSearchAll
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetShopAddressInfoAll(pageInfo); err != nil {
		log.Log().Error("获取商家地址管理列表失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取商家地址管理列表成功", c)
	}
}

// @Tags 供应商商家地址管理
// @Summary 创建供应商商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/jsons
// @Param data body model.ShopAddress true "创建供应商商家地址管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /supplierShopAddress/createShopAddressBySupplier [post]
func CreateShopAddressBySupplier(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindJSON(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
		return
	}
	shopAddress.SupplierID = supplier.ID
	if err := service.CreateShopAddress(shopAddress); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "新增商家地址")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags 供应商商家地址管理
// @Summary 删除供应商商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ShopAddress true "删除供应商商家地址管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /supplierShopAddress/deleteShopAddressBySupplier [post]
func DeleteShopAddressBySupplier(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindJSON(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
		return
	}

	if err, res := service.GetShopAddressById(shopAddress.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		if res.SupplierID != supplier.ID {
			yzResponse.FailWithMessage("不是这个供应商的地址", c)
			return
		}
		if err := service.DeleteShopAddress(shopAddress); err != nil {
			log.Log().Error("删除失败!", zap.Any("err", err))
			yzResponse.FailWithMessage("删除失败", c)
			return
		} else {
			service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "删除商家地址")
			yzResponse.OkWithMessage("删除成功", c)
		}
	}

}

// @Tags 供应商商家地址管理
// @Summary 更新商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierQualification true "更新商家地址管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /supplierShopAddress/updateShopAddressBySupplier [post]
func UpdateShopAddressBySupplier(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindJSON(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
		return
	}
	shopAddress.SupplierID = supplier.ID
	if err := service.UpdateShopAddress(shopAddress); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 7, c.ClientIP(), "修改商家地址")
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags 供应商商家地址管理
// @Summary 用id查询供应商商家地址管理
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierQualification true "用id查询供应商商家地址管理"
// @Success 200 {object} model.ShopAddress
// @Router /supplierShopAddress/findShopAddressByIdBySupplier [get]
func FindShopAddressByIdBySupplier(c *gin.Context) {
	var shopAddress model.ShopAddress
	var err error
	err = c.ShouldBindQuery(&shopAddress)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
		return
	}

	if err, res := service.GetShopAddressById(shopAddress.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		if res.SupplierID != supplier.ID {
			yzResponse.FailWithMessage("不是这个供应商的地址", c)
			return
		}
		yzResponse.OkWithData(res, c)
	}
}

// @Tags 供应商商家地址管理
// @Summary 供应商商家地址管理列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierQualificationSearch true "商家地址管理列表"
// @Success 200 {string} string []model.ShopAddress
// @Router /supplierShopAddress/getShopAddressListBySupplier [get]
func GetShopAddressListBySupplier(c *gin.Context) {
	var pageInfo request.ShopAddressSearch
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
		return
	}

	pageInfo.SupplierID = &supplier.ID

	if err, list, total := service.GetShopAddressInfoList(pageInfo); err != nil {
		log.Log().Error("获取商家地址管理列表失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取商家地址管理列表成功", c)
	}
}

// @Tags 供应商商家地址管理
// @Summary 供应商商家地址管理列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierQualificationSearch true "商家地址管理列表"
// @Success 200 {string} string []model.ShopAddress
// @Router /supplierShopAddress/getShopAddressAllBySupplier [get]
func GetShopAddressAllBySupplier(c *gin.Context) {
	var pageInfo request.ShopAddressSearchAll
	var err error
	err = c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
		return
	}

	pageInfo.SupplierID = &supplier.ID

	if err, list := service.GetShopAddressInfoAll(pageInfo); err != nil {
		log.Log().Error("获取商家地址管理列表失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取商家地址管理列表成功", c)
	}
}

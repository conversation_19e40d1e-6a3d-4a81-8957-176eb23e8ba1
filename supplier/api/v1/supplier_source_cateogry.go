package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"supplier/model"
	"supplier/request"
	"supplier/service"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
)

// @Tags SupplierSourceCategory
// @Summary 创建SupplierSourceCategory
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierSourceCategory true "创建SupplierSourceCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/createSupplierSourceCategory [post]
func CreateSupplierSourceCategory(c *gin.Context) {
	var user model.SupplierSourceCategory
	err := c.ShouldBindJSON(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if supplierID == 0 {
		log.Log().Error("supplier_id为必填", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	user.SupplierID = uint(supplierID)
	if err := service.CreateSupplierSourceCategory(user); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 2, c.ClientIP(), "新增了会员等级'"+user.Name+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags SupplierSourceCategory
// @Summary 删除SupplierSourceCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierSourceCategory true "删除SupplierSourceCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /user/deleteSupplierSourceCategory [delete]
func DeleteSupplierSourceCategory(c *gin.Context) {
	var user model.SupplierSourceCategory
	err := c.ShouldBindJSON(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteSupplierSourceCategory(user); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 2, c.ClientIP(), "删除了会员等级'"+user.Name+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags SupplierSourceCategory
// @Summary 批量删除SupplierSourceCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除SupplierSourceCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /user/deleteSupplierSourceCategoryByIds [delete]
func DeleteSupplierSourceCategoryByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
	err := c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteSupplierSourceCategoryByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 2, c.ClientIP(), "批量删除会员等级'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags SupplierSourceCategory
// @Summary 更新SupplierSourceCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierSourceCategory true "更新SupplierSourceCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /user/updateSupplierSourceCategory [put]
func UpdateSupplierSourceCategory(c *gin.Context) {
	var user model.SupplierSourceCategory
	err := c.ShouldBindJSON(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.UpdateSupplierSourceCategory(user); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 2, c.ClientIP(), "编辑了会员等级'"+user.Name+"'")
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags SupplierSourceCategory
// @Summary 用id查询SupplierSourceCategory
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierSourceCategory true "用id查询SupplierSourceCategory"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /user/findSupplierSourceCategory [get]
func FindSupplierSourceCategory(c *gin.Context) {
	var user model.SupplierSourceCategory
	err := c.ShouldBindQuery(&user)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, reuser := service.GetSupplierSourceCategory(user.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reuser": reuser}, c)
	}
}

// @Tags SupplierSourceCategory
// @Summary 分页获取SupplierSourceCategory列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierSourceCategorySearch true "分页获取SupplierSourceCategory列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getSupplierSourceCategoryList [get]
func GetSupplierSourceCategoryList(c *gin.Context) {
	var pageInfo request.SupplierSourceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if supplierID == 0 {
		yzResponse.FailWithMessage("supplier_id为必填", c)
		return
	}
	pageInfo.SupplierID = uint(supplierID)
	if err, list, total := service.GetSupplierSourceCategoryInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}



// @Tags SupplierSourceCategory
// @Summary 分页获取SupplierSourceCategory列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierSourceCategorySearch true "分页获取SupplierSourceCategory列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /user/getSupplierSourceCategoryList [get]
func GetSupplierSourceCategoryOptionList(c *gin.Context) {
	var pageInfo request.SupplierSourceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if pageInfo.SupplierID == 0 {
		var supplierID int
		err, supplierID = v1.GetSupplierByUserId(v1.GetUserID(c))
		if err != nil || supplierID == 0 {
			yzResponse.FailWithMessage("supplier_id为必填", c)
			return
		}
		pageInfo.SupplierID = uint(supplierID)
	}
	if err, list := service.GetSupplierSourceCategoryOptionList(pageInfo.SupplierID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}



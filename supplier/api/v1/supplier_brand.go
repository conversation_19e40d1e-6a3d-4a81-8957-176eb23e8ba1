package v1

import (
	"category/model"
	"category/request"
	"category/service"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// @Tags Brands
// @Summary 创建Brands
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Brand true "创建Brands"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"获取成功"}"
// @Router /brands/createBrands [post]
func CreateBrand(c *gin.Context) {
	var brands model.Brand
	err := c.ShouldBindJSON(&brands)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	brands.SupplierID = uint(supplierID)
	if err := service.CreateBrands(brands); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags Brands
// @Summary 删除Brands
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Brand true "删除Brands"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /brands/deleteBrands [delete]
func DeleteBrand(c *gin.Context) {
	var brands model.Brand
	err := c.ShouldBindJSON(&brands)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteBrands(brands); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags Brands
// @Summary 批量删除Brands
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除Brands"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /brands/deleteBrandsByIds [delete]
func DeleteBrandByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteBrandsByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags Brands
// @Summary 更新Brands
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Brand true "更新Brands"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /brands/updateBrands [put]
func UpdateBrand(c *gin.Context) {
	var brands model.Brand
	err := c.ShouldBindJSON(&brands)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateBrands(brands); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags Brands
// @Summary 用id查询Brands
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Brand true "用id查询Brands"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /brands/findBrands [get]
func FindBrand(c *gin.Context) {
	var brands model.Brand
	_ = c.ShouldBindQuery(&brands)

	if err, rebrands := service.GetBrands(brands.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"rebrands": rebrands}, c)
	}
}

// @Tags Brands
// @Summary 分页获取Brands列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.BrandsSearch true "分页获取Brands列表"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"获取成功"}"
// @Router /brands/getBrandsList [get]
func GetSupplierBrandsInfoList(c *gin.Context) {
	var pageInfo request.BrandsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.SupplierID = uint(supplierID)
	if err, list, total := service.GetSupplierBrandsInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}


// @Tags Brands
// @Summary 分页获取Brands列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.BrandsSearch true "分页获取Brands列表"
// @Success 200 {string} string "{"code":0,"data":{},"msg":"获取成功"}"
// @Router /brands/getBrandsList [get]
func GetBrandList(c *gin.Context) {
	var pageInfo request.BrandsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.SupplierID = uint(supplierID)
	if err, list, total := service.GetBrandsInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetBrandOptionList(c *gin.Context) {

	err, supplierID := v1.GetSupplierByUserId(v1.GetUserID(c))
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetBrandsList(model.Brand{SupplierID: uint(supplierID)}); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

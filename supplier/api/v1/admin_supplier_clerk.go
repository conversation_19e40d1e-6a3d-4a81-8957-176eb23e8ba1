package v1

import (
	v1 "gin-vue-admin/admin/api/v1"
    "supplier/model"
	"yz-go/component/log"
	yzRequest "yz-go/request"
    "supplier/request"
	yzResponse "yz-go/response"
    "supplier/service"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

// @Tags 成员
// @Summary 创建
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "创建"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /api/supplierClerk/createSupplierClerk [post]
func CreateSupplierClerk(c *gin.Context) {
	var supplierClerk request.SupplierClerkCreate
	var err error
	err = c.ShouldBindJSON(&supplierClerk)
	if err != nil{
	    yzResponse.FailWithMessage(err.Error(), c)
	    return
	}

	err = service.CreateSupplierClerk(supplierClerk)
	if err != nil {
        log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
    yzResponse.OkWithMessage("创建成功", c)
}

// @Tags 成员
// @Summary 删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /api/supplierClerk/deleteSupplierClerk [post]
func DeleteSupplierClerk(c *gin.Context) {
	var supplierClerk model.SupplierClerk
	var err error
    err = c.ShouldBindJSON(&supplierClerk)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err = service.DeleteSupplierClerk(supplierClerk)
	if err != nil {
        log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
    yzResponse.OkWithMessage("删除成功", c)

}

// @Tags 成员
// @Summary 批量删除
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /api/supplierClerk/deleteSupplierClerkByIds [post]
func DeleteSupplierClerkByIds(c *gin.Context) {
	var IDS yzRequest.IdsReq
    var err error
    err = c.ShouldBindJSON(&IDS)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err = service.DeleteSupplierClerkByIds(IDS)
	if err != nil {
        log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
    yzResponse.OkWithMessage("批量删除成功", c)
}

// @Tags 成员
// @Summary 更新
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "更新"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /api/supplierClerk/updateSupplierClerk [post]
func UpdateSupplierClerk(c *gin.Context) {
	var supplierClerk request.SupplierClerkCreate
	var err error
    err = c.ShouldBindJSON(&supplierClerk)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	err = service.UpdateSupplierClerk(supplierClerk)
	if err != nil {
        log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	}
    yzResponse.OkWithMessage("更新成功", c)
}

// @Tags 成员
// @Summary 用id查询
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询"
// @Success 200 {object} model.SupplierClerk
// @Router /api/supplierClerk/findSupplierClerk [get]
func FindSupplierClerk(c *gin.Context) {
	var supplierClerk model.SupplierClerk
	err := c.ShouldBindQuery(&supplierClerk)
	if err != nil{
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, resupplierClerk := service.GetSupplierClerk(supplierClerk.ID)
	if err != nil {
        log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	yzResponse.OkWithData(gin.H{"resupplierClerk": resupplierClerk}, c)
}

// @Tags 成员
// @Summary 分页获取列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.SupplierClerkSearch true "分页获取列表"
// @Success 200 {string} string []model.SupplierClerk
// @Router /api/supplierClerk/getSupplierClerkList [get]
func GetSupplierClerkList(c *gin.Context) {
	var pageInfo request.SupplierClerkSearch
	var err error
    err = c.ShouldBindQuery(&pageInfo)
    if err != nil{
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
	serr, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if serr != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
	}
	pageInfo.Sid = int(supplier.ID)
	err, list, total := service.GetSupplierClerkInfoList(pageInfo)
	if err != nil {
        log.Log().Error("获取失败", zap.Any("err", err))
        yzResponse.FailWithMessage(err.Error(), c)
        return
    }
    yzResponse.OkWithDetailed(yzResponse.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

func GetSupplierJobInfoListNotPage(c *gin.Context) {
	serr, supplier := service.GetSupplierByUserId(v1.GetUserID(c))
	if serr != nil {
		yzResponse.FailWithMessage("供应商信息查询失败", c)
	}
	if err, list := service.GetSupplierJobInfoListNotPage(int(supplier.ID)); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}



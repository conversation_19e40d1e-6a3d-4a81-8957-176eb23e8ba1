package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"supplier/request"
	"supplier/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// @Tags 供应商
// @Summary 获取供应商列表及三个销量最高的商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "获取供应商列表及三个销量最高的商品"
// @Success 200 {object} response.SupplierProduct
// @Router /supplier/getSupplierProductList [post]
func GetSupplierProductList(c *gin.Context) {
	var pageInfo request.GetSupplierProductListSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 10
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}
	if err, list, total := service.GetSupplierProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 供应商
// @Summary 获取供应链及三个销量最高的商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "获取供应链及三个销量最高的商品"
// @Success 200 {object} response.SupplierProduct
// @Router /supplier/getSupplierProductList [post]
func GetGatherSupplierProductList(c *gin.Context) {
	var pageInfo request.GetGatherSupplierProductListSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 10
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}

	if err, list,total := service.GetGatherSupplierProductList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// @Tags 供应商
// @Summary 获取供应商分类
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param  true "获取供应商分类"
// @Success 200 {object} response.SupplierProduct
// @Router /supplier/getSupplierProductList [post]
func GetSupplierCategorys(c *gin.Context) {
	var pageInfo request.GetGatherSupplierProductListSearch
	_ = c.ShouldBindJSON(&pageInfo)

	if err, list:= service.GetSupplierCategorys(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list,c)
	}
}
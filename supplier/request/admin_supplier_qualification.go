package request

import (
	"supplier/model"
	yzRequest "yz-go/request"
)

type SupplierQualificationSearch struct{
    model.SupplierQualification
    yzRequest.PageInfo
}

type SupplierQualifications struct{
	SupplierID        uint       `json:"supplier_id" form:"supplier_id"`
}
type SupplierQualificationsCategories struct{
	SupplierID        uint       `json:"supplier_id" form:"supplier_id"`
	CategoryId int `json:"category_id" form:"category_id"`  //上级分类id
	Level int `json:"level"  form:"level"`  //层级 1-3

}
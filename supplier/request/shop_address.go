package request

import (
	"supplier/model"
	yzRequest "yz-go/request"
)

type ShopAddressSearch struct {
	model.ShopAddress
	yzRequest.PageInfo
	Keyword    string `json:"keyword" form:"keyword" gorm:"column:key_word;comment:关键字;type:varchar(255);size:255;"`
	SupplierID *uint  `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"` // 供应商id

}

type ShopAddressSearchAll struct {
	model.ShopAddress
	yzRequest.PageInfo
	Keyword    string `json:"keyword" form:"keyword" gorm:"column:key_word;comment:关键字;type:varchar(255);size:255;"`
	SupplierID *uint  `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;index;"` // 供应商id

}
type ShopAddressOrderId struct {
	OrderId      uint   `json:"order_id" form:"order_id"`
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"` // 采购端单号

}

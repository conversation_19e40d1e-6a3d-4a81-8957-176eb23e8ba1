package service

import (
	"errors"
	"gorm.io/gorm"
	mq2 "notification/mq"
	"supplier/model"
	"supplier/mq"
	"supplier/request"
	service2 "yz-go/service"
	"yz-go/source"
)

// @author: [piexlmax](https://github.com/piexlmax)
// @function: CreateSupplier
// @description: 创建SupplierApply记录
// @param: supplierApply model.SupplierApply
// @return: err error
func CreateSupplierApply(supplierApply model.SupplierApply) (err error) {
	var supplier model.Supplier
	if !errors.Is(source.DB().Where("uid = ?", supplierApply.Uid).First(&supplier).Error, gorm.ErrRecordNotFound) {
		return errors.New("您已经是供应商了")
	}
	var resSupplierApply model.SupplierApply
	if !errors.Is(source.DB().Where("name = ? AND status != ?", supplierApply.Name, -1).First(&resSupplierApply).Error, gorm.ErrRecordNotFound) {
		return errors.New("供应商名称已存在")
	}
	if !errors.Is(source.DB().Where("username = ?", supplierApply.Username).First(&model.SysUser{}).Error, gorm.ErrRecordNotFound) {
		return errors.New("操作员名已存在")
	}
	err = source.DB().Create(&supplierApply).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateSupplierApply
//@description: 更新SupplierApply记录
//@param: supplierApply *model.SupplierApply
//@return: err error

func UpdateSupplierApply(supplierApply model.SupplierApply, userID uint, ip string) (err error) {

	status := *supplierApply.Status
	if status == 1 {
		if !errors.Is(source.DB().Where("name = ?", supplierApply.Name).First(&model.SaveSupplier{}).Error, gorm.ErrRecordNotFound) {
			return errors.New("供应商名称已存在")
		}
		if !errors.Is(source.DB().Where("username = ?", supplierApply.Username).First(&model.SysUser{}).Error, gorm.ErrRecordNotFound) {
			return errors.New("操作员名已存在")
		}

	}

	err = source.DB().Model(&model.SupplierApply{}).First(&supplierApply, supplierApply.ID).Error
	supplierApply.Status = &status
	// 手动调用钩子
	if err = supplierApply.BeforeSave(source.DB()); err != nil {
		return err
	}
	err = source.DB().Model(&model.SupplierApply{}).Where("id = ?", supplierApply.ID).Updates(supplierApply).Error
	if err != nil {
		return err
	}
	if *supplierApply.Status == 1 {
		err = mq.PublishMessage(supplierApply.Uid, mq.Applied)
		if err != nil {
			return err
		}
		service2.CreateOperationRecord(userID, 4, ip, "供应商"+supplierApply.Name+"申请通过")
		err = mq2.PublishMessage(supplierApply.Uid, "supplierSign", 1)
		if err != nil {
			return
		}
	} else {
		service2.CreateOperationRecord(userID, 4, ip, "供应商"+supplierApply.Name+"申请驳回")
		err = mq2.PublishMessage(supplierApply.Uid, "supplierSign", 0)
		if err != nil {
			return
		}

	}

	return err
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetSupplierApply
// @description: 根据id获取SupplierApply记录
// @param: id uint
// @return: err error, supplierApply model.SupplierApply
func GetSupplierApply(id uint) (err error, supplierApply model.SupplierApply) {
	err = source.DB().Preload("Category").Where("id = ?", id).First(&supplierApply).Error
	return
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: GetSupplierApply
// @description: 根据uid获取SupplierApply记录
// @param: uid uint
// @return: err error, supplierApply model.SupplierApply
func GetSupplierApplyByUid(uid int) (err error, supplierApply model.SupplierApply, supplier model.SaveSupplier, isApply int) {
	// 查询未驳回的申请,已驳回的不处理(因为驳回的可以继续申请)
	err = source.DB().Where("uid = ? AND status != ?", uid, -1).First(&supplierApply).Error
	// 没有申请记录
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		isApply = 0
		err = nil
		status := 0
		supplierApply.Status = &status
		return
	} else {
		// 有申请记录
		isApply = 1
		// 申请通过
		if supplierApply.Status != nil && *supplierApply.Status == 1 {
			err = source.DB().Where("uid = ?", uid).First(&supplier).Error
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("供应商不存在, 或被删除")
				return
			}
		}
	}
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetSupplierApplyInfoList
//@description: 分页获取SupplierApply记录
//@param: info request.SupplierApplySearch
//@return: err error, list interface{}, total int64

func GetSupplierApplyInfoList(info request.SupplierApplySearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SupplierApply{})
	var supplierApplys []model.SupplierApply
	// 如果有条件搜索 下方会自动创建搜索语句
	if info.Name != "" {
		db.Where("name like ?", "%"+info.Name+"%")
	}
	if info.Uid != 0 {
		db = db.Where("`uid` = ?", info.Uid)
	}
	if info.Realname != "" {
		db = db.Where("`realname` LIKE ?", "%"+info.Realname+"%")
	}
	if info.Mobile != "" {
		db = db.Where("`mobile` LIKE ?", "%"+info.Mobile+"%")
	}
	if info.CategoryID != 0 {
		db = db.Where("`category_id` = ?", info.CategoryID)
	}
	if info.Status != nil {
		db = db.Where("`status` = ?", *info.Status)
	}
	err = db.Count(&total).Error
	err = db.Preload("Category").Order("created_at desc").Limit(limit).Offset(offset).Find(&supplierApplys).Error
	return err, supplierApplys, total
}

func GetSupplierApplyCount() (total int64, err error) {
	db := source.DB().Model(&model.SupplierApply{})

	db.Where("status = 0")
	//db.Preload("Application.ApplicationLevel")

	err = db.Count(&total).Error
	return
}

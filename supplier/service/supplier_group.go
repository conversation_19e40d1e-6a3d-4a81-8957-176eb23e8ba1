package service

import (
	"supplier/model"
	"supplier/request"
	yzRequest "yz-go/request"
	"yz-go/source"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: CreateSupplierGroup
//@description: 创建SupplierGroup记录
//@param: supplierGroup model.SupplierGroup
//@return: err error

func CreateSupplierGroup(supplierGroup model.SupplierGroup) (err error) {
	err = source.DB().Create(&supplierGroup).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteSupplierGroup
//@description: 删除SupplierGroup记录
//@param: supplierGroup model.SupplierGroup
//@return: err error

func DeleteSupplierGroup(supplierGroup model.SupplierGroup) (err error) {
	err = source.DB().Delete(&supplierGroup).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteSupplierGroupByIds
//@description: 批量删除SupplierGroup记录
//@param: ids yzRequest.IdsReq
//@return: err error

func DeleteSupplierGroupByIds(ids yzRequest.IdsReq) (err error) {
	err = source.DB().Delete(&[]model.SupplierGroup{}, "id in ?", ids.Ids).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UpdateSupplierGroup
//@description: 更新SupplierGroup记录
//@param: supplierGroup *model.SupplierGroup
//@return: err error

func UpdateSupplierGroup(supplierGroup model.SupplierGroup) (err error) {
	err = source.DB().Save(&supplierGroup).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetSupplierGroup
//@description: 根据id获取SupplierGroup记录
//@param: id uint
//@return: err error, supplierGroup model.SupplierGroup

func GetSupplierGroup(id uint) (err error, supplierGroup model.SupplierGroup) {
	err = source.DB().Where("id = ?", id).First(&supplierGroup).Error
	return
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetSupplierGroupInfoList
//@description: 分页获取SupplierGroup记录
//@param: info request.SupplierGroupSearch
//@return: err error, list interface{}, total int64

func GetSupplierGroupInfoList(info request.SupplierGroupSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.SupplierGroup{})
	var supplierGroups []model.SupplierGroup
	// 如果有条件搜索 下方会自动创建搜索语句
	db.Where("is_show = 1")
	if info.Name != "" {
		db = db.Where("`name` LIKE ?", "%"+info.Name+"%")
	}
	if info.Sort != 0 {
		db = db.Where("`sort` = ?", info.Sort)
	}
	if info.IsShow != 0 {
		db = db.Where("`is_show` = ?", info.IsShow)
	}
	err = db.Count(&total).Error
	err = db.Order("sort desc").Limit(limit).Offset(offset).Find(&supplierGroups).Error
	return err, supplierGroups, total
}

//@author:
//@function: GetSupplierGroupInfoListNotPage
//@description: 获取SupplierGroup列表(无分页)
//@param:
//@return: err error, list interface{}
func GetSupplierGroupInfoListNotPage() (err error, list interface{}) {
	db := source.DB().Model(&model.SupplierGroup{})
	var supplierGroups []model.SupplierGroup
	err = db.Order("sort desc").Find(&supplierGroups).Error
	return err, supplierGroups
}

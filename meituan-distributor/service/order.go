package service

import (
	"bytes"
	model3 "cps/model"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io/ioutil"
	"meituan-distributor/model"
	"net/http"
	model2 "order/model"
	"strconv"
	"strings"
	"sync"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

func GetMeituanOrderList(info model.MeituanOrderSearch) (err error, data interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Model(&model.MeituanDistributorOrderModel{}).Preload("User.Level")
	var applications []model.MeituanDistributorOrderModel
	// 如果有条件搜索 下方会自动创建搜索语句

	if info.AppID != 0 {
		db = db.Where("`app_id` = ?", info.AppID)
	}
	if info.ThirdUserID != 0 {
		db = db.Where("`third_user_id` = ?", info.ThirdUserID)
	}
	if info.Status > 0 {
		if info.Status == 4 {
			db = db.Where("`item_status` = 3")
		} else {
			db = db.Where("`item_biz_status` = ?", info.Status)
		}
	}

	if info.OrderSn != "" {
		db = db.Where("`unique_item_id` like ?", "%"+info.OrderSn+"%")

	}
	if info.StartAT != "" {
		db.Where(info.TimeType+" >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where(info.TimeType+" <= ?", info.EndAT)
	}
	if info.StartAddDate != "" {
		db.Where("order_pay_time >= ?", info.StartAddDate+" 00:00:00")
	}
	if info.EndAddDate != "" {
		db.Where("order_pay_time <= ?", info.EndAddDate+" 23:59:59")
	}
	if info.OrderSn != "" {
		db = db.Where("unique_item_id like ?", "%"+info.OrderSn+"%")
	}
	if info.Username != "" {
		var usersIds []uint
		err = source.DB().Model(&model2.User{}).Where("username like ?", "%"+info.Username+"%").Or("nick_name like ?", "%"+info.Username+"%").Pluck("id", &usersIds).Error
		if err != nil {
			return
		}
		db = db.Where("user_id in ?", usersIds)
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&applications).Error
	return err, applications, total
}

func NotifyOrder(order model.MeituanDistributorOrder) (err error) {
	var meituanOrder model.MeituanDistributorOrder
	var notifyType = "meituanDistributor.update"
	err = source.DB().Where("unique_item_id = ?", order.UniqueItemId).First(&meituanOrder).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = nil
		notifyType = "meituanDistributor.create"
		err = source.DB().Create(&order).Error
		err = NotifyApp([]model.MeituanDistributorOrder{order}, notifyType)
	} else {
		if meituanOrder.Md5 != order.Md5 {
			order.ID = meituanOrder.ID
			err = source.DB().Omit("created_at,app_id,third_user_id,settled,local_settle_at,app_commission_price").Save(&order).Error
			if err != nil {
				return
			}
			err = NotifyApp([]model.MeituanDistributorOrder{order}, notifyType)
		}
	}
	return
}
func MeituanNotifyV2(info model3.MeituanNotifyOrderV2) (err error) {

	log.Log().Info("meituan回调数据", zap.Any("data", info))
	var cpsOrder model.MeituanDistributorOrderV2
	var notifyType = "meituanDistributor.update"
	err = source.DB().Where("order_id = ?", info.OrderId).First(&cpsOrder).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			notifyType = "meituanDistributor.create"
		} else {
			return
		}
	}
	customize := strings.Split(info.Sid, "a")
	if len(customize) != 2 {
		err = errors.New("customize不正确")
		return
	}
	var applicationID, thirdUserID int
	applicationID, err = strconv.Atoi(customize[0])
	thirdUserID, err = strconv.Atoi(customize[1])
	if err != nil {
		return
	}
	cpsOrder.ApplicationID = uint(applicationID)
	var application Application
	err = source.DB().First(&application, applicationID).Error
	if err != nil {
		return
	}
	cpsOrder.UserID = uint(application.MemberId)
	cpsOrder.ThirdUserID = uint(thirdUserID)
	cpsOrder.ActivityID = source.Strval(info.ActId)
	cpsOrder.OrderId = info.OrderId
	var payPrice float64
	payPrice, err = strconv.ParseFloat(info.PayPrice, 64)
	if err != nil {
		log.Log().Info("meituan价格转换错误", zap.Any("data", info))
		return
	}
	cpsOrder.Price = uint(payPrice * 100)
	ratio, _ := strconv.ParseFloat(info.Ratio, 64)
	if ratio == 0 {
		profit, _ := strconv.ParseFloat(info.Profit, 64)

		cpsOrder.CommissionPrice = uint(profit * 100)
		cpsOrder.Ratio = uint(float64(cpsOrder.CommissionPrice) / float64(cpsOrder.Price) * 100)

	} else {
		cpsOrder.Ratio = uint(100 * ratio)
		cpsOrder.CommissionPrice = uint(payPrice * 100 * ratio)
	}

	var dataString []byte
	dataString, err = json.Marshal(info)
	if err != nil {
		return
	}
	cpsOrder.DataString = string(dataString)
	cpsOrder.Title = info.Smstitle
	var paytime int
	paytime, err = strconv.Atoi(info.Paytime)
	cpsOrder.PayAt = &source.LocalTime{Time: time.Unix(int64(paytime), 0)}
	var refundtime int
	refundtime, err = strconv.Atoi(info.Refundtime)
	if refundtime > 0 {
		cpsOrder.RefundAt = &source.LocalTime{Time: time.Unix(int64(refundtime), 0)}
	}
	if info.Status == "2" {
		cpsOrder.Status = model.Payed

	} else if info.Status == "4" {
		cpsOrder.Status = model.Refund

	} else if info.Status == "6" {
		cpsOrder.Status = model.Completed
		cpsOrder.CompleteAt = &source.LocalTime{Time: time.Now()}
	}
	err = source.DB().Save(&cpsOrder).Error

	err = NotifyMeituanOrder(cpsOrder, notifyType)

	return
}

func NotifyMeituanOrder(orders model.MeituanDistributorOrderV2, operationType string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}
	var message MeituanDistributorOrderMessage
	message.OperationType = operationType
	message.Ids = []string{orders.OrderId}
	var application Application
	err = source.DB().First(&application, orders.ApplicationID).Error
	if err != nil {
		return
	}
	if application.CallBackLink != "" {
		log.Log().Info("开始通知下游", zap.Any("application", message))
		err, _ = utils.Post(application.CallBackLink, message, header)
		if err != nil {
			log.Log().Info("通知下游失败", zap.Any("application", application))
			return err
		}
	}

	return
}

type CpsOrderMessage struct {
	OperationType string `json:"message_type"`
	Ids           []int  `json:"ids"`
	MemberSign    string `json:"member_sign"`
}

type MeituanDistributorOrderMessage struct {
	OperationType string   `json:"message_type"`
	Ids           []string `json:"ids"`
	MemberSign    string   `json:"member_sign"`
}
type Application struct {
	source.Model
	MemberId          int    `json:"memberId" form:"memberId" gorm:"column:member_id;comment:;type:int;size:10;"`
	CallBackLinkJhCps string `json:"callBackLinkJhCps" form:"callBackLinkJhCps" gorm:"column:call_back_link_jh_cps;comment:;type:varchar(255);size:255;"`
	CallBackLinkCps   string `json:"callBackLinkCps" form:"callBackLinkCps" gorm:"column:call_back_link_cps;comment:;type:varchar(255);size:255;"`
	CallBackLink      string `json:"callBackLink" form:"callBackLink" gorm:"column:call_back_link;comment:;type:varchar(255);size:255;"`
	AppSecret         string `json:"appSecret" gorm:"column:app_secret;comment:;type:varchar(255);"`
}

func (i Application) TableName() string {
	return "application"
}

func NotifyApp(orders []model.MeituanDistributorOrder, operationType string) (err error) {
	header := map[string]string{
		"Content-Type": "application/json",
	}
	var orderIdsByAppID = make(map[uint][]int)
	for _, order := range orders {
		orderIdsByAppID[order.AppID] = append(orderIdsByAppID[order.AppID], order.UniqueItemId)
	}
	for app, ids := range orderIdsByAppID {
		var message CpsOrderMessage
		message.OperationType = operationType
		message.Ids = ids
		var application Application
		err = source.DB().First(&application, app).Error
		if err != nil {
			continue
		}
		message.MemberSign = application.AppSecret
		if application.CallBackLink != "" {
			var wg sync.WaitGroup
			wg.Add(1)
			log.Log().Info("开始通知下游", zap.Any("application", message))
			go post(application.CallBackLink, message, header, &wg)
			wg.Wait()

		}
	}
	return
}

type Resp struct {
	Code   int    `json:"code"`
	Result int    `json:"result"`
	Msg    string `json:"msg"`
}

func post(url string, data interface{}, header map[string]string, wg *sync.WaitGroup) (error, Resp) {
	defer wg.Done()

	// 超时时间：5秒
	client := &http.Client{Timeout: 5 * time.Second}
	jsonStr, _ := json.Marshal(data)
	var req, err = http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	if err != nil {
		return err, Resp{}
	}

	//设置header
	for k, v := range header {
		req.Header.Set(k, v)
	}

	//执行请求
	resp, err := client.Do(req)
	if err != nil {
		return err, Resp{}
	}
	defer resp.Body.Close()

	//将结果转成结构体
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, Resp{}
	}
	//log.Log().Info("打印请求回调接口的url----"+string(url), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的数据----"+string(jsonStr), zap.Any("info", string(jsonStr)))
	//log.Log().Info("打印请求回调接口的返回数据----"+string(result), zap.Any("info", string(result)))
	var respon Resp
	err = json.Unmarshal(result, &respon)
	if err != nil {
		log.Log().Info("通知下游失败", zap.Any("data", data))
	}
	return err, respon
}

func SettleAwardByTx(tx *gorm.DB, award model.MeituanDistributorOrder) (err error) {
	award.Settled = 1
	award.LocalSettleAt = &source.LocalTime{Time: time.Now()}
	err = tx.Updates(&award).Error
	return
}

package cron

import (
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"meituan-distributor/model"
	"meituan-distributor/service"
	"strings"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func PushUpdatePayCpsOrderHandle() {
	task := cron.Task{
		Key:  "updatePayMeituanOrder",
		Name: "定时更新美团分销付款订单",
		Spec: "30 */5 * * * *",
		//Spec: "*/1 * * * * *",
		Handle: func(task cron.Task) {
			UpdateMeituanPayOrder()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func UpdateMeituanPayOrder() {

	var payOrders []model.MeituanDistributorOrder
	err := source.DB().Where("itemBizStatus = 1").Limit(1000).Find(&payOrders).Error
	if err != nil {
		return
	}
	var payOrdersCount = len(payOrders)
	if payOrdersCount > 0 {
		var requestOrders []model.MeituanDistributorOrder
		for _, payOrder := range payOrders {
			requestOrders = append(requestOrders, payOrder)
			//每10条请求一次
			if len(requestOrders) == 100 {
				err = handlePayOrder(requestOrders)
				if err != nil {
					return
				}
				requestOrders = []model.MeituanDistributorOrder{}
			}
		}
		//补漏
		err = handlePayOrder(requestOrders)
		if err != nil {
			return
		}
	}

	return
}

func handlePayOrder(requestOrders []model.MeituanDistributorOrder) (err error) {

	var orderIds []int
	var requestOrdersIdMap = make(map[int]model.MeituanDistributorOrder)
	for _, requestOrder := range requestOrders {
		orderIds = append(orderIds, requestOrder.UniqueItemId)
		requestOrdersIdMap[requestOrder.UniqueItemId] = requestOrder
	}
	var info = make(map[string]interface{})
	err, info = service.GetRequestParams("1_1")
	//info["bizLine"] = 201
	err, meituanSetting := model.GetMeituanSetting()
	if err != nil {
		return
	}
	info["promotionId"] = meituanSetting.PromotionId
	info["uniqueItemIds"] = orderIds
	info["size"] = 100
	info["page"] = 1

	url := "https://union.dianping.com/data/promote/verify/item?"
	var queryString []string
	for k, v := range info {
		queryString = append(queryString, k+"="+source.Strval(v))
	}
	query := strings.Join(queryString, "&")
	var responseParams model.MeituanOrderListResponse
	var result []byte
	err, result = utils.Get(url+query, nil)
	if err != nil {

		return
	}
	err = json.Unmarshal(result, &responseParams)
	if err != nil {
		return
	}
	if responseParams.Code != 200 {
		err = errors.New("定时更新美团分销付款订单出现错误")
		log.Log().Info("定时更新美团分销付款订单出现错误", zap.Any("data", responseParams))
		return
	}
	for _, order := range responseParams.Msg.Records {
		if _, ok := requestOrdersIdMap[order.UniqueItemId]; ok {
			var jsonData []byte
			jsonData, err = json.Marshal(order)
			if err != nil {
				log.Log().Error("美团分销订单同步 初始化失败0")
				return
			}
			order.Md5 = utils.MD5V(jsonData)
			if order.Md5 != requestOrdersIdMap[order.UniqueItemId].Md5 {
				order.ID = requestOrdersIdMap[order.UniqueItemId].ID
				err = source.DB().Omit("created_at,app_id,third_user_id,settled,local_settle_at,app_commission_price").Save(&order).Error
				if err != nil {
					return
				}
				err = service.NotifyApp([]model.MeituanDistributorOrder{order}, "meituanDistributor.update")
			}
		}
	}
	return
}

package cron

import (
	"area-agency/model"
	"area-agency/service"
	"errors"
	incomeModel "finance/model"
	incomeService "finance/service"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"time"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushSettleHandle() {
	task := cron.Task{
		Key:  "areaAgencySettleAward",
		Name: "区域分红结算",
		// 每半小时执行一次
		Spec: "0 */30 * * * *",
		Handle: func(task cron.Task) {
			Settle()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func Settle() {
	//log.Log().Info("区域分红结算,开始")

	// 区域代理基础设置
	var setting model.Setting
	err := source.DB().Where("`key` = ?", "area_agency_setting").First(&setting).Error
	if err == nil && setting.Values.SettleType == 1 {
		//log.Log().Info("结算方式为手动结算,返回")
		return
	}

	var awards []model.SettleAward
	// todo 分段查询?批量修改?
	err = source.DB().Model(&model.SettleAward{}).Preload("Agency").Where("UNIX_TIMESTAMP(`created_at`) + (IFNULL(`settle_days`, 0) * 86400) <= ?", time.Now().Unix()).Where("status = ? AND order_status = ?", 0, 1).Find(&awards).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//log.Log().Info("未找到待结算奖励奖励,返回")
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
	for _, award := range awards {
		//log.Log().Info("区域分红结算,奖励id[" + strconv.Itoa(int(award.ID)) + "]")
		if award.Agency.ID == 0 {
			//log.Log().Info("区域代理不存在,返回")
			return
		}
		// 结算
		err = service.SettleAward(award)
		if err != nil {
			//log.Log().Info("结算失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		// 修改代理商统计
		err = service.UpdateAgencyBySettle(award.Amount, award.Agency)
		if err != nil {
			//log.Log().Info("修改区域代理统计失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		// 增加收入
		var income incomeModel.UserIncomeDetails
		income.UserID = int(award.Uid)
		income.IncomeType = incomeModel.AreaAgency
		income.Amount = uint(award.Amount)
		err = incomeService.IncreaseIncome(income)
		if err != nil {
			//log.Log().Info("增加收入失败,返回")
			log.Log().Error(err.Error(), zap.Any("err", err))
			return
		}
		//log.Log().Info("区域分红结算,成功")
	}
}

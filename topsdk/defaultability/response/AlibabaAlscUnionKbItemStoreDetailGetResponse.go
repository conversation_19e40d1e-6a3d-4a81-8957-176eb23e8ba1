package response

import (
    "topsdk/defaultability/domain"
)

type AlibabaAlscUnionKbItemStoreDetailGetResponse struct {

    /*
        System request id
    */
    RequestId string `json:"request_id,omitempty" `

    /*
        System body
    */
    Body string

    /*
        门店详情
    */
    Data  domain.AlibabaAlscUnionKbItemStoreDetailGetKbShopDetailDto `json:"data,omitempty" `
    /*
        返回码
    */
    ResultCode  int64 `json:"result_code,omitempty" `
    /*
        返回消息
    */
    Message  string `json:"message,omitempty" `
}

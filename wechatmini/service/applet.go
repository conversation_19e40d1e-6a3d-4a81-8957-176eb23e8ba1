package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"wechatmini/setting"
	"yz-go/utils"
)

type AppletParams struct {
	Identifier  string `json:"identifier" form:"identifier"` //参数唯一码
	Domain      string `json:"domain"`                       //域名
	Version     string `json:"version"`                      //小程序版本
	Description string `json:"description"`                  //小程序版本描述
}

type AppletResponse struct {
	Version    string `json:"version"`    //小程序版本
	Status     string `json:"status"`     //结果状态 SUCCESS 成功，WAIT 等待
	Count      int    `json:"count"`      //排队人数
	Time       int    `json:"time"`       //排队时间(s)
	QrCode     string `json:"qr_code"`    //登录二维码链接
	Identifier string `json:"identifier"` //身份唯一码
}

// curlParams 小程序云端请求参数类型
type curlParams struct {
	Identifier  string   `json:"identifier"`      //身份唯一码
	Domain      string   `json:"domain"`          //域名
	AppId       string   `json:"app_id"`          //小程序APP ID
	PathType    int      `json:"type"`            //根据路径值上传不同小程序
	Version     string   `json:"version"`         //小程序版本
	Description string   `json:"description"`     //版本描述
	Parameters  string   `json:"parameters"`      //版本描述
	PluginsText []string `json:"plugins_text"`    //插件信息
	MinOriginId string   `json:"min_original_id"` //小程序原始ID
}

// curlResponse 小程序云端请求返回参数类型
type curlResponse struct {
	Code    int    `json:"code"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// curlDataResponse 小程序云端请求返回参数类型(带资源类型）
type curlDataResponse struct {
	Code    int    `json:"code"`
	Status  string `json:"status"`
	Message string `json:"message"`
	Info    struct {
		Version    string `json:"version"`
		QrCode     string `json:"qr_code"`
		Count      int    `json:"count"`
		Time       int    `json:"time"`
		Identifier string `json:"identifier"`
	} `json:"data"`
}

// AppletVersion 小程序上次上传版本号
func AppletVersion() (err error, aResponse AppletResponse) {
	//url := "http://applet.yunzhong.com:8890/api/devtools/last"

	url := "https://applet.yunzmall.com/api/devtools/last"

	_, appletSetting := setting.GetSetting()

	err, resData := utils.Post(
		url,
		curlParams{AppId: appletSetting.Value.AppId},
		map[string]string{"Content-Type": "application/json"},
	)

	var cResponse curlDataResponse

	err = json.Unmarshal(resData, &cResponse)

	if err != nil {
		err = errors.New(err.Error())
		return
	}
	if cResponse.Code != 0 {
		err = errors.New("小程序发布错误" + cResponse.Message)
	}

	aResponse = AppletResponse{Version: cResponse.Info.Version}

	return
}

// AppletLogin 小程序登录请求
func AppletLogin(aParams AppletParams) (err error, aResponse AppletResponse) {

	_, appletSetting := setting.GetSetting()

	cParams := curlParams{
		AppId:       appletSetting.Value.AppId,
		MinOriginId: appletSetting.Value.OriginalId,
		Parameters:  fmt.Sprintf("export default{httpUrl:'%s'}", aParams.Domain),
		PluginsText: []string{"supply-chain"},
		PathType:    2,
		Domain:      aParams.Domain,
		Version:     aParams.Version,
		Description: aParams.Description,
	}

	if cParams.AppId == "" {
		err = errors.New("请先设置小程序App ID")
		return
	}
	if cParams.MinOriginId == "" {
		err = errors.New("请先设置小程序原始ID")
		return
	}
	if cParams.Version == "" {
		err = errors.New("请填写小程序版本号")
		return
	}
	if cParams.Description == "" {
		err = errors.New("请填写小程序版本描述")
		return
	}

	//url := "http://applet.yunzhong.com:8890/api/devtools/login"

	url := "https://applet.yunzmall.com/api/devtools/login"

	err, resData := utils.Post(
		url,
		cParams,
		map[string]string{"Content-Type": "application/json"},
	)

	var cResponse curlDataResponse

	err = json.Unmarshal(resData, &cResponse)

	if err != nil {
		err = errors.New("小程序发布错误，请稍后重试！Code:200101")
		return
	}
	if cResponse.Code != 0 {
		err = errors.New("小程序发布错误，请稍后重试！Code:200102")
	}

	aResponse = AppletResponse{
		Status:     cResponse.Status,
		Count:      cResponse.Info.Count,
		Time:       cResponse.Info.Time,
		QrCode:     cResponse.Info.QrCode,
		Identifier: cResponse.Info.Identifier,
	}
	return
}

// AppletLoginOutput 小程序登录结果验证
func AppletLoginOutput(aParams AppletParams) (err error, aResponse AppletResponse) {

	//url := "http://applet.yunzhong.com:8890/api/devtools/login/output"

	url := "https://applet.yunzmall.com/api/devtools/login/output"

	err, resData := utils.Post(
		url,
		curlParams{Identifier: aParams.Identifier},
		map[string]string{"Content-Type": "application/json"},
	)

	var cResponse curlResponse

	err = json.Unmarshal(resData, &cResponse)

	if err != nil {
		err = errors.New("小程序发布错误，请稍后重试！Code:200201")
		return
	}
	if cResponse.Code != 0 {
		err = errors.New("小程序发布错误，请稍后重试！Code:200202")
	}

	aResponse = AppletResponse{Status: cResponse.Status}

	return
}

// AppletUpload 小程序上传操作
func AppletUpload(aParams AppletParams) (err error, aResponse AppletResponse) {

	//url := "http://applet.yunzhong.com:8890/api/devtools/upload"

	url := "https://applet.yunzmall.com/api/devtools/upload"

	err, resData := utils.Post(
		url,
		curlParams{Identifier: aParams.Identifier},
		map[string]string{"Content-Type": "application/json"},
	)

	var cResponse curlResponse

	err = json.Unmarshal(resData, &cResponse)

	if err != nil {
		err = errors.New("小程序发布错误，请稍后重试！Code:200301")
		return
	}
	if cResponse.Code != 0 {
		err = errors.New("小程序发布错误，请稍后重试！Code:200302")
	}

	aResponse = AppletResponse{Status: cResponse.Status}

	return
}

// AppletUploadOutput 小程序上传结果验证
func AppletUploadOutput(aParams AppletParams) (err error, aResponse AppletResponse) {

	//url := "http://applet.yunzhong.com:8890/api/devtools/upload/output"

	url := "https://applet.yunzmall.com/api/devtools/upload/output"

	err, resData := utils.Post(
		url,
		curlParams{Identifier: aParams.Identifier},
		map[string]string{"Content-Type": "application/json"},
	)

	var cResponse curlResponse

	err = json.Unmarshal(resData, &cResponse)

	if err != nil {
		err = errors.New("小程序发布错误，请稍后重试！Code:200401")
		return
	}
	if cResponse.Code != 0 {
		err = errors.New("小程序发布错误，请稍后重试！Code:200402")
	}

	aResponse = AppletResponse{Status: cResponse.Status}

	return
}

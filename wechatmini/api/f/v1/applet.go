package v1

import (
	"github.com/gin-gonic/gin"
	"wechatmini/request"
	"wechatmini/service"
	"yz-go/response"
)

func AppletLastVersion(c *gin.Context) {
	err, result := service.AppletVersion()

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// AppletLogin 小程序登录
func AppletLogin(c *gin.Context) {

	params := request.LoginPostParams{Version: "", Description: "版本升级"}

	_ = c.ShouldBindJSON(&params)

	err, result := service.AppletLogin(service.AppletParams{
		Domain:      c.Request.Header.Get("X-Forwarded-Proto") + "://" + c.Request.Host,
		Version:     params.Version,
		Description: params.Description,
	})

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// AppletLoginOutput 小程序登录验证
func AppletLoginOutput(c *gin.Context) {
	var params request.OperatePostParams

	_ = c.ShouldBind<PERSON>(&params)

	err, result := service.AppletLoginOutput(service.AppletParams{Identifier: params.Identifier})

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// AppletUpload 小程序上传
func AppletUpload(c *gin.Context) {
	var params request.OperatePostParams

	_ = c.ShouldBindJSON(&params)

	err, result := service.AppletUpload(service.AppletParams{Identifier: params.Identifier})

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// AppletUploadOutput 小程序上传验证
func AppletUploadOutput(c *gin.Context) {
	var params request.OperatePostParams

	_ = c.ShouldBindJSON(&params)

	err, result := service.AppletUploadOutput(service.AppletParams{Identifier: params.Identifier})

	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

package cron

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"
	"weipinshang/model"
)

func TestGoodsUpdate(t *testing.T) {

	nnn := "{\"code\":11001,\"msg\":\"亮芝美小苏打牙膏110克地区不能购买;\",\"data\":[{\"error_goodsId\":\"WPS405_kl00054\",\"error_msg\":\"地区不能购买,抱歉！\",\"error_goodSpecId\":\"WPS405_0512173447763286\"}]}"

	var resOrder model.PreOrder
	err := json.Unmarshal([]byte(nnn), &resOrder)
	if err != nil {
		fmt.Println(err)
	}

	nowDate := time.Now().Format("2006-01-02 15:04:05")
	if "2024-01-26 03:00:00" < nowDate {
		fmt.Println("333")
	}

	GoodsUpdate(57, "WPS75_ZJ00013")

}

func TestUpdateProductCron(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		{name: ""},
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateProductCron(); (err != nil) != tt.wantErr {
				t.Errorf("UpdateProductCron() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

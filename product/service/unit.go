package service

import (
	"errors"
	"gorm.io/gorm"
	"product/model"
	"yz-go/source"
)

type UnitListParams struct {
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
	Title    string `json:"title"`
}

func UnitList(p UnitListParams) (err error, list []model.ProductUnit, total int64) {
	db := source.DB().Model(&model.ProductUnit{})

	// 查询总条数
	if err = db.Count(&total).Error; err != nil {
		return
	}

	// 搜索列表
	if p.Title != "" {
		db.Where("title LIKE ?", "%"+p.Title+"%")
	}

	limit := p.PageSize
	offset := p.PageSize * (p.Page - 1)
	if err = db.Order("id desc").Limit(limit).Offset(offset).Find(&list).Error; err != nil {
		return
	}

	return
}

func UnitOption() (err error, list []model.ProductUnit) {
	err = source.DB().Find(&list).Error

	return
}

func UnitCreate(title string) (err error) {
	if title == "" {
		return errors.New("单位名称不能为空")
	}

	// 如果为提示 record not found，证明记录已经存在
	var exist model.ProductUnit
	if err = source.DB().Where("title = ?", title).First(&exist).Error; err == nil {
		return errors.New("单位名称已存在")
	}

	// 如果错误不是 record not found，则返回错误
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	err = nil

	// 继续创建
	if err = source.DB().Create(&model.ProductUnit{Title: title}).Error; err != nil {
		return err
	}

	return nil
}

func UnitDelete(id uint) (err error) {
	// 查询记录
	var unitModel model.ProductUnit
	if err = source.DB().Where("id = ?", id).First(&unitModel).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("记录不存在或已删除")
		}
		return err
	}

	// 商品表中有使用到单位，则不允许删除
	if err = source.DB().Where("unit = ?", unitModel.Title).First(&model.Product{}).Error; err == nil {
		return errors.New("该单位正在使用中，不允许删除")
	}

	// 如果错误不是 record not found，则返回错误
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	err = nil

	if err = source.DB().Delete(&unitModel).Error; err != nil {
		return err
	}

	return nil
}

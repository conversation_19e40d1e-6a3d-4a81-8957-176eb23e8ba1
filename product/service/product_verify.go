package service

import (
	"errors"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"product/model"
	"product/mq"
	request2 "product/request"
	"time"
	"yz-go/source"
	"yz-go/utils"
)

func GetProductVerifyList(info request2.ProductSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := source.DB().Preload(clause.Associations).Model(&model.ProductVerify{})
	var applications []model.ProductVerify
	db.Joins("INNER join products on product_verifies.product_id = products.id and products.deleted_at is NULL").Omit("products.id")

	// 如果有条件搜索 下方会自动创建搜索语句
	if info.SupplierID != nil {
		db.Where("product_verifies.supplier_id = ?", info.SupplierID)
	}
	if info.Title != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model.Product{}).Where("title like ?", "%"+info.Title+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("product_verifies.product_id in ?", shippingIds)

	}
	if info.IsDisplay != nil {
		db.Where("product_verifies.is_display = ? and status = 1", info.IsDisplay)
	} else {
		db.Where("product_verifies.status = 0")
	}
	err = db.Count(&total).Error
	err = db.Limit(limit).Offset(offset).Find(&applications).Error
	for key, item := range applications {
		if item.Product.Price > 0 && item.Product.OriginPrice > 0 && item.Product.OriginPrice > item.Product.Price {
			applications[key].Product.AgreementProfitRate = utils.ExecProfitRate(item.Product.OriginPrice, item.Product.Price) * 100
		}
	}
	return err, applications, total
}

func Verify(verifyInfo request2.VerifyInfo) (err error) {
	err = source.DB().Model(&model.Product{}).Where("id = ?", verifyInfo.ProductID).Update("is_display", verifyInfo.IsDisplay).Error
	if err != nil {
		return
	}

	var productVerify model.ProductVerify
	if err = source.DB().Where("id", verifyInfo.ID).First(&productVerify).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("记录不存在或已审核，请刷新重新")
		}
		return
	}
	if verifyInfo.IsDisplay == 0 {
		productVerify.RemarkTime = &source.LocalTime{time.Unix(time.Now().Unix(), 0)}
	}
	productVerify.IsDisplay = verifyInfo.IsDisplay
	productVerify.Status = 1
	productVerify.Remark = verifyInfo.Remark
	// 更新审核记录
	if err = source.DB().Save(productVerify).Error; err != nil {
		return
	}

	// 推送消息
	if verifyInfo.IsDisplay == 0 {
		err = mq.PublishMessage(verifyInfo.ProductID, mq.Undercarriage, 0)
	} else {
		err = mq.PublishMessage(verifyInfo.ProductID, mq.OnSale, 0)
	}
	return
}

// 批量审核
// todo 不是批量修改，完全可以循环调用单条审核方法 Verify

func VerifyByIds(verifyInfo request2.VerifyByIds) (err error) {
	for _, v := range verifyInfo.Ids {
		err = source.DB().Model(&model.Product{}).Where("id = ?", v.ProductID).Update("is_display", verifyInfo.IsDisplay).Error
		if err != nil {
			return
		}

		var productVerify = model.ProductVerify{}
		if err = source.DB().First(&productVerify, v.ID).Error; err != nil {
			return
		}

		productVerify.IsDisplay = verifyInfo.IsDisplay
		productVerify.Status = 1
		productVerify.Remark = verifyInfo.Remark

		// 更新审核记录
		if err = source.DB().Save(productVerify).Error; err != nil {
			return
		}

		// 推送消息
		if verifyInfo.IsDisplay == 0 {
			err = mq.PublishMessage(v.ProductID, mq.Undercarriage, 0)
		} else {
			err = mq.PublishMessage(v.ProductID, mq.OnSale, 0)
		}
	}

	return
}

func InsertVerifyRecord(info model.ProductVerify) (err error) {
	var verifyList model.ProductVerify
	err = source.DB().Where("product_id = ?", info.ProductID).Where("supplier_id = ?", info.SupplierID).First(&verifyList).Error

	if err == nil {
		err = source.DB().Model(&model.ProductVerify{}).Where("id = ?", verifyList.ID).Update("status", 0).Error
	} else {
		err = source.DB().Create(&info).Error
	}

	if err != nil {
		return
	}
	err = source.DB().Model(&model.Product{}).Where("id = ?", info.ProductID).Update("is_display", 0).Error
	return
}

func GetProductVerifyCount() (total int64, err error) {
	db := source.DB().Model(&model.ProductVerify{})
	db.Joins("INNER join products on product_verifies.product_id = products.id and products.deleted_at is NULL")
	db.Where("product_verifies.status = 0")
	//db.Preload("Application.ApplicationLevel")

	err = db.Count(&total).Error
	return
}

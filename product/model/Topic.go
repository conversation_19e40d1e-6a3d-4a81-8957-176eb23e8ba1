package model

import "yz-go/source"
import "yz-go/response"

type TopicModel struct {
	source.Model
	SupplierID uint   `json:"supplier_id" form:"supplier_id"`
	Title      string `json:"title" form:"title"`
	Category   string `json:"category" form:"category"`
	Banner     string `json:"banner"`
	MainColor  string `json:"main_color"`
	PostImg    string `json:"post_img"`
	AdImg      string `json:"ad_img"`
	Content    string `json:"text"`
}

func (TopicModel) TableName() string {
	return "topics"
}

type Topic struct {
	TopicModel
	Products []response.Product `json:"products" gorm:"many2many:topic_products"`
}

type TopicProduct struct {
	TopicID   uint             `json:"topic_id" form:"topic_id"`
	ProductID uint             `json:"product_id" form:"product_id"`
	Product   response.Product `json:"product"`
}

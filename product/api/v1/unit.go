package v1

import (
	"github.com/gin-gonic/gin"
	"product/service"
	yzResponse "yz-go/response"
)

func UnitList(c *gin.Context) {
	var pageInfo service.UnitListParams
	if err := c.ShouldBindJSON(&pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.UnitList(pageInfo); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func UnitOption(c *gin.Context) {
	if err, list := service.UnitOption(); err != nil {
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.OptionResult{
			List: list,
		}, "获取成功", c)
	}
}

func UnitCreate(c *gin.Context) {
	type params struct {
		Title string `json:"title"`
	}

	var p params
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.UnitCreate(p.Title); err != nil {
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

func UnitDelete(c *gin.Context) {
	type params struct {
		ID uint `json:"id"`
	}

	var p params
	if err := c.ShouldBindJSON(&p); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err := service.UnitDelete(p.ID); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.Ok(c)
	}
}

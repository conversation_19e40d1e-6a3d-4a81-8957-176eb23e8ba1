package mq

import (
	"encoding/json"
	"fmt"
	"github.com/streadway/amqp"
	"sync"
	"time"
	"yz-go/mq"
	"yz-go/source"
)

const (
	EXCHANGE = "productE"
	KEY      = "product"
)

type ProductMessageType string

const (
	Edit            ProductMessageType = "goods.alter"
	Undercarriage   ProductMessageType = "goods.undercarriage"
	OnSale          ProductMessageType = "goods.on.sale"
	Create          ProductMessageType = "goods.create"
	Delete          ProductMessageType = "goods.delete"
	DistributorSync ProductMessageType = "goods.distributorSync"
	Stock           ProductMessageType = "goods.stock"
	StorageChange   ProductMessageType = "goods.storageChange"
)

func init() {
	handles = map[string]handleItem{}
	mq.PushConsumer(ConsumeRabbitMQ)
}

var publishRabbitMQ *source.SafeCh
var mqOnce sync.Once

func getPublishRabbitMQ() *amqp.Channel {
	mqOnce.Do(func() {
		var err error
		publishRabbitMQ = source.SafeMQ(func(ch *amqp.Channel) {
			// 定义话题交换机
			err = ch.ExchangeDeclare(
				EXCHANGE,
				"topic", //话题类型
				true,
				false,
				//true表示这个exchange不可以被client用来推送消息，仅用来进行exchange和exchange之间的绑定
				false,
				false,
				nil,
			)
			if err != nil {
				fmt.Println(err)
			}
		})
	})

	return publishRabbitMQ.GetCh()
}

// PublishMessage
// 订单消息推送
func PublishMessage(productID uint, messageType ProductMessageType, level int) (err error) {
	var isStock int
	if messageType == Stock {
		messageType = Edit
		isStock = 1
	}
	message, err := json.Marshal(ProductMessage{
		MessageType: messageType,
		ProductID:   productID,
		Level:       level,
		IsStock:     isStock,
	})

	if err != nil {
		return
	}
	key := KEY
	err = getPublishRabbitMQ().Publish(
		EXCHANGE,
		key,
		//如果为true，根据exchange类型和routkey规则，如果无法找到符合条件的队列，那么会把发送的消息返回给发送者
		false,
		//如果为true，当exchange发送消息到队列侯发现队列上没有绑定消费者，则会把消息发还给发送者
		false,
		amqp.Publishing{
			ContentType:  "text/plain",
			Body:         message,
			DeliveryMode: amqp.Persistent,
		})
	return
}

type ProductMessage struct {
	MessageCode uint               `json:"message_code"`
	MessageType ProductMessageType `json:"message_type"`
	ProductID   uint               `json:"product_id"`
	MemberSign  string             `json:"member_sign"`
	MessageID   string             `json:"message_id"`
	Level       int                `json:"level"` //重试次数
	IsStock     int                `json:"is_stock"`
}
type Handle func(product ProductMessage) error

var handles map[string]handleItem

type handleItem struct {
	ThreadNum int
	Handle    Handle
}

func PushHandles(queueName string, threadNum int, handle Handle) {
	handles[queueName] = handleItem{threadNum, handle}
	getPublishRabbitMQ()
}
func ConsumeHandle(queueName string, handle Handle) {
	var consumeRabbitMQ *amqp.Channel
	var err error
	consumeRabbitMQ = source.MQ()
	if err != nil {
		fmt.Println(err)
	}
	// 定义队列
	_, err = consumeRabbitMQ.QueueDeclare(queueName, true, false, false, false, nil)
	if err != nil {
		fmt.Println(err)
	}
	// 绑定队列
	err = consumeRabbitMQ.QueueBind(
		queueName, // queue name
		KEY,       // routing key
		EXCHANGE,  // exchange
		false,
		nil)
	if err != nil {
		fmt.Println(err)
	}
	msgs, err := consumeRabbitMQ.Consume(
		queueName,
		//用来区分多个消费者
		"",
		//是否自动应答
		false,
		//是否具有排他性
		false,
		//如果设置为true，表示不能将同一个connection中发送的消息传递给这个connection中的消费者
		false,
		//消息队列是否阻塞
		false,
		nil,
	)
	if err != nil {
		fmt.Println(err)
	}
	forever := make(chan bool)
	for msg := range msgs {
		productMessage := ProductMessage{}
		err = json.Unmarshal(msg.Body, &productMessage)
		if err != nil {
			fmt.Println(err)
			// 拒绝消息并重试
			err = msg.Reject(true)
			if err != nil {
				fmt.Println(err)
			}
		} else {
			err = handle(productMessage)
			if err != nil {
				fmt.Println(err)
				// 拒绝消息并重试
				err = msg.Reject(true)
				if err != nil {
					fmt.Println(err)
				}
			} else {
				// 成功接收消息
				err = msg.Ack(false)
				if err != nil {
					fmt.Println(err)
				}
			}
		}
	}
	<-forever
	return
}
func ConsumeRabbitMQ() {
	go KeepConsumerAlive()
}

var keeperCh *source.SafeCh

func getKeeperCh() *amqp.Channel {
	if keeperCh == nil {
		keeperCh = source.SafeMQ(func(ch *amqp.Channel) {

		})
	}
	return keeperCh.GetCh()
}
func createConsumer() {
	var err error
	var queue amqp.Queue
	ch := getKeeperCh()
	for queueName, handle := range handles {
		// 定义队列
		if ch == nil {
			return
		}
		queue, err = ch.QueueDeclare(queueName, true, false, false, false, nil)
		if err != nil {
			fmt.Println(err)
		}
		if queue.Consumers < handle.ThreadNum {
			fmt.Println(fmt.Sprintf("队列%s的消费者数量为%d不足%d个，生成新的%d消费者", queueName, queue.Consumers, handle.ThreadNum, handle.ThreadNum-queue.Consumers))
			// 每次增加一个
			for i := 0; i < handle.ThreadNum-queue.Consumers; i++ {
				go ConsumeHandle(queueName, handle.Handle)
			}
		}
	}
}
func KeepConsumerAlive() {

	for {
		createConsumer()
		time.Sleep(time.Minute)
	}
}

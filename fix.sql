select * from orders where id in (select order_id from order_pay_infos where pay_info_id in (select id from pay_infos where pay_sn in(select pay_sn from purchasing_balances where id>900))) and status =0

-- 修复casbin_rule表创建索引报错问题
select id from `casbin_rule` where v1 in (select v1 from `casbin_rule` group by `p_type`,`v0`,`v1`,`v2` having count(1)>1) and id not in (select min(id) from `casbin_rule` group by `p_type`,`v0`,`v1`,`v2` having count(1)>1)
update casbin_rule set v1 = CONCAT(v1, id) where id in ()
update casbin_rule set v0 = 888 where v0=8888

package request

type MeituanDistirbutorRequest struct {
	RequestId string `json:"request_id"`
	ThirdUserID int `json:"third_user_id" query:"third_user_id" form:"third_user_id"`
	ProvinceId int `json:"provinceId" form:"provinceId"`
	CityId int `json:"cityId" form:"cityId"`
	ShopId string `json:"shopId" form:"shopId"`
	Cat0Id int `json:"cat0Id" form:"cat0Id"`
	Cat1Id int `json:"cat1Id" form:"cat1Id"`
	Cat1Ids []int `json:"cat1Ids" form:"cat1Ids"`
	ShopIds []string `json:"shopIds" form:"shopIds"`
	DealIds []int `json:"dealIds" form:"dealIds"`
	DealType int `json:"dealType" form:"dealType"`
	SortType int `json:"sortType" form:"sortType"`
	Filters *struct{
		FilterType int `json:"filterType" form:"filterType"`
		Upper int `json:"upper" form:"upper"`
		Lower int `json:"lower" form:"lower"`
	} `json:"filters" form:"filters"`
	KeyWords string `json:"keyWords" form:"keyWords"`
	Geo struct{
		CityId int `json:"cityId" form:"cityId"`
		RegionId int `json:"regionId" form:"regionId"`
		DistrictId int `json:"districtId" form:"districtId"`
		Lng string `json:"lng" form:"lng"`
		Lat string `json:"lat" form:"lat"`
		Radii int `json:"radii" form:"radii"`
	} `json:"geo" form:"geo"`
	UtmSource string `json:"utmSource" form:"utmSource"`
	UtmMedium string `json:"utmMedium" form:"utmMedium"`
	PromotionId string `json:"promotionId" form:"promotionId"`
	Page int `json:"page" form:"page"`
	Size int `json:"size" form:"size"`
	ShowId int `json:"showId" form:"showId"`
	Phone string `json:"phone" form:"phone"`
	Os int `json:"os" form:"os"`
	Lat string `json:"lat" form:"lat"`
	Lng string `json:"lng" form:"lng"`
}

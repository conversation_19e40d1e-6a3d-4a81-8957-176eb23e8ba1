package cron

import (
	"cps/model"
	"time"
	"yz-go/cron"
	"yz-go/source"
)

func PushAutoCloseHandle() {
	task := cron.Task{
		Key:  "autoClose",
		Name: "自动关闭活动",
		Spec: "23 */1 * * * *",
		Handle: func(task cron.Task) {
			AutoClose()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func AutoClose() {
	var activities []model.Activity
	err := source.DB().Where("status = 1").Find(&activities).Error
	if err != nil {
		return
	}
	var timeStr = time.Now().Unix()
	var closeIds []uint
	for _, activity := range activities {
		if activity.ExpireTime > 0 {
			if activity.ExpireTime <= timeStr {
				closeIds = append(closeIds, activity.ID)
			}
		}

	}
	if len(closeIds) > 0 {
		err = source.DB().Model(&model.Activity{}).Where("id in ?", closeIds).Update("status", 0).Error
	}
	return
}

package Pay

import (
	"errors"
	model3 "finance/model"
	"go.uber.org/zap"
	"gorm.io/gorm"
	_ "gorm.io/gorm"
	"payment/model"
	model2 "payment/model"
	"payment/mq"
	"payment/request"
	"yz-go/component/log"
	"yz-go/source"
)

type VirtualStock struct {
	VirtualStockPayment interface{}
	VirtualStockRefund  interface{}
}

func (station *VirtualStock) SplitAccount() (err error) {

	return

}

func (station *VirtualStock) AfterOperation() (err error) {
	panic("implement me")
}

func (station *VirtualStock) BeforeOperation() (err error) {

	//payment := station.VirtualStockPayment.(model.UpdateBalance)
	//if payment.Amount <= 0 {
	//	err = errors.New("扣款余额不能空")
	//	return
	//}

	//var orderPayInfo model2.OrderPayInfo
	//
	//err = source.DB().Where("pay_info_id=?", payment.PayInfoID).First(&orderPayInfo).Error
	//if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = errors.New("未查询到支付单")
	//	return
	//}
	//
	//var order model2.Order
	//err = source.DB().Where("id=?", orderPayInfo.OrderID).First(&order).Error
	//if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
	//	err = errors.New("未查询到支付单")
	//	return
	//}
	//if order.Status > 0 {
	//	err = errors.New("该订单已支付,请勿重复支付")
	//	return
	//}

	return
}

func (station *VirtualStock) Init() (err error, resData interface{}) {
	//PayTypeList = append(PayTypeList, Type{Name: "站内余额", Code: 2, Status: 1, Mode: 1})

	return
}

type ApplicationVirtualStock struct {
	source.Model
	ApplicationID uint `json:"application_id"`
	ProductID     uint `json:"product_id"`
	Stock         uint `json:"stock"`
}

type VirtualStockRecord struct {
	source.Model
	ApplicationID uint   `json:"application_id"`
	ProductID     uint   `json:"product_id"`
	OrderID       uint   `json:"order_id"`
	OrderItemID   uint   `json:"order_item_id"`
	UserID        uint   `json:"user_id"`
	Stock         uint   `json:"stock"`
	Type          int    `json:"type"` //1增加 2扣除
	Remark        string `json:"remark"`
}
type Application struct {
	source.Model
}

func (Application) TableName() string {
	return "application"
}

type OrderItem struct {
	source.Model
	OrderID   uint `json:"order_id"`
	ProductID uint `json:"product_id"`
	Qty       uint `json:"qty"`
}

func (station *VirtualStock) Payment() (err error, resData interface{}) {
	payment := station.VirtualStockPayment.(model.UpdateBalance)
	if err = station.BeforeOperation(); err != nil {
		return
	}

	var data = make(map[string]interface{})
	data["status"] = 0
	//var orderList []model2.OrderPayInfo

	params := model.UpdateBalance{
		Uid:           payment.Uid,
		Amount:        payment.Amount,
		Type:          payment.PayType,
		PayType:       model.VIRTUAL_STOCK,
		OperationType: model.BALANCE_PURCHASING,
		Action:        model.REDUCE,
		PayInfoID:     payment.PayInfoID,
	}

	data["status"] = 1
	var payInfo model2.PayInfo
	err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo).Error
	if err != nil {
		log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
		return
	}

	var orderPayInfo model.OrderPayInfo
	err = source.DB().First(&orderPayInfo, "pay_info_id=?", payInfo.ID).Error
	if err != nil {
		return
	}

	var isPay model3.PurchasingBalanceOrderIds
	err = source.DB().Where("order_id = ? and is_local_order = ?", orderPayInfo.OrderID, 0).First(&isPay).Error
	if isPay.ID > 0 {
		err = errors.New("已经支付，请勿重复支付")
		return
	}

	//验证采购端库存是否足够

	type Order struct {
		source.Model
		ApplicationID uint `json:"application_id"`
		UserID        uint `json:"user_id"`
	}

	var productIds []uint
	var OrderItems []OrderItem
	err = source.DB().Model(&OrderItem{}).Where("order_id = ?", orderPayInfo.OrderID).Find(&OrderItems).Error
	if err != nil {
		return
	}
	for _, orderItem := range OrderItems {
		productIds = append(productIds, orderItem.ProductID)
	}
	var orderInfo Order
	err = source.DB().Where("id = ?", orderPayInfo.OrderID).First(&orderInfo).Error
	if err != nil {
		return
	}
	var applicationID uint
	var application Application
	if orderInfo.ApplicationID == 0 {
		err = source.DB().Where("member_id = ?", orderInfo.UserID).First(&application).Error
		if err != nil {
			err = errors.New("未绑定采购端，无法支付")
			return
		}
		applicationID = application.ID
	} else {
		applicationID = orderInfo.ApplicationID
	}
	var stockInfo []ApplicationVirtualStock
	err = source.DB().Where("application_id = ?", applicationID).Where("product_id in ?", productIds).Find(&stockInfo).Error
	if err != nil {
		return
	}
	if len(stockInfo) == 0 {
		err = errors.New("采购端库存不足，无法支付")
		return
	}
	var stockCheck int
	for _, orderI := range OrderItems {
		for _, stock := range stockInfo {
			if orderI.ProductID == stock.ProductID {
				if stock.Stock >= orderI.Qty {
					stockCheck++
				}
			}
		}
	}
	if stockCheck != len(OrderItems) {
		err = errors.New("采购端库存不足，无法支付")
		return
	}
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		//开始扣库存
		for _, orderItemExec := range OrderItems {
			err = tx.Model(&ApplicationVirtualStock{}).Where("application_id = ?", applicationID).Where("product_id = ?", orderItemExec.ProductID).Update("stock", gorm.Expr("`stock` - ?", orderItemExec.Qty)).Error
			if err != nil {
				return err
			}
			err = tx.Create(&VirtualStockRecord{
				ApplicationID: applicationID,
				ProductID:     orderItemExec.ProductID,
				UserID:        orderInfo.UserID,
				OrderID:       orderItemExec.OrderID,
				OrderItemID:   orderItemExec.ID,
				Stock:         orderItemExec.Qty,
				Type:          2,
			}).Error
			if err != nil {
				return err
			}

		}

		return err
	})

	if err != nil {
		return
	}
	//err = service.UpdateSettlementBalance(params)

	err = mq.PublishMessage(payInfo.PaySN, params.PayType)
	resData = data
	return

}

func (station *VirtualStock) Refund() (err error, resData interface{}) {

	refund := station.VirtualStockRefund.(request.Refund)
	type Order struct {
		source.Model
		ApplicationID  uint   `json:"application_id"`
		UserID         uint   `json:"user_id"`
		GatherSupplyID uint   `json:"gather_supply_id"`
		PayTypeID      int    `json:"pay_type_id"`
		OrderSN        string `json:"order_sn"`
	}
	var payInfo model2.PayInfo
	var order Order
	err = source.DB().Where("pay_sn=?", refund.PaySN).First(&payInfo).Error
	if err != nil {
		log.Log().Error("StationBalance Refund payInfo 错误", zap.Any("err", err.Error()))

	}
	if payInfo.ID > 0 {
		err = source.DB().Table("orders").Where("orders.pay_info_id=?", payInfo.ID).First(&order).Error
		if err != nil {
			log.Log().Error("VirtualStock Refund 错误", zap.Any("err", err.Error()))

		}
	}
	var OrderItems []OrderItem
	if refund.OrderItemID == 0 {
		//整单全部退款
		err = source.DB().Model(&OrderItem{}).Where("order_id = ?", order.ID).Find(&OrderItems).Error
	} else {
		//单独orderItem退款
		err = source.DB().Model(&OrderItem{}).Where("id = ?", refund.OrderItemID).Find(&OrderItems).Error
	}
	if err != nil {
		return
	}
	balanceData := model.UpdateBalance{
		Uid:           refund.UserID,
		Amount:        refund.Amount,
		Type:          2,
		PayType:       model.VIRTUAL_STOCK,
		OperationType: model.BALANCE_PURCHASING,
		Action:        model.PLUS,
		Remarks:       "订单号：" + order.OrderSN,
		PaySn:         payInfo.PaySN,
	}
	if order.GatherSupplyID > 0 {
		balanceData.PayType = order.PayTypeID
		log.Log().Info("采购账户退款", zap.Any("info", balanceData))
	}
	var applicationID uint
	var application Application
	if order.ApplicationID == 0 {
		err = source.DB().Where("member_id = ?", order.UserID).First(&application).Error
		if err != nil {
			err = errors.New("未绑定采购端，无法退款")
			return
		}
		applicationID = application.ID
	} else {
		applicationID = order.ApplicationID
	}
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		//开始回流库存
		for _, orderItemExec := range OrderItems {
			err = tx.Model(&ApplicationVirtualStock{}).Where("application_id = ?", applicationID).Where("product_id = ?", orderItemExec.ProductID).Update("stock", gorm.Expr("`stock` + ?", orderItemExec.Qty)).Error
			if err != nil {
				return err
			}
			err = tx.Create(&VirtualStockRecord{
				ApplicationID: applicationID,
				ProductID:     orderItemExec.ProductID,
				UserID:        order.UserID,
				OrderID:       orderItemExec.OrderID,
				OrderItemID:   orderItemExec.ID,
				Stock:         orderItemExec.Qty,
				Type:          1,
				Remark:        "退款订单号：" + order.OrderSN,
			}).Error
			if err != nil {
				return err
			}

		}

		return err
	})
	return

}

func (station *VirtualStock) Recharge() (err error, resData interface{}) {

	return
}

func (station *VirtualStock) Deduction() (err error, resData interface{}) {
	return
}

func (station *VirtualStock) GetBalance() (err error, resData interface{}) {
	return
}

func (station *VirtualStock) Settlement() (err error) {
	return
}

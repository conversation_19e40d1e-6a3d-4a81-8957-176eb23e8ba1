package Pay

import (
	joinModel "convergence/model"
	joinpay_res "convergence/response"
	joinService "convergence/service"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"payment/model"
	model2 "payment/model"
	pmodel "payment/model"
	"payment/mq"
	"payment/request"
	"payment/service"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

type ConvergenceBalanceRefund struct {
	request.Refund
}

type ConvergenceBalanceRecharge struct {
	model.UserTopUp
}
type ConvergenceBalancePayment struct {
	model.UpdateBalance
}

// TableName 自定义表名
func (ConvergenceBalanceRecharge) TableName() string {
	return "user_top_ups"
}

type ConvergenceBalance struct {
	ConvergenceRecharge          ConvergenceBalanceRecharge
	ConvergenceBalancePayment    interface{}
	ConvergenceBalanceRefund     interface{}
	ConvergenceBalanceSettlement interface{}
}

func (convergence *ConvergenceBalance) SplitAccount() (err error) {

	order := convergence.ConvergenceBalanceSettlement.(*model2.Order)

	var supplierOrder model.SupplierSettlement
	err = source.DB().Where("order_id = ?", order.ID).First(&supplierOrder).Error
	if err != nil {
		log.Log().Error("ConvergenceBalance_Increase", zap.Any("err", err.Error()))
	}
	var settlementSub []model.SettlementSubAccount
	source.DB().Where("supplier_settlement_id=?", supplierOrder.ID).Find(&settlementSub)
	var AltMchNo string

	if supplierOrder.SupplierID > 0 {

		var supplierAccount model.AccountApply
		err = source.DB().Where("member_id = ?", supplierOrder.SupplierID).First(&supplierAccount).Error //查询供应商汇聚分帐编号
		if err != nil {                                                                                  //判断是供应商的， 切供应商开户id未查询到
			return err
		}
		AltMchNo = supplierAccount.AltMchNo
	} else { //平台
		AltMchNo = config.Config().Join.AltMchNo
	}
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		var amount string
		var isSuccess uint = 1

		for _, item := range settlementSub {
			var infoData []joinModel.AltInfo
			amount = utils.Fen2Yuan(item.Amount)
			orderNo := utils.GetOrderNo()
			supplierAmount := utils.Fen2Yuan(item.SupplierAmount)
			if item.SupplierAmount > 0 {
				infoData = append(infoData, joinModel.AltInfo{AltMchNo: AltMchNo, AltAmount: supplierAmount}) //供应商
			}
			joinData := joinModel.MoreSeparateData{
				AltOrderNo:    orderNo,
				MchOrderNo:    strconv.Itoa(int(item.UserTopUp)),
				AltThisAmount: amount,
				AltInfo:       infoData,
			}

			var res joinpay_res.Response
			err, res = joinService.MoreSeparateAccount(joinData)
			jsonData, _ := json.Marshal(res)
			postData, _ := json.Marshal(joinData)

			var separate joinModel.SeparateAccountingRecords

			separate.Info = res.Data.BizMsg
			separate.ReqData = string(postData)
			separate.ResData = string(jsonData)
			separate.Amount = item.SupplierAmount
			separate.OrderSN = order.OrderSN
			separate.SupplierID = order.SupplierID

			if "B100000" == res.Data.BizCode {
				separate.Status = 1

			} else {
				isSuccess = 0
			}
			err = tx.Create(&separate).Error
			if err != nil {
				return err
			}

		}

		if isSuccess == 1 {
			supplierOrder.Status = isSuccess
			supplierOrder.SettlementTime = &source.LocalTime{Time: time.Now()}
			tx.Save(&supplierOrder)
		} else {
			supplierOrder.TaskStatus = 1
			tx.Save(&supplierOrder)
		}

		return nil
	})

	return
}

func (convergence *ConvergenceBalance) Settlement() (err error) {

	supplierSettlement := convergence.ConvergenceBalanceSettlement.(model.SupplierSettlement)

	var order model2.Order
	source.DB().First(&order, "id=?", supplierSettlement.OrderID)

	deductionAmount := order.Amount
	//	settlementAmount := supplierSettlement.SupplyAmount

	totalSettlementAmount := supplierSettlement.SettlementAmount

	var userTopUp []model.UserTopUp
	err = source.DB().Scopes(IsPay, Available).Order("remaining_amount asc").Find(&userTopUp).Error
	if err != nil {
		return
	}
	//var payMap = make(map[uint]uint)
	//var IsDeduct = 0

	err = source.DB().Transaction(func(tx *gorm.DB) error {
		for _, item := range userTopUp {

			var settlementAccount model.SettlementSubAccount
			settlementAccount.OrderSN = supplierSettlement.OrderSn
			settlementAccount.SupplierID = supplierSettlement.SupplierID
			settlementAccount.UserTopUp = item.PaySN
			settlementAccount.SupplierSettlementID = supplierSettlement.ID

			if deductionAmount <= 0 {
				return nil
			}
			//var supplierAmount uint
			if item.RemainingAmount >= deductionAmount {

				settlementAccount.Amount = deductionAmount

				//SupplyAmount := settlementAccount.Amount - supplierSettlement.TechnicalServicesFee
				//if forCount == 0 { //判断如果是单笔够付的情况 直接用结算价格计算     否则用扣除后的价格
				//	SupplyAmount = supplierSettlement.SupplyAmount
				//}
				//
				//supplierAmount, err = service.MoreSupplierSettlmentCalculation(uint(supplierSettlement.DeductionRatio), uint(supplierSettlement.WithdrawalRatio), SupplyAmount)

				//if forCount == 0 { //判断如果是单笔够付的情况 直接用结算价格计算     否则用扣除后的价格
				settlementAccount.SupplierAmount = totalSettlementAmount
				//}

				//if settlementAmount > supplierAmount && IsDeduct == 0 {
				//	settlementAccount.SupplierAmount = settlementAmount - supplierAmount
				//	IsDeduct = 1
				//}

				if createErr := tx.Create(&settlementAccount).Error; createErr != nil {
					return createErr
				}
				updateErr := tx.Model(&model.UserTopUp{}).Where("id=?", item.ID).Update("remaining_amount", gorm.Expr("remaining_amount - ?", deductionAmount)).Error
				if updateErr != nil {
					return updateErr
				}
				return nil
			}

			deductionAmount = deductionAmount - item.RemainingAmount

			settlementAccount.Amount = item.RemainingAmount
			if totalSettlementAmount > item.RemainingAmount {
				settlementAccount.SupplierAmount = item.RemainingAmount
				totalSettlementAmount = totalSettlementAmount - item.RemainingAmount
			} else {
				settlementAccount.SupplierAmount = totalSettlementAmount
				totalSettlementAmount = 0

			}

			//supplierAmount, err = service.MoreSupplierSettlmentCalculation(uint(supplierSettlement.DeductionRatio), uint(supplierSettlement.WithdrawalRatio), settlementAccount.Amount)
			//settlementAccount.SupplierAmount = item.RemainingAmount - supplierAmount
			//if item.RemainingAmount > supplierAmount && IsDeduct == 0 {
			//	settlementAccount.SupplierAmount = item.RemainingAmount - supplierAmount
			//	IsDeduct = 1
			//}

			if createErr := tx.Create(&settlementAccount).Error; createErr != nil {
				return createErr
			}
			updateErr := tx.Model(model.UserTopUp{}).Where("id=?", item.ID).Update("remaining_amount", gorm.Expr("remaining_amount - ?", item.RemainingAmount)).Error
			if updateErr != nil {
				return updateErr
			}

		}

		return nil
	})

	return

}

func (convergence *ConvergenceBalance) Deduction() (err error, resData interface{}) {

	return

}

func (convergence *ConvergenceBalance) Init() (err error, resData interface{}) {
	//PayTypeList = append(PayTypeList, Type{Name: "汇聚余额", Code: 1, Status: 1, Mode: 1})
	return
}

func (convergence ConvergenceBalance) Recharge() (err error, resData interface{}) {
	if convergence.ConvergenceRecharge.PayType <= 0 {
		err = errors.New("支付类型不能为空")
		return
	}
	var paySN = strconv.Itoa(int(convergence.ConvergenceRecharge.Uid)) + strconv.Itoa(int(time.Now().Unix()))
	var count int64
	source.DB().Model(model.UserTopUp{}).Debug().Where("pay_sn", paySN).Count(&count)
	if count > 0 {
		err = errors.New("请勿使用重复支付单")
		return
	}
	convergence.ConvergenceRecharge.PaySN = utils.StrToUInt(paySN)
	convergence.ConvergenceRecharge.RemainingAmount = convergence.ConvergenceRecharge.Amount
	var uniPay joinModel.UniPay
	uniPay.P2_OrderNo = paySN
	uniPay.P3_Amount = utils.Fen2Yuan(convergence.ConvergenceRecharge.Amount)
	uniPay.P7_Mp = convergence.ConvergenceRecharge.PayType
	uniPay.Uid = convergence.ConvergenceRecharge.Uid
	err = source.DB().Create(&convergence.ConvergenceRecharge).Error
	if err != nil {
		return
	}
	err, resData = joinService.GetPayQrCode(uniPay)
	return

}

func (convergence ConvergenceBalance) Payment() (err error, resData interface{}) {
	payment := convergence.ConvergenceBalancePayment.(model.UpdateBalance)
	if err = convergence.BeforeOperation(); err != nil {
		return
	}
	var data = make(map[string]interface{})
	data["status"] = 0
	var balance model.Balance
	err = source.DB().Where("uid = ?", payment.Uid).Where("type = ?", payment.PayType).First(&balance).Error
	if err != nil {
		return
	}

	if balance.ID <= 0 {
		err = errors.New("未查询到此用户余额")
		return
	}
	if balance.PurchasingBalance < payment.Amount {
		err = errors.New("余额不足")
		return
	}

	params := model.UpdateBalance{
		Uid:           payment.Uid,
		Amount:        payment.Amount,
		Type:          model.CONVERGENCEBALANCE,
		PayType:       model.CONVERGENCEBALANCE,
		OperationType: model.BALANCE_PURCHASING,
		Action:        model.REDUCE,
		PayInfoID:     payment.PayInfoID,
	}

	err = service.UpdateSettlementBalance(params)
	if err != nil {
		return
	}

	data["status"] = 1
	var payInfo pmodel.PayInfo
	err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo).Error
	if err != nil {
		log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
		return
	}
	err = mq.PublishMessage(payInfo.PaySN, params.PayType)

	resData = data
	err = convergence.AfterOperation()
	return
}

func (convergence ConvergenceBalance) Refund() (err error, resData interface{}) {
	refund := convergence.ConvergenceBalanceRefund.(request.Refund)
	balanceData := model.UpdateBalance{
		Uid:           refund.UserID,
		Amount:        refund.Amount,
		Type:          2,
		PayType:       model.CONVERGENCEBALANCE,
		OperationType: model.BALANCE_PURCHASING,
		Action:        model.PLUS,
		Remarks:       "",
	}
	err = service.ChangeBalance(model.BusinessConvergenceBalanceRefund, balanceData)

	return
}

func (convergence ConvergenceBalance) GetBalance() (err error, resData interface{}) {

	return
}
func (convergence ConvergenceBalance) BeforeOperation() (err error) {
	payment := convergence.ConvergenceBalancePayment.(model.UpdateBalance)
	if payment.Amount <= 0 {
		err = errors.New("扣款余额不能空")
		return
	}

	var orderPayInfo model2.OrderPayInfo

	err = source.DB().Where("pay_info_id=?", payment.PayInfoID).First(&orderPayInfo).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("未查询到支付单")
		return
	}

	var order model2.Order
	err = source.DB().Where("id=?", orderPayInfo.OrderID).First(&order).Error
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = errors.New("未查询到支付单")
		return
	}
	if order.Status > 0 {
		err = errors.New("该订单已支付,请勿重复支付")
		return
	}

	return
}
func (convergence ConvergenceBalance) AfterOperation() (err error) {

	return

}

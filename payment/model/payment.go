package model

import (
	"gorm.io/gorm"
	"time"
	"yz-go/source"
)

type PayStatus int

const (
	NotPay PayStatus = iota
	Paid
	InvalidPay   = -1
	Refunded     = -2
	PartRefunded = -3
)

// PayInfo
// 支付信息
type PayInfo struct {
	source.Model
	Status         PayStatus         `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"`                    // 状态（0待支付，1已支付，-1已失效，-2已退款 -3部分退款）
	PaySN          uint              `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;index;"`                                   // 编号
	Amount         uint              `json:"amount" form:"amount" gorm:"column:amount;comment:支付总金额(分);"`                                   // 支付总金额
	RefundedAmount uint              `json:"refunded_amount" form:"refunded_amount" gorm:"column:refunded_amount;comment:已退款金额(分);"`        // 已退款金额
	UserID         uint              `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;index;"`                              // 用户id
	PayTypeID      int               `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"` // 订单支付方式
	TransactionId  string            `json:"transaction_id" form:"transaction_id" gorm:"column:transaction_id;comment:微信支付订单号;"`            //微信支付订单号
	PluginId       int               `json:"plugin_id" form:"plugin_id" gorm:"column:plugin_id;comment:插件id;type:smallint;size:3;"`         // 订单支付方式
	Type           uint              `json:"type" form:"type" gorm:"column:type;comment:用来区分什么支付: 30000 数据通-聚合支付（无论pay_type_id是多少都是聚合支付）;"` //目前只有数据通-汇聚支付 此值为30000
	Desc           string            `json:"desc" form:"desc" gorm:"column:desc;comment:支付之后需要记录的信息;"`                                      //支付之后需要记录的信息
	ExpireAt       *source.LocalTime `json:"expire_at"`                                                                                     // 过期时间
	PaidAt         *source.LocalTime `json:"paid_at"`                                                                                       // 支付时间
	RefundAt       *source.LocalTime `json:"refund_at"`                                                                                     // 退款时间
}

func (p *PayInfo) AfterCreate(tx *gorm.DB) (err error) {
	p.ExpireAt = &source.LocalTime{Time: time.Now().Add(time.Hour)}

	timestamp := uint(p.CreatedAt.Unix())
	paySn := p.ID + timestamp*110
	err = tx.Model(&p).Update("pay_sn", paySn).Error
	return
}

type ApplicationPaySort struct {
	source.Model
	ApplicationID uint   `json:"application_id" form:"application_id" gorm:"column:application_id;type:int(11);"`
	Sort          int    `json:"sort"`
	PayTypeID     int    `json:"pay_type_id"`
	Name          string `json:"name"`
}

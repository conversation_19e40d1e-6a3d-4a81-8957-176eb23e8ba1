package payment

import (
	"errors"
	"fmt"
	"payment/entrance"
	"payment/model"
	"payment/request"
	"time"
	"yz-go/source"
)

type Payment struct {
	IconUrl   string `json:"icon_url"`
	Title     string `json:"title"`
	Desc      string `json:"desc"`
	QrCode    string `json:"qr_code"`
	QrCodeUrl string `json:"qr_code_url"`
}

func GetList(Sn string, Amount uint) (err error, payments []Payment) {
	payments = append(payments, Payment{
		IconUrl:   "",
		Title:     "聚合支付-微信",
		Desc:      "聚合分账支付，货款更安全",
		QrCode:    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAANIAAADSAQAAAAAX4qPvAAABxElEQVR42u2YO66EMAxFPaJIyRKyk2FjSCCxMdhJlkBJgfC71xkNzPt0T3ZDCgg5Kaz4d4Pon2OWm93s/9guGIOmZR2KPNZG+Z2DGN5rn9MhTdGjVU11LYbBukF10TFv0k6aZukjWS9pbkeRJ08vmA04LsFjliaW0X+yPXBmv/vWkTGOEUvL+np8j3lHVkeRjgF1IJZ+1AI/Bjsfq8j2ZCy9ZlMc4wdrjyLHxpx0vZynL6PXBLWHGxBLReS005sx1RHIVnuQ76WWohCmBXY2xWoPLRYcnEYxvNodE/qPvQr5PuYwxmVh7aH/OJs0iJWtw7J0jKqZzXO5+M+XoT908B83wH94bKed3szstOBWtWWk/9t/zmyHipBe0Dclm9hKx6UW+LKXdQxutKlaB4c41lFPMNVN5Vz958yoHViAbBc6Bc9szFHMdBaznMtw56VvujO4zvSEXQ8apfoLY+hQki3fsYEqqz31oC+zwXwX0xM0+62FvVnVn5bvVgeZ9I0GMerywXp4NlWM1Y87pSuzLK/53mFmBSiSWdLDfxgQFRLKxFSEBVS63BHcmd3jLN/rfeWjFviyGtdnvmM2BbH7H9nNQtkXHajjS3mORykAAAAASUVORK5CYII=",
		QrCodeUrl: "https://trade.joinpay.com/wxPay.action?trxNo=100221050774346920",
	})
	return
}
func Refund(PaySn string, amount uint, orderItemID uint, orderSN uint) (err error) {
	var payInfo model.PayInfo
	err = source.DB().Where("pay_sn = ?", PaySn).Find(&payInfo).Error
	// 校验
	if payInfo.Status != model.Paid && payInfo.Status != model.PartRefunded {
		err = errors.New(GetStatusName(payInfo.Status) + "状态的支付单无法执行退款操作")
		return
	}
	// 原路退款
	err = entrance.RefundService(request.Refund{PaySN: PaySn, Amount: amount, UserID: payInfo.UserID, PayTypeID: payInfo.PayTypeID, OrderItemID: orderItemID, Type: payInfo.Type, OrderSN: orderSN})
	if err != nil {
		return
	}
	// 修改支付信息
	var updatePayInfo map[string]interface{}
	if amount > payInfo.Amount {
		err = errors.New(fmt.Sprintf("退款金额(￥%d)不能大于支付金额(￥%d)", amount/100, payInfo.Amount/100))
	} else {
		var payStatus model.PayStatus
		if payInfo.Amount-amount == 0 {
			payStatus = model.Refunded
		} else {
			payStatus = model.PartRefunded
		}
		updatePayInfo = map[string]interface{}{"amount": payInfo.Amount - amount, "status": payStatus, "refunded_amount": payInfo.RefundedAmount + amount, "refund_at": time.Now().Format("2006-01-02 15:04:05")}

	}
	payInfo.RefundAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Model(&model.PayInfo{}).Where("id = ?", payInfo.ID).Updates(updatePayInfo).Error
	return
}

func Pay(PaySn string, PayType int) (err error) {
	var payInfo model.PayInfo
	err = source.DB().Where("pay_sn = ?", PaySn).Find(&payInfo).Error
	if payInfo.Status != model.NotPay {
		err = errors.New(GetStatusName(payInfo.Status) + "状态的支付单无法执行付款操作")
		return
	}
	payInfo.Status = model.Paid
	payInfo.PayTypeID = PayType
	payInfo.PaidAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Updates(payInfo).Error
	return
}

func GetStatusName(status model.PayStatus) string {
	Names := map[model.PayStatus]string{
		model.Refunded:   "已退款",
		model.InvalidPay: "已关闭",
		model.NotPay:     "待支付",
		model.Paid:       "已付款",
	}
	return Names[status]
}

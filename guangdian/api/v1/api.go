package v1

import (
	"errors"
	v1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"guangdian/component/goods"
	"guangdian/model"
	"guangdian/service"
	yzmodel "yz-go/model"

	productCommon "product/common"
	model3 "product/model"
	request3 "product/request"
	service3 "product/service"
	model2 "public-supply/model"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"
	"yz-go/utils"
)

func GuangDianUpdateProduct(c *gin.Context) {
	var product service3.ProductForUpdate
	err := c.ShouldBindJSON(&product)
	if (product.SingleOption == 1 && len(product.Skus) == 0) || len(product.Skus) == 0 {
		sku := service3.Sku{
			Title:         "默认",
			Price:         product.Price,
			CostPrice:     product.CostPrice,
			OriginPrice:   product.OriginPrice,
			GuidePrice:    product.GuidePrice,
			ActivityPrice: product.ActivityPrice,
			Barcode:       product.Barcode,
			Stock:         int(product.Stock),
			Weight:        product.Weight,
			Sn:            product.Sn,
			OriginalSkuID: product.OriginalSkuId,
			Options:       model3.Options{{SpecName: "规格", SpecItemName: "默认"}},
			ImageUrl:      product.SkuImage,
		}
		sku.ID = product.SkuID
		product.Skus = append(product.Skus, sku)
	} else {
		product.Price = product.Skus[0].Price
		product.CostPrice = product.Skus[0].CostPrice
		product.OriginPrice = product.Skus[0].OriginPrice
		product.GuidePrice = product.Skus[0].GuidePrice
		product.ActivityPrice = product.Skus[0].ActivityPrice
		if product.Sn == "" {
			product.Sn = product.Skus[0].Sn
		}
		product.Barcode = product.Skus[0].Barcode
	}
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, productOld := service3.GetProductForUpdate(product.ID)

	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}

	if err = service.GuangDianUpdateProduct(product); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		//service2.CreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "编辑了商品'"+product.Title+"'")
		service2.ProductCreateOperationRecord(v1.GetUserID(c), 1, c.ClientIP(), "编辑了商品'"+product.Title+"'", productCommon.CreateLogDetailContent(productOld, product))

		yzResponse.OkWithMessage("更新成功", c)
	}
}

func SetSetting(c *gin.Context) {
	var setting model.Setting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SetSetting(setting); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("设置成功", c)
	}
}

func SetSettingSelf(c *gin.Context) {
	var setting yzmodel.SysSetting
	err := c.ShouldBindJSON(&setting)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SetSettingSelf(setting); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("设置成功", c)
	}
}

func GetSetting(c *gin.Context) {
	err, data := service.GetSetting()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}
func GetSettingSelf(c *gin.Context) {
	err, data := service.GetSettingSelf()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {

		var maps = make(map[string]interface{})
		maps["data"] = data
		maps["domain"] = c.Request.Host
		maps["proto"] = c.Request.Header.Get("X-Forwarded-Proto")

		maps["url"] = string(c.Request.Host)

		yzResponse.OkWithData(maps, c)

		//yzResponse.OkWithData(data, c)
	}
}
func GetAppCountTotal(c *gin.Context) {

	var searchApp model.SearchApp
	err := c.ShouldBindJSON(&searchApp)
	if err != nil {
		log.Log().Error("获取参数失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, list, total := service.GetAppCountTotal(searchApp)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     searchApp.Page,
			PageSize: searchApp.PageSize,
		}, "获取成功", c)
	}
}

type PageResult struct {
	yzResponse.PageResult
}

func GetSettlement(c *gin.Context) {

	var searchSettlement model.SearchSettlement
	err := c.ShouldBindJSON(&searchSettlement)
	if err != nil {
		log.Log().Error("获取参数失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, list, total, totalize, lastMonth := service.GetSettlement(searchSettlement)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {

		yzResponse.OkWithDetailed(gin.H{
			"list":      list,
			"total":     total,
			"page":      searchSettlement.Page,
			"pageSize":  searchSettlement.PageSize,
			"totalize":  totalize,
			"lastMonth": lastMonth,
		}, "获取成功", c)
	}
}
func GetReward(c *gin.Context) {

	var searchSettlement model.SearchSettlement
	err := c.ShouldBindJSON(&searchSettlement)
	if err != nil {
		log.Log().Error("获取参数失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, list, total := service.GetReward(searchSettlement)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     searchSettlement.Page,
			PageSize: searchSettlement.PageSize,
		}, "获取成功", c)
	}
}
func GetUserInfo(c *gin.Context) {

	var searchUserInfo model.SearchUserInfo
	err := c.ShouldBindJSON(&searchUserInfo)
	if err != nil {
		log.Log().Error("获取参数失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, data := service.GetUserInfo(searchUserInfo.ThirdOrderSn)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func GetArea(c *gin.Context) {
	var err error
	var guangdian goods.GuangDian
	var gather model2.GatherSupply
	err = source.DB().Where("category_id=?", 132).First(&gather).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if gather.ID == 0 {
		yzResponse.FailWithMessage("中台未配置广电信息", c)
		return
	}
	err = guangdian.InitSetting(gather.ID)
	if err != nil {
		return
	}
	err, data := guangdian.GetArea()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data.Data, c)
	}
}

func GetNumber(c *gin.Context) {
	var err error
	var requestNumberData model.RequestNumberData

	err = c.ShouldBindQuery(&requestNumberData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if requestNumberData.Code == "" {
		yzResponse.FailWithMessage("请传入code", c)
		return
	}
	var guangdian goods.GuangDian
	var gather model2.GatherSupply
	source.DB().Where("category_id=?", 132).First(&gather)

	if gather.ID > 0 {
		guangdian.InitSetting(gather.ID)

	}

	if guangdian.GuangdianSetting.Key == "" && guangdian.GuangdianSetting.Secret == "" {
		err, data := guangdian.GetUpNumbers(requestNumberData)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		} else {
			yzResponse.OkWithData(data.Data, c)
		}
	} else {
		err, data := guangdian.GetNumbers(requestNumberData)
		if err != nil {
			yzResponse.FailWithMessage(err.Error(), c)
			return
		} else {
			yzResponse.OkWithData(data.Data, c)
		}
	}

}
func RechargeRecord(c *gin.Context) {
	var err error
	var requestNumberData model.RequestRechargeRecord

	err = c.ShouldBindQuery(&requestNumberData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	//if requestNumberData.PhoneNumber == "" {
	//	yzResponse.FailWithMessage("请传入code", c)
	//	return
	//}
	var guangdian goods.GuangDian
	var gather model2.GatherSupply
	err = source.DB().Where("category_id=?", 132).First(&gather).Error
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if gather.ID == 0 {
		yzResponse.FailWithMessage("中台未配置广电信息", c)
		return
	}
	err = guangdian.InitSetting(gather.ID)
	if err != nil {
		return
	}
	err, data := guangdian.RechargeRecord(requestNumberData)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data.Data, c)
	}
}

type Application struct {
	ID               uint             `json:"id"`
	SupplierID       uint             `json:"supplier_id"`
	AppLevelID       uint             `json:"app_level_id"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}

func (Application) TableName() string {
	return "application"
}

type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}

func GetProductList(c *gin.Context) {
	var pageInfo request3.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetProductList(pageInfo, app.AppLevelID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
		}, "获取成功", c)
	}
}

type AppProductPageResult struct {
	yzResponse.PageResult
}

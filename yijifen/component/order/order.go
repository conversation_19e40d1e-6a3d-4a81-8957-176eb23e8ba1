package order

import (
	model5 "after-sales/model"
	"encoding/json"
	"errors"
	"fmt"
	reqv3 "github.com/imroc/req/v3"
	"math/rand"
	url2 "net/url"
	v1 "order/api/v1"
	orderModel "order/model"
	orderRequest2 "order/request"
	common2 "public-supply/common"
	"shipping/address"
	model6 "yijifen/model"
	model3 "yiyatong/model"

	model4 "product/model"
	callback2 "public-supply/callback"
	"public-supply/model"
	"public-supply/request"
	"public-supply/response"
	gsetting "public-supply/setting"
	"strconv"
	"strings"
	"time"
	yjfGoods "yijifen/component/goods"
	"yz-go/component/log"
	model2 "yz-go/model"
	"yz-go/source"
	"yz-go/utils"

	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
)

type Yjf struct {
	dat       *model.SupplySetting
	PublicDat *model.SupplySetting
	SupplyID  uint
	Http      string
	//goods     []model3.YjfGoods
}

func (y *Yjf) UploadGatherSupplySN(request request.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (y *Yjf) GetRefundTypes(orderId uint, orderItemID uint) (err error, data []model5.AfterSalesType) {
	return
}

func (y *Yjf) GetToken() (err error) {

	return
}

func (y *Yjf) AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{}) {
	return
}

func (y *Yjf) AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (y *Yjf) AfterSale(request request.AfterSale) (err error, info interface{}) {
	return
}

func (y *Yjf) OrderDelivery(OrderData callback2.OrderCallBack) (err error) {
	return
}

func (y *Yjf) SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse) {
	return
}

func (y *Yjf) InitSetting(gatherSupplyID uint) (err error) {
	y.SupplyID = gatherSupplyID
	var setting model2.SysSetting
	err, setting = gsetting.GetSetting("gatherSupply" + strconv.Itoa(int(gatherSupplyID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}
	err = json.Unmarshal([]byte(setting.Value), &y.dat)
	if err != nil {

		log.Log().Error("获取失败", zap.Any("err", err))
		return
	}
	y.PublicDat = y.dat
	log.Log().Info("Yjf InitSetting", zap.Any("info", y.dat))

	if y.dat.BaseInfo.AppKey == "" && y.dat.BaseInfo.AppSecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}

	//y.Http = y.dat.BaseInfo.ApiUrl

	y.Http = y.dat.BaseInfo.ApiUrl
	log.Log().Info("Yjf InitSetting", zap.Any("y.dat.BaseInfo.ApiUrl", y.dat.BaseInfo.ApiUrl))
	log.Log().Info("Yjf InitSetting", zap.Any("y.Http", y.Http))

	model6.SetYjfKd()

	return
}

func (y *Yjf) CancelOrder(orderID uint) {

	//var order model3.Order
	//err := source.DB().Where("id=?", orderID).First(&order).Error
	//if err != nil {
	//	log.Log().Info("yzh供应链订单取消错误", zap.Any("info", err))
	//	return
	//}
	//
	//if order.GatherSupplyType == common2.SUPPLY_YZHNEW && order.GatherSupplySN != "" {
	//	y.InitSetting(order.GatherSupplyID)
	//	if y.dat == nil {
	//		return
	//	}
	//	log.Log().Info("yzhnew供应链订单取消order开始", zap.Any("info", order))
	//
	//	url := y.Http + "open/api/order/cancelOrder"
	//	var resData []byte
	//	headerData := make(map[string]interface{})
	//	headerData["accessToken"] = y.dat.BaseInfo.Token
	//	headerData["parentOrderCode"] = order.GatherSupplySN
	//	reqData, _ := json.Marshal(headerData)
	//
	//	log.Log().Info("yzhnew供应链订单取消order开始 请求数据", zap.Any("info", string(reqData)))
	//
	//	resData = utils.HttpPostJson(reqData, url)
	//	var CancelOrder model5.CancelOrder
	//	err = json.Unmarshal(resData, &CancelOrder)
	//	if CancelOrder.Success == true {
	//		source.DB().Table("orders").Where("id=?", orderID).UpdateColumn("gather_supply_sn", gorm.Expr("CONCAT_WS('-',gather_supply_sn,?)", "取消成功"))
	//		return
	//	}
	//	return
	//
	//}

}

func (y *Yjf) QueryGoodsSaleLimitList(request request.RequestSaleBeforeCheck) (err error) { //查询商品的限制销售地区

	return
}

// padLeft 使用0字符在字符串的左侧补充到指定长度
func padLeft(str string, length int) string {
	if len(str) == 6 {
		return str
	}
	for {
		str = str + "0"
		if len(str) >= length {
			return str[len(str)-length:]
		}
	}
}
func (y *Yjf) GetFreight(request request.RequestSaleBeforeCheck, product interface{}) (err error, TotalFreight int) {
	var url = y.dat.BaseInfo.ApiUrl + "/freight/getFreight"
	var reqMap = make(map[string]interface{})
	var reqHeader = map[string]string{}
	reqMap["products"] = product
	reqMap["contacts"] = UnicodeText(request.Address.Consignee)
	reqMap["contactsPhone"] = request.Address.Phone

	var shippingAddress address.ShippingAddress
	err = source.DB().Where("province=? and city=? and county=?", request.Address.Province, request.Address.City, request.Address.Area).First(&shippingAddress).Error

	if err != nil {
		log.Log().Info("yjf GetFreight shippingAddress err", zap.Any("err", err))
		return
	}
	pStr := strconv.Itoa(shippingAddress.ProvinceId)
	cStr := strconv.Itoa(shippingAddress.CityId)
	aStr := strconv.Itoa(shippingAddress.CountyId)
	reqMap["provinceCode"] = padLeft(pStr, 6)
	reqMap["cityCode"] = padLeft(cStr, 6)
	reqMap["districtCode"] = padLeft(aStr, 6)
	reqMap["address"] = UnicodeText(request.Address.Description)
	reqMap["provinceLabel"] = UnicodeText(request.Address.Province)
	reqMap["cityLabel"] = UnicodeText(request.Address.City)
	reqMap["districtLabel"] = UnicodeText(request.Address.Area)
	channelId := y.dat.BaseInfo.Channel
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)

	var freightData model6.FreightData
	client := reqv3.C()
	resp, err := client.R().SetSuccessResult(&freightData).SetBody(reqMap).SetHeaders(reqHeader).Post(url)

	if err != nil {
		fmt.Println(err)
	}
	if resp.IsSuccessState() {
		fmt.Println(resp.Status)
	}

	if resp.IsErrorState() {
		fmt.Println(resp.Body)
	}
	//TotalFreight = int(freight.Data * 100)
	TotalFreight = freightData.Data.TotalFreight

	return
}

type ItemDTO struct {
	SkuId  int64 `json:"skuId"`
	SkuNum int   `json:"skuNum"`
}

func (y *Yjf) CheckSkuStock(itemDTOList []ItemDTO, reginId, productTitle, cityName string) (err error) {
	url := y.Http + "/scce/cmc/cmc/spu/open/checkSkuStock"
	reqData := make(map[string]string)
	requestParam := make(map[string]interface{})

	jsonSpuInfoList, _ := json.Marshal(&itemDTOList)
	reqData["itemDTOList"] = string(jsonSpuInfoList)
	reqData["areaRegionCode"] = reginId
	requestParam["itemDTOList"] = itemDTOList
	requestParam["areaRegionCode"] = reginId

	//JsonData, _ := json.Marshal(reqData)
	requestParamJsonData, _ := json.Marshal(requestParam)
	var headMap = make(map[string]string)
	headMap["appKey"] = y.dat.BaseInfo.AppKey
	now := time.Now()
	nanoseconds := now.UnixNano()
	milliseconds := nanoseconds / 1e6
	strTime := strconv.Itoa(int(milliseconds))
	headMap["timeStamp"] = strTime
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Int()

	randStr := strconv.Itoa(randomNumber)
	headMap["randomNumber"] = randStr
	headMap["version"] = "1.0"
	log.Log().Info("请求数据", zap.Any("", reqData))
	signMap := MergeMaps(reqData, headMap)
	signSort := Sign(signMap, y.dat.BaseInfo.AppSecret)
	headMap["sign"] = signSort
	headMap["Content-Type"] = "application/json"
	log.Log().Info("请求头", zap.Any("", headMap))

	err, resData := utils.PostDataJson(url, requestParamJsonData, headMap)
	log.Log().Info("返回数据", zap.Any("", string(resData)))

	var checkSkuStockData model3.CheckSkuStockData
	err = json.Unmarshal(resData, &checkSkuStockData)
	if err != nil {
		return
	}
	if checkSkuStockData.Status != 200 || checkSkuStockData.Success != true {
		err = errors.New(checkSkuStockData.Msg)
	}
	for _, item := range checkSkuStockData.Data {
		if item.SailState != 1 {
			err = errors.New(productTitle + cityName + "地区不可售")
			return
		}
		if item.StockState != 1 {
			err = errors.New(productTitle + cityName + "地区无库存")
			return
		}
	}

	return
}

// 订单前置校验  返回运费   //立即购买校验
func (y *Yjf) OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.BeforeCheck) {
	log.Log().Info("Yjf OrderBeforeCheck request", zap.Any("info", request))
	res.Code = 1
	var Freight int
	for _, item := range request.LocalSkus {
		var sku model4.Sku
		err = source.DB().Where("id=?", item.Sku.Sku).Preload("Product").First(&sku).Error
		res.Freight = uint(Freight * 100)
		var products = make(map[string]interface{})
		var productList []interface{}
		products["id"] = sku.Product.SourceGoodsID
		products["buyNum"] = item.Number
		products["thirdOrderId"] = strconv.Itoa(int(time.Now().Unix()))
		if sku.SpecId != "" {
			products["skuId"] = sku.SpecId
			products["skuName"] = UnicodeText(sku.Title)
		}
		productList = append(productList, products)
		freightErr, freight := y.GetFreight(request, productList)
		if freightErr != nil {
			return
		}
		Freight = Freight + freight

		if err != nil {
			return
		}
	}

	res.Freight = uint(Freight)

	return
}

// 商品是否可售前置校验   下单支付校验
func (y *Yjf) SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, res response.ResSaleBeforeCheck) {
	res.Code = 0
	res.IsOriginalSkuID = 1
	for _, item := range request.LocalSkus {
		var sku model4.Sku

		err = source.DB().Preload("Product").Where("id=?", item.Sku.Sku).First(&sku).Error

		if sku.Stock > 0 {
			res.Data.Available = append(res.Data.Available, uint(item.Sku.Sku))

		} else {
			res.Code = 1
			res.Msg = "存在库存不足商品"
			res.Data.Ban = append(res.Data.Ban, uint(item.Sku.Sku))
		}

	}

	return

}

//var ProvinceAddress, CityAddress, AreaAddress []model5.YzhAddress

func (y *Yjf) GetAreaAddress(Area, pid string) (AreaID string, err error) {

	return

}

func (y *Yjf) GetStreetAddress(Street, pid string) (StreetID string, err error) {

	return

}

func (y *Yjf) GetGoodsPriceList(arr []string) int64 {

	return 0

}

func (y *Yjf) GetCityAddress(City, pid string) (CityID string, err error) {

	return

}

func (y *Yjf) QueryCategoryList(level, pid uint) (data interface{}) {

	return

}

func (y *Yjf) GetAllAddress() (err error, data interface{}) {

	return

}
func (y *Yjf) GetProvinceAddress(Province, City, Area string) (ProvinceID, CityID, AreaID string) {

	return

}

// 地址映射
func (y *Yjf) CreateAddressMapping(address model.AddressMapping) (err error) {

	//var addressMapping model.AddressMapping
	//addressMapping.MyAddress = address.MyAddress
	//addressMapping.OtherAddress = address.OtherAddress
	//addressMapping.GatherSupply = address.GatherSupply
	//
	//err = source.DB().Where("my_address=? and other_address=? and gather_supply=?", address.MyAddress, address.OtherAddress, address.GatherSupply).FirstOrCreate(&addressMapping).Error

	return
}

type GoodsList struct {
	GoodsQuantity uint   `json:"goodsQuantity"`
	GoodsSpecId   string `json:"goodsSpecId"`
}

func (y *Yjf) OrderReturnGoodsDetail(orderSN string) (err error, orderAfterSaleDetail model3.OrderAfterSaleDetail) {
	url := y.Http + "/scce/ctc/ctc/reseller/orderReturnGoods/detail"
	reqData := make(map[string]string)

	reqData["returnSn"] = orderSN

	requestParamJsonData, _ := json.Marshal(reqData)
	var headMap = make(map[string]string)
	headMap["appKey"] = y.dat.BaseInfo.AppKey
	now := time.Now()
	nanoseconds := now.UnixNano()
	milliseconds := nanoseconds / 1e6
	strTime := strconv.Itoa(int(milliseconds))
	headMap["timeStamp"] = strTime
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Int()

	randStr := strconv.Itoa(randomNumber)
	headMap["randomNumber"] = randStr
	headMap["version"] = "1.0"
	log.Log().Info("yyt 售后查询请求数据", zap.Any("", reqData))
	signMap := MergeMaps(reqData, headMap)
	signSort := Sign(signMap, y.dat.BaseInfo.AppSecret)
	headMap["sign"] = signSort
	headMap["Content-Type"] = "application/json"

	err, resData := utils.PostDataJson(url, requestParamJsonData, headMap)
	log.Log().Info("yyt 售后查询返回数据 ", zap.Any("", string(resData)))

	//var orderAfterSaleDetail model3.OrderAfterSaleDetail
	err = json.Unmarshal(resData, &orderAfterSaleDetail)
	if err != nil {
		return
	}

	return
}

func (y *Yjf) ReceiptRefund(orderSn string) (err error) {

	url := y.Http + "/scce/ctc/ctc/reseller/orderReturnGoods/receiptRefund"
	reqData := make(map[string]string)
	requestParam := make(map[string]interface{})
	reqData["returnSn"] = orderSn
	requestParam["returnSn"] = orderSn

	requestParamJsonData, _ := json.Marshal(requestParam)
	var headMap = make(map[string]string)
	headMap["appKey"] = y.dat.BaseInfo.AppKey
	now := time.Now()
	nanoseconds := now.UnixNano()
	milliseconds := nanoseconds / 1e6
	strTime := strconv.Itoa(int(milliseconds))
	headMap["timeStamp"] = strTime
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Int()

	randStr := strconv.Itoa(randomNumber)
	headMap["randomNumber"] = randStr
	headMap["version"] = "1.0"
	log.Log().Info("yyt ReceiptRefund 请求数据reqData", zap.Any("reqData", reqData))
	log.Log().Info("yyt ReceiptRefund 请求数据requestParam", zap.Any("requestParam", requestParam))
	signMap := MergeMaps(reqData, headMap)
	signSort := Sign(signMap, y.dat.BaseInfo.AppSecret)
	headMap["sign"] = signSort
	headMap["Content-Type"] = "application/json"
	log.Log().Info("请求头ReceiptRefund", zap.Any("", headMap))

	err, resData := utils.PostDataJson(url, requestParamJsonData, headMap)
	log.Log().Info("yyt ReceiptRefund ", zap.Any("", string(resData)))

	return
}

func (y *Yjf) CancelRefund(orderSn string) (err error) {

	url := y.Http + "/scce/ctc/ctc/reseller/orderReturnGoods/cancelRefund"
	reqData := make(map[string]string)
	requestParam := make(map[string]interface{})
	reqData["returnSn"] = orderSn
	requestParam["returnSn"] = orderSn

	requestParamJsonData, _ := json.Marshal(requestParam)
	var headMap = make(map[string]string)
	headMap["appKey"] = y.dat.BaseInfo.AppKey
	now := time.Now()
	nanoseconds := now.UnixNano()
	milliseconds := nanoseconds / 1e6
	strTime := strconv.Itoa(int(milliseconds))
	headMap["timeStamp"] = strTime
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Int()

	randStr := strconv.Itoa(randomNumber)
	headMap["randomNumber"] = randStr
	headMap["version"] = "1.0"
	log.Log().Info("yyt cancelRefund 请求数据reqData", zap.Any("reqData", reqData))
	log.Log().Info("yyt cancelRefund 请求数据requestParam", zap.Any("requestParam", requestParam))
	signMap := MergeMaps(reqData, headMap)
	signSort := Sign(signMap, y.dat.BaseInfo.AppSecret)
	headMap["sign"] = signSort
	headMap["Content-Type"] = "application/json"
	log.Log().Info("请求头cancelRefund", zap.Any("", headMap))

	err, resData := utils.PostDataJson(url, requestParamJsonData, headMap)
	log.Log().Info("yyt cancelRefund res  ", zap.Any("", string(resData)))

	return
}

func (y *Yjf) AfsApply(goodsList []GoodsList, orderSN, returnType, refundReasonName, Reason string, pictureurlList string, afterSaleID uint) (err error) { //售后类型，1：退款，2：退货退款，3：换货，4：仅退款，5：部分退款

	url := y.Http + "/refund/refundOrder"
	reqData := make(map[string]string)
	requestParam := make(map[string]interface{})

	jsonData, _ := json.Marshal(&goodsList)
	reqData["reason"] = refundReasonName
	reqData["orderSn"] = orderSN
	if returnType == "1" { //未发货退款
		reqData["refundReasonId"] = "6"
	}
	if returnType == "2" { //退货退款
		reqData["refundReasonId"] = "8"
	}
	if returnType == "4" { //已发货仅退款
		reqData["refundReasonId"] = "30"
	}
	if returnType == "3" { //换货
		reqData["refundReasonId"] = "29"
	}
	reqData["goodsList"] = string(jsonData)
	reqData["returnType"] = returnType
	reqData["memo"] = Reason
	if pictureurlList != "[]" {
		reqData["pictureurlList"] = pictureurlList

	}

	requestParam["reason"] = refundReasonName
	requestParam["orderSn"] = orderSN
	requestParam["refundReasonId"] = reqData["refundReasonId"]
	requestParam["goodsList"] = goodsList
	requestParam["returnType"] = returnType
	requestParam["memo"] = Reason
	if pictureurlList != "[]" {
		var imgArr ImgArr
		json.Unmarshal([]byte(pictureurlList), &imgArr)

		requestParam["pictureurlList"] = imgArr

	}

	requestParamJsonData, _ := json.Marshal(requestParam)
	var headMap = make(map[string]string)
	headMap["appKey"] = y.dat.BaseInfo.AppKey
	now := time.Now()
	nanoseconds := now.UnixNano()
	milliseconds := nanoseconds / 1e6
	strTime := strconv.Itoa(int(milliseconds))
	headMap["timeStamp"] = strTime
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Int()

	randStr := strconv.Itoa(randomNumber)
	headMap["randomNumber"] = randStr
	headMap["version"] = "1.0"
	log.Log().Info("yyt AfsApply 请求数据reqData", zap.Any("reqData", reqData))
	log.Log().Info("yyt AfsApply 请求数据requestParam", zap.Any("requestParam", requestParam))
	signMap := MergeMaps(reqData, headMap)
	signSort := Sign(signMap, y.dat.BaseInfo.AppSecret)
	headMap["sign"] = signSort
	headMap["Content-Type"] = "application/json"
	log.Log().Info("请求头AfsApply", zap.Any("", headMap))

	err, resData := utils.PostDataJson(url, requestParamJsonData, headMap)
	log.Log().Info("yyt AfsApply ", zap.Any("", string(resData)))

	var applyRefundData model3.ApplyRefundData
	err = json.Unmarshal(resData, &applyRefundData)
	if err != nil {
		return
	}
	if applyRefundData.Success != true {
		err = errors.New(applyRefundData.Msg)
		return
	}

	var afterSales model5.AfterSalesMigration
	afterSales.GatherSupplyAfterSaleSN = applyRefundData.Data.ReturnSn
	afterSales.Source = common2.YIYATONG_SOURCE
	err = source.DB().Where("id=?", afterSaleID).Updates(&afterSales).Error
	if err != nil {
		log.Log().Error("yyt AfsApply Updates err", zap.Any("", err))
		return
	}
	return
}

type ImgArr []string

func (y *Yjf) GetRefundOrder(refundOrderId string) (err error, refundOrder model6.RefundOrder) {
	url := y.Http + "/refund/getRefundOrder"
	var reqMap = make(map[string]interface{})
	var reqHeader = map[string]string{}
	reqMap["refundOrderId"] = refundOrderId

	channelId := y.dat.BaseInfo.Channel
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)
	client := reqv3.C()

	resp, httpErr := client.R().SetBody(reqMap).SetSuccessResult(&refundOrder).SetHeaders(reqHeader).Post(url)
	log.Log().Info("yjf GetRefundOrder", zap.Any("info ", resp.Body))

	//if refundOrder.Code == 200 {
	//	if refundOrder.Data.RefundOrdersInfo.RefundStatus == 18 || refundOrder.Data.RefundOrdersInfo.RefundStatus == 19 {
	//
	//
	//
	//
	//	}
	//
	//	//if refundOrder.Data.RefundOrdersInfo.RefundStatus == 2 { //售后完成
	//	//
	//	//}
	//
	//}

	if httpErr != nil {
		log.Log().Info("yjf GetRefundOrder httpErr", zap.Any("err", httpErr))

	}
	return
}
func (y *Yjf) GetRefundAddress(refundOrderId string) (err error, refundAddress model6.RefundAddress) {
	url := y.Http + "/refund/getRefundAddress"
	var reqMap = make(map[string]interface{})
	var reqHeader = map[string]string{}
	reqMap["refundOrderId"] = refundOrderId

	channelId := y.dat.BaseInfo.Channel
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)
	client := reqv3.C()

	resp, httpErr := client.R().SetBody(reqMap).SetSuccessResult(&refundAddress).SetHeaders(reqHeader).Post(url)
	log.Log().Info("yjf 获取退货地址", zap.Any("info ", resp.Body))

	if httpErr != nil {
		log.Log().Info("yjf 获取退货地址 httpErr", zap.Any("err", httpErr))

	}

	return
}

func (y *Yjf) AfsApplyRefund(orderId, subOrderId, thirdRefundOrderId, reason string, afterSaleID uint) (err error) {

	url := y.Http + "/refund/refundOrder"
	var reqMap = make(map[string]interface{})
	var postMap = make(map[string]interface{})
	var reqHeader = map[string]string{}
	//var productList []interface{}
	reqMap["orderId"] = orderId
	reqMap["subOrderId"] = subOrderId
	reqMap["thirdRefundOrderId"] = thirdRefundOrderId
	if reason != "" {
		reqMap["reason"] = UnicodeText(reason)
	}

	postMap["orderId"] = orderId
	postMap["subOrderId"] = subOrderId
	postMap["thirdRefundOrderId"] = thirdRefundOrderId
	if reason != "" {
		postMap["reason"] = reason
	}

	channelId := y.dat.BaseInfo.Channel
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)
	client := reqv3.C()

	var refundData model6.RefundData
	resp, httpErr := client.R().SetBody(postMap).SetSuccessResult(&refundData).SetHeaders(reqHeader).Post(url)
	log.Log().Info("yjf 退货", zap.Any("info ", resp.Body))

	if httpErr != nil {
		log.Log().Info("yjf 退货 httpErr", zap.Any("err", httpErr))

	}

	if refundData.Code != 200 {
		err = errors.New("退款申请失败:" + refundData.Message)
		return
	}
	var afterSales model5.AfterSalesMigration
	afterSales.GatherSupplyAfterSaleSN = refundData.Data.RefundOrdersInfo.RefundOrderId
	afterSales.Source = common2.YIJIFEN_SOURCE
	err = source.DB().Where("id=?", afterSaleID).Updates(&afterSales).Error
	if err != nil {
		log.Log().Error("yjf AfsApply Updates err", zap.Any("", err))
		return
	}
	return

}

func GetExpressInfo(localExpressName string) (expressNo, expressName string) {

	for _, item := range model6.KDArr {

		if strings.ContainsAny(item.Label, localExpressName) || strings.ContainsAny(localExpressName, item.Label) {
			expressNo = item.Value
			expressName = item.Label
			return
		}

	}

	return
}

func (y *Yjf) UpdateRefundOrderExpress(refundOrderId, localExpressName, expressCode string, jd bool) (err error) {

	url := y.Http + "/refund/updateRefundOrderExpress"
	var reqMap = make(map[string]interface{})
	var postMap = make(map[string]interface{})
	var reqHeader = map[string]string{}
	expressNo, expressName := GetExpressInfo(localExpressName)

	reqMap["refundOrderId"] = refundOrderId
	reqMap["express"] = expressNo       //编码
	reqMap["expressCode"] = expressCode //单号

	if jd { //京东商品传物流公司名称

		reqMap["expressCompany"] = UnicodeText(expressName) //物流公司名称
		postMap["expressCompany"] = expressName

	}

	postMap["refundOrderId"] = refundOrderId
	postMap["express"] = expressNo
	postMap["expressCode"] = expressCode

	channelId := y.dat.BaseInfo.Channel
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)
	client := reqv3.C()

	var refundOrderExpress model6.RefundOrderExpress
	resp, httpErr := client.R().SetBody(postMap).SetSuccessResult(&refundOrderExpress).SetHeaders(reqHeader).Post(url)
	log.Log().Info("yjf UpdateRefundOrderExpress", zap.Any("info ", resp.Body))

	if httpErr != nil {
		log.Log().Info("yjf UpdateRefundOrderExpress httpErr", zap.Any("err", httpErr))

	}

	if refundOrderExpress.Code != 200 {
		err = errors.New("提交物流信息失败:" + refundOrderExpress.Message)
		return
	}
	//var afterSales model5.AfterSalesMigration
	//afterSales.GatherSupplyAfterSaleSN = refundData.Data.RefundOrdersInfo.RefundOrderId
	//afterSales.Source = common2.YIJIFEN_SOURCE
	//err = source.DB().Where("id=?", afterSaleID).Updates(&afterSales).Error
	//if err != nil {
	//	log.Log().Error("yjf AfsApply Updates err", zap.Any("", err))
	//	return
	//}
	return

}

func (y *Yjf) DeliverGoods(orderSN string) (err error) {

	url := y.Http + "/scce/ctc/openApi/reseller/order/getOrderDetailList"
	reqData := make(map[string]string)
	reqData["thirdOrderSn"] = orderSN
	requestParamJsonData, _ := json.Marshal(reqData)
	var headMap = make(map[string]string)
	headMap["appKey"] = y.dat.BaseInfo.AppKey
	now := time.Now()
	nanoseconds := now.UnixNano()
	milliseconds := nanoseconds / 1e6
	strTime := strconv.Itoa(int(milliseconds))
	headMap["timeStamp"] = strTime
	rand.Seed(time.Now().UnixNano())
	randomNumber := rand.Int()

	randStr := strconv.Itoa(randomNumber)
	headMap["randomNumber"] = randStr
	headMap["version"] = "1.0"
	log.Log().Info("yyt 订单详情请求数据", zap.Any("", reqData))
	signMap := MergeMaps(reqData, headMap)
	signSort := Sign(signMap, y.dat.BaseInfo.AppSecret)
	headMap["sign"] = signSort
	headMap["Content-Type"] = "application/json"
	log.Log().Info("yyt订单详情请求头", zap.Any("", headMap))

	err, resData := utils.PostDataJson(url, requestParamJsonData, headMap)

	var deliverGoodsInfo model3.OrderDetail

	err = json.Unmarshal(resData, &deliverGoodsInfo)
	if err != nil {
		log.Log().Info("yyt订单详情 Unmarshal err", zap.Any("", err))
		return err
	}

	for _, orderItem := range deliverGoodsInfo.Data {

		for _, item := range orderItem.DeliverList {
			if item.ExpressName != "" || item.DeliverySn != "" {

				for _, goods := range item.OrderExpressItemVOList {
					var orderItemIds []orderRequest2.OrderItemSendInfo
					var orderRequest v1.HandleOrderRequest
					var orderItems orderModel.OrderItem
					err = source.DB().Where("original_sku_id=? and gather_supply_sn=?", goods.GoodsSpecId, item.OrderSn).First(&orderItems).Error
					if err != nil {
						continue
					}
					orderItemIds = append(orderItemIds, orderRequest2.OrderItemSendInfo{
						ID:  orderItems.ID,
						Num: orderItems.Qty,
					})

					orderRequest.OrderID = orderItems.OrderID
					orderRequest.ExpressNo = strings.TrimSpace(item.DeliverySn) //过滤掉左右空格
					orderRequest.OrderItemIDs = orderItemIds
					err, orderRequest.CompanyCode = ExpressList(item.ExpressName)
					log.Log().Info("yyt 发货信息", zap.Any("info", orderRequest))
					err = ExpressSent(orderRequest)
					if err != nil {
						log.Log().Error("yyt ExpressSent", zap.Any("info", err))
					}
				}

			}

		}

	}

	return

}

func (y *Yjf) PreOrder(request request.RequestConfirmOrder) (err error, PreOrderData model6.PreOrderData) {

	var url = y.dat.BaseInfo.ApiUrl + "/order/preOrder"
	var reqMap = make(map[string]interface{})
	var postMap = make(map[string]interface{})
	var reqHeader = map[string]string{}

	var productList []interface{}
	for _, item := range request.LocalSkus {
		var sku model4.Sku
		err = source.DB().Where("id=?", item.Sku.Sku).Preload("Product").First(&sku).Error

		var products = make(map[string]interface{})
		products["id"] = sku.Product.SourceGoodsID
		products["buyNum"] = item.Number
		if sku.Product.SingleOption != 1 {
			products["skuId"] = sku.OriginalSkuID
			products["skuName"] = UnicodeText(sku.Title)
		}
		productList = append(productList, products)
	}

	reqMap["products"] = productList
	reqMap["contacts"] = UnicodeText(request.Address.Consignee)
	reqMap["contactsPhone"] = request.Address.Phone
	postMap["products"] = productList
	postMap["contacts"] = request.Address.Consignee
	postMap["contactsPhone"] = request.Address.Phone

	var shippingAddress address.ShippingAddress
	err = source.DB().Where("province=? and city=? and county=?", request.Address.Province, request.Address.City, request.Address.Area).First(&shippingAddress).Error

	if err != nil {
		log.Log().Info("yjf GetFreight shippingAddress err", zap.Any("err", err))
		return
	}
	pStr := strconv.Itoa(shippingAddress.ProvinceId)
	cStr := strconv.Itoa(shippingAddress.CityId)
	aStr := strconv.Itoa(shippingAddress.CountyId)

	reqMap["provinceCode"] = padLeft(pStr, 6)
	reqMap["cityCode"] = padLeft(cStr, 6)
	reqMap["districtCode"] = padLeft(aStr, 6)

	postMap["provinceCode"] = padLeft(pStr, 6)
	postMap["cityCode"] = padLeft(cStr, 6)
	postMap["districtCode"] = padLeft(aStr, 6)

	//if 1 == 2 {  //天猫商品
	//tStr := strconv.Itoa(shippingAddress.TownId)
	//	reqMap["townCode"] = padLeft(tStr, 6)
	//
	//	reqMap["townLabel"] = order.UnicodeText(request.Address.Street)
	//
	//}

	reqMap["address"] = UnicodeText(request.Address.Description)
	reqMap["provinceLabel"] = UnicodeText(request.Address.Province)
	reqMap["cityLabel"] = UnicodeText(request.Address.City)
	reqMap["districtLabel"] = UnicodeText(request.Address.Area)
	reqMap["thirdOrderId"] = request.OrderSn.OrderSn

	postMap["address"] = request.Address.Description
	postMap["provinceLabel"] = request.Address.Province
	postMap["cityLabel"] = request.Address.City
	postMap["districtLabel"] = request.Address.Area
	postMap["thirdOrderId"] = request.OrderSn.OrderSn

	channelId := y.dat.BaseInfo.Channel

	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)

	client := reqv3.C()

	resp, httpErr := client.R().SetSuccessResult(&PreOrderData).SetBody(postMap).SetHeaders(reqHeader).Post(url)

	if httpErr != nil {
		log.Log().Info("yjf 预下单httperr", zap.Any("err", httpErr))

	}
	if PreOrderData.Code == 500 {
		err = errors.New(PreOrderData.Msg)
		log.Log().Info("yjf 预下单 err", zap.Any("err", resp.Body))
		return
	}
	if resp.IsSuccessState() {
		log.Log().Info("yjf 预下单返回数据", zap.Any("info", resp.Body))
	}

	if resp.IsErrorState() {
		err = resp.Err
		return
	}
	return

}

// 确认下单
func (y *Yjf) ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	log.Log().Info("Yjf 供应链商品准备下单", zap.Any("info", request))
	var updateOrder orderModel.OrderModel
	preErr, orderInfo := y.PreOrder(request)
	if preErr != nil {
		log.Log().Info("yjf 预下单失败", zap.Any("info", preErr))
		updateOrder.GatherSupplyMsg = preErr.Error()
		err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&updateOrder).Error

		return
	}

	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).First(&updateOrder).Error

	if err != nil {
		log.Log().Error("Yjf ConfirmOrder err", zap.Any("err", err))
		return
	}
	var url = y.dat.BaseInfo.ApiUrl + "/order/createOrder"
	var reqMap = make(map[string]interface{})
	var reqHeader = map[string]string{}
	reqMap["orderId"] = orderInfo.Data.OrderId
	channelId := y.dat.BaseInfo.Channel
	times := strconv.Itoa(int(time.Now().Unix() * 1000))
	key := channelId + times + y.dat.BaseInfo.AppSecret
	reqHeader["channelId"] = channelId
	reqHeader["timestamp"] = times
	reqHeader["sign"] = yjfGoods.Sign(reqMap, key)

	var CreateOrderData model6.CreateOrderData
	client := reqv3.C()
	resp, err := client.R().SetSuccessResult(&CreateOrderData).SetBody(reqMap).SetHeaders(reqHeader).Post(url)

	if err != nil {
		fmt.Println(err)
	}
	if resp.IsSuccessState() {
		fmt.Println(resp.Status)
	}

	if resp.IsErrorState() {
		fmt.Println(resp.Body)
	}

	//var orderData model3.OrderData

	updateOrder.GatherSupplyType = common2.SUPPLY_YJF
	updateOrder.GatherSupplySN = orderInfo.Data.OrderId
	if orderInfo.Status == 1 {
		for _, item := range orderInfo.Data.OrdersInfo {

			//ids := strings.Split(item.SkuIds, ",")
			var OrderItem orderModel.OrderItem
			OrderItem.GatherSupplySN = item.SubOrderId

			var product model4.Product

			err = source.DB().Where("source_goods_id=?", item.ProductId).First(&product).Error

			db := source.DB().Model(&orderModel.OrderItem{})
			db.Where("order_id=?", updateOrder.ID)
			if item.SkuId > 0 {
				db.Where("original_sku_id=?", item.SkuId)
			}
			db.Where("product_id=?", product.ID)
			db.Updates(&OrderItem)
			//source.DB().Where("original_sku_id in (?) and order_id=?", ids, updateOrder.ID).Updates(&OrderItem)
			if err != nil {
				log.Log().Error("err", zap.Any("err", err))
			}
		}
		//updateOrder.GatherSupplySN = updateOrder.GatherSupplySN[:len(updateOrder.GatherSupplySN)-1]

	} else {
		errData, _ := json.Marshal(&orderInfo)
		updateOrder.GatherSupplyMsg = string(errData)
	}

	err = source.DB().Where("order_sn=?", request.OrderSn.OrderSn).Updates(&updateOrder).Error
	if err != nil {
		log.Log().Info("yyt 保存三方单号失败", zap.Any("info", err))
	}

	return

}

func (y *Yjf) ThirdOrder(orderSn string) (err error, orderDetail ThirdOrderRes) {

	url := string(y.Http + "/api/order/thirdOrder.php")
	var resData []byte
	headerData := make(map[string]string)
	timeUnix := strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	reqData := url2.Values{}
	reqData.Add("wid", y.dat.BaseInfo.AppKey)
	reqData.Add("token", strings.ToUpper(utils.MD5V([]byte(y.dat.BaseInfo.AppKey+y.dat.BaseInfo.AppSecret+timeUnix))))
	reqData.Add("timestamp", timeUnix)
	reqData.Add("thirdOrder", orderSn)
	log.Log().Error("yzh ThirdOrder请求参数", zap.Any("info", reqData))
	err, resData = utils.PostForm(url, reqData, headerData)

	if err != nil {
		log.Log().Error("yzh ThirdOrder请求失败", zap.Any("info", err))
		return
	}

	err = json.Unmarshal(resData, &orderDetail)
	if err != nil {
		log.Log().Error("yzh 查询订单详情解析失败", zap.Any("info", err))
		return
	}
	return
}

type ThirdOrderRes struct {
	RESPONSESTATUS string `json:"RESPONSE_STATUS"`
	RESULTDATA     string `json:"RESULT_DATA"`
}

func (y *Yjf) OrderDetail(orderSn string) (err error, ResYzhOrderDetail interface{}) {
	url := string(y.Http + "open/api/order/queryOrderDetail")
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["tparOrderCode"] = orderSn
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	//var ResYzhOrderDetail model5.ResYzhOrderDetail
	json.Unmarshal(resData, &ResYzhOrderDetail)

	if err != nil {
		return
	}

	fmt.Println(string(resData))

	return
}

// 物流查询
func (y *Yjf) ExpressQuery(request request.RequestExpress) (err error, data interface{}) {
	url := string(y.Http + "/open/api/order/queryOrderLogistics")
	var resData []byte
	headerData := make(map[string]interface{})
	headerData["accessToken"] = y.dat.BaseInfo.Token
	headerData["tparOrderCode"] = request.OrderSn
	headerData["parentOrderCode"] = request.GatherSupplySn
	reqData, _ := json.Marshal(headerData)
	resData = utils.HttpPostJson(reqData, url)
	//var ResYzhOrderDetail model5.ShipmentList
	data = string(resData)

	return

}

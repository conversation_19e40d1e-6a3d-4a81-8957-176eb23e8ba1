package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"kunsheng-supply/component/goods"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// SyncProducts 手动同步商品
func SyncProducts(c *gin.Context) {
	var req yzRequest.GetById
	err := c.ShouldBindJSON(&req)
	if err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	}

	// 初始化坤盛供应链
	var kunsheng = new(goods.KunSheng)
	err = kunsheng.InitSetting(req.Id)
	if err != nil {
		log.Log().Error("初始化坤盛供应链失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	// 异步执行同步
	go func() {
		err = kunsheng.SynchronizeProductsToLocal()
		if err != nil {
			log.Log().Error("同步坤盛供应链商品失败", zap.Any("err", err))
		}
	}()

	yzResponse.OkWithMessage("后台同步中，预计10-20分钟同步完成", c)
}

package cron

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	v1 "order/api/v1"
	omodel "order/model"
	orderRequest2 "order/request"
	"public-supply/common"
	"public-supply/model"
	pubmodel "public-supply/model"
	setting2 "public-supply/setting"
	express2 "shipping/express"
	"strconv"
	"strings"
	"tianma-supply/component/order"
	"yz-go/component/log"
	"yz-go/cron"

	"yz-go/source"
)

func PushTianmaOrderSendGoodsHandle() {
	log.Log().Info("wps PushWpsOrderSendGoodsHandle info ", zap.Any("err", "info"))

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.TIANMA_SOURCE).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreateCronTask(v.ID)

	}
}

func CreateCronTask(taskID uint) {
	log.Log().Info("wps CreateCronTask info ", zap.Any("err", taskID))

	var dat pubmodel.SupplySetting
	err, setting := setting2.GetSetting("gatherSupply" + strconv.Itoa(int(taskID)))
	if err != nil {
		fmt.Println("获取供应链key设置失败")
		return
	}

	err = json.Unmarshal([]byte(setting.Value), &dat)
	if err != nil {

		return
	}

	var cronStr string
	if dat.UpdateInfo.Cron != "" {
		cronStr = dat.UpdateInfo.Cron
	} else {
		cronStr = "0 0 */3 * * ?"
	}
	log.Log().Info("tianma  cronStr info ", zap.Any("err", cronStr))

	cron.PushTask(cron.Task{
		Key:  "tianmaOrderSendGoods" + strconv.Itoa(int(taskID)),
		Name: "tianmaOrderSendGoods发货更新" + strconv.Itoa(int(taskID)),
		Spec: cronStr,
		Handle: func(task cron.Task) {
			TaskRun(int(taskID))

		},
		Status: cron.ENABLED,
	})

}

var taskMap = make(map[int]bool)

func TaskRun(taskID int) {
	if taskMap[taskID] == false {
		taskMap[taskID] = true
		SendGoods(uint(taskID))
		taskMap[taskID] = false
	}

}

func SendGoods(gatherSupplyID uint) (err error) {
	log.Log().Info("tianma SendGoods info ", zap.Any("err", gatherSupplyID))

	var tianma order.TianMa
	err = tianma.InitSetting(gatherSupplyID)
	if err != nil {
		return
	}
	var deliverOrder []omodel.Order
	err = source.DB().Preload("OrderItems").Where("status=? and gather_supply_id=?", 1, gatherSupplyID).Find(&deliverOrder).Error

	if err != nil {
		return
	}

	for _, od := range deliverOrder {
		log.Log().Info("tianma tianma 查询发货状态 ", zap.Any("info", od.OrderSN))

		orderSN := strconv.Itoa(int(od.OrderSN))
		orderInfoErr, orderList := tianma.GetOrderInfo(orderSN)
		if orderInfoErr != nil {
			log.Log().Error("tianma tianma.GetOrderInfo(orderSN) err ", zap.Any("err", err))
			continue
		}

		if len(orderList.Rows) > 0 {
			if orderList.Rows[0].DeliveryNo == "" && orderList.Rows[0].Delivery == "" {
				continue
			}
			log.Log().Info("tianma 开始发货 ", zap.Any("info", od.OrderSN))

			var orderItemIds []orderRequest2.OrderItemSendInfo
			var orderRequest v1.HandleOrderRequest
			for _, odItem := range od.OrderItems {
				orderItemIds = append(orderItemIds, orderRequest2.OrderItemSendInfo{
					ID:  odItem.ID,
					Num: uint(odItem.Qty),
				})
			}

			orderRequest.OrderID = od.ID
			orderRequest.ExpressNo = strings.TrimSpace(orderList.Rows[0].DeliveryNo) //过滤掉左右空格
			orderRequest.OrderItemIDs = orderItemIds
			err, orderRequest.CompanyCode = ExpressList(orderList.Rows[0].Delivery)
			log.Log().Info("tianma 发货信息", zap.Any("info", orderRequest))
			err = ExpressSent(orderRequest)
			if err != nil {
				log.Log().Error("wps ExpressSent", zap.Any("info", err))
			}

		}

	}

	return

}
func ExpressList(name string) (err error, code string) {

	for _, item := range express2.GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

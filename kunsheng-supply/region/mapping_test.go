package region

import (
	"testing"
)

func TestLoadRegionsFromXlsx(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "case1",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := LoadRegionsFromXlsx(); (err != nil) != tt.wantErr {
				t.Errorf("LoadRegionsFromXlsx() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLoadRegionsFromXlsx1(t *testing.T) {
	tests := []struct {
		name    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:    "case1",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := LoadRegionsFromXlsx(); (err != nil) != tt.wantErr {
				t.<PERSON><PERSON><PERSON>("LoadRegionsFromXlsx() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

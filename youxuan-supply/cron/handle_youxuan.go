package cron

import (
	"context"
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"strconv"
	"time"
	"youxuan-supply/component/goods"
	"youxuan-supply/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
	"yz-go/utils"
)

func PushGoodsYouxuanHandle() {
	task := cron.Task{
		Key:  "handleyouxuan",
		Name: "处理优选商品",
		//Spec: "0 47 */1 * * *",
		Spec: "10 */1 * * * *",
		Handle: func(task cron.Task) {
			GoodsYouxuanCronV2()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

var IsRun *int
var one = 1

func GoodsYouxuanCronV2() {
	if IsRun != nil {
		fmt.Println("已经执行中")
		return
	}
	result, err := source.Redis().LRange(context.Background(), "youxuanGoodsHandle", 0, 59).Result()
	if err != nil {
		IsRun = nil
		return
	}
	var rCount int
	rCount = len(result)
	if rCount == 0 {
		IsRun = nil
		return
	}
	fmt.Println("开始执行")
	IsRun = &one
	err = source.Redis().LTrim(context.Background(), "youxuanGoodsHandle", int64(rCount), -1).Err()
	if err != nil {
		IsRun = nil
		return
	}
	type youxuanData struct {
		GatherSupplyID uint `json:"gather_supply_id"`
		SpuId          int  `json:"spu_id"`
	}
	var yx = goods.Yx{}
	var detailData []model.YouxuanGoodsDetail
	for _, row := range result {
		var rowData youxuanData
		err = json.Unmarshal([]byte(row), &rowData)
		if err != nil {
			return
		}
		err = yx.InitSetting(rowData.GatherSupplyID)
		var requestData = make(map[string]interface{})
		requestData["spu_id"] = rowData.SpuId
		var responseByte []byte
		err, responseByte = yx.RequestYouxuanComponent(requestData, "/products/getSpuIdByDetails", "")
		time.Sleep(300 * time.Millisecond)
		if err != nil {
			IsRun = nil
			return
		}

		var response model.YouxuanGoodsDetailResponseConfirm
		err = json.Unmarshal(responseByte, &response)
		if err != nil {
			IsRun = nil
			return
		}
		////response.Data.GatherSupplyID = data.GatherSupplyID
		//response.Data.DistributorSupplierName = data.DistributorSupplierName
		//response.Data.DistributorCoId = data.DistributorCoId
		//if len(response.Data.ItemSkuList) > 0 {
		//	response.Data.SalePrice = response.Data.ItemSkuList[0].SalePrice
		//	response.Data.SupplyPrice = response.Data.ItemSkuList[0].SupplyPrice
		//	response.Data.CostPrice = response.Data.ItemSkuList[0].CostPrice
		//}
		if response.Code == 200 {
			var responseData model.YouxuanGoodsDetailResponse
			err = json.Unmarshal(responseByte, &responseData)
			if err != nil {
				IsRun = nil
				return
			}
			var jsonData []byte
			jsonData, err = json.Marshal(responseData.Data)
			responseData.Data.MD5 = utils.MD5V(jsonData)
			var localData model.YouxuanGoodsDetail
			localData.YouxuanGoodsDetailBase = responseData.Data.YouxuanGoodsDetailBase
			localData.ClassId, err = strconv.Atoi(responseData.Data.ClassId)
			if err != nil {
				return
			}
			detailData = append(detailData, localData)
		} else {
			log.Log().Error("优选供应链获取商品失败", zap.Any("err", response.Message), zap.Any("id", rowData.SpuId))
		}

		//err = source.DB().Save(&response.Data).Error
		//if err != nil {
		//	IsRun = nil
		//	return
		//}
		//time.Sleep(1 * 50 * time.Millisecond)

	}
	var oldData []model.YouxuanGoodsDetail
	var itemSpuIds []int
	for _, detail := range detailData {
		itemSpuIds = append(itemSpuIds, detail.SpuId)
	}
	err = source.DB().Where("spu_id in ?", itemSpuIds).Find(&oldData).Error
	if err != nil {
		IsRun = nil
		return
	}
	var updateData []model.YouxuanGoodsDetail
	var insertData []model.YouxuanGoodsDetail
	for _, newData := range detailData {
		var check int
		for _, old := range oldData {
			if old.SpuId == newData.SpuId {
				newData.ID = old.ID
				newData.CreatedAt = old.CreatedAt
				updateData = append(updateData, newData)
				check++
			}
		}
		if check == 0 {
			insertData = append(insertData, newData)
		}
	}
	err = source.DB().CreateInBatches(&insertData, 100).Error
	if err != nil {
		IsRun = nil
		return
	}

	for _, update := range updateData {
		err = source.DB().Updates(&update).Error
		if err != nil {
			IsRun = nil
			return
		}
	}
	IsRun = nil
	return
}

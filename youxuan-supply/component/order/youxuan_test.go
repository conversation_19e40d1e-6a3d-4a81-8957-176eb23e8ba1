package order

import (
	"reflect"
	"region/mapping"
	"testing"
)

func TestGetYouxuanRegionIDsFromRequest(t *testing.T) {
	type args struct {
		province string
		city     string
		area     string
		street   string
	}
	tests := []struct {
		name string
		args args
		want mapping.Region
	}{
		// TODO: Add test cases.
		{
			name: "TestGetYouxuanRegionIDsFromRequest",
			args: args{
				province: "上海市",
				city:     "上海市",
				area:     "杨浦区",
				street:   "朝天宫街道",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetYouxuanRegionIDsFromRequest(tt.args.province, tt.args.city, tt.args.area); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("GetYouxuanRegionIDsFromRequest() = %v, want %v", got, tt.want)
			}
		})
	}
}

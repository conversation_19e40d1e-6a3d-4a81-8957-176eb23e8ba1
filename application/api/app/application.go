package app

import (
	appm "application/model"
	"application/request"
	"application/response"
	service2 "application/service"
	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/frame/g"
	"go.uber.org/zap"
	request2 "order/request"
	service3 "order/service"
	"payment/model"
	"payment/service"
	model2 "purchase-account/model"
	service4 "purchase-account/service"
	v1 "user/api/f/v1"
	"yz-go/component/log"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

func SetPaySort(c *gin.Context) {

	var applicationPaySort request.ApplicationPaySortSet
	err := c.ShouldBindJSON(&applicationPaySort)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	if err := service.SetPaySort(appID, applicationPaySort.ApplicationPaySorts); err != nil {
		log.Log().Error("设置失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("设置失败", c)
		return
	} else {
		yzResponse.OkWithMessage("设置成功", c)
	}
}

func GetPaySort(c *gin.Context) {
	var applicationPaySort []model.ApplicationPaySort
	var err error
	appID := utils.GetAppID(c)
	err, applicationPaySort = service.GetPaySort(appID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(gin.H{"list": applicationPaySort}, c)
}

func GetSupplySource(c *gin.Context) {
	appID := utils.GetAppID(c)
	data := service2.GetSupplySource(appID)
	yzResponse.OkWithData(data, c)
}

/*
*

	获取采购端支持的来源（胜天半子）和供应链
*/
func GetGatherSupplyAndSource(c *gin.Context) {
	appID := utils.GetAppID(c)
	data := service2.GetGatherSupplyAndSource(appID)
	yzResponse.OkWithData(data, c)
}

func GetGatherSupplyAndSourceFront(c *gin.Context) {
	userID := v1.GetUserID(c)
	var application appm.Application
	err := source.DB().Where("member_id = ?", userID).First(&application).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	data := service2.GetGatherSupplyAndSource(application.ID)
	yzResponse.OkWithData(data, c)
}
func GetMyGatherSupplyAndSourceFront(c *gin.Context) {

	data := service2.GetMyGatherSupplyAndSource(0)
	yzResponse.OkWithData(data, c)
}
func ExportCloudOrderList(c *gin.Context) {
	var pageInfo request2.OrderAdminSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, application := service2.GetApplication(utils.GetAppID(c))
	if application.SupplierID == 0 {
		log.Log().Error("云仓尚未绑定供应商", zap.Any("err", err))
		yzResponse.FailWithMessage("云仓尚未绑定供应商", c)
		return
	}
	pageInfo.SupplierID = &application.SupplierID
	if err, link := service3.ExportOrder(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

// @Tags 获取用户站内余额
// @Summary 获取用户站内余额
// @accept application/json
// @finance application/json
// @Param data body  true "获取用户站内余额"
// @Router /api/finance/getUserGoinBalance [post]
func GetUserGoinBalance(c *gin.Context) {

	appID := utils.GetAppID(c)

	var userBalance response.UserBalance

	err, application := service2.GetApplication(appID)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("获取失败", c)
		return
	}

	if application.MemberId == 0 {
		yzResponse.FailWithMessage("采购端未绑定用户", c)
		return
	}

	var balance model.Balance
	err = source.DB().Where("`uid` = ?", application.MemberId).Where("`type` = ?", 2).Find(&balance).Error

	if err == nil {
		userBalance.GoinBalance = balance.PurchasingBalance
	} else {
		userBalance.GoinBalance = 0
	}
	yzResponse.OkWithData(userBalance, c)

}

// @Tags 获取用户采购端的采购账户列表
// @Summary 获取用户采购端的采购账户列表
// @accept application/json
// @finance application/json
// @Param data body  true "获取用户站内余额"
// @Router /getUserPurchasingAccount [post]
func GetUserPurchasingAccount(c *gin.Context) {

	appID := utils.GetAppID(c)
	var application appm.Application
	err := source.DB().Where("id = ?", appID).First(&application).Error
	if err != nil {
		return
	}
	var param model2.SearchAccountUserBalance
	param.Uid = application.MemberId
	if dataErr, total, list, totalSum := service4.GetUserAccount(param); dataErr != nil {
		yzResponse.FailWithMessage(dataErr.Error(), c)
		return
	} else {
		var accountList interface{}

		err, accountList = service4.GetAccountList()
		yzResponse.OkWithDetailed(g.Map{
			"list":        list,
			"total":       total,
			"page":        param.Page,
			"pageSize":    param.PageSize,
			"accountType": accountList,
			"typeList":    service4.GetTypeList(),
			"totalSum":    totalSum,
		}, "获取成功", c)
	}

}

package v1

import (
	"application/model"
	"application/request"
	"application/service"
	v1 "gin-vue-admin/admin/api/v1"
	"strconv"
	"strings"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	service2 "yz-go/service"
	"yz-go/source"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// @Tags ApplicationLevel
// @Summary 创建ApplicationLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationLevel true "创建ApplicationLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /applicationLevel/createApplicationLevel [post]
func CreateApplicationLevel(c *gin.Context) {
	var applicationLevel model.ApplicationLevel
	err := c.ShouldBindJSON(&applicationLevel)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.CreateApplicationLevel(applicationLevel); err != nil {
		log.Log().Error("创建失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("创建失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "新增采购端等级'"+applicationLevel.LevelName+"'")
		yzResponse.OkWithMessage("创建成功", c)
	}
}

// @Tags ApplicationLevel
// @Summary 删除ApplicationLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationLevel true "删除ApplicationLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /applicationLevel/deleteApplicationLevel [delete]
func DeleteApplicationLevel(c *gin.Context) {
	var applicationLevel model.ApplicationLevel
	err := c.ShouldBindJSON(&applicationLevel)
	err = source.DB().First(&applicationLevel, applicationLevel.ID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.DeleteApplicationLevel(applicationLevel); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "删除采购端等级'"+applicationLevel.LevelName+"'")
		yzResponse.OkWithMessage("删除成功", c)
	}
}

// @Tags ApplicationLevel
// @Summary 批量删除ApplicationLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.IdsReq true "批量删除ApplicationLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"批量删除成功"}"
// @Router /applicationLevel/deleteApplicationLevelByIds [delete]
func DeleteApplicationLevelByIds(c *gin.Context) {
	var err error
	var IDS yzRequest.IdsReq
	err = c.ShouldBindJSON(&IDS)
	if err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	}
	if err = service.DeleteApplicationLevelByIds(IDS); err != nil {
		log.Log().Error("批量删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("批量删除失败", c)
		return
	} else {
		var idsString []string
		for _, v := range IDS.Ids {
			idsString = append(idsString, strconv.Itoa(int(v)))
		}
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "批量删除采购端等级'"+strings.Join(idsString, ",")+"'")
		yzResponse.OkWithMessage("批量删除成功", c)
	}
}

// @Tags ApplicationLevel
// @Summary 更新ApplicationLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationLevel true "更新ApplicationLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /applicationLevel/updateApplicationLevel [put]
func UpdateApplicationLevel(c *gin.Context) {
	var applicationLevel model.ApplicationLevel
	err := c.ShouldBindJSON(&applicationLevel)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err := service.UpdateApplicationLevel(applicationLevel); err != nil {
		log.Log().Error("更新失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("更新失败", c)
		return
	} else {
		service2.CreateOperationRecord(v1.GetUserID(c), 6, c.ClientIP(), "更新采购端等级'"+applicationLevel.LevelName+"'")
		yzResponse.OkWithMessage("更新成功", c)
	}
}

// @Tags ApplicationLevel
// @Summary 用id查询ApplicationLevel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplicationLevel true "用id查询ApplicationLevel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /applicationLevel/findApplicationLevel [get]
func FindApplicationLevel(c *gin.Context) {
	var applicationLevel model.ApplicationLevel
	_ = c.ShouldBindQuery(&applicationLevel)
	if err, reapplicationLevel := service.GetApplicationLevel(applicationLevel.ID); err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"reapplicationLevel": reapplicationLevel}, c)
	}
}

// @Tags ApplicationLevel
// @Summary 分页获取ApplicationLevel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ApplicationLevelSearch true "分页获取ApplicationLevel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /applicationLevel/getApplicationLevelList [get]
func GetApplicationLevelList(c *gin.Context) {
	var pageInfo request.ApplicationLevelSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetApplicationLevelInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

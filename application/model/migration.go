package model

import (
	_ "embed"
	"encoding/json"
	"errors"
	"gin-vue-admin/admin/model"
	"gorm.io/gorm"
	"yz-go/source"
)

//go:embed menu.json
var menu string

func Migrate() (err error) {
	err = source.DB().AutoMigrate(
		ApplicationModel{},
		ApplicationLevel{},
		ApplicationApplyRecordModel{},
		PushMessageErr{},
		ApplicationSource{},
		AppOrderExportRecordModel{},
		ApplicationPetSupplierModel{},
		ApplicationGroup{},
		ApplicationShop{},
		OrderConnectionRecord{},
		MessagePool{},
		MessagePoolBackup{},
		MessagePoolWhiteList{},
		StorageStrategy{},
		CategoryStrategy{},
		ApplicationCollection{},
		ApplicationExportRecord{},
	)
	if err != nil {
		return
	}
	// 菜单,权限
	menus := []model.SysMenu{}

	menuJson := menu
	json.Unmarshal([]byte(menuJson), &menus)
	model.GVA_MENUS = append(model.GVA_MENUS, menus...)

	if source.DB().Migrator().HasTable(&ApplicationSetting{}) {

		var sysSetting ApplicationSetting
		err = source.DB().Where("`key` = 'application_setting'").First(&sysSetting).Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = nil
			sysSetting.Key = "application_setting"
			sysSetting.Value.IsOpenApply = 1
			sysSetting.Value.ApplyDesc = ""
			sysSetting.Value.IsOpenAgreement = 0
			sysSetting.Value.Agreement = ""
			sysSetting.Value.EditPrice = 0
			sysSetting.Value.ShowTechFee = 1
			sysSetting.Value.MultiPetSupplier = 1

			err = source.DB().Create(&sysSetting).Error
			if err != nil {
				return
			}
		}
	}
	if source.DB().Migrator().HasTable(&MessagePool{}) {
		if source.DB().Migrator().HasColumn(&MessagePool{}, "status") {
			err = source.DB().Unscoped().Delete(&MessagePool{}, "status = 1").Error
			if err != nil {
				return
			}
		}
	}
	return
}

type OrderConnectionRecord struct {
	source.Model
	OrderID     uint   `json:"order_id"`
	MessageType int8   `json:"message_type"`
	Status      int    `json:"status"`
	Content     string `json:"content"`
}

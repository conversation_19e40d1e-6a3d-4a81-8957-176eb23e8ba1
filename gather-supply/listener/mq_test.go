package listener

import (
	"testing"
	pmq "product/mq"
)

func TestPublishMessage(t *testing.T) {

	_ = pmq.PublishMessage(10848, pmq.Delete)

	//var jjjj=OrderMessage{
	//	MessageType: 1,
	//	OrderSN: "342342342345",
	//
	//}
	//_ = PublishExchange("order.payment.pay", jjjj)
}



type OrderMessage struct {
	MessageType int  `json:"message_type"`
	OrderID     uint `json:"order_id"`
	OrderSN     string `json:"order_sn"`
}
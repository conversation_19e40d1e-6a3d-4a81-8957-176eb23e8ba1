package callback

import (
	byn "byn-supply/component/callback"
	"category/model"
	dwd "dwd-supply/component/callback"
	fulu "fulu-supply/component/callback"
	hehe "hehe-supply/component/callback"
	pubmodel "public-supply/model"
	self "self-supply/component/callback"
	stbz "stbz-supply/component/callback"

	"sync"
)

var (
	category []model.Category
	mutex    sync.Mutex
)

//@author: [ccfish86](https://github.com/ccfish86)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@interface_name: OSS
//@description: OSS接口

type Callback interface {
	CallBackMessage(request pubmodel.RequestCallBackType) (err error)
}

//@author: [ccfish86](https://github.com/ccfish86)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: NewOss
//@description: OSS接口
//@description: OSS的实例化方法
//@return: OSS

func NewCallback(key string) Callback {
	switch key {
	case "stbz":
		return &stbz.Stbz{}
	case "self":
		return &self.Self{}
	case "byn":
		return &byn.Byn{}
	case "dwd":
		return &dwd.Dwd{}
	case "hehe":
		return &hehe.Hehe{}
	case "fulu":
		return &fulu.Fulu{}
	default:
		return &stbz.Stbz{}
	}
}

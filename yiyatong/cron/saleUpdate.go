package cron

import (
	"go.uber.org/zap"
	"gorm.io/gorm"
	pservice "product/service"
	"public-supply/common"
	"public-supply/model"
	"strconv"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushYiyatongProductSaleHandle() {

	log.Log().Info("cron  PushYiyatongProductSaleHandle")

	var gatherList []model.GatherSupply
	err := source.DB().Where("category_id = ?", common.YIYATONG_SOURCE).Where("deleted_at is null").Find(&gatherList).Error
	if err != nil {
		return
	}

	for _, v := range gatherList {

		CreatePushYiyatongProductSaleHandle(int(v.ID))

	}

}

func CreatePushYiyatongProductSaleHandle(taskID int) {

	InitSetting(taskID)

	var cronStr string

	cronStr = "0 0 */2 * * *"

	cron.PushTask(cron.Task{
		Key:  "CreatePushYiyatongProductSaleHandle" + strconv.Itoa(taskID),
		Name: "CreatePushYiyatongProductSaleHandle商品更新" + strconv.Itoa(taskID),
		Spec: cronStr, //"0/3 * * * * ?"
		Handle: func(task cron.Task) {
			YytCronSaleUpdate(uint(taskID))

		},
		Status: cron.ENABLED,
	})

}

func YytCronSaleUpdate(supplyId uint) (err error) { //定时更新商品下架
	log.Log().Info("cron  YytCronSaleUpdate", zap.Any("info", supplyId))

	err = yyt.InitSetting(supplyId)
	var product []pservice.ProductForUpdate

	db := source.DB().Model(pservice.ProductForUpdate{})

	result := db.Select("source_goods_id_string,id").Where("status_lock=0 and source =?    and gather_supply_id=?", common.YIYATONG_SOURCE, supplyId).Order("updated_at asc ").FindInBatches(&product, 500, func(tx *gorm.DB, batch int) error {
		for _, item := range product {
			SourceGoodsIDString, _ := strconv.Atoi(item.SourceGoodsIDString)

			var ids []string
			ids = append(ids, item.SourceGoodsIDString)

			GetSpuInfoErr, data := yyt.GetSpuInfo(ids)
			if GetSpuInfoErr != nil {
				log.Log().Info("cronGetSpuInfo下架yyt商品", zap.Any("err", GetSpuInfoErr))

				continue
			}

			if data.Success != true {
				log.Log().Info("cronGetSpuInfo下架yyt商品", zap.Any("err", data.Success))

				continue
			}

			if len(data.Data) > 0 {
				status, _ := strconv.Atoi(data.Data[0].Status)
				YytGoodsUnSale(int64(SourceGoodsIDString), int64(status)) //先下架本地选品库

				if data.Data[0].Status == "1" && item.IsDisplay == 1 { //下架

					log.Log().Info("cron下架yyt商品", zap.Any("id", item.ID))
					YytUndercarriageProduct(item.ID) //下架本地商品
				}

			}

		}

		return nil
	})

	if result.Error != nil {
		log.Log().Info("cron  YytCronSaleUpdate", zap.Any("err", result.Error))
	}

	return

}

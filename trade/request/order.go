package request

import (
	"fmt"
	"strings"
)

type Address struct {
	Consignee   string `json:"consignee"`
	Phone       string `json:"phone"`
	Province    string `json:"province"`
	City        string `json:"city"`
	Area        string `json:"area"`
	Street      string `json:"street"`
	ProvinceId  int    `json:"province_id"`
	CityId      int    `json:"city_id"`
	AreaId      int    `json:"area_id"`
	StreetId    int    `json:"street_id"`
	Description string `json:"description"`
}

// GetSimplifyDescription 删除详情中拼接的省市区街道地址串
func (a Address) GetSimplifyDescription() string {
	return strings.TrimPrefix(a.Description, fmt.Sprintf("%s%s%s%s", a.Province, a.City, a.Area, a.Street))
}

type GuangDianPreOrder struct {
	Number      string `json:"number"`
	IdenNr      string `json:"idenNr"`
	RegionId    string `json:"regionId"`
	Area        string `json:"area"`
	Consignee   string `json:"consignee"`
	AreaName    string `json:"area_name"`
	Phone       string `json:"phone"`
	Description string `json:"description" form:"description"` //详细信息
}
type BeforeCheckRequest struct {
	Spu       []Spu             `json:"spu"`
	Address   Address           `json:"address"`
	LianLian  LianLian          `json:"lian_lian"`
	Guangdian GuangDianPreOrder `json:"guang_dian"`
	CakeData  CakeOrderData     `json:"cake_order_data"`
}

type LeaseBeforeCheckRequest struct {
	Spu                 []Spu    `json:"spu"`
	Address             Address  `json:"address"`
	LianLian            LianLian `json:"lian_lian"`
	LeaseTenancyTermsId uint     `json:"lease_tenancy_terms_id"` //租期id
	NumDays             int      `json:"num_days"`               //租赁天数
}

type Spu struct {
	Sku                 uint `json:"sku"`
	Number              uint `json:"number"`
	ShareLiveRoomId     uint `json:"share_live_room_id" ` // 直播间id 存在代表直播间下单
	EventDistributionId uint `json:"event_distribution_id" form:"event_distribution_id" gorm:"column:event_distribution_id;comment:活动id;index;"`
}

type LianLian struct {
	IdCard       string `json:"id_card"`
	Memo         string `json:"memo"`
	ThirdOrderSN string `json:"third_order_sn" form:"third_order_sn"`
	TravelDate   string `json:"travelDate" form:"travelDate"`
}
type CakeOrderData struct {
	AddressId          string      `json:"address_id" form:"address_id"` //uid
	DistributionRuleId string      `json:"distribution_rule_id"`
	CityId             string      `json:"city_id"`
	DeliveryAmount     string      `json:"delivery_amount"`
	BuyerMsg           string      `json:"buyer_msg"`
	Group              interface{} `json:"group"`
	BuyerPhone         string      `json:"buyer_phone"`
	ShipType           string      `json:"ship_type"`
}

type Hbsk struct {
	TotalAmount string `json:"total_amount"`
	Freight     string `json:"freight"`
	Type        string `json:"type"`
	ProductItem []struct {
		Sku    int    `json:"sku"`
		Number int    `json:"number"`
		Price  string `json:"price"`
	} `json:"product_item"`
}
type ConfirmRequest struct {
	OrderSn       string            `json:"order_sn"`
	Spu           []Spu             `json:"spu"`
	Address       Address           `json:"address"`
	CakeOrderData CakeOrderData     `json:"cake_order_data"`
	GuangDian     GuangDianPreOrder `json:"guang_dian"`
	LianLian      LianLian          `json:"lian_lian"`
	Hbsk          Hbsk              `json:"hbsk"`
	Remark        string            `json:"remark"`                                                                                     //备注
	OrderType     int               `json:"order_type" form:"order_type" gorm:"column:order_type;comment:订单类型 1 套餐订单;default:0;index;"` //订单类型 1 套餐订单
	OrderTypeNote string            `json:"order_type_note" form:"order_type_note" gorm:"column:order_type_note;"`
}

type UpdateShippingAddress struct {
	OrderSn string  `json:"order_sn"`
	Address Address `json:"address"`
}

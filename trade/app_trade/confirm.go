package app_trade

import (
	"encoding/json"
	"errors"
	"gin-vue-admin/cmd/gva"
	"math"
	om "order/model"
	model2 "product/model"
	shoppingCartModel "shopping-cart/model"
	shoppingCartService "shopping-cart/service"
	"time"
	"trade/checkout"
	"trade/confirm"
	"trade/model"
	"trade/request"
	"trade/service"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type Users struct {
	source.Model
	ThousandsPricesID uint            `json:"thousands_prices_id"`
	ThousandsPrices   ThousandsPrices `json:"thousands_prices"`
	LevelID           uint            `json:"level_id"`
}
type ThousandsPrices struct {
	source.Model
	Name                    string                    `json:"name"`
	Status                  int                       `json:"status"`
	IsFee                   int                       `json:"is_fee"`
	UnifyRatio              uint                      `json:"unify_ratio"`
	GuideRatio              uint                      `json:"guide_ratio" form:"guide_ratio"`
	OriginRatio             uint                      `json:"origin_ratio" form:"origin_ratio"`
	Products                []model2.Product          `json:"products" gorm:"many2many:thousands_prices_products;"`
	ThousandsPricesProducts []ThousandsPricesProducts `json:"thousands_prices_products"`
	FilterImport            int                       `json:"filter_import" gorm:"column:filter_import;default:1"` //是否过滤导入商品，0不过滤 1过滤

}
type ThousandsPricesProducts struct {
	source.Model
	ThousandsPricesID   uint `json:"thousands_prices_id"`
	ProductID           uint `json:"product_id"`
	SkuID               uint `json:"sku_id"`
	Price               uint `json:"price"`
	SkuPrice            uint `json:"sku_price"`
	GuidePrice          uint `json:"guide_price"`
	SkuGuidePrice       uint `json:"sku_guide_price"`
	OriginPrice         uint `json:"origin_price"`
	SkuOriginPrice      uint `json:"sku_origin_price"`
	Strategy            uint `json:"strategy"`              //定价策略方式 1统一定价策略  2供应商定价策略  3分类
	StrategyRatio       uint `json:"strategy_ratio"`        //定价系数
	GuideStrategyRatio  uint `json:"guide_strategy_ratio"`  //定价系数
	OriginStrategyRatio uint `json:"origin_strategy_ratio"` //定价系数
}

func AppOrderConfirm(confirmRequest request.ConfirmRequest, userID uint, appID uint, appShopID uint) (err error, confirmInfo confirm.Confirm) {

	if confirmRequest.OrderSn == "" {
		log.Log().Error("order_sn不能为空", zap.Any("err", err))
		err = errors.New("order_sn不能为空")
		return
	}
	var thirdOrders []om.Order
	var needPayOrders []om.Order
	var closeOrders []om.Order
	err = source.DB().Preload("OrderItems").Where("third_order_sn = ?", confirmRequest.OrderSn).Find(&thirdOrders).Error
	if err != nil {
		return
	}
	if len(thirdOrders) > 0 {
		//订单已经存在
		var existsSku = make(map[uint]uint)
		for _, thirdOrder := range thirdOrders {
			for _, item := range thirdOrder.OrderItems {
				for _, spu := range confirmRequest.Spu {
					if spu.Sku == item.SkuID {
						//获取之前已经下单成功的sku
						existsSku[spu.Sku] = spu.Sku
					}
				}
			}

			if thirdOrder.Status == om.WaitPay {
				needPayOrders = append(needPayOrders, thirdOrder)
			}
			if thirdOrder.Status == om.Closed {
				closeOrders = append(closeOrders, thirdOrder)
			}

		}

		if len(existsSku) < len(confirmRequest.Spu) {
			//sku数量少,需要补单
			var needConfirmSpus []request.Spu
			//获取需要补单的sku
			for _, sku := range confirmRequest.Spu {
				if _, ok := existsSku[sku.Sku]; !ok {
					needConfirmSpus = append(needConfirmSpus, sku)
				}
			}
			confirmRequest.Spu = needConfirmSpus
			var fixConfirmInfo confirm.Confirm
			//补单
			log.Log().Info("接到补单参数记录", zap.Any("info", confirmRequest))
			err, fixConfirmInfo = Confirm(confirmRequest, userID, appID, appShopID)
			if err != nil {
				//补单失败,但继续进行下一步支付逻辑
				log.Log().Info("补单失败", zap.Any("data", confirmRequest))
			} else {
				//补单成功,修改下单记录状态
				err = source.DB().Model(&model.OrderDataRecord{}).Where("order_sn = ?", confirmRequest.OrderSn).Update("status", 0).Error
				if err != nil {
					return
				}
			}
			if len(fixConfirmInfo.Orders) > 0 {
				//将补单数据填充到需要支付的订单中
				needPayOrders = append(needPayOrders, fixConfirmInfo.Orders...)
			}
		}

		if len(closeOrders) > 0 {
			//订单已关闭
			err = errors.New("订单已关闭")
			confirmInfo.Orders = closeOrders
			return
		}
		if len(needPayOrders) == 0 {
			//订单都已支付，直接返回
			err = errors.New("订单已支付")
			confirmInfo.Orders = thirdOrders
			return
		}

		confirmInfo.Orders = needPayOrders
		return
	} else {
		log.Log().Info("商城下单接到参数记录", zap.Any("info", confirmRequest), zap.Any("confirmRequest.OrderSn", confirmRequest.OrderSn))
		//不存在历史订单,直接走下单逻辑
		var checkExits model.OrderDataRecord
		err = source.DB().Where("order_sn = ?", confirmRequest.OrderSn).First(&checkExits).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			var orderData model.OrderDataRecord
			orderData.OrderSn = confirmRequest.OrderSn
			jsonData, _ := json.Marshal(confirmRequest)
			orderData.Data = string(jsonData)
			orderData.Type = confirmRequest.Hbsk.Type
			orderData.TotalAmount = confirmRequest.Hbsk.TotalAmount
			orderData.UserID = userID
			orderData.AppID = appID
			orderData.AppShopID = appShopID
			err = source.DB().Create(&orderData).Error
			if err != nil {
				log.Log().Error("errOrderDataRecord", zap.Any("err", err))
			}
			if confirmRequest.CakeOrderData.DistributionRuleId != "" {
				gva.ThirdOrderSN = confirmRequest.OrderSn
			}

		}
		var user Users
		err = source.DB().Preload("ThousandsPrices.Products").Preload("ThousandsPrices.ThousandsPricesProducts").First(&user, userID).Error
		if err != nil {
			return
		}
		var thousandsProductIds = make(map[uint]uint)
		for _, product := range user.ThousandsPrices.Products {
			thousandsProductIds[product.ID] = product.ID
		}
		//千人千价 过滤商品下单
		for _, v := range confirmRequest.Spu {
			var sku model2.Sku
			err = source.DB().Preload("Product").First(&sku, v.Sku).Error
			if err != nil || sku.Product == nil {
				return errors.New("商品数据出错"), confirmInfo
			}

			if len(user.ThousandsPrices.Products) > 0 && user.ThousandsPrices.Status == 1 && user.ThousandsPrices.FilterImport == 1 {
				//千人千价 只查询指定商品
				if _, ok := thousandsProductIds[sku.ProductID]; !ok {
					log.Log().Error("下单失败!商品池专辑过滤商品下单", zap.Any("err", err))
					err = errors.New("下单失败,此商品无下单权限：" + sku.Product.Title + "[" + sku.Title + "]")
					return
				}
			}
		}
		return Confirm(confirmRequest, userID, appID, appShopID)
	}

}
func Confirm(confirmRequest request.ConfirmRequest, userID uint, appID uint, appShopID uint) (err error, confirmInfo confirm.Confirm) {

	one := 1
	zero := 0

	//未下单
	//获取地址id
	var addressId uint
	if confirmRequest.Address.Phone != "" && confirmRequest.Address.Province != "" && confirmRequest.Address.City != "" {
		err, addressId = service.GetOrSetAddress(confirmRequest.Address)
		if err != nil {
			log.Log().Error(err.Error(), zap.Any("err", err))
			if utils.IsChineseChar(err.Error()) {
			} else {
				err = errors.New("收货地址保存失败")
			}
			return
		}
	} else {
		err = errors.New("地址信息异常，请检查地址")
		return
	}

	var id int64
	id, err = cache.GetID("api_buy")
	if err != nil {
		log.Log().Error("buy_id生成失败", zap.Any("err", err))
		err = errors.New("buy_id生成失败")
		return
	}
	buyID := id*int64(math.Pow(10, 10)) + time.Now().Unix()
	//插入购物车记录
	for _, item := range confirmRequest.Spu {
		shoppingCart := shoppingCartModel.ShoppingCart{
			UserID:              userID,
			SkuID:               item.Sku,
			Qty:                 item.Number,
			Status:              &zero,
			Checked:             &one,
			BuyID:               uint(buyID),
			ApplicationID:       appID,
			ApplicationShopID:   appShopID,
			BuyWay:              2,
			AddressID:           addressId,
			ShareLiveRoomId:     item.ShareLiveRoomId,
			EventDistributionId: item.EventDistributionId,
		}
		err = shoppingCartService.CreateShoppingCart(shoppingCart)
		if err != nil {
			log.Log().Error("添加失败!", zap.Any("err", err))
			err = errors.New("添加失败,错误：" + err.Error())
			return
		}
	}
	// 读取购物车记录
	var shoppingCarts checkout.ShoppingCarts
	err, shoppingCarts = checkout.GetCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID)})
	if err != nil {
		log.Log().Error("获取失败:读取购物车记录", zap.Any("err", err))
		err = errors.New("获取失败1" + err.Error())
		return
	}

	if len(shoppingCarts) == 0 {
		//  没有购物车记录
		log.Log().Error("请选择要结算的商品", zap.Any("err", err))
		err = errors.New("请选择要结算的商品" + err.Error())
		return
	}
	var shoppingCartIDs []uint
	for _, shoppingCart := range shoppingCarts {
		shoppingCartIDs = append(shoppingCartIDs, shoppingCart.ID)
	}
	err = shoppingCartService.CheckBeforeSale(shoppingCartIDs)
	if err != nil {
		return
	}
	// 结算信息
	var checkoutInfo checkout.Checkout
	err, checkoutInfo = checkout.ShoppingCartCheckout(userID, shoppingCarts)
	if err != nil {
		log.Log().Error("获取失败:结算信息", zap.Any("err", err))
		err = errors.New("获取失败2" + err.Error())
		return
	}

	// 下单
	checkoutInfo.ThirdOrderSn = confirmRequest.OrderSn
	for k, _ := range checkoutInfo.Orders {
		checkoutInfo.Orders[k].Remark = confirmRequest.Remark               //备注
		checkoutInfo.Orders[k].OrderTypeNote = confirmRequest.OrderTypeNote //订单类型的备注 如order_type =1 时为套餐名称
		checkoutInfo.Orders[k].OrderType = confirmRequest.OrderType         //订单类型 1 套餐订单
	}
	//checkoutInfo.ShareLiveRoomId = confirmRequest.ShareLiveRoomId //直播间id
	//checkoutInfo.Remark = confirmRequest.Remark  //备注
	err, confirmInfo = confirm.ShoppingCartConfirm(checkoutInfo)
	if err != nil {
		log.Log().Error("获取失败:下单", zap.Any("err", err))
		source.DB().Model(&model.OrderDataRecord{}).Where("order_sn = ?", confirmRequest.OrderSn).Update("status", 1)
		err = errors.New("获取失败3" + err.Error())
		return
	}
	err = checkout.ClearCheckedShoppingCarts(checkout.ShoppingCart{UserID: userID, BuyID: uint(buyID), BuyWay: 2})
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		err = errors.New("获取失败4" + err.Error())
		return
	}
	return
}

// 验证是否是这个API能购买的商品
func IsApiBuy(confirmRequest request.ConfirmRequest) (err error) {
	//120 租赁
	var nots = [...]uint{120}
	for _, item := range confirmRequest.Spu {
		var sku model2.Sku
		err = source.DB().Where("id = ?", item.Sku).Preload("Product").Preload("Product.GatherSupply").First(&sku).Error
		if err != nil {
			err = errors.New("商品不存在" + err.Error())
			return
		}
		if sku.Product == nil {
			err = errors.New("商品已删除或者不存在")
			return
		}
		for _, not := range nots {
			if sku.Product.GatherSupply.CategoryID == not {
				err = errors.New("请使用专用下单API购买此商品")
				return
			}
		}
	}
	return
}

package listener

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/mq"
	"order/service"
	"user/setting"
	"yz-go/component/log"
	"yz-go/source"
)

func PushCustomerHandles() {

	log.Log().Info("会员升级监听订单完成!")

	mq.PushHandles("UserUpdateReceived", func(orderMsg mq.OrderMessage) (err error) {
		if orderMsg.MessageType != mq.Received {
			return
		}
		var userSetting setting.SysSetting
		err = source.DB().Where("`key` = ?", "user_setting").First(&userSetting).Error
		if err != nil && !errors.Is(err,gorm.ErrRecordNotFound){
			log.Log().Error(err.Error(), zap.Any("err", err))
			return nil
		}
		if userSetting.Value.UpgradeTiming == 2 {
			err = service.AfterOrderOperationUserUpgrade(orderMsg.OrderID)
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("err", err))
			}
		}

		return nil
	})
}


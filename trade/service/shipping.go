package service

import (
	"order/order"
	"shipping/express"
	"shipping/response"
	"shipping/service"
	"yz-go/source"
)

func GetExpressInfo(orderID uint)(err error, expressInfoResps []response.ExpressInfoResp){
	var order order.Order
	err = source.DB().Preload("OrderExpress").First(&order,orderID).Error
	if err != nil {
		return
	}

	for _,v := range order.OrderExpress {
		var expressInfoResp response.ExpressInfoResp
		err, expressInfoResp = service.GetExpressInfo(v.ExpressNo, v.CompanyCode)
		if err != nil {
			return
		}
		expressInfoResp.ExpressCode = v.ExpressNo
		err,expressInfoResp.ExpressCom = express.GetCompanyByCode(v.CompanyCode)
		expressInfoResps = append(expressInfoResps, expressInfoResp)
	}

	return
}
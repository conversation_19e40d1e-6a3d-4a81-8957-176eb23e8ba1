package service

import (
	"trade/model"
	"yz-go/source"
)

// SaveSysSetting
//@author: [piexlmax](https://github.com/piexlmax)
//@function: SaveSysSetting
//@description: 保存SysTop记录
//@param: data *setting.SysSetting
//@return: err error
func SaveTradeSetting(data model.TradeSetting) (err error) {
	if data.ID != 0{
		err = source.DB().Updates(&data).Error
	}else {
		err = source.DB().Create(&data).Error
	}
	return err
}


func GetTradeSetting() (err error, sysSetting model.TradeSetting) {

	err = source.DB().Where("`key` = ?", "trade_setting").First(&sysSetting).Error

	return
}
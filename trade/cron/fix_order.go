package cron

import (
	"encoding/json"
	"go.uber.org/zap"
	"trade/app_trade"
	"trade/confirm"
	tmodel "trade/model"
	"trade/request"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushFixHandle() {
	task := cron.Task{
		Key:  "fixOrder",
		Name: "补单",
		Spec: "52 */10 * * * *",
		Handle: func(task cron.Task) {
			OrderFix()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func OrderFix() {
	//暂时关闭补单功能，因为补单功能存在争议
	return
	var orderRecord []tmodel.OrderDataRecord
	err := source.DB().Where("status = 1").Find(&orderRecord).Limit(100).Error
	if err != nil {
		return
	}
	for _, item := range orderRecord {
		var confirmRequest request.ConfirmRequest
		err = json.Unmarshal([]byte(item.Data), &confirmRequest)
		if err != nil {
			log.Log().Info("补单解析数据失败", zap.Any("data", item.Data))
			err = nil
			continue
		}
		var confirmInfo confirm.Confirm
		err, confirmInfo = app_trade.AppOrderConfirm(confirmRequest, item.UserID, item.AppID, item.AppShopID)
		if err != nil {
			continue
		}
		_, errMessages, _ := app_trade.AppOrderPay(confirmInfo.Orders, item.AppID)
		if len(errMessages) > 0 {
			log.Log().Info("补单支付提交失败，请查看messages", zap.Any("err", errMessages))
			continue
		}
	}
	return
}

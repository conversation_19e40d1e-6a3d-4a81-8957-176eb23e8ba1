package cron

import (
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"order/model"
	"order/order"
	"time"
	tmodel "trade/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushReceiveHandle() {
	task := cron.Task{
		Key:  "receiveOrder",
		Name: "订单自动收货",
		Spec: "0 */10 * * * *",
		Handle: func(task cron.Task) {
			OrderReceive()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func OrderReceive() {

	var orderIds []uint
	var tradeSetting tmodel.TradeSetting
	//查出交易设置信息
	err := source.DB().Where("`key` = ?", "trade_setting").First(&tradeSetting).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error(err.Error(), zap.Any("err", err))
		return
	}
	if tradeSetting.Value.OrderReceiveTime == 0 {
		log.Log().Error("自动收货时间未设置，停止执行")
		return
	}
	tm := time.Unix(time.Now().Unix()-int64(tradeSetting.Value.OrderReceiveTime)*86400, 0)
	//租赁订单无需自动收货 120 为租赁订单
	err = source.DB().Model(&model.Order{}).Where("gather_supply_type != ?", 120).Where("`sent_at` <= ?", tm.Format("2006-01-02 15:04:05")).Where("status = 2").Pluck("id", &orderIds).Error
	if err != nil {
		log.Log().Info(err.Error())
	}

	for _, v := range orderIds {
		if err = order.Received(v); err != nil {
			log.Log().Error("订单自动收货失败!", zap.Any("err", err))
			continue
		}
	}

	fmt.Println("订单自动收货完成", orderIds)
}

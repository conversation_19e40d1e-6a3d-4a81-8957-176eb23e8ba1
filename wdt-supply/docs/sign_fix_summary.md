# WDT API 签名验证错误修复总结

## 问题描述

在调用 `trade_push.php` 接口时出现 sign 验证错误，需要根据 PHP SDK 优化 Go 代码的签名生成逻辑。

## 问题分析

通过对比 PHP SDK (`WdtClient.php`) 和原有 Go 代码，发现以下问题：

### 1. **字符串长度计算方式不一致**
- **PHP SDK**: 使用 `mb_strlen($key, 'utf-8')` 计算 UTF-8 字符数
- **原 Go 代码**: 使用 `len(k)` 计算字节数
- **问题**: 对于包含中文等多字节字符的参数，长度计算不一致

### 2. **签名字符串拼接格式问题**
- **PHP SDK**: 参数间用 `;` 分隔，但最后一个参数后不加分隔符
- **原 Go 代码**: 使用 `strings.Join(signParts, ";")` 可能导致格式不一致

### 3. **参数处理顺序**
- 需要确保参数排序和拼接逻辑与 PHP SDK 完全一致

## 修复方案

### 1. **修复字符串长度计算**

```go
import "unicode/utf8"

// 使用 UTF-8 字符数计算长度（与 PHP 的 mb_strlen 一致）
keyLen := utf8.RuneCountInString(k)
valueLen := utf8.RuneCountInString(v)
```

### 2. **修复签名字符串拼接**

```go
// 构建签名字符串（与PHP SDK完全一致）
var signStr strings.Builder
for i, k := range keys {
    v := params[k]
    keyLen := utf8.RuneCountInString(k)
    valueLen := utf8.RuneCountInString(v)
    
    // 构建格式：keyLen-key:valueLen-value
    part := fmt.Sprintf("%02d-%s:%04d-%s", keyLen, k, valueLen, v)
    signStr.WriteString(part)
    
    // 除了最后一个参数，其他参数后面都要加分号
    if i < len(keys)-1 {
        signStr.WriteString(";")
    }
}

// 添加 appsecret
signStr.WriteString(w.Appsecret)
```

### 3. **添加调试功能**

新增 `GetSignDebug()` 方法，返回签名和调试字符串：

```go
func (w *WdtRequest) GetSignDebug() (string, string) {
    // ... 签名生成逻辑
    return sign, debugStr
}
```

## 修复后的完整流程

### 1. **参数收集和排序**
```go
params := make(map[string]string)
params["sid"] = w.Sid
params["appkey"] = w.Appkey
params["timestamp"] = fmt.Sprintf("%d", w.Timestamp)
// 添加业务参数...

// 按键名排序
var keys []string
for k := range params {
    keys = append(keys, k)
}
sort.Strings(keys)
```

### 2. **签名字符串构建**
```go
// 格式：keyLen-key:valueLen-value;keyLen-key:valueLen-value...
// 示例：06-appkey:0012-test_appkey;03-sid:0008-test_sid;...
```

### 3. **MD5 计算**
```go
hash := md5.New()
hash.Write([]byte(signStr.String()))
return hex.EncodeToString(hash.Sum(nil))
```

## 使用方法

### 1. **基本使用**
```go
req := &common.WdtRequest{
    Sid:       "your_sid",
    Appkey:    "your_appkey", 
    Appsecret: "your_appsecret",
}

params := map[string]interface{}{
    "trade_no": "your_trade_no",
    "status":   "1",
}
req.SetParams(params)

// 调用接口
resp, err := req.DoRequest("trade_push.php", false)
```

### 2. **调试签名**
```go
sign, debugStr := req.GetSignDebug()
fmt.Printf("签名字符串: %s\n", debugStr)
fmt.Printf("生成的签名: %s\n", sign)
```

### 3. **验证签名正确性**
```go
// 使用测试工具验证
example.ValidateSignature()
example.DebugRequest(sid, appkey, appsecret, params)
```

## 测试验证

### 1. **运行测试**
```bash
cd wdt-supply
go test ./test/ -v
```

### 2. **使用示例工具**
```go
import "wdt-supply/example"

// 运行所有调试示例
example.RunAllExamples()

// 调试特定请求
example.DebugRequest("your_sid", "your_appkey", "your_secret", params)
```

### 3. **与 PHP SDK 对比**
使用相同的参数在 PHP 和 Go 中生成签名，确保结果一致。

## 常见问题排查

### 1. **签名仍然验证失败**
- 检查 SID、AppKey、AppSecret 是否正确
- 确认时间戳是否在有效范围内（通常5分钟内）
- 验证参数名和参数值是否完全一致

### 2. **中文参数问题**
- 确保使用 UTF-8 编码
- 使用 `utf8.RuneCountInString()` 计算字符数

### 3. **时间戳问题**
- 确保服务器时间准确
- 可以手动设置时间戳进行测试

## 文件清单

### 修改的文件
- `wdt-supply/common/request.go` - 修复签名生成逻辑

### 新增的文件
- `wdt-supply/test/sign_test.go` - 签名测试
- `wdt-supply/example/trade_push_example.go` - 使用示例
- `wdt-supply/docs/sign_fix_summary.md` - 修复总结

## 总结

通过对比 PHP SDK 并修复字符串长度计算和拼接逻辑，现在 Go 代码生成的签名应该与 PHP SDK 完全一致。建议：

1. 先使用测试工具验证签名生成逻辑
2. 与 PHP SDK 生成的签名进行对比
3. 确认无误后再调用实际接口

如果仍有问题，可以使用调试工具查看详细的签名生成过程，便于排查具体问题。

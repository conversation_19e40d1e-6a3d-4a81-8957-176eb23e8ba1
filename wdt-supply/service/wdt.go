package service

import (
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"strconv"
	"wdt-supply/model"
	"yz-go/component/log"
	setting2 "yz-go/setting"
)

type WdtSetting struct {
	BaseInfo model.WdtSetting
}

func GetWdtSetting(id uint) (err error, setting model.WdtSetting) {
	var settingValue string
	err, settingValue = setting2.GetSetting("gatherSupply" + strconv.Itoa(int(id)))
	if err != nil {
		return
	}
	var wdtSetting WdtSetting
	err = json.Unmarshal([]byte(settingValue), &wdtSetting)
	if err != nil {
		log.Log().Error("获取wdt配置失败", zap.Any("err", err))
		return
	}
	setting = wdtSetting.BaseInfo
	if setting.Appkey == "" || setting.Appsecret == "" || setting.Sid == "" || setting.DistributorAppkey == "" || setting.DistributorAppsecret == "" {
		err = errors.New("请先配置供应链key")
		return
	}
	return
}

package example

import (
	"fmt"
	"log"
	"wdt-supply/common"
)

// TradePushExample 演示如何调用 trade_push.php 接口
func TradePushExample() {
	// 1. 创建请求实例
	req := &common.WdtRequest{
		Sid:       "your_sid_here",        // 替换为您的真实SID
		Appkey:    "your_appkey_here",     // 替换为您的真实AppKey
		Appsecret: "your_appsecret_here",  // 替换为您的真实AppSecret
	}

	// 2. 设置 trade_push 接口参数
	params := map[string]interface{}{
		"trade_no": "your_trade_no_here",  // 替换为真实的订单号
		"status":   "1",                   // 订单状态
		// 根据API文档添加其他必要参数
	}
	req.SetParams(params)

	// 3. 调试签名生成过程
	fmt.Println("=== 签名调试信息 ===")
	sign, debugStr := req.GetSignDebug()
	fmt.Printf("签名字符串: %s\n", debugStr)
	fmt.Printf("生成的签名: %s\n", sign)
	fmt.Printf("时间戳: %d\n", req.Timestamp)
	fmt.Println()

	// 4. 发送请求
	fmt.Println("=== 发送请求 ===")
	resp, err := req.DoRequest("trade_push.php", false) // false表示生产环境，true表示沙箱环境
	if err != nil {
		log.Printf("请求失败: %v", err)
		return
	}

	// 5. 处理响应
	fmt.Printf("响应代码: %d\n", resp.Code)
	fmt.Printf("响应消息: %s\n", resp.Message)
	if resp.Code == 0 {
		fmt.Println("请求成功！")
		// 处理成功响应的数据
		fmt.Printf("响应数据: %+v\n", resp.Data)
	} else {
		fmt.Printf("API错误: %s\n", resp.Message)
	}
}

// TradePushWithRetry 带重试机制的 trade_push 调用
func TradePushWithRetry(tradeNo string, status string, maxRetries int) error {
	req := &common.WdtRequest{
		Sid:       "your_sid_here",        // 替换为您的真实配置
		Appkey:    "your_appkey_here",
		Appsecret: "your_appsecret_here",
	}

	params := map[string]interface{}{
		"trade_no": tradeNo,
		"status":   status,
	}
	req.SetParams(params)

	var lastErr error
	for i := 0; i < maxRetries; i++ {
		fmt.Printf("第 %d 次尝试调用 trade_push 接口...\n", i+1)
		
		resp, err := req.DoRequest("trade_push.php", false)
		if err != nil {
			lastErr = err
			fmt.Printf("第 %d 次尝试失败: %v\n", i+1, err)
			continue
		}

		if resp.Code == 0 {
			fmt.Printf("第 %d 次尝试成功！\n", i+1)
			return nil
		} else {
			lastErr = fmt.Errorf("API错误: %s", resp.Message)
			fmt.Printf("第 %d 次尝试API返回错误: %s\n", i+1, resp.Message)
		}
	}

	return fmt.Errorf("重试 %d 次后仍然失败，最后错误: %v", maxRetries, lastErr)
}

// ValidateSignature 验证签名生成是否正确
func ValidateSignature() {
	fmt.Println("=== 签名验证工具 ===")
	
	// 使用固定的测试数据
	req := &common.WdtRequest{
		Sid:       "test_sid",
		Appkey:    "test_appkey",
		Appsecret: "test_secret",
	}

	// 设置固定的时间戳便于对比
	req.Timestamp = 1640995200 // 2022-01-01 00:00:00

	params := map[string]interface{}{
		"trade_no": "TEST123456",
		"status":   "1",
	}
	req.SetParams(params)

	// 手动构建签名字符串进行验证
	expectedSignStr := "06-appkey:0012-test_appkey;03-sid:0008-test_sid;06-status:0001-1;09-timestamp:0010-1640995200;08-trade_no:0009-TEST123456test_secret"
	
	sign, actualSignStr := req.GetSignDebug()
	
	fmt.Printf("期望的签名字符串: %s\n", expectedSignStr)
	fmt.Printf("实际的签名字符串: %s\n", actualSignStr)
	fmt.Printf("签名字符串匹配: %t\n", expectedSignStr == actualSignStr)
	fmt.Printf("生成的签名: %s\n", sign)
	
	if expectedSignStr == actualSignStr {
		fmt.Println("✅ 签名生成逻辑正确！")
	} else {
		fmt.Println("❌ 签名生成逻辑有误，请检查！")
	}
}

// DebugRequest 调试请求参数
func DebugRequest(sid, appkey, appsecret string, params map[string]interface{}) {
	fmt.Println("=== 请求调试信息 ===")
	
	req := &common.WdtRequest{
		Sid:       sid,
		Appkey:    appkey,
		Appsecret: appsecret,
	}
	req.SetParams(params)

	sign, debugStr := req.GetSignDebug()

	fmt.Printf("SID: %s\n", req.Sid)
	fmt.Printf("AppKey: %s\n", req.Appkey)
	fmt.Printf("AppSecret: %s (长度: %d)\n", req.Appsecret, len(req.Appsecret))
	fmt.Printf("时间戳: %d\n", req.Timestamp)
	fmt.Printf("参数: %+v\n", req.Params)
	fmt.Printf("签名字符串: %s\n", debugStr)
	fmt.Printf("生成的签名: %s\n", sign)
	
	fmt.Println("\n=== 请求参数格式 ===")
	fmt.Printf("sid=%s\n", req.Sid)
	fmt.Printf("appkey=%s\n", req.Appkey)
	fmt.Printf("timestamp=%d\n", req.Timestamp)
	fmt.Printf("sign=%s\n", sign)
	for k, v := range req.Params {
		fmt.Printf("%s=%v\n", k, v)
	}
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	fmt.Println("开始运行 trade_push 接口调试示例...")
	
	// 1. 验证签名生成逻辑
	ValidateSignature()
	fmt.Println()
	
	// 2. 调试请求参数
	testParams := map[string]interface{}{
		"trade_no": "TEST123456",
		"status":   "1",
	}
	DebugRequest("test_sid", "test_appkey", "test_secret", testParams)
	fmt.Println()
	
	// 3. 实际接口调用示例（需要替换真实配置）
	fmt.Println("=== 实际接口调用示例 ===")
	fmt.Println("请在 TradePushExample() 函数中替换真实的配置信息后调用")
	// TradePushExample() // 取消注释并替换真实配置后使用
}

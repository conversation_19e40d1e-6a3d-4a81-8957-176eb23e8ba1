package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/source"

	"go.uber.org/zap"
)

// WdtLogisticsConfig WDT物流同步配置
type WdtLogisticsConfig struct {
	ID        uint   `json:"id" gorm:"primarykey"`
	Name      string `json:"name" gorm:"column:name;comment:配置名称;type:varchar(100);not null"`
	Sid       string `json:"sid" gorm:"column:sid;comment:WDT卖家账号;type:varchar(100);not null"`
	Appkey    string `json:"appkey" gorm:"column:appkey;comment:WDT接口账号;type:varchar(100);not null"`
	Appsecret string `json:"appsecret" gorm:"column:appsecret;comment:WDT接口密钥;type:varchar(100);not null"`
	ShopId    string `json:"shop_id" gorm:"column:shop_id;comment:店铺编号;type:varchar(50);"`
	Enabled   bool   `json:"enabled" gorm:"column:enabled;comment:是否启用;default:false"`

	// 同步配置
	SyncInterval  int `json:"sync_interval" gorm:"column:sync_interval;comment:同步间隔(分钟);default:5"`
	BatchSize     int `json:"batch_size" gorm:"column:batch_size;comment:批次大小;default:100"`
	RetryTimes    int `json:"retry_times" gorm:"column:retry_times;comment:重试次数;default:3"`
	TimeoutSecond int `json:"timeout_second" gorm:"column:timeout_second;comment:超时时间(秒);default:30"`

	// 环境配置
	Environment string `json:"environment" gorm:"column:environment;comment:环境(test/prod);default:prod"`

	// 统计信息
	LastSyncTime *time.Time `json:"last_sync_time" gorm:"column:last_sync_time;comment:最后同步时间"`
	TotalSynced  int64      `json:"total_synced" gorm:"column:total_synced;comment:总同步数量;default:0"`
	TotalSuccess int64      `json:"total_success" gorm:"column:total_success;comment:成功数量;default:0"`
	TotalFailure int64      `json:"total_failure" gorm:"column:total_failure;comment:失败数量;default:0"`

	// 时间戳
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;comment:创建时间"`
	UpdatedAt time.Time `json:"updated_at" gorm:"column:updated_at;comment:更新时间"`
}

// TableName 表名
func (WdtLogisticsConfig) TableName() string {
	return "wdt_logistics_configs"
}

// WdtLogisticsConfigManager WDT物流配置管理器
type WdtLogisticsConfigManager struct{}

// GetAllConfigs 获取所有配置
func (m *WdtLogisticsConfigManager) GetAllConfigs() ([]WdtLogisticsConfig, error) {
	var configs []WdtLogisticsConfig
	err := source.DB().Find(&configs).Error
	if err != nil {
		log.Log().Error("获取WDT物流配置列表失败", zap.Error(err))
		return nil, err
	}
	return configs, nil
}

// GetEnabledConfigs 获取启用的配置
func (m *WdtLogisticsConfigManager) GetEnabledConfigs() ([]WdtLogisticsConfig, error) {
	var configs []WdtLogisticsConfig
	err := source.DB().Where("enabled = ?", true).Find(&configs).Error
	if err != nil {
		log.Log().Error("获取启用的WDT物流配置失败", zap.Error(err))
		return nil, err
	}
	return configs, nil
}

// GetConfigById 根据ID获取配置
func (m *WdtLogisticsConfigManager) GetConfigById(id uint) (*WdtLogisticsConfig, error) {
	var config WdtLogisticsConfig
	err := source.DB().First(&config, id).Error
	if err != nil {
		log.Log().Error("获取WDT物流配置失败", zap.Uint("id", id), zap.Error(err))
		return nil, err
	}
	return &config, nil
}

// GetConfigBySid 根据SID获取配置
func (m *WdtLogisticsConfigManager) GetConfigBySid(sid string) (*WdtLogisticsConfig, error) {
	var config WdtLogisticsConfig
	err := source.DB().Where("sid = ?", sid).First(&config).Error
	if err != nil {
		log.Log().Error("根据SID获取WDT物流配置失败", zap.String("sid", sid), zap.Error(err))
		return nil, err
	}
	return &config, nil
}

// CreateConfig 创建配置
func (m *WdtLogisticsConfigManager) CreateConfig(config *WdtLogisticsConfig) error {
	// 验证配置
	if err := m.ValidateConfig(config); err != nil {
		return err
	}

	err := source.DB().Create(config).Error
	if err != nil {
		log.Log().Error("创建WDT物流配置失败", zap.Error(err))
		return err
	}

	log.Log().Info("创建WDT物流配置成功",
		zap.String("name", config.Name),
		zap.String("sid", config.Sid),
	)
	return nil
}

// UpdateConfig 更新配置
func (m *WdtLogisticsConfigManager) UpdateConfig(config *WdtLogisticsConfig) error {
	// 验证配置
	if err := m.ValidateConfig(config); err != nil {
		return err
	}

	err := source.DB().Save(config).Error
	if err != nil {
		log.Log().Error("更新WDT物流配置失败", zap.Error(err))
		return err
	}

	log.Log().Info("更新WDT物流配置成功",
		zap.String("name", config.Name),
		zap.String("sid", config.Sid),
	)
	return nil
}

// DeleteConfig 删除配置
func (m *WdtLogisticsConfigManager) DeleteConfig(id uint) error {
	err := source.DB().Delete(&WdtLogisticsConfig{}, id).Error
	if err != nil {
		log.Log().Error("删除WDT物流配置失败", zap.Uint("id", id), zap.Error(err))
		return err
	}

	log.Log().Info("删除WDT物流配置成功", zap.Uint("id", id))
	return nil
}

// UpdateSyncStats 更新同步统计
func (m *WdtLogisticsConfigManager) UpdateSyncStats(sid string, successCount, failureCount int64) error {
	now := time.Now()
	updates := map[string]interface{}{
		"last_sync_time": &now,
		"total_synced":   source.DB().Raw("total_synced + ?", successCount+failureCount),
		"total_success":  source.DB().Raw("total_success + ?", successCount),
		"total_failure":  source.DB().Raw("total_failure + ?", failureCount),
		"updated_at":     now,
	}

	err := source.DB().Model(&WdtLogisticsConfig{}).Where("sid = ?", sid).Updates(updates).Error
	if err != nil {
		log.Log().Error("更新WDT物流同步统计失败", zap.String("sid", sid), zap.Error(err))
		return err
	}

	log.Log().Info("更新WDT物流同步统计成功",
		zap.String("sid", sid),
		zap.Int64("success", successCount),
		zap.Int64("failure", failureCount),
	)
	return nil
}

// ValidateConfig 验证配置
func (m *WdtLogisticsConfigManager) ValidateConfig(config *WdtLogisticsConfig) error {
	if config.Name == "" {
		return fmt.Errorf("配置名称不能为空")
	}
	if config.Sid == "" {
		return fmt.Errorf("WDT卖家账号不能为空")
	}
	if config.Appkey == "" {
		return fmt.Errorf("WDT接口账号不能为空")
	}
	if config.Appsecret == "" {
		return fmt.Errorf("WDT接口密钥不能为空")
	}
	if config.SyncInterval <= 0 {
		config.SyncInterval = 5
	}
	if config.BatchSize <= 0 || config.BatchSize > 100 {
		config.BatchSize = 100
	}
	if config.RetryTimes <= 0 {
		config.RetryTimes = 3
	}
	if config.TimeoutSecond <= 0 {
		config.TimeoutSecond = 30
	}
	if config.Environment == "" {
		config.Environment = "prod"
	}
	return nil
}

// LoadFromEnv 从环境变量加载配置
func (m *WdtLogisticsConfigManager) LoadFromEnv() (*WdtLogisticsConfig, error) {
	config := &WdtLogisticsConfig{
		Name:        "环境变量配置",
		Sid:         os.Getenv("WDT_SID"),
		Appkey:      os.Getenv("WDT_APPKEY"),
		Appsecret:   os.Getenv("WDT_APPSECRET"),
		ShopId:      os.Getenv("WDT_SHOP_ID"),
		Environment: os.Getenv("WDT_ENVIRONMENT"),
	}

	// 解析布尔值
	if enabled := os.Getenv("WDT_LOGISTICS_ENABLED"); enabled != "" {
		config.Enabled = enabled == "true"
	}

	// 解析整数值
	if interval := os.Getenv("WDT_LOGISTICS_SYNC_INTERVAL"); interval != "" {
		if val, err := strconv.Atoi(interval); err == nil {
			config.SyncInterval = val
		}
	}

	if batchSize := os.Getenv("WDT_LOGISTICS_BATCH_SIZE"); batchSize != "" {
		if val, err := strconv.Atoi(batchSize); err == nil {
			config.BatchSize = val
		}
	}

	if retryTimes := os.Getenv("WDT_LOGISTICS_RETRY_TIMES"); retryTimes != "" {
		if val, err := strconv.Atoi(retryTimes); err == nil {
			config.RetryTimes = val
		}
	}

	if timeout := os.Getenv("WDT_LOGISTICS_TIMEOUT_SECOND"); timeout != "" {
		if val, err := strconv.Atoi(timeout); err == nil {
			config.TimeoutSecond = val
		}
	}

	// 验证配置
	err := m.ValidateConfig(config)
	if err != nil {
		return nil, fmt.Errorf("环境变量配置验证失败: %v", err)
	}

	return config, nil
}

// ExportToJSON 导出配置为JSON
func (m *WdtLogisticsConfigManager) ExportToJSON(configs []WdtLogisticsConfig) (string, error) {
	// 隐藏敏感信息
	for i := range configs {
		if len(configs[i].Appsecret) > 4 {
			configs[i].Appsecret = configs[i].Appsecret[:4] + "****"
		}
	}

	data, err := json.MarshalIndent(configs, "", "  ")
	if err != nil {
		return "", fmt.Errorf("导出JSON失败: %v", err)
	}

	return string(data), nil
}

// GetConfigStats 获取配置统计信息
func (m *WdtLogisticsConfigManager) GetConfigStats() (map[string]interface{}, error) {
	var stats struct {
		TotalConfigs   int64 `json:"total_configs"`
		EnabledConfigs int64 `json:"enabled_configs"`
		TotalSynced    int64 `json:"total_synced"`
		TotalSuccess   int64 `json:"total_success"`
		TotalFailure   int64 `json:"total_failure"`
	}

	// 统计总配置数
	err := source.DB().Model(&WdtLogisticsConfig{}).Count(&stats.TotalConfigs).Error
	if err != nil {
		return nil, err
	}

	// 统计启用的配置数
	err = source.DB().Model(&WdtLogisticsConfig{}).Where("enabled = ?", true).Count(&stats.EnabledConfigs).Error
	if err != nil {
		return nil, err
	}

	// 统计总同步数据
	err = source.DB().Model(&WdtLogisticsConfig{}).
		Select("SUM(total_synced) as total_synced, SUM(total_success) as total_success, SUM(total_failure) as total_failure").
		Scan(&stats).Error
	if err != nil {
		return nil, err
	}

	successRate := float64(0)
	if stats.TotalSynced > 0 {
		successRate = float64(stats.TotalSuccess) / float64(stats.TotalSynced) * 100
	}

	result := map[string]interface{}{
		"total_configs":   stats.TotalConfigs,
		"enabled_configs": stats.EnabledConfigs,
		"total_synced":    stats.TotalSynced,
		"total_success":   stats.TotalSuccess,
		"total_failure":   stats.TotalFailure,
		"success_rate":    fmt.Sprintf("%.2f%%", successRate),
	}

	return result, nil
}

// 全局配置管理器实例
var WdtLogisticsConfigMgr = &WdtLogisticsConfigManager{}

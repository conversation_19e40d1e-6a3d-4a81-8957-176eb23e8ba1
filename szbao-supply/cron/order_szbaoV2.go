package cron

import (
	"fmt"
	"go.uber.org/zap"
	v1 "order/api/v1"
	orderRequest2 "order/request"
	publicModel "public-supply/model"
	"public-supply/response"
	"strings"
	"szbao-supply/component/order"
	"szbao-supply/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

func PushOrderSzbaoV2Handle() {
	task := cron.Task{
		Key:  "orderdeliverv2",
		Name: "永源供应链发货查询定时任务V2",
		//Spec: "12 12 */1 * * *",
		Spec: "12 */3 * * * *",
		Handle: func(task cron.Task) {
			OrderSzbaoV2Cron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func OrderSzbaoV2Cron() {

	var gatherList []publicModel.GatherSupply
	err := source.DB().Where("`category_id` = ?", 3).Find(&gatherList).Error
	if err != nil {
		return
	}
	fmt.Println("开始处理永源订单自动同步运单号")

	for _, v := range gatherList {
		//log.Log().Info("开始处理供应链" + strconv.Itoa(int(v.ID)) + "的订单")
		orderClass := order.Szbao{}
		err = orderClass.InitSetting(v.ID)
		if err != nil {
			continue
		}
		var orderList []model.Order
		err = source.DB().Preload("OrderItems.Sku").Where("`gather_supply_id` = ?", v.ID).Where("`status` = ?", 1).Find(&orderList).Error
		if err != nil {
			return
		}
		var unionIds []string
		var orderData = make(map[string]model.Order)
		for _, orderd := range orderList {
			unionIds = append(unionIds, orderd.GatherSupplySN)
			orderData[orderd.GatherSupplySN] = orderd

		}
		//log.Log().Info("订单数据", zap.Any("data", orderData))
		log.Log().Info("待处理订单号", zap.Any("data", unionIds))

		var result response.SyncOrderExpNoResponseV2
		err, result = orderClass.SyncOrderExpNo(unionIds)
		log.Log().Info("查询结果", zap.Any("data", result))

		if err != nil {
			return
		}
		if len(result.Result) > 0 {
			for _, item := range result.Result {
				for _, itemOrder := range item.OrderList {
					if len(itemOrder.OrderDeliveredList) > 0 {
						for _, OrderDeliveredInfo := range itemOrder.OrderDeliveredList {
							var orderRequest v1.HandleOrderRequest
							orderRequest.OrderID = orderData[item.UnionNo].ID
							orderRequest.ExpressNo = OrderDeliveredInfo.ExpNo
							orderRequest.CompanyCode = getSzbaoExpressCode(OrderDeliveredInfo.ExpCode, OrderDeliveredInfo.ExpName, OrderDeliveredInfo.ExpNo)
							for _, packageInfo := range OrderDeliveredInfo.PackageList {
								var orderItemId uint
								for _, orderDetail := range itemOrder.OrderDetailList {
									if packageInfo.OrderDetailId == orderDetail.OrderDetailId {
										var skuCode = orderDetail.SpecCode
										for _, localOrdeItem := range orderData[item.UnionNo].OrderItems {
											if localOrdeItem.Sku.Sn == skuCode {
												orderItemId = localOrdeItem.ID
											}
										}
									}
								}
								if orderItemId == 0 {
									log.Log().Info("永源v2发货失败1", zap.Any("data", item))
									continue
								}

								orderRequest.OrderItemIDs = append(orderRequest.OrderItemIDs, orderRequest2.OrderItemSendInfo{
									ID:  orderItemId,
									Num: uint(packageInfo.DeliveredNum),
								})

							}
							if len(orderRequest.OrderItemIDs) == 0 {
								log.Log().Info("永源v2发货失败", zap.Any("data", item))
								continue
							}
							err = order.ExpressSent(orderRequest)
							if err != nil {
								log.Log().Info("永源v2发货失败2", zap.Any("err", err))
								return
							}
						}
					}
				}

			}
		}
		//log.Log().Info("结束处理供应链" + strconv.Itoa(int(v.ID)) + "的订单")

	}
}
func getSzbaoExpressCode(code string, name string, trackingNumber string) (resultCode string) {
	// 首先尝试使用现有的code
	if code != "" {
		if code == "SXJD" {
			code = "SXJE"
		}
		resultCode = code
		return
	}

	// 如果code为空，尝试通过name匹配
	if strings.Contains(name, "中通") {
		resultCode = "ZTO"
	} else if strings.Contains(name, "圆通") {
		resultCode = "YTO"
	} else if strings.Contains(name, "顺丰") {
		resultCode = "SFEXPRESS"
	} else if strings.Contains(name, "韵达") {
		resultCode = "YUNDA"
	} else if strings.Contains(name, "申通") {
		resultCode = "STO"
	} else if strings.Contains(name, "京东") {
		resultCode = "JD"
	} else if strings.Contains(name, "德邦") {
		resultCode = "DEPPON"
	} else if strings.Contains(name, "邮政") {
		resultCode = "EMS"
	} else if strings.Contains(name, "极兔") {
		resultCode = "JITU"
	} else if strings.Contains(name, "菜鸟") {
		resultCode = "CAINIAO"
	}

	// 如果通过name也无法识别，且有运单号，则尝试通过运单号识别
	if resultCode == "" && trackingNumber != "" {
		log.Log().Info("通过name无法识别快递公司，尝试使用运单号识别",
			zap.String("name", name),
			zap.String("trackingNumber", trackingNumber))

		err, detectedCode := order.DetectExpressCompanyByTrackingNumber(trackingNumber)
		if err == nil && detectedCode != "" {
			resultCode = detectedCode
			log.Log().Info("通过运单号成功识别快递公司",
				zap.String("trackingNumber", trackingNumber),
				zap.String("detectedCode", detectedCode))
		} else {
			log.Log().Warn("通过运单号识别快递公司失败",
				zap.String("trackingNumber", trackingNumber),
				zap.String("name", name),
				zap.Error(err))
		}
	}

	return
}

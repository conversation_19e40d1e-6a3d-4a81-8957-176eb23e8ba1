package cron

import (
	"fmt"
	model2 "public-supply/model"
	"szbao-supply/component/goods"
	"yz-go/cron"
	"yz-go/source"
)

var productOpenV2 *int

func PushGoodsProductHandle() {
	task := cron.Task{
		Key:  "goodsSzbaoProduct",
		Name: "szbao供应链自动更新本地商品",
		Spec: "45 35 */1 * * *",
		Handle: func(task cron.Task) {
			GoodsProductCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func GoodsProductCron() {
	if productOpenV2 != nil {
		fmt.Println("已经在执行中")
		return
	}

	var gatherList []model2.GatherSupply
	if err := source.DB().Where("`category_id` = ?", 3).Find(&gatherList).Error; err != nil || len(gatherList) == 0 {
		productOpenV2 = nil
		fmt.Println("未找到szbao供应链")
		return
	}

	productOpenV2 = new(int)
	*productOpenV2 = 1
	for _, v := range gatherList {
		goodsClass := goods.Szbao{}
		// 初始化基础设置
		if err := goodsClass.InitSetting(v.ID); err != nil {
			continue
		}

		// 同步商品数据
		if err := goodsClass.InitProducts(); err != nil {
			continue
		}
	}

	productOpenV2 = nil
	fmt.Println("szbao本地商品自动更新成功")
}

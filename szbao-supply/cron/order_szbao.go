package cron

import (
	"fmt"
	v1 "order/api/v1"
	orderRequest2 "order/request"
	publicModel "public-supply/model"
	"public-supply/response"
	"szbao-supply/component/order"
	"szbao-supply/model"
	"yz-go/cron"
	"yz-go/source"
)

func PushOrderSzbaoHandle() {
	task := cron.Task{
		Key:  "orderdeliver",
		Name: "永源供应链发货查询定时任务",
		Spec: "0 */1 * * * *",
		Handle: func(task cron.Task) {
			OrderSzbaoCron()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func OrderSzbaoCron() {

	var gatherList []publicModel.GatherSupply
	err := source.DB().Where("`category_id` = ?", 3).Find(&gatherList).Error
	if err != nil {
		return
	}
	fmt.Println("开始处理永源订单自动同步运单号")

	for _, v := range gatherList {
		//log.Log().Info("开始处理供应链" + strconv.Itoa(int(v.ID)) + "的订单")
		orderClass := order.Szbao{}
		err = orderClass.InitSetting(v.ID)
		if err != nil {
			continue
		}
		var orderList []model.Order
		err = source.DB().Preload("SzbaoOrderExpress").Where("`gather_supply_id` = ?", v.ID).Where("`status` = ?", 1).Find(&orderList).Error
		if err != nil {
			return
		}
		var unionIds []string
		var orderData = make(map[string]model.Order)
		for _, order := range orderList {
			if order.SzbaoOrderExpress.ID > 0 {
				unionIds = append(unionIds, order.GatherSupplySN)
				orderData[order.GatherSupplySN] = order
			}
		}
		//log.Log().Info("订单数据", zap.Any("data", orderData))
		//log.Log().Info("待处理订单号", zap.Any("data", unionIds))

		var result []response.SyncOrderExpNoResponse
		err, result = orderClass.SyncOrderExpNoV1(unionIds)
		//log.Log().Info("查询结果", zap.Any("data", result))

		if err != nil {
			return
		}
		if len(result) > 0 {
			for _, item := range result {
				if len(item.ExpNos) > 0 {
					for _, expNo := range item.ExpNos {
						var orderRequest v1.HandleOrderRequest
						orderRequest.OrderID = orderData[item.UnionId].ID
						orderRequest.ExpressNo = expNo
						orderRequest.OrderItemIDs = append(orderRequest.OrderItemIDs, orderRequest2.OrderItemSendInfo{
							ID:  0,
							Num: 0,
						})
						orderRequest.CompanyCode = orderData[item.UnionId].SzbaoOrderExpress.ExpCode
						orderRequest.IsEmpty = 1
						//log.Log().Info("准备提交的数据", zap.Any("data", orderRequest))
						err = order.ExpressSent(orderRequest)
						//log.Log().Info("准备提交的数据1", zap.Any("data", err))

						if err != nil {
							return
						}
					}
				}
			}
		}
		//log.Log().Info("结束处理供应链" + strconv.Itoa(int(v.ID)) + "的订单")

	}
}

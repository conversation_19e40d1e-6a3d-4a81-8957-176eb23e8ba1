package order

import (
	"testing"
)

func TestDetectExpressCompanyByTrackingNumber(t *testing.T) {
	tests := []struct {
		name           string
		trackingNumber string
		expectedCode   string
		expectError    bool
	}{
		// 顺丰速运测试
		{
			name:           "顺丰12位数字",
			trackingNumber: "123456789012",
			expectedCode:   "SFEXPRESS",
			expectError:    false,
		},
		// 圆通速递测试
		{
			name:           "圆通10位数字",
			trackingNumber: "1234567890",
			expectedCode:   "YTO",
			expectError:    false,
		},
		// 中通快递测试
		{
			name:           "中通12位数字",
			trackingNumber: "712345678901",
			expectedCode:   "ZTO",
			expectError:    false,
		},
		// 申通快递测试
		{
			name:           "申通12位数字",
			trackingNumber: "312345678901",
			expectedCode:   "STO",
			expectError:    false,
		},
		// 韵达快递测试
		{
			name:           "韵达13位数字",
			trackingNumber: "1234567890123",
			expectedCode:   "YUNDA",
			expectError:    false,
		},
		// 京东物流测试
		{
			name:           "京东15位数字",
			trackingNumber: "123456789012345",
			expectedCode:   "JD",
			expectError:    false,
		},
		// 极兔速递测试
		{
			name:           "极兔JT开头",
			trackingNumber: "JT123456789012345",
			expectedCode:   "JITU",
			expectError:    false,
		},
		// 错误情况测试
		{
			name:           "空运单号",
			trackingNumber: "",
			expectedCode:   "",
			expectError:    true,
		},
		{
			name:           "无法识别的运单号",
			trackingNumber: "ABC123",
			expectedCode:   "",
			expectError:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err, code := DetectExpressCompanyByTrackingNumber(tt.trackingNumber)
			
			if tt.expectError {
				if err == nil {
					t.Errorf("期望有错误，但没有错误")
				}
			} else {
				if err != nil {
					t.Errorf("不期望有错误，但得到错误: %v", err)
				}
			}
			
			if code != tt.expectedCode {
				t.Errorf("期望快递公司代码 %s，但得到 %s", tt.expectedCode, code)
			}
		})
	}
}

package order

import (
	"fmt"
	"go.uber.org/zap"
	"yz-go/component/log"
)

// ExampleUsage 展示如何使用优化后的快递公司识别功能
func ExampleUsage() {
	// 模拟不同的场景

	// 场景1: 有快递公司代码
	fmt.Println("=== 场景1: 有快递公司代码 ===")
	code1 := getSzbaoExpressCode("ZTO", "中通快递", "712345678901")
	fmt.Printf("输入: code=ZTO, name=中通快递, trackingNumber=712345678901\n")
	fmt.Printf("输出: %s\n\n", code1)

	// 场景2: 没有代码，但有名称
	fmt.Println("=== 场景2: 没有代码，但有名称 ===")
	code2 := getSzbaoExpressCode("", "顺丰速运", "123456789012")
	fmt.Printf("输入: code=, name=顺丰速运, trackingNumber=123456789012\n")
	fmt.Printf("输出: %s\n\n", code2)

	// 场景3: 没有代码，名称也无法识别，使用运单号识别
	fmt.Println("=== 场景3: 没有代码，名称无法识别，使用运单号识别 ===")
	code3 := getSzbaoExpressCode("", "未知快递", "JT123456789012345")
	fmt.Printf("输入: code=, name=未知快递, trackingNumber=JT123456789012345\n")
	fmt.Printf("输出: %s\n\n", code3)

	// 场景4: 都无法识别
	fmt.Println("=== 场景4: 都无法识别 ===")
	code4 := getSzbaoExpressCode("", "未知快递", "UNKNOWN123")
	fmt.Printf("输入: code=, name=未知快递, trackingNumber=UNKNOWN123\n")
	fmt.Printf("输出: %s\n\n", code4)

	// 场景5: 特殊处理 - SXJD转换为SXJE
	fmt.Println("=== 场景5: 特殊处理 - SXJD转换为SXJE ===")
	code5 := getSzbaoExpressCode("SXJD", "顺心捷达", "SX1234567890")
	fmt.Printf("输入: code=SXJD, name=顺心捷达, trackingNumber=SX1234567890\n")
	fmt.Printf("输出: %s\n\n", code5)
}

// getSzbaoExpressCode 这是一个示例函数，展示优化后的逻辑
func getSzbaoExpressCode(code string, name string, trackingNumber string) (resultCode string) {
	// 首先尝试使用现有的code
	if code != "" {
		if code == "SXJD" {
			code = "SXJE"
		}
		resultCode = code
		return
	}

	// 如果code为空，尝试通过name匹配
	if name != "" {
		if contains(name, "中通") {
			resultCode = "ZTO"
		} else if contains(name, "圆通") {
			resultCode = "YTO"
		} else if contains(name, "顺丰") {
			resultCode = "SFEXPRESS"
		} else if contains(name, "韵达") {
			resultCode = "YUNDA"
		} else if contains(name, "申通") {
			resultCode = "STO"
		} else if contains(name, "京东") {
			resultCode = "JD"
		} else if contains(name, "德邦") {
			resultCode = "DEPPON"
		} else if contains(name, "邮政") {
			resultCode = "EMS"
		} else if contains(name, "极兔") {
			resultCode = "JITU"
		} else if contains(name, "菜鸟") {
			resultCode = "CAINIAO"
		}
	}

	// 如果通过name也无法识别，且有运单号，则尝试通过运单号识别
	if resultCode == "" && trackingNumber != "" {
		log.Log().Info("通过name无法识别快递公司，尝试使用运单号识别",
			zap.String("name", name),
			zap.String("trackingNumber", trackingNumber))

		err, detectedCode := DetectExpressCompanyByTrackingNumber(trackingNumber)
		if err == nil && detectedCode != "" {
			resultCode = detectedCode
			log.Log().Info("通过运单号成功识别快递公司",
				zap.String("trackingNumber", trackingNumber),
				zap.String("detectedCode", detectedCode))
		} else {
			log.Log().Warn("通过运单号识别快递公司失败",
				zap.String("trackingNumber", trackingNumber),
				zap.String("name", name),
				zap.Error(err))
		}
	}

	return
}

// contains 辅助函数，检查字符串是否包含子字符串
func contains(s, substr string) bool {
	return len(s) > 0 && len(substr) > 0 && 
		   (s == substr || 
		    (len(s) > len(substr) && 
		     (s[:len(substr)] == substr || 
		      s[len(s)-len(substr):] == substr ||
		      findSubstring(s, substr))))
}

// findSubstring 查找子字符串
func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

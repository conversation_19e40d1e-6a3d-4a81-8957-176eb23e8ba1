package order

import (
	_ "embed"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	v1 "order/api/v1"
	"order/express"
	"regexp"
	model3 "region/model"
	"strings"
	"yz-go/component/log"
	"yz-go/source"
)

func ExpressSent(orderRequest v1.HandleOrderRequest) (err error) {
	err = v1.CallBackSendOrder(orderRequest)
	return
}

type Company struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func ExpressList(name string) (err error, code string) {

	for _, item := range GetCompanyList() {
		if item.Name == name {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(item.Name, name) {
			code = item.Code
			fmt.Println(code)
			return
		} else if strings.Contains(name, item.Name) {
			code = item.Code
			fmt.Println(code)
			return
		}
	}
	return
}

type ExpressName struct {
	Name string `json:"name"`
	NO   string `json:"no"`
}

//func ExpressCode(name string) (err error, code string) {
//
//	var result *stbz.APIResult
//	result, err = stbz.API(
//		stbz.Method.GET,
//		string("/v2/logistic/firms"),
//		map[string]string{},
//		g.Map{},
//	)
//
//	var expressName []ExpressName
//	log.Log().Info("发货获取物流列表", zap.Any("info", result))
//	var jsonData []byte
//	jsonData, err = json.Marshal(result.Data)
//	if err != nil {
//		log.Log().Error("发货解析物流列表错误", zap.Any("err", err))
//		return
//	}
//	err = json.Unmarshal(jsonData, &expressName)
//	if err != nil {
//		log.Log().Error("发货解析物流列表错误1", zap.Any("err", err))
//		return
//	}
//
//	return
//}
var Companies []Company

//go:embed kd.json
var kd string

func GetCompanyList() []Company {
	if Companies == nil {
		// 公司列表
		err := json.Unmarshal([]byte(kd), &Companies)
		if err != nil {
			log.Log().Error(err.Error())
		}
	}
	return Companies
}

type Response struct {
	Code int                    `json:"code"`
	Data []express.OrderExpress `json:"data"`
	Msg  string                 `json:"msg"`
}

type ExpressInfo struct {
	Data struct {
		Info struct {
			Name string `json:"name"`
			No   string `json:"no"`
		} `json:"info"`
	} `json:"data"`
}

type Address struct {
	Consignee   string `json:"consignee"`
	Phone       string `json:"phone"`
	Province    string `json:"province"`
	City        string `json:"city"`
	Area        string `json:"area"`
	Street      string `json:"street"`
	ProvinceId  int    `json:"province_id"`
	CityId      int    `json:"city_id"`
	AreaId      int    `json:"area_id"`
	StreetId    int    `json:"street_id"`
	Description string `json:"description"`
}

//省市区街道匹配
func MatchingRegion(addressRequest Address) (err error, province model3.Region, city model3.Region, county model3.Region, town model3.Region) {
	err = source.DB().Where("name = ?", addressRequest.Province).Where("parent_id = ?", 0).Where("level = ?", 1).First(&province).Error

	if err != nil {
		err = source.DB().Where("name like ?", AddressName(addressRequest.Province)+"%").Where("parent_id = ?", 0).Where("level = ?", 1).First(&province).Error
		if err != nil {
			err = errors.New("省份匹配出错")
			return
		}
	}

	err = source.DB().Where("name = ?", addressRequest.City).Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error

	if err != nil {
		if province.ID == 11 || province.ID == 12 || province.ID == 31 || province.ID == 50 {
			//直辖市
			err = source.DB().Where("name = ?", "市辖区").Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error

		} else {
			err = source.DB().Where("name like ?", AddressName(addressRequest.City)+"%").Where("parent_id = ?", province.ID).Where("level = ?", 2).First(&city).Error
		}
		if err != nil {
			err = errors.New("城市匹配出错")
			return
		}
	}
	//部分区域固定名称
	switch addressRequest.Area {
	case "遵义县":
		addressRequest.Area = "播州区"
		break
	}
	err = source.DB().Where("name = ?", addressRequest.Area).Where("parent_id = ?", city.ID).Where("level = ?", 3).First(&county).Error

	if err != nil {
		err = source.DB().Where("name like ?", AddressName(addressRequest.Area)+"%").Where("parent_id = ?", city.ID).Where("level = ?", 3).First(&county).Error
		if err != nil {
			err = errors.New("区域匹配出错")
			return
		}
	}

	err = source.DB().Where("name = ?", addressRequest.Street).Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error

	if err != nil {
		if addressRequest.Street == "" || addressRequest.Street == "其他" {
			err = source.DB().Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
			if err != nil {
				err = errors.New("街道地址匹配出错")
				return
			}
		} else {
			err = source.DB().Where("name like ?", AddressName(addressRequest.Street)+"%").Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					err = errors.New("街道地址匹配出错")
					return
				} else {
					var counties []model3.Region
					err = source.DB().Where("parent_id = ?", city.ID).Where("level = ?", 3).Find(&counties).Error
					if err != nil {
						err = errors.New("街道地址匹配出错")
						return
					}
					var countyIds []int
					for _, aCounty := range counties {
						countyIds = append(countyIds, aCounty.ID)
					}
					// 尝试匹配城市的所有街道，成功后使用匹配到的town
					err = source.DB().Where("name like ?", AddressName(addressRequest.Street)+"%").Where("parent_id in ?", countyIds).Where("level = ?", 4).First(&town).Error
					if err != nil {
						// 没有匹配到选择第一个街道
						err = source.DB().Where("parent_id = ?", county.ID).Where("level = ?", 4).First(&town).Error
						if err != nil {
							if errors.Is(err, gorm.ErrRecordNotFound) {
								town = model3.Region{
									Name:  "其他",
									Level: 4,
								}
								err = nil
							} else {
								err = errors.New("街道地址匹配出错")
								return
							}
						}
					} else {
						var newCounty model3.Region
						// 使用匹配到的town所属的county
						err = source.DB().Where("id = ?", town.ParentID).Where("level = ?", 3).First(&newCounty).Error
						if err != nil {
							err = errors.New("街道地址匹配出错")
							return
						}
						county = newCounty
					}

				}
			}
		}
	}
	return
}

/**
  如果首次没有查询到地址则使用这个返回没有省市区县的地址 再次进行匹配
*/
func AddressName(name string) (rname string) {
	var names = [...]string{"省", "市", "区", "县", "镇", "街道", "办事处", "乡"}

	for _, x := range names {
		//从右往左检测是否存在字符串
		rname = strings.TrimRight(name, x)
		//如果不相等证明已经裁剪过了
		if rname != name {
			break
		}
	}

	return rname
}

// DetectExpressCompanyByTrackingNumber 根据运单号识别快递公司
// 参数: trackingNumber - 运单号
// 返回: error - 错误信息, code - 快递公司代码
func DetectExpressCompanyByTrackingNumber(trackingNumber string) (error, string) {
	if trackingNumber == "" {
		return fmt.Errorf("运单号不能为空"), ""
	}

	// 去除空格和特殊字符
	trackingNumber = strings.TrimSpace(trackingNumber)
	trackingNumber = strings.ReplaceAll(trackingNumber, " ", "")
	trackingNumber = strings.ReplaceAll(trackingNumber, "-", "")

	// 运单号长度
	length := len(trackingNumber)

	// 顺丰速运 - 12位数字，或者带冒号的格式
	if matched, _ := regexp.MatchString(`^\d{12}$`, trackingNumber); matched {
		return nil, "SFEXPRESS"
	}
	if matched, _ := regexp.MatchString(`^\d{12}:\d{4}$`, trackingNumber); matched {
		return nil, "SFEXPRESS"
	}

	// 圆通速递 - 10位数字，通常以1、2、6、8、D开头
	if length == 10 {
		if matched, _ := regexp.MatchString(`^[1268D]\d{9}$`, trackingNumber); matched {
			return nil, "YTO"
		}
	}
	// 圆通新格式 - 12位数字
	if length == 12 {
		if matched, _ := regexp.MatchString(`^[1268]\d{11}$`, trackingNumber); matched {
			return nil, "YTO"
		}
	}

	// 中通快递 - 12位数字，通常以2、7、6开头
	if length == 12 {
		if matched, _ := regexp.MatchString(`^[276]\d{11}$`, trackingNumber); matched {
			return nil, "ZTO"
		}
	}

	// 申通快递 - 12位数字，通常以3、4、5开头
	if length == 12 {
		if matched, _ := regexp.MatchString(`^[345]\d{11}$`, trackingNumber); matched {
			return nil, "STO"
		}
	}

	// 韵达快递 - 13位数字，通常以1、4开头
	if length == 13 {
		if matched, _ := regexp.MatchString(`^[14]\d{12}$`, trackingNumber); matched {
			return nil, "YUNDA"
		}
	}

	// 百世快递 - 10-12位数字，通常以5、6、7开头
	if length >= 10 && length <= 12 {
		if matched, _ := regexp.MatchString(`^[567]\d{9,11}$`, trackingNumber); matched {
			return nil, "HTKY"
		}
	}

	// 京东物流 - 15位数字，通常以JD开头或者纯数字
	if length == 15 {
		if matched, _ := regexp.MatchString(`^\d{15}$`, trackingNumber); matched {
			return nil, "JD"
		}
	}
	if matched, _ := regexp.MatchString(`^JD\d{13}$`, trackingNumber); matched {
		return nil, "JD"
	}

	// EMS - 13位，通常以E开头，以CN结尾
	if length == 13 {
		if matched, _ := regexp.MatchString(`^E\w{10}CN$`, trackingNumber); matched {
			return nil, "EMS"
		}
	}

	// 德邦快递 - 8-10位数字
	if length >= 8 && length <= 10 {
		if matched, _ := regexp.MatchString(`^\d{8,10}$`, trackingNumber); matched {
			return nil, "DEPPON"
		}
	}

	// 天天快递 - 14位数字，通常以6、5开头
	if length == 14 {
		if matched, _ := regexp.MatchString(`^[65]\d{13}$`, trackingNumber); matched {
			return nil, "TTKDEX"
		}
	}

	// 宅急送 - 10位数字，通常以4开头
	if length == 10 {
		if matched, _ := regexp.MatchString(`^4\d{9}$`, trackingNumber); matched {
			return nil, "ZJS"
		}
	}

	// 优速快递 - 12位数字，通常以1、0开头
	if length == 12 {
		if matched, _ := regexp.MatchString(`^[10]\d{11}$`, trackingNumber); matched {
			return nil, "UC56"
		}
	}

	// 极兔速递 - 17位数字，通常以JT开头
	if matched, _ := regexp.MatchString(`^JT\d{15}$`, trackingNumber); matched {
		return nil, "JITU"
	}

	// 丹鸟快递 - 14位数字
	if length == 14 {
		if matched, _ := regexp.MatchString(`^\d{14}$`, trackingNumber); matched {
			return nil, "DANNIAO"
		}
	}

	// 顺心捷达 - 12位数字，通常以SX开头
	if matched, _ := regexp.MatchString(`^SX\d{10}$`, trackingNumber); matched {
		return nil, "SXJE"
	}

	// 如果都不匹配，尝试通过长度和数字特征进行模糊匹配
	return detectByFuzzyMatch(trackingNumber)
}

package v1

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gorm.io/gorm"
	"user/service"
	yzResponse "yz-go/response"
)

func FindAgentSetting(c *gin.Context) {
	err, setting := service.GetAgentSetting("agent_setting")
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取推广设置失败", c)
		return
	}
	agentSetting := setting.Value
	// 接口不暴露默认邀请码信息
	if agentSetting.ShowDefaultInviteCode != 1 {
		agentSetting.DefaultInviteCode = ""
	}
	err, userSetting := service.GetAgentSetting("user_setting")
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage("获取推广设置失败", c)
		return
	}
	agentSetting.NeedPassword = userSetting.Value.NeedPassword
	yzResponse.OkWithData(gin.H{"setting": agentSetting}, c)
}

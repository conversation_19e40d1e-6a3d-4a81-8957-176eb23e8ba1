package service

import (
	"errors"
	"gorm.io/gorm"
	"strconv"
	"time"
	"user/model"
	"user/mq"
	"user/request"
	"user/response"
	yzService "yz-go/service"
	"yz-go/source"
)

type OrderRecord struct {
	UserID       uint   `json:"user_id" form:"user_id"`
	OrderAmount  uint   `json:"order_amount" form:"order_amount"`
	OrderPayName string `json:"order_pay_name" form:"order_pay_name"`
	ProductID    uint   `json:"product_id" form:"product_id"`
}

func OrderCreate(orderID uint, orderRecord OrderRecord) (err error) {
	if orderRecord.ProductID == 0 {
		return
	}
	// 查询商品绑定的会员等级
	var userLevel model.UserLevel
	err, userLevel = GetLevelByProductID(orderRecord.ProductID)
	if err != nil || userLevel.ID == 0 {
		return
	}
	// 查询会员
	var user model.User
	err = source.DB().Preload("UserLevel").Where("id = ?", orderRecord.UserID).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	var purchaseRecord model.UserLevelPurchaseRecord
	purchaseRecord.Uid = orderRecord.UserID
	purchaseRecord.BeforeLevelID = user.LevelID
	if user.LevelID == 0 {
		purchaseRecord.BeforeLevelName = "默认等级"
	} else {
		purchaseRecord.BeforeLevelName = user.UserLevel.Name
	}
	purchaseRecord.AfterLevelID = userLevel.ID
	purchaseRecord.AfterLevelName = userLevel.Name
	purchaseRecord.BeforeValidityAt = user.ValidityAt
	purchaseRecord.PurchaseCode = 1
	purchaseRecord.OrderID = orderID
	purchaseRecord.OrderAmount = orderRecord.OrderAmount
	purchaseRecord.OrderStatusName = "待支付"

	// 等级相同续费, 否则升级
	if user.LevelID == userLevel.ID {
		purchaseRecord.PurchaseType = 2

	} else {
		// 有效期变更为新等级设置的有效期
		purchaseRecord.PurchaseType = 1
	}
	if userLevel.ExpireDays == 0 {
		userLevel.ExpireDays = 3650
	}
	if purchaseRecord.PurchaseType == 1 {
		date, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
		// 有效期相加
		purchaseRecord.AfterValidityAt = &source.LocalTime{Time: date.AddDate(0, 0, userLevel.ExpireDays)}
	} else {
		at := user.ValidityAt
		if user.ValidityAt == nil {
			at = &source.LocalTime{Time: time.Now()}
		}
		date, _ := time.Parse("2006-01-02", at.Format("2006-01-02"))
		// 有效期相加
		purchaseRecord.AfterValidityAt = &source.LocalTime{Time: date.AddDate(0, 0, userLevel.ExpireDays)}
	}
	err = source.DB().Create(&purchaseRecord).Error
	return
}

func OrderPaid(orderID uint, orderRecord OrderRecord) (err error) {
	// 查询开通记录, 没有退出
	var purchaseRecord model.UserLevelPurchaseRecord
	err = source.DB().Where("order_id = ?", orderID).First(&purchaseRecord).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if purchaseRecord.ID == 0 {
		return
	}
	// 查询会员
	var user model.User
	err = source.DB().Where("id = ?", purchaseRecord.Uid).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	// 修改记录状态
	purchaseRecord.OrderStatusName = "已支付"
	purchaseRecord.OrderStatus = 1
	purchaseRecord.OrderPayName = orderRecord.OrderPayName
	var userLevel model.UserLevel
	err, userLevel = GetUserLevel(purchaseRecord.AfterLevelID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if userLevel.ExpireDays == 0 {
		userLevel.ExpireDays = 3650
	}
	if purchaseRecord.PurchaseType == 1 {
		date, _ := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
		// 有效期相加
		purchaseRecord.AfterValidityAt = &source.LocalTime{Time: date.AddDate(0, 0, userLevel.ExpireDays)}
	} else {
		at := user.ValidityAt
		if user.ValidityAt == nil {
			at = &source.LocalTime{Time: time.Now()}
		}
		date, _ := time.Parse("2006-01-02", at.Format("2006-01-02"))
		// 有效期相加
		purchaseRecord.AfterValidityAt = &source.LocalTime{Time: date.AddDate(0, 0, userLevel.ExpireDays)}
	}
	err = source.DB().Save(&purchaseRecord).Error
	if err != nil {
		return
	}
	user.LevelID = userLevel.ID
	// 修改会员有效期
	user.IsPermanent = 0
	user.ValidityAt = &source.LocalTime{Time: purchaseRecord.AfterValidityAt.Time}
	err = source.DB().Save(&user).Error
	if err != nil {
		return
	}
	// 发送升级消息
	err = mq.PublishMessage(purchaseRecord.OrderID, purchaseRecord.Uid, userLevel.ID, mq.Upgrade)

	yzService.CreateOperationRecord(0, 9, "0", "会员等级升级,会员名称'"+user.Username+"',会员等级id:"+strconv.Itoa(int(purchaseRecord.BeforeLevelID))+"变更为会员等级id:"+strconv.Itoa(int(purchaseRecord.AfterLevelID))+"")

	return
}

func OrderRefunded(orderID uint) (err error) {
	// 修改记录状态
	// 查询开通记录, 没有退出
	var purchaseRecord model.UserLevelPurchaseRecord
	err = source.DB().Where("order_id = ?", orderID).First(&purchaseRecord).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return
	}
	if purchaseRecord.ID == 0 {
		return
	}
	purchaseRecord.OrderStatusName = "已退款"
	purchaseRecord.OrderStatus = -1
	err = source.DB().Save(&purchaseRecord).Error
	return
}

func GetPurchaseRecordList(info request.PurchaseRecordSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Preload("User").Model(&response.UserLevelPurchaseRecord{})
	var records []response.UserLevelPurchaseRecord
	if info.Uid != 0 {
		db = db.Where("`uid` = ?", info.Uid)
	}
	if info.Mobile != "" {
		var userIds []uint
		err = source.DB().Model(&response.User{}).Where("username like ?", "%"+info.Mobile+"%").Or("mobile like ?", "%"+info.Mobile+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			db = db.Where("`uid` = ?", 0)
		} else {
			db = db.Where("`uid` in ?", userIds)
		}
	}
	if info.Nickname != "" {
		var userIds []uint
		err = source.DB().Model(&response.User{}).Where("nick_name LIKE ?", "%"+info.Nickname+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			db = db.Where("`uid` = ?", 0)
		} else {
			db = db.Where("`uid` in ?", userIds)
		}
	}
	if info.ParentMobile != "" {
		var userIds []uint
		err = source.DB().Model(&response.User{}).Where("username LIKE ?", "%"+info.ParentMobile+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db = db.Where("`uid` = ?", 0)
		} else {
			var uids []uint
			err = source.DB().Model(&response.User{}).Where("parent_id in ?", userIds).Pluck("id", &uids).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
			if len(uids) == 0 {
				db = db.Where("`uid` = ?", 0)
			} else {
				db = db.Where("`uid` in ?", uids)
			}
		}
	}
	if info.BeforeLevelID != 0 {
		db = db.Where("`before_level_id` = ?", info.BeforeLevelID)
	}
	if info.AfterLevelID != 0 {
		db = db.Where("`after_level_id` = ?", info.AfterLevelID)
	}
	// 开通前默认等级搜索
	if info.BeforeDefaultLevel == true {
		db = db.Where("before_level_id = 0")
	}
	// 开通后会默认等级搜索
	if info.AfterDefaultLevel == true {
		db = db.Where("after_level_id = 0")
	}
	if info.StartAt != "" {
		db.Where("`created_at` >= ?", info.StartAt)
	}
	if info.EndAt != "" {
		db.Where("`created_at` <= ?", info.EndAt)
	}
	if info.PurchaseType != 0 {
		db = db.Where("`purchase_type` = ?", info.PurchaseType)
	}
	if info.PurchaseCode != 0 {
		db = db.Where("`purchase_code` = ?", info.PurchaseCode)
	}
	if info.OrderStatus != 0 {
		db = db.Where("`order_status` = ?", info.OrderStatus)
	}
	err = db.Count(&total).Error
	err = db.Order("id desc").Limit(limit).Offset(offset).Find(&records).Error
	return err, records, total
}

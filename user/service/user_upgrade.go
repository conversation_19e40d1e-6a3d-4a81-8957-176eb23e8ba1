package service

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"strconv"
	"user/model"
	register_mq "user/register-mq"
	"user/request"
	"user/setting"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/source"
)

func AutoUpgrade(userId uint) (err error) {

	var user model.User
	//查出会员等级id
	err = source.DB().Where("id = ?", userId).First(&user).Error
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		return nil
	}

	var level model.UserLevel
	//查出会员当前等级信息
	if user.LevelID > 0 {
		err = source.DB().Where("id = ?", user.LevelID).First(&level).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error(err.<PERSON>(), zap.Any("err", err))
			return nil
		}
	} else {
		level.ID = 0
		level.UpdatedAt = &source.LocalTime{}
		level.Discount = 10000
		level.Level = 0
		level.Name = "默认等级"
	}

	//如果不自动升级，则不继续执行
	if level.IsUpdate == 1 {
		return
	}
	var levelAll []model.UserLevel
	//查出全部等级信息
	err = source.DB().Where("level > ?", level.Level).Order("level asc").Find(&levelAll).Error
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		return nil
	}

	var userSetting setting.SysSetting
	//查出会员设置信息
	err = source.DB().Where("`key` = ?", "user_setting").First(&userSetting).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		log.Log().Error(err.Error(), zap.Any("err", err))
		return nil
	}
	var orderStatus int
	if userSetting.Value.UpgradeTiming == 1 {
		orderStatus = 1 //付款后的订单
	} else {
		orderStatus = 3 //完成后的订单
	}
	//遍历所有会员等级，如果满足条件逐个升级
	for _, v := range levelAll {

		switch userSetting.Value.UpgradeType {
		case 1:
			var productIds []uint
			err = source.DB().Model(&model.UpgradeProduct{}).Where("`level_id` = ?", v.ID).Pluck("product_id", &productIds).Error
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("err", err))
				break
			}

			var orderIds []uint

			err = source.DB().Model(&model.Order{}).Where("`user_id` = ?", userId).Where("status = ?", orderStatus).Pluck("id", &orderIds).Error
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("err", err))
				break
			}

			var orderItems []model.OrderItem
			err = source.DB().Where("`order_id` in ?", orderIds).Where("product_id in ?", productIds).Find(&orderItems).Error
			if err == nil {
				//执行升级程序
				err = ExecUpgrade(user.ID, userSetting.Value.UpgradeType, v.ID)
				if err != nil {
					log.Log().Error("会员"+strconv.Itoa(int(user.ID))+"升级到等级"+strconv.Itoa(int(v.ID))+"失败："+err.Error(), zap.Any("err", err))
					break
				}
			}

		case 2:
			var orderData model.OrderStatistic
			err = source.DB().Model(&model.Order{}).Select("COUNT(1) as total_count").Where("`user_id` = ?", userId).Where("status >= ?", orderStatus).First(&orderData).Error
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("err", err))
				break
			}
			if v.UpgradeOrderCount <= orderData.TotalCount {
				//执行升级程序
				err = ExecUpgrade(user.ID, userSetting.Value.UpgradeType, v.ID)
				if err != nil {
					log.Log().Error("会员"+strconv.Itoa(int(user.ID))+"升级到等级"+strconv.Itoa(int(v.ID))+"失败："+err.Error(), zap.Any("err", err))
					break
				}
			}

		case 3:
			var orderData model.OrderStatistic
			err = source.DB().Model(&model.Order{}).Select("SUM(amount) as total_amount").Where("`user_id` = ?", userId).Where("status >= ?", orderStatus).First(&orderData).Error
			if err != nil {
				log.Log().Error(err.Error(), zap.Any("err", err))
				break
			}
			if v.UpgradeOrderAmount*100 <= orderData.TotalAmount {
				//执行升级程序
				err = ExecUpgrade(user.ID, userSetting.Value.UpgradeType, v.ID)
				if err != nil {
					log.Log().Error("会员"+strconv.Itoa(int(user.ID))+"升级到等级"+strconv.Itoa(int(v.ID))+"失败："+err.Error(), zap.Any("err", err))
					break
				}
			}

		}
	}

	return
}

func ExecUpgrade(userId uint, upType int, levelObjective uint) (err error) {
	err = source.DB().Transaction(func(tx *gorm.DB) error {
		var user model.User
		err = tx.Where("`id` = ?", userId).First(&user).Error
		if err != nil {
			return err
		}

		if user.LevelID == levelObjective {
			return nil
		}
		var level model.UserLevel
		if user.LevelID != 0 {
			err = tx.Preload("UpgradeProduct").Where("`id` = ?", user.LevelID).First(&level).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
		}

		var levelUp model.UserLevel
		// 如果升级等级ID大于0，则查出升级等级信息
		if levelObjective > 0 {
			err = tx.Preload("UpgradeProduct").First(&levelUp, levelObjective).Error
			if err != nil {
				return err
			}

		} else { // 如果升级等级ID等于0，则修改为默认等级
			levelUp.ID = 0
			levelUp.Name = "默认等级"
		}

		err = tx.Model(&model.User{}).Where("id = ?", userId).Update("level_id", levelUp.ID).Error
		if err != nil {
			return err
		}

		var record model.UserUpgradeRecord

		record.UpgradeType = upType
		if upType == 0 {
			record.UpgradeCode = 2
		} else {
			record.UpgradeCode = 1
		}

		record.UserID = userId
		record.BeforeLevelID = level.ID
		record.AfterLevelID = levelUp.ID
		record.BeforeLevelData = model.LevelData{
			Name:               level.Name,
			Level:              level.Level,
			ExpireDays:         level.ExpireDays,
			Discount:           level.Discount,
			FreightDeduction:   level.FreightDeduction,
			Description:        level.Description,
			UpgradeOrderCount:  level.UpgradeOrderCount,
			UpgradeOrderAmount: level.UpgradeOrderAmount,
			UpgradeProduct:     level.UpgradeProduct,
		}
		record.AfterLevelData = model.LevelData{
			Name:               levelUp.Name,
			Level:              levelUp.Level,
			ExpireDays:         levelUp.ExpireDays,
			Discount:           levelUp.Discount,
			FreightDeduction:   levelUp.FreightDeduction,
			Description:        levelUp.Description,
			UpgradeOrderCount:  levelUp.UpgradeOrderCount,
			UpgradeOrderAmount: levelUp.UpgradeOrderAmount,
			UpgradeProduct:     levelUp.UpgradeProduct,
		}
		err = tx.Create(&record).Error
		if err != nil {
			return err
		}
		cache.ClearUser(userId)
		err = register_mq.PublishMessage(user.ID, 2, "会员id："+strconv.Itoa(int(user.ID))+";升级成为新等级："+levelUp.Name)

		return nil
	})
	return err
}

func HandUpgrade(data request.UserUpgradeOrDownGrade) error {

	err := ExecUpgrade(data.UserID, 0, data.LevelObjective)
	if err != nil {
		return err
	}
	return err
}

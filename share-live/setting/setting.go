package setting

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"gorm.io/gorm"
	"yz-go/model"
	"yz-go/source"
	"yz-go/utils"
)

type SysSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}
type Value struct {
	IsOpen     int    `mapstructure:"is_open" json:"is_open" yaml:"is_open"`             //状态1开始0关闭
	IsCallback int64  `mapstructure:"is_callback" json:"is_callback" yaml:"is_callback"` //状态是否自动创建回调 1是0否
	SecretId   string `mapstructure:"secret_id" json:"secret_id" yaml:"secret_id"`       //SecretId
	SecretKey  string `mapstructure:"secret_key" json:"secret_key" yaml:"secret_key"`    //SecretKey

	PlugFlowUrl string `mapstructure:"plug_flow_url" json:"plug_flow_url" yaml:"plug_flow_url"` //推流域名
	PlugFlowKey string `mapstructure:"plug_flow_key" json:"plug_flow_key" yaml:"plug_flow_key"` //推流防盗key
	PlayUrl     string `mapstructure:"play_url" json:"play_url" yaml:"play_url"`                //播放域名
	PlayKey     string `mapstructure:"play_key" json:"play_key" yaml:"play_key"`                //播放防盗key

	AppId        string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`                      //通讯sdkappid
	ImAppKey     string `mapstructure:"im_app_key" json:"im_app_key" yaml:"im_app_key"`          //ImAppKey
	ImAppSecret  string `mapstructure:"im_app_secret" json:"im_app_secret" yaml:"im_app_secret"` //ImAppSecret
	SerialNumber string `mapstructure:"serial_number" json:"serial_number" yaml:"serial_number"` //序号

	TemplateId     int64  `mapstructure:"template_id" json:"template_id" yaml:"template_id"`                //回调模板id //如果有值代表已经创建的回调模板
	IsCallbackRule int64  `mapstructure:"is_callback_rule" json:"is_callback_rule" yaml:"is_callback_rule"` //回调规格是否创建成功 1是 其他否
	CallbackKey    string `mapstructure:"callback_key" json:"callback_key" yaml:"callback_key"`             //回调验证KEY
	IsRtc          int64  `mapstructure:"is_rtc" json:"is_rtc" yaml:"is_rtc"`                               //是否开启低延时直播 1是 0否

}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func GetSysShareLiveSetting() (err error, sysSetting SysSetting) {
	var key = "share_live"
	err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
	return
}

// 判断是否开启了直播
func GetIsOpenShareLiveSetting() (isOpen int) {
	var shareLiveSettingData SysSetting
	var err error
	if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(30) == true || utils.LocalEnv() != true {
		err, shareLiveSettingData = GetSysShareLiveSetting()
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			shareLiveSettingData.Value.IsOpen = 0
		}
	} else {
		shareLiveSettingData.Value.IsOpen = 0
	}
	return shareLiveSettingData.Value.IsOpen
}

func SaveSysShareLiveSetting(data SysSetting) (err error) {
	if data.Value.TemplateId == 0 {
		_, sysSetting := GetSysShareLiveSetting()
		if sysSetting.ID != 0 {
			data.ID = sysSetting.ID
			data.Value.TemplateId = sysSetting.Value.TemplateId
			data.Value.IsCallbackRule = sysSetting.Value.IsCallbackRule
			data.Value.CallbackKey = sysSetting.Value.CallbackKey
		}
	}
	if data.ID != 0 {
		err = source.DB().Updates(&data).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	return err
}

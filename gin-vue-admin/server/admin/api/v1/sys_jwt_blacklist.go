package v1

import (
	"gin-vue-admin/admin/model"
	"gin-vue-admin/admin/service"
	"yz-go/component/log"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	yzResponse "yz-go/response"
)

// @Tags Jwt
// @Summary jwt加入黑名单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"拉黑成功"}"
// @Router /jwt/jsonInBlacklist [post]
func JsonInBlacklist(c *gin.Context) {
	token := c.Request.Header.Get("x-token")
	jwt := model.JwtBlacklist{Jwt: token}
	if err := service.JsonInBlacklist(jwt); err != nil {
		log.Log().Error("jwt作废失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("jwt作废失败", c)
		return
	} else {
		yzResponse.OkWithMessage("jwt作废成功", c)
	}
}

package middleware

import (
	"gin-vue-admin/admin/model/request"
	"gin-vue-admin/admin/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"yz-go/component/log"
	"yz-go/config"
	yzResponse "yz-go/response"
)

// 拦截器
func CasbinHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		claims, _ := c.Get("claims")
		waitUse := claims.(*request.CustomClaims)
		// 获取请求的URI
		obj := c.Request.URL.RequestURI()
		// 获取请求方法
		act := c.Request.Method
		// 获取用户的角色
		sub := waitUse.AuthorityId
		e,err := service.Casbin()
		if err != nil {
			log.Log().Error("权限读取失败!", zap.Any("err", err))
			yzResponse.FailWithDetailed(gin.H{}, "权限读取失败", c)
			c.Abort()
			return
		}
		// 判断策略中是否存在
		success, _ := e.Enforce(sub, obj, act)
		if  config.Config().System.Env == "develop" || success {
			c.Next()
		}
		//else {
		//	yzResponse.FailWithDetailed(gin.H{}, "权限不足", c)
		//	c.Abort()
		//	return
		//}
	}
}

import {
  createAdChannel,
  deleteAdChannel,
  deleteAdChannelByIds,
  updateAdChannel,
  findAdChannel,
  getAdChannelList,
} from "@/api/adChannel";
export default {
  name: "channelSetting",
  data() {
    return {
      tableList: [],
      page: 1,
      pageSize: 10,
      total: 0,
      dialogTitle: "新增",
      isShow: false,
      formData: {
        title: "",
        desc: "",
      },
      // 表单验证
      rules: {
        title: { required: true, message: "请输入标题", trigger: "blur" },
        desc: { required: true, message: "请输入子标题", trigger: "blur" },
      },
    };
  },
  mounted() {
    this.fetch();
    if (this.$route.query.addChannel) {
      this.openDialog("add");
    }
  },
  methods: {
    handleSizeChange(size) {
      this.pageSize = size;
      this.fetch();
    },
    handleCurrentChange(page) {
      this.page = page;
      this.fetch();
    },
    fetch() {
      getAdChannelList({ page: this.page, pageSize: this.pageSize }).then(
        (res) => {
          if (res.code === 0) {
            this.tableList = res.data.list;
            this.total = res.data.total;
          } else {
            this.tableList = [];
            this.total = 0;
          }
        }
      );
    },
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
    },
    // 删除
    deletChannelDialog(item) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.deletChannel(item);
      }).catch(() => { });
    },

    deletChannel(item) {
      let para = {
        id: item.id
      }
      deleteAdChannel(para).then((res) => {
        if (res.code === 0) {
          this.$message.success("删除成功");
          this.fetch();
        }
      });
    },
    /*************************  dialog部分  ***************************/
    openDialog(type, row = {}) {
      this.isShow = true;
      this.$nextTick(() => {
        switch (type) {
          case "add":
            this.dialogTitle = "新增";
            break;
          case "edit":
            this.dialogTitle = "编辑";
            this.setFrom(row);
            break;
        }
      });
    },
    handleClose(isRefresh) {
      this.isShow = false;
      if (this.formData.id) {
        delete this.formData.id;
      }
      if (this.$route.query.addChannel) {
        this.$router.push({ query: {} });
      }
      this.$refs.form.resetFields();

      if (isRefresh) {
        this.fetch();
      }
    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 修改
          if (this.formData.id) {
            updateAdChannel({ ...this.formData }).then((res) => {
              if (res.code === 0) {
                this.$message.success("编辑成功");
                this.handleClose(true);
              }
            });
          } else {
            // 添加
            createAdChannel({ ...this.formData }).then((res) => {
              if (res.code === 0) {
                this.$message.success("新增成功");
                this.handleClose(true);
              }
            });
          }
        } else {
          return false;
        }
      });
    },
  },
};
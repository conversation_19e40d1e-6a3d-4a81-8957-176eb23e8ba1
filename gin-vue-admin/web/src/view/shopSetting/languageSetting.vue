<template>
  <div>
    <el-row>
      <el-form :model="formData.language_setting" label-width="100px">
        <el-col :span="13">
          <el-form-item label="批发价:">
            <el-input v-model="formData.language_setting.price"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="超级批发价:">
            <el-input v-model="formData.language_setting.super_wholesal_price"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="建议零售价:">
            <el-input v-model="formData.language_setting.suggested_retail_price"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item>
            <el-button type="primary" @click="save">保 存</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script>
import shopSetting from "@/mixins/shopSetting";

export default {
  name: "languageSetting",
  mixins: [shopSetting]
}
</script>

<style scoped>

</style>
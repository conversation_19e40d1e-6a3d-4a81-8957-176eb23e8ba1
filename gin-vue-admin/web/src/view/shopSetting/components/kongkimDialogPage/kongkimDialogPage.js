import { mapGetters } from "vuex";
import {getCollectionList} from "@/api/collection";
import { createWapDiamondArea,updateWapDiamondArea } from "@/api/shopSetting"

export default {
    data() {
        return {
            title: '',
            path: this.$path,
            dialogVisible: false,
            formData: {
                title: '',
                logo: '',

                h5_url: '', // h5跳转链接

                mini_url_type: 1, // 小程序跳转链接
                mini_collection_id: null, // 商品专辑
                mini_url: '', // 内部链接 或 第三链接
                mini_id: '', //  第三方小程序APPID

                app_url_type: 1, // APP跳转链接
                app_collection_id: null, // 商品专辑 -- APP
                app_url: '', // 内部链接 或 外部链接 -- APP
                app_id: '', // 第三方小程序标识 -- APP

                sort: null, // 排序
            },
            appOptions: [], // 商品专辑

            rules: {
                title: { required: true, message: "请输入金刚区名称", trigger: "blur" },
                logo:  { required: true, message: "请上传图标", trigger: "blur" },
                h5_url: { required: true, message: "请输入H5跳转链接", trigger: "blur" },
                mini_url_type: { trigger: "change",required: true,validator: (rule, value, callback) => {
                    if (value === 1) {
                        if (!this.formData.mini_collection_id) {
                            callback(new Error('请选择商品专辑'));
                        }
                        else {
                            callback();
                        }
                    }
                    else if (value === 2) {
                        if (!this.formData.mini_url) {
                            callback(new Error('请填写内部链接'));
                        }
                        else {
                            callback();
                        }
                    }
                    else if(value === 3) {
                        if (!this.formData.mini_id || !this.formData.mini_url) {
                            callback(new Error('请填写APPID和路径'));
                        }
                        else {
                            callback();
                        }
                    }
                }},
                app_url_type: {trigger: "change", required: true,validator: (rule, value, callback) => {
                    if (value === 1) {
                        if (!this.formData.app_collection_id) {
                            callback(new Error('请选择商品专辑'));
                        }
                        else {
                            callback();
                        }
                    }
                    else if (value === 2) {
                        if (!this.formData.app_url) {
                            callback(new Error('请填写内部链接'));
                        }
                        else {
                            callback();
                        }
                    }
                    else if(value === 3) {
                        if (!this.formData.app_url || !this.formData.app_id) {
                            callback(new Error('请填写小程序 gh_开头的原始 id和路径'));
                        }
                        else {
                            callback();
                        }
                    }
                }}
            }
        }
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),
    },
    methods: {
        // 新增
        info() {
            this.dialogVisible = true;
            this.title = '新增';
            this.getCollectionList()
        },
        // 编辑
        edit(row) {
            this.formData = row
            this.dialogVisible = true;
            this.title = '编辑';
            this.getCollectionList()
        },
        // 关闭
        handleClose() {
            this.dialogVisible = false;
            this.title = '';
            this.formData = {
                title: '',
                logo: '',

                h5_url: '', // h5跳转链接

                mini_url_type: 1, // 小程序跳转链接
                mini_collection_id: null, // 商品专辑
                mini_url: '', // 内部链接 或 第三方小程序链接
                mini_id: '', //  第三方小程序APPID

                app_url_type: 1, // APP跳转链接
                app_collection_id: null, // 商品专辑 -- APP
                app_url: '', // 内部链接 或 外部链接 -- APP
                app_id: '', // 第三方小程序标识 -- APP

                sort: null, // 排序
            }
            this.appOptions = [] // 商品专辑
        },
        // 保存
        save() {
            this.$refs.form.validate(async valid => {
                if (!valid) return false;
                // 编辑保存
                if (this.title === '编辑') {
                    let {code,msg} = await updateWapDiamondArea(this.formData)
                    if (code === 0) {
                        this.$message.success(msg)
                        this.$emit('editSave')
                    }
                } 
                // 新增保存
                else {
                    this.formData.sort = parseInt(this.formData.sort)
                    let {code,msg} = await createWapDiamondArea(this.formData)
                    if (code === 0) {
                        this.$message.success(msg)
                        this.$emit('addSave')
                    }
                }
                this.handleClose()
                
            })
        },
        // 获取商品专辑
        getCollectionList() {
            getCollectionList({ page: 1, pageSize: 9999 }).then((res) => {
                if (res.code == 0) {
                    this.appOptions = res.data.list;
                }
            })
        },
        // 小程序跳转链接 切换清空数据
        radioChange() {
            this.formData.mini_collection_id = null;
            this.formData.mini_url = '';
            this.formData.mini_id = '';
        },
        // APP跳转链接 切换清空数据
        radioChange1() {
            this.formData.app_collection_id = null;
            this.formData.app_url = '';
            this.formData.app_id = '';
        },
        // 图片
        handleBannerImgSuccess(res) {
            this.formData.logo = res.data.file.url;
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
    }
}
<template>
  <el-drawer :title="`${formData.id ? '编辑' : '新增'}移动端首页列表`" :visible="isShow" :close-on-press-escape="false"
             :wrapperClosable="false"
             :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <el-row>
      <el-form :model="formData" label-width="100px" :rules="rules" ref="form">
        <el-col :span="13">
          <el-form-item label="排序:" prop="sort">
            <m-num-input v-model="formData.sort" placeholder="请输入排序"></m-num-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="显示名称:" prop="title">
            <el-input v-model="formData.title" placeholder="请输入显示名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="图标:" prop="title">
            <el-upload class="avatar-uploader" :show-file-list="false"
                :action="path + '/fileUploadAndDownload/upload'" :headers="{ 'x-token': token }"
                :on-success="handleBannerImgSuccess" :before-upload="$fn.beforeAvatarUpload"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                <img v-if="formData.logo" :src="formData.logo" class="avatar w100" />
                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
        </el-col>
        <el-col :span="13">
          <el-form-item label="数据类型:" prop="data_type">
            <el-radio-group v-model="formData.data_type">
              <el-radio :label="1">商品</el-radio>
              <el-radio :label="2">共享商品专辑</el-radio>
              <el-radio :label="3">供应商</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <!-- 选择商品显示以下数据 -->
        <el-col v-if="formData.data_type === 1">
          <el-col :span="16">
            <el-form-item label="商品数据:" prop="type">
              <el-radio-group v-model="formData.type">
                <el-radio :label="0">全部商品</el-radio>
                <el-radio :label="1">指定分类</el-radio>
                <el-radio :label="2">营销属性</el-radio>
                <el-radio :label="3">指定专辑</el-radio>
                <el-radio :label="4">指定供应商</el-radio>
                <el-radio :label="5">指定供应链</el-radio>
                <el-radio :label="6">指定共享商品专辑</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="13" v-if="formData.type === 1">
            <div class="f fac" style="padding-left: 90px;">
              <el-form-item label-width="0" prop="category1_id" class="f1">
                <el-select v-model="formData.category1_id" class="w100" placeholder="请选择一级分类"
                          @change="getCategory(2,formData.category1_id)" clearable>
                  <el-option v-for="item in category1List" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <div style="width: 10px;"></div>
              <el-form-item label-width="0" prop="category2_id" class="f1">
                <el-select v-model="formData.category2_id" class="w100" placeholder="请选择二级分类"
                          @change="getCategory(3,formData.category2_id)" clearable>
                  <el-option v-for="item in category2List" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <div style="width: 10px;"></div>
              <el-form-item label-width="0" prop="category2_id" class="f1">
                <el-select v-model="formData.category3_id" class="w100" placeholder="请选择三级分类" clearable
                          @change="getCategory(4,formData.category3_id)">
                  <el-option v-for="item in category3List" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="13" v-if="formData.type === 2">
            <el-form-item>
              <el-radio-group v-model="formData.marketing">
                <el-radio :label="0">热卖</el-radio>
                <el-radio :label="1">促销</el-radio>
                <el-radio :label="2">新品</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="13" v-if="formData.type === 3">
            <el-form-item prop="collection_id">
              <el-select v-model="formData.collection_id" class="f1" placeholder="请选择专辑" clearable>
                <el-option v-for="item in collectionList" :key="item.id" :label="item.title" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 指定供应商 -->
          <el-col :span="13" v-if="formData.type === 4">
            <el-form-item prop="supplier_id">
              <el-select v-model="formData.supplier_id" class="fl" placeholder="请选择供应商" clearable>
                <!-- <el-option label="全部" :value="null"></el-option>
                <el-option label="平台自营" :value="0"></el-option>
                <el-option label="全部供应商" :value="999999"></el-option> -->
                <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 指定供应链 -->
          <el-col :span="13" v-if="formData.type === 5">
            <el-form-item prop="gather_supply_id">
              <el-select v-model="formData.gather_supply_id" class="fl" placeholder="请选择供应链" clearable>
                <el-option v-for="item in supplyList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 指定共享商品专辑 -->
          <el-col :span="13" v-if="formData.type === 6">
            <el-form-item prop="share_collection_id">
              <el-select v-model="formData.share_collection_id" class="fl" placeholder="请选择共享商品专辑" clearable>
                <el-option v-for="item in albumList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-col>
        <!-- 选择共享商品专辑显示以下数据 -->
        <el-col v-if="formData.data_type === 2">
          <el-col :span="13">
            <el-form-item label="专辑数据:" prop="share_collection_type">
              <el-radio-group v-model="formData.share_collection_type">
                <el-radio :label="1">全部专辑</el-radio>
                <el-radio :label="2">指定标签</el-radio>
                <el-radio :label="3">指定专辑</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
           <!-- 指定标签 -->
          <el-col :span="13" v-if="formData.share_collection_type === 2">
            <el-form-item prop="share_collection_tags">
              <el-select v-model="formData.share_collection_tags" class="fl" placeholder="请选择指定标签" multiple clearable>
                <el-option v-for="item in tagList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 指定专辑 -->
          <el-col :span="13" v-if="formData.share_collection_type === 3">
            <el-form-item prop="share_collection_ids">
              <el-select v-model="formData.share_collection_ids" class="fl" placeholder="请选择指定专辑" multiple clearable>
                <el-option v-for="item in albumList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 共享商品的专辑排序 -->
          <el-col :span="13">
            <el-form-item label="专辑排序:" prop="share_collection_sort">
              <el-radio-group v-model="formData.share_collection_sort">
                <el-radio :label="1">按创建时间</el-radio>
                <el-radio :label="2">按累计销量</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>
        <!-- 选择供应商显示以下数据 -->
        <el-col v-if="formData.data_type === 3">
          <el-col :span="13">
            <el-form-item label="供应商数据:" prop="supplier_type">
              <el-radio-group v-model="formData.supplier_type">
                <el-radio :label="1">全部</el-radio>
                <el-radio :label="2">指定分类</el-radio>
                <el-radio :label="3">指定供应商</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- 指定分类 -->
          <el-col :span="13" v-if="formData.supplier_type === 2">
            <!-- <div class="f fac" style="padding-left: 90px;">
              <el-form-item label-width="0" prop="category1_id" class="f1">
                <el-select v-model="formData.category1_id" class="w100" placeholder="请选择一级分类"
                          @change="getCategory(2,formData.category1_id)" clearable>
                  <el-option v-for="item in category1List" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <div style="width: 10px;"></div>
              <el-form-item label-width="0" prop="category2_id" class="f1">
                <el-select v-model="formData.category2_id" class="w100" placeholder="请选择二级分类"
                          @change="getCategory(3,formData.category2_id)" clearable>
                  <el-option v-for="item in category2List" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <div style="width: 10px;"></div>
              <el-form-item label-width="0" prop="category2_id" class="f1">
                <el-select v-model="formData.category3_id" class="w100" placeholder="请选择三级分类" clearable
                          @change="getCategory(4,formData.category3_id)">
                  <el-option v-for="item in category3List" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </div> -->
            <el-form-item prop="supplier_category_id">
              <el-select v-model="formData.supplier_category_id" class="fl" placeholder="请指定分类" clearable>
                <el-option v-for="item in supplierCategoryList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 指定供应商 -->
          <el-col :span="13" v-if="formData.supplier_type === 3">
            <el-form-item prop="supplier_id">
              <el-select v-model="formData.supplier_id" class="fl" placeholder="请选择供应商" clearable>
                <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 供应商的专辑排序 -->
          <el-col :span="13">
            <el-form-item label="专辑排序:" prop="supplier_sort_type">
              <el-radio-group v-model="formData.supplier_sort_type">
                <el-radio :label="1">按创建时间</el-radio>
                <el-radio :label="2">按累计销量</el-radio>
                <el-radio :label="3">按商品数量</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-col>
        <el-col :span="13">
          <el-form-item>
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="handleClose">取 消</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </el-drawer>
</template>

<script>
import { mapGetters } from "vuex";
import {getCategoryListWithParentId} from "@/api/category";
import {getCollectionList} from "@/api/collection";
import {createWapHomeSetting, updateWapHomeSetting} from "@/api/shopSetting";
import {isNumber} from "@antv/util";
import {getSupplierOptionList,} from "@/api/goods"
import {getSupplyList} from "@/api/order"
import {getAlbumOption, getAllTag} from "@/api/shareAlbum"
import {getSupplierCategoryList} from "@/api/adminSupplierQualification"
export default {
  name: "mobileHomeListDrawer",
  data() {
    return {
      isShow: false,
      path: this.$path,
      formData: {
        sort: 0,
        title: "",
        status: 0,//开启状态0开启 1关闭
        type: 0,//0全部商品，1指定分类，2营销属性，3指定专辑，4指定供应商，5指定供应链，6指定共享商品专辑
        category1_id: null,
        category2_id: null,
        category3_id: null,
        marketing: 0,//营销属性0热卖1促销2新品 默认热卖
        collection_id: null, // 专辑id
        data_type: 1, //数据类型
        supplier_id: null, // 供应商id
        gather_supply_id: null, // 供应链id
        share_collection_id: null, // 共享商品专辑id
        share_collection_type: 1, // 专辑数据
        share_collection_sort: 1, // 专辑排序
        supplier_type: 1, // 供应商数据
        supplier_sort_type: 1, // 供应商专辑排序
        share_collection_tags: [], // 指定标签
        share_collection_ids: [], //指定专辑
        supplier_category_id: null, //指定分类id
      },
      category1List: [],
      category2List: [],
      category3List: [],
      collectionList: [], // 专辑数据列表
      supplierList: [], // 供应商数据列表
      supplyList: [], // 供应链数据列表
      albumList: [], // 共享商品专辑数据列表
      tagList: [], // 标签数据列表
      supplierCategoryList: [], // 指定标签列表
      rules: {
        sort: {required: true, message: "请输入排序", trigger: "blur"},
        title: {required: true, message: "请输入显示名称", trigger: "blur"},
        type: {required: true, message: "请选择商品数据", trigger: "blur"},
        data_type: { required: true, message: "请选择数据类型", trigger: "blur" },
        share_collection_type: { required: true, message: "请选择专辑数据", trigger: "blur" },
        share_collection_sort: { required: true, message: "请选择专辑排序", trigger: "blur" },
        supplier_type: { required: true, message: "请选择供应商数据", trigger: "blur" },
        supplier_sort_type: { required: true, message: "请选择专辑排序", trigger: "blur" },
      }
    }
  },
  computed: {
      ...mapGetters("user", ["userInfo", "token"]),
  },
  methods: {
    init(row) {
      row.category1_id = row.category1_id ? row.category1_id : null;
      row.category2_id = row.category2_id ? row.category2_id : null;
      row.category3_id = row.category3_id ? row.category3_id : null;
      row.collection_id = row.collection_id ? row.collection_id : null;
      row.supplier_id = row.supplier_id ? row.supplier_id : null;
      row.supplier_category_id = row.supplier_category_id ? row.supplier_category_id : null;
      row.gather_supply_id = row.gather_supply_id ? row.gather_supply_id : null;
      row.share_collection_id = row.share_collection_id ? row.share_collection_id : null;
      this.formData = this.$fn.deepClone(row)
      this.initCategory(row.category1_id, row.category2_id, row.category3_id)
    },
    async initCategory(c1, c2, c3) {
      let {data} = await getCategoryListWithParentId()
      this.category1List = data.list
      if (c2) {
        let {data} = await getCategoryListWithParentId({parent_id: c1})
        this.category2List = data.list
      }
      if (c3) {
        let {data} = await getCategoryListWithParentId({parent_id: c2})
        this.category3List = data.list
      }
    },
    // 提交
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false
        // 数据类型为商品
        switch (this.formData.type) {
          case 1: // 指定分类
            if (!this.formData.category1_id && !this.formData.category2_id && !this.formData.category3_id) {
              this.$message.error("分类至少选择一级")
              return false;
            }
            break;
          case 3: // 指定专辑
            if (!this.formData.collection_id) {
              this.$message.error("请选择专辑")
              return false;
            }
            break;
          case 4: // 指定供应商
            if(!this.formData.supplier_id) {
              this.$message.error("请选择供应商")
              return false;
            }
            break;
          case 5: // 指定供应链
            if(!this.formData.gather_supply_id) {
              this.$message.error("请选择供应链")
              return false;
            }
            break;
          case 6: // 指定共享商品专辑id
            if(!this.formData.share_collection_id) {
              this.$message.error("请选择共享商品专辑")
              return false;
            }
            break;
        }
        // 数据类型为共享商品专辑
        switch (this.formData.share_collection_type) {
          case 2:
            if(!this.formData.share_collection_tags.length) {
              this.$message.error("请选择指定标签")
              return false;
            }
            break;
          case 3:
            if(!this.formData.share_collection_ids.length) {
              this.$message.error("请选择指定专辑")
              return false;
            }
            break;

        }
        // 数据类型为供应商
        switch (this.formData.supplier_type) {
          case 2: // 指定分类
            if (!this.formData.supplier_category_id) {
              this.$message.error("请指定分类")
              return false;
            }
            break;
          case 3: // 指定供应商
            if(!this.formData.supplier_id) {
              this.$message.error("请选择供应商")
              return false;
            }
            break;
          default:
            break;
        }
        // 选择多个标签和专辑时进行数据转换
        this.formData.share_collection_tags = this.formData.share_collection_tags.join(',')
        this.formData.share_collection_ids = this.formData.share_collection_ids.join(',')
        if (this.formData.id) {
          updateWapHomeSetting(this.formData).then(({code, msg}) => {
            if (code === 0) {
              this.$message.success(msg)
              this.handleClose()
              this.$emit("reload")
            }
          })
        } else {
          createWapHomeSetting(this.formData).then(({code, msg}) => {
            if (code === 0) {
              this.$message.success(msg)
              this.handleClose()
              this.$emit("reload")
            }
          })
        }
      })
    },
    // 关闭抽屉
    handleClose() {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.formData = {
          sort: 0,
          title: "",
          status: 0,//开启状态0开启 1关闭
          type: 0,//0全部商品，1指定分类，2营销属性，3指定专辑，4指定供应商，5指定供应链，6指定共享商品专辑
          category1_id: null,
          category2_id: null,
          category3_id: null,
          marketing: 0,//营销属性0热卖1促销2新品 默认热卖
          collection_id: null, // 专辑id
          data_type: 1, // 数据类型
          supplier_id: null, // 供应商id
          gather_supply_id: null, // 供应链id
          share_collection_id: null, // 共享商品专辑id
          share_collection_type: 1, // 专辑数据
          share_collection_sort: 1, // 专辑排序
          supplier_type: 1, // 供应商数据
          supplier_sort_type: 1, // 供应商专辑排序
          share_collection_tags: [], //指定标签
          share_collection_ids: [], //指定专辑
          supplier_category_id: null, //指定分类id
        }
        this.category1List = []
        this.category2List = []
        this.category3List = []
        this.collectionList = []
        this.supplierList = []
        this.supplyList = []
        this.albumList = []
      }
    },
    // 获取分类
    async getCategory(level = 1, pid = 0) {
      if (isNumber(pid)) {
        const {data} = await getCategoryListWithParentId({parent_id: pid})
        switch (level) {
          case 1:
            this.category1List = []
            this.formData.category1_id = null
            this.category2List = []
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            this.category1List = data.list
            break;
          case 2:
            this.category2List = []
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            this.category2List = data.list
            break;
          case 3:
            this.category3List = []
            this.formData.category3_id = null
            this.category3List = data.list
            break;
        }
      } else {
        level = level - 1
        switch (level) {
          case 1:
            this.formData.category1_id = null
            this.category2List = []
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            break;
          case 2:
            this.formData.category2_id = null
            this.category3List = []
            this.formData.category3_id = null
            break;
          case 3:
            this.formData.category3_id = null
            break;
        }
      }
    },
    // 获取专辑
    async getAlbum() {
      let {code, data} = await getCollectionList()
      if (code === 0) {
        this.collectionList = data.list
      }
    },
    // 获取供应商
    async getSupplier() {
      const { code, data } = await getSupplierOptionList()
      if (code === 0) {
        this.supplierList = data.list
      }
    },
    // 获取供应链
    async getSupply() {
      const { code, data } = await  getSupplyList()
      if (code === 0) {
        this.supplyList = data.list
      }
    },
    // 获取共享商品专辑
    async getAlbumOption() {
      const { code, data } = await getAlbumOption()
      if (code === 0) {
        this.albumList = data.list
      }
    },
    // 获取所有标签
    async getAllTag() {
      const { code, data } = await getAllTag()
      if (code === 0) {
        this.tagList = data.tags
      }
    },
    // 获取指定分类
    async getSupplierCategory() {
      const { code, data } = await getSupplierCategoryList()
      if (code === 0) {
        this.supplierCategoryList = data.list
      }
    },
    // 数据转换
    async changeData() {
      if(this.formData.data_type === 2 && this.formData.share_collection_type === 2) {
        // 将获取的字符串数据转换成数组
        const tags = this.formData.share_collection_tags.split(',') 
        // 获取所有标签
        const { code, data } = await getAllTag()
        if (code === 0) {
          // 当id相同是将name字段进行回显
          const newTags = data.tags.filter(item => tags.includes(String(item.id))).map(item => item.name)
          this.formData.share_collection_tags = newTags
        }
        this.formData.share_collection_ids = []
      } else if(this.formData.data_type === 2 && this.formData.share_collection_type === 3) {
        // 将获取的字符串数据转换成数组
        const ids = this.formData.share_collection_ids.split(',')
        // 获取所有共享商品专辑
        const { code, data } = await getAlbumOption()
        if (code === 0) {
          // 当id相同是将name字段进行回显
          const newIds = data.list.filter(item => ids.includes(String(item.id))).map(item => item.name)
          this.formData.share_collection_ids = newIds
        }
        this.formData.share_collection_tags= []
      } else {
        this.formData.share_collection_ids = []
        this.formData.share_collection_tags= []
      }
    },
    // 图片
    handleBannerImgSuccess(res) {
        this.formData.logo = res.data.file.url;
    },
    beforeAvatarUpload(file) {
        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
            this.$message.error("上传头像图片大小不能超过 10MB!");
        }
        return isLt10M;
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer {
  .el-drawer__body {
    background-color: #fff !important;
  }
}
::v-deep .avatar-uploader {
    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 150px;
        height: 150px;
        .avatar-uploader-icon {
            font-size: 28px;
            color: #8c939d;

            line-height: 150px;
            text-align: center;
        }
    }
}
</style>
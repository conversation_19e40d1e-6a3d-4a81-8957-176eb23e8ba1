import { getUserLevelListNotPage,findAgentSetting,updateAgentSetting } from "@/api/listNotPage";
export default {
    name: "promotionSetting",
    data() {
        return {
            // 推广权限列表数据
            userLevel: [],
            formData: {
                id:null,
                // 推广权限
                level_id: null,
                default_invite_code: "",//默认邀请码
                show_default_invite_code:"",//是否显示默认邀请码
                required_invite_code:"",//邀请码必填
                lock_condition:"",//锁定客户的条件
                show_agent_button: 0,
            },
            // rules:{
            //     //required 验证必填  message提示语 trigger验证形势 -> 1. change 2 blur就这两种
            //     level_id:{required:true,message:"请选择推广权限",trigger:"change"}
            // }
        }
    },
    mounted() {
        this.getListNotPage()
        this.findSetting()

    },
    methods: {
        getListNotPage(){
            getUserLevelListNotPage().then(res => {
              this.userLevel=res.data.list
            })
        },
        findSetting(){
            findAgentSetting().then(res =>{
                let data=res.data.setting.value
                this.formData.id =res.data.setting.id
                this.formData.level_id=data.level_id || null
                this.formData.default_invite_code=data.default_invite_code
                this.formData.show_default_invite_code=data.show_default_invite_code
                this.formData.required_invite_code=data.required_invite_code
                this.formData.lock_condition=data.lock_condition
                this.formData.show_agent_button=data.show_agent_button
            })
        },
        save(){
            let param={
                id:this.formData.id,
                key:"",
                value:{
                    level_id:this.formData.level_id,
                    show_default_invite_code:this.formData.show_default_invite_code,
                    required_invite_code:this.formData.required_invite_code,
                    lock_condition:this.formData.lock_condition,
                    default_invite_code:this.formData.default_invite_code,
                    show_agent_button: this.formData.show_agent_button
                },
            }
            updateAgentSetting(param).then(res =>{
                if(res.code === 0){
                    this.$message.success(res.msg)
                }
            })
        },

        }



}
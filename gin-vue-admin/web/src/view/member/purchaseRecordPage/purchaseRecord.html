<m-card>
    <el-form :model="formData" ref="form" class="search-term mt25" label-width="120px" inline>
        <el-form-item>
            <div class="line-input line-input-date">
                <div class="line-box" style="min-width: auto; padding: 0px 10px;">
                    <span>开通时间</span>
                </div>
                <div class="f fac">
                    <el-date-picker 
                        class="w100" 
                        value-format="yyyy-MM-dd HH:mm:ss" 
                        v-model="formData.start_at"
                        clearable 
                        placeholder="开始时间"
                    ></el-date-picker>
                    <p class="title-3">至</p>
                    <el-date-picker 
                        class="w100" 
                        value-format="yyyy-MM-dd HH:mm:ss" 
                        v-model="formData.end_at"
                        clearable placeholder="截止时间"
                    ></el-date-picker>
                </div>
            </div>
        </el-form-item>
        <br>
        <el-form-item>
            <el-input placeholder="请输入" v-model="formData.uid" class="line-input" clearable>
                <span slot="prepend">会员ID</span>
            </el-input>
        </el-form-item>
        <el-form-item>
            <el-input placeholder="请输入" v-model="formData.mobile" class="line-input" clearable>
                <span slot="prepend">手机号</span>
            </el-input>
        </el-form-item>
        <el-form-item>
            <el-input placeholder="请输入" v-model="formData.nickname" class="line-input" clearable>
                <span slot="prepend">会员昵称</span>
            </el-input>
        </el-form-item>
        <el-form-item>
            <el-input placeholder="请输入" v-model="formData.parent_mobile" class="line-input" clearable>
                <span slot="prepend">推荐人</span>
            </el-input>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >开通前会员等级</span>
                </div>
                <el-select v-model="formData.before_level_id" clearable class="w100">
                    <el-option :value="item.id" v-for="item in optionList" :key="item.id" :label="item.name">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >开通后会员等级</span>
                </div>
                <el-select v-model="formData.after_level_id" clearable class="w100">
                    <el-option :value="item.id" v-for="item in optionList" :key="item.id" :label="item.name">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >开通类型</span>
                </div>
                <el-select v-model="formData.purchase_type" clearable class="w100">
                    <el-option :value="item.value" v-for="item in typeName" :key="item.value" :label="item.name">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >支付状态</span>
                </div>
                <el-select v-model="formData.order_status" clearable class="w100">
                    <el-option :value="item.value" v-for="item in statusName" :key="item.value" :label="item.name">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item label="">
            <div class="line-input" >
                <div class="line-box ">
                    <span >开通方式</span>
                </div>
                <el-select v-model="formData.purchase_code" clearable class="w100">
                    <el-option :value="item.value" v-for="item in codeName" :key="item.value" :label="item.name">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <br>
        <el-form-item>
            <el-button type="primary" @click="search" >搜索</el-button>
            <el-button type="text" @click="onReset">重置搜索条件</el-button>
        </el-form-item>
    </el-form>
    <el-table :data="tableData" class="mt25">
        <el-table-column label="开通时间" align="center">
            <template slot-scope="scope">
                <span>{{ scope.row.created_at | formatDate }}</span>
            </template>
        </el-table-column>
        <el-table-column label="会员ID" prop="user.id" align="center"></el-table-column>
        <el-table-column label="头像" align="center">
            <template slot-scope="scope">
                <m-image :src="scope.row.user.avatar" style="width: 40px;height: 40px;"></m-image>
            </template>
        </el-table-column>
        <el-table-column align="center" width="150"  show-overflow-tooltip>
            <template slot="header">
                <p>昵称</p>
                <p>手机号</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.user.nickname }}</p>
                <p>{{ scope.row.user.username }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center" width="150"  show-overflow-tooltip>
            <template slot="header">
                <p>开通前会员等级</p>
                <p>开通后会员等级</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.before_level_name }}</p>
                <p>{{ scope.row.after_level_name }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center" width="150"  show-overflow-tooltip>
            <template slot="header">
                <p>开通前有效日期</p>
                <p>开通后有效日期</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.before_validity }}</p>
                <p>{{ scope.row.after_validity }}</p>
<!--                <p v-if="scope.row.before_validity_at">{{ scope.row.before_validity_at | formatDate }}</p>-->
<!--                <p v-if="scope.row.after_validity_at">{{ scope.row.after_validity_at | formatDate }}</p>-->
            </template>
        </el-table-column>
        <el-table-column label="开通方式" align="center">
            <template slot-scope="scope">
                {{ scope.row.purchase_code | formatCode }}
            </template>
        </el-table-column>
        <el-table-column label="开通类型" align="center">
            <template slot-scope="scope">
                {{ scope.row.purchase_type | formatType }}
            </template>
        </el-table-column>
        <el-table-column label="支付金额（元）/激活码" align="center">
            <template slot-scope="scope">   
                <p>{{ scope.row.order_amount | formatF2Y }}</p>
                <p>{{ scope.row.activation_code }}</p>
            </template>
        </el-table-column>
        <el-table-column align="center" width="150"  show-overflow-tooltip>
            <template slot="header">
                <p>支付状态</p>
                <p>支付方式</p>
            </template>
            <template slot-scope="scope">
                <p>{{ scope.row.order_status_name }}</p>
                <p>{{ scope.row.order_pay_name }}</p>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right" align="center">
            <template slot-scope="scope" v-if="scope.row.order_status === 1 ">
                <el-button type="text" @click="onRefund(scope.row.order_id)">退款</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination 
        background 
        :current-page="page" 
        :page-size="pageSize" 
        :page-sizes="[10, 30, 50, 100]" 
        :style="{
            display: 'flex',
            justifyContent: 'flex-end',
            marginRight: '20px',
        }" 
        :total="total" 
        @current-change="handleCurrentChange" 
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>

</m-card>
<template>
  <m-card>
    <!-- <el-button type="primary" @click="showDialog">新增商品池</el-button> -->
    <el-form :model="searchForm" class="search-term mt25" label-width="100px" inline>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchForm.name" class="line-input" clearable>
              <span slot="prepend">商品池名称</span>
          </el-input>
      </el-form-item>
        <el-form-item>
          <el-input placeholder="请输入" v-model="searchForm.user_name" class="line-input" clearable>
              <span slot="prepend">会员</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >状态</span>
              </div>
              <el-select v-model="searchForm.status" placeholder="请输入" class="w100">
                <el-option label="启用" :value="1"></el-option>
                <el-option label="禁用" :value="0"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" style="width: 500px">
              <div class="line-box ">
                  <span >创建时间</span>
              </div>
              <m-daterange v-model="date"></m-daterange>
          </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button type="text" @click="reset">重置搜索条件</el-button>
        <el-button type="text" @click="orderAmount">订单金额补正</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableList" style="margin-top: 20px;">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="商品池名称" prop="name" align="center"></el-table-column>
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">{{ scope.row.created_at | formatDate }} </template>
      </el-table-column>
      <el-table-column label="会员数" prop="user_count" align="center"></el-table-column>
      <el-table-column label="商品数量" align="center">
        <template slot-scope="scope">
          <div v-if="scope.row.product_count_switch">
            <p>{{ scope.row.product_count }}</p>
            <el-button type="text" :loading="scope.row.loading"
                       @click="clickToView(scope.row,scope.$index)">刷新
            </el-button>
          </div>
          <el-button v-else type="text" :loading="scope.row.loading" @click="clickToView(scope.row,scope.$index)">
            点击查看
          </el-button>
          <!-- <el-button type="text" @click="copyLink(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">复制</el-button> -->
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            @change="handleStatus(scope.row.status, scope.row.id)"
            :active-value="1"
            :inactive-value="0"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="270">
        <template slot-scope="scope">
          <div class="f fac fjc">
            <el-button type="text" @click="copyLink(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">复制</el-button>
            <el-button type="text" @click="edit(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
            <el-button type="text" @click="exportData(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">导出</el-button>
            <el-button type="text" class="color-red" @click="delDialog(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="pagination"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
    <el-dialog title="新增" :visible.sync="isShow" width="30%" :before-close="close">
      <el-form :model="addForm" label-width="120px">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="商品池名称:">
              <el-input v-model="addForm.name" placeholder="请输入"></el-input>
            </el-form-item>
          </el-col>
          <el-col :offset="11" :span="13">
            <el-form-item>
              <el-button type="primary" @click="addBtn">确 定</el-button>
              <el-button @click="close">取 消</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
    <el-dialog class="delDIalog" :visible.sync="isDel" width="20%">
      <p>是否删除当前商品池</p>
      <el-button class="delBtn" type="danger" @click="del">确 定</el-button>
      <el-button @click="cancelDel">取 消</el-button>
    </el-dialog>
    <el-dialog :visible.sync="isExoprt" width="25%">
      <el-form :model="exportForm" label-width="80px">
        <el-form-item label="导出时间:">
          <m-daterange v-model="exportForm.exportDate"></m-daterange>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="exTable">确 定</el-button>
        <el-button @click="isExoprt = false">取 消</el-button>
      </span>
    </el-dialog>
  </m-card>
</template>

<script>
import MDaterange from '@/components/mDate/daterange'
import {confirm} from "@/decorators/decorators";
import {
  getThousandPricesList,
  createThousandPrices,
  updateMange,
  delMange,
  copyMange,
  exportMange,
  productCount,
  execOrderFix
} from '@/api/thousandPrice'
export default {
  name: 'thousandPriceMangeIndex',
  components: { MDaterange },
  data() {
    return {
      addForm: {
        name: '',
      },
      searchForm: {
        name: '', // 商品池名称
        user_name: '', // 会员
        status: null, // 状态
        start_time: '', // 开始时间
        end_time: '', // 结束时间
      }, // 搜索列表
      isShow: false, // 弹出框显示或隐藏
      date: [], // 创建时间数组
      tableList: [], // table列表数组
      page: 1, // 当前页面
      pageSize: 10, // 每页条数
      total: null, // 总数
      isDel: false, // 是否删除弹层
      delId: '', // 删除的ID
      isExoprt: false, // 是否导出弹层
      exportId: '', // 导出ID
      exportForm: {
        exportDate: [], // 导出时间
      }
    }
  },
  mounted() {
    this.getThousandPricesList()
  },
  methods: {
    // 点击查看
    async clickToView(row, index) {
      this.$set(this.tableList[index], 'loading', true)
      let res = await productCount({id: row.id})
      this.$set(this.tableList[index], 'loading', false)
      if (res.code === 0) {
        let product_count = res.data.count ?? "";
        this.$set(this.tableList[index], 'product_count', product_count)
        this.$set(this.tableList[index], 'product_count_switch', true)
      }
    },
    // 新增商品池
    showDialog() {
      this.isShow = true
    },
    // 确认新增
    async addBtn() {
      if (this.addForm.name) {
        const res = await createThousandPrices(this.addForm)
        if (res.code === 0) {
          this.$message.success('创建成功')
          this.getThousandPricesList()
        }
        this.close()
      } else {
        this.$message.error('请填写商品池名称')
      }
    },
    // 取消新增
    close() {
      this.isShow = false
      this.addForm.name = ''
    },
    // 获取列表
    async getThousandPricesList() {
      const params = {
        page: this.page,
        pageSize: this.pageSize,
        ...this.searchForm,
      }
      const res = await getThousandPricesList(params)
      if (res.code === 0) {
        this.tableList = []
        res.data.list.forEach(element => {
          this.tableList.push({...element,product_count_switch: false})
        });
        this.total = res.data.total
      }
    },
    // 搜索
    async search() {
      if (this.date !== null) {
        this.searchForm.start_time = this.date[0]
        this.searchForm.end_time = this.date[1]
      } else {
        this.searchForm.start_time = ''
        this.searchForm.end_time = ''
      }
      this.page = 1
      this.getThousandPricesList()
    },
    // 重置
    reset() {
      this.searchForm = {}
      this.date = []
    },
    // 改变状态
    async handleStatus(status, id) {
      const params = {
        id,
      }
      const res = await updateMange(params)
      if (res.code === 0) {
        this.$message.success('修改成功')
        this.getThousandPricesList()
      }
    },
    // 复制
    async copyLink(id) {
      const params = {
        id,
      }
      const res = await copyMange(params)
      if (res.code === 0) {
        this.$message.success('复制成功')
        this.getThousandPricesList()
      }
    },
    // 编辑
    edit(id) {
      // 页面跳转
      this.$router.push({ path: `/layout/thousandPriceIndex/thousandPriceBaseIndex?id=${id}` })
    },
    // 导出
    exportData(id) {
      this.exportId = id
      this.isExoprt = true
      this.exportForm.exportDate = []
    },
    async exTable() {
      const params = {
        id :parseInt(this.exportId),
        start_time: this.exportForm.exportDate ? this.exportForm.exportDate[0] : null,
        end_time: this.exportForm.exportDate ? this.exportForm.exportDate[1]: null,
      }
      const res = await exportMange(params)
      if (res.code === 0) {
        this.$message.success('导出成功')
        this.isExoprt = false
        // this.download(res.data.link)
      }
    },
    // 导出方法
    download(link) {
      window.open(this.$path + '/' + link)
    },
    // 删除弹层
    delDialog(id) {
      this.isDel = true
      this.delId = id
    },
    // 删除
    async del() {
      const params = {
        id: this.delId,
      }
      const res = await delMange(params)
      if (res.code === 0) {
        this.$message.success('删除成功')
        this.getThousandPricesList()
        this.cancelDel()
      }
    },
    // 取消删除
    cancelDel() {
      this.isDel = false
    },
    // 改变每页条数
    handleSizeChange(val) {
      this.pageSize = val
      this.page = 1
      this.getThousandPricesList()
    },
    // 改变页码
    handleCurrentChange(val) {
      this.page = val
      this.getThousandPricesList()
    },
    // 订单金额补正
    @confirm("提示","执行此操作前请联系客服，请勿擅自点击此按钮， 确认继续执行此操作吗?")
    async orderAmount() {
      const res = await execOrderFix()
      if (res.code  === 0) {
        this.$messgae.success(res.msg)
      }
    },
  },
}
</script>

<style scoped lang="scss">
.pagination {
  float: right;
}
.color-red {
  color: red;
}
.delDIalog {
  text-align: center;
  p {
    color: red;
  }
  .delBtn {
    margin-top: 40px;
  }
}
</style>

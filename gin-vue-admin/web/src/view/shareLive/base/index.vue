<template>
    <m-card>
        <el-tabs v-model="activeName">
            <el-tab-pane label="基础设置" name="baseSetting">
                <Setting ref="baseSetting"/>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>
<script>
import Setting from "./setting.vue"

export default {
    name: "shareLiveBaseIndex",
    components: {
        Setting,
    },
    data() {
        return {
            activeName: "baseSetting"
        }
    },
    mounted() {
        this.initializesComponentData()
    },
    methods: {
        initializesComponentData() {
            this.$refs[this.activeName].init()
        }
    }
}
</script>
<style scoped lang="scss"></style>
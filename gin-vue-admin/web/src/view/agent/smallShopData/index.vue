<template>
    <m-card>
        <el-form :model="formData" label-width="80px" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.uid" class="line-input" clearable>
                    <span slot="prepend">会员ID</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.mobile" class="line-input" clearable>
                    <span slot="prepend">手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.nickname" class="line-input" clearable>
                    <span slot="prepend">昵称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入小商店名称" v-model="formData.small_shop_name" class="line-input" clearable>
                    <span slot="prepend">小商店</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">
                    搜索
                </el-button>
            </el-form-item>
            <el-form-item>
                <el-button @click="exportList">导出</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reset">
                    重置搜索条件
                </el-button>
            </el-form-item>
        </el-form>
        <el-table :data="optionList">
            <el-table-column
                label="ID"
                prop="id"
                align="center"
            ></el-table-column>
            <el-table-column
                label="会员ID"
                prop="uid"
                align="center"
            ></el-table-column>
            <el-table-column label="昵称/手机号" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.user_info.nickname }}</div>
                    <div>{{ scope.row.user_info.username }}</div>
                </template>
            </el-table-column>
            <el-table-column
                label="小商店"
                prop="small_shop.title"
                align="center"
            ></el-table-column>
            <el-table-column label="直推小商店数量" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.team_small_shop_count }}</div>
                    <el-button
                        type="text"
                        @click.native="
                            toTarget(
                                '/layout/agentIndex/smallShopPushSmallShopIndex',
                                scope.row.uid,
                            )
                        "
                        >查看</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </m-card>
</template>

<script>
import { getInsSmallShopList, exportInsSmallShop } from '@/api/agent';

export default {
    name: 'smallShopDataIndex',

    data() {
        return {
            formData: {
                uid: null, // 会员ID
                mobile: '', // 手机号
                nickname: '', // 昵称
                small_shop_name: '', // 小商店名称
            },
            optionList: [], // 列表数据
            page: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: null, // 总数
        };
    },

    mounted() {
        this.getInsSmallShopList();
    },

    methods: {
        // 获取小商店数据列表
        async getInsSmallShopList() {
            const params = {
                page: this.page,
                pageSize: this.pageSize,
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                small_shop_name: this.formData.small_shop_name,
            };
            const res = await getInsSmallShopList(params);
            if (res.code === 0) {
                this.optionList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getInsSmallShopList();
        },
        // 导出列表
        async exportList() {
            this.search();
            const params = {
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                small_shop_name: this.formData.small_shop_name,
            };
            if (this.total === 0) {
                this.$message.error('请选择需要导出的数据');
                return;
            }
            const res = await exportInsSmallShop(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.download(res.data.link);
            }
        },
        // 导出方法
        download(link) {
            window.open(this.$path + '/' + link);
        },
        // 重置搜索条件
        reset() {
            this.formData = {};
            this.search();
        },
        // 页面跳转
        toTarget(name, uid) {
            this.$router.push({ path: name, query: { uid: uid } });
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getInsSmallShopList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getInsSmallShopList();
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination {
    float: right;
}
</style>

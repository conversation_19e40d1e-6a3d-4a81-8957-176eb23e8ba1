<template>
    <m-card>
        <el-form :model="formData" label-width="80px" inline>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.uid" class="line-input" clearable>
                    <span slot="prepend">会员ID</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.mobile" class="line-input" clearable>
                    <span slot="prepend">手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input placeholder="请输入" v-model="formData.nickname" class="line-input" clearable>
                    <span slot="prepend">昵称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >支付状态</span>
                    </div>
                    <el-select
                        class="w100"
                        v-model="formData.order_status"
                        clearable
                    >
                        <el-option label="待支付" :value="1"></el-option>
                        <el-option label="已支付" :value="2"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" style="width: 500px">
                    <div class="line-box ">
                        <span >创建时间</span>
                    </div>
                    <m-daterange v-model="formData.date"></m-daterange>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">
                    搜索
                </el-button>
            </el-form-item>
            <el-form-item>
                <el-button @click="exportList">导出</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reset">
                    重置搜索条件
                </el-button>
            </el-form-item>
        </el-form>
        <el-table :data="activateList">
            <el-table-column label="时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column
                label="会员ID"
                prop="uid"
                align="center"
            ></el-table-column>
            <el-table-column label="昵称/手机号" align="center">
                <template slot-scope="scope">
                    <div>{{ scope.row.user_info.nickname }}</div>
                    <div>{{ scope.row.user_info.username }}</div>
                </template>
            </el-table-column>
            <el-table-column label="支付金额(元)" align="center">
                <template slot-scope="scope">
                    {{ scope.row.order_amount | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column label="支付状态" align="center">
                <template slot-scope="scope">
                    {{ scope.row.order_status === 1 ? '待支付' : '已支付' }}
                </template>
            </el-table-column>
            <el-table-column
                label="支付方式"
                prop="order_pay_type_name"
                align="center"
            ></el-table-column>
        </el-table>
        <el-pagination
            class="pagination"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
        >
        </el-pagination>
    </m-card>
</template>

<script>
import MDaterange from '@/components/mDate/daterange';
import { getInsActivateList, exportInsActivate } from '@/api/agent';

export default {
    name: 'activationRecordsIndex',
    components: { MDaterange },

    data() {
        return {
            formData: {
                uid: null, // 会员ID
                mobile: '', // 手机号
                nickname: '', // 昵称
                order_status: null, // 支付状态
                date: [], // 创建时间
            },
            activateList: [], // 数据列表
            page: 1, // 当前页数
            pageSize: 10, // 每页条数
            total: null, // 总数
        };
    },

    mounted() {
        this.getInsActivateList();
    },

    methods: {
        async getInsActivateList() {
            const params = {
                page: this.page,
                pageSize: this.pageSize,
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                order_status: this.formData.order_status,
                start_time: !this.formData.date ? [] : this.formData.date[0],
                end_time: !this.formData.date ? [] : this.formData.date[1],
            };
            const res = await getInsActivateList(params);
            if (res.code === 0) {
                this.activateList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getInsActivateList();
        },
        // 导出列表
        async exportList() {
            this.search();
            const params = {
                uid: this.formData.uid,
                mobile: this.formData.mobile,
                nickname: this.formData.nickname,
                order_status: this.formData.order_status,
                start_time: !this.formData.date ? [] : this.formData.date[0],
                end_time: !this.formData.date ? [] : this.formData.date[1],
            };
            if (this.total === 0) {
                this.$message.error('请选择需要导出的数据');
                return;
            }
            const res = await exportInsActivate(params);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.download(res.data.link);
            }
        },
        // 导出方法
        download(link) {
            window.open(this.$path + '/' + link);
        },
        // 重置搜索
        reset() {
            this.formData = {};
            this.search();
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getInsActivateList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getInsActivateList();
        },
    },
};
</script>

<style lang="scss" scoped>
.pagination {
    float: right;
}
</style>

<template>
  <m-card>
    <el-form :model="searchInfo" ref="form" class="search-term mt25" label-width="120px" inline>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.uid" class="line-input" clearable>
              <span slot="prepend">会员ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.member" class="line-input" clearable>
              <span slot="prepend">会员用户名/手机号</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >分销商等级</span>
            </div>
            <el-select v-model="searchInfo.level_id" class="w100" filterable clearable>
              <el-option :value="item.id" v-for="item in optionList" :key="item.id" :label="item.name"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="订单号" v-model="searchInfo.order_sn" class="line-input" clearable>
              <span slot="prepend">订单号</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >分成类型</span>
            </div>
            <el-select v-model="searchInfo.settle_type" class="w100" filterable clearable>
              <el-option :value="item.value" v-for="item in divideList" :key="item.value" :label="item.name"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >订单类型</span>
            </div>
            <el-select v-model="searchInfo.order_type" class="w100" filterable clearable>
              <el-option :value="item.value" v-for="item in orderList" :key="item.value" :label="item.name"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
            <div class="line-box ">
                <span >分成状态</span>
            </div>
            <el-select v-model="searchInfo.status" class="w100" filterable clearable>
              <el-option  :value="item.value" v-for="item in stateList" :key="item.value" :label="item.name"></el-option>
            </el-select>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="line-input" style="width: 520px">
            <div class="line-box" style="min-width: auto;padding: 0 10px;">
                <span>分成时间</span>
            </div>
            <div class="f fac">
                <el-date-picker type="datetime" class="w100" value-format="yyyy-MM-dd HH:mm:ss" v-model="searchInfo.start_at" clearable placeholder="开始时间"></el-date-picker>
                <span class="zih-span">至</span>
                <el-date-picker type="datetime" class="w100" value-format="yyyy-MM-dd HH:mm:ss" v-model="searchInfo.end_at" clearable placeholder="截止时间"></el-date-picker>
            </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="exportTable">导出</el-button>
        <el-button type="text" @click="resetSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <div class="search-term mt25 f fac">
      <p>总数: {{ resData.total }}条</p>
      <p class="ml10">累积分成金额: {{ resData.amount_total | formatF2Y }}元</p>
    </div>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="分成时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="会员ID" prop="uid" align="center"></el-table-column>

      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>昵称</p>
          <p>手机号</p>
        </template>
        <template slot-scope="scope">
          <p v-if="scope.row.user_info.nickname">{{scope.row.user_info.nickname}}</p>
          <p v-else>暂无昵称</p>
          <p>{{scope.row.user_info.username}}</p>
        </template>
      </el-table-column>

      <el-table-column label="分销商等级" prop="level_name" align="center">
      </el-table-column>

        <el-table-column align="center" show-overflow-tooltip>
            <template slot="header">
                <p>购买会员ID</p>
                <p>昵称</p>
                <p>手机号</p>
            </template>
            <template slot-scope="scope">
                <p>{{scope.row.child_uid}}</p>
                <p v-if="scope.row.child_user_info.nickname">{{scope.row.child_user_info.nickname}}</p>
                <p v-else>暂无昵称</p>
                <p>{{scope.row.child_user_info.username}}</p>
            </template>
        </el-table-column>

        <el-table-column label="客户关系" prop="layers_name" align="center">
        </el-table-column>

      <el-table-column label="订单号" prop="order_sn" align="center">
      </el-table-column>
      <el-table-column label="订单类型" prop="order_type_name" align="center">
      </el-table-column>
      <el-table-column label="订单金额(元)" align="center">
        <template slot-scope="scope">
          {{ scope.row.order_amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="分成基数(元)" align="center">
        <template slot-scope="scope">
          {{ scope.row.settle_amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="分成类型" prop="settle_type_name" align="center">
      </el-table-column>
      <el-table-column label="分成比例(%)" align="center">
        <template slot-scope="scope">
          {{ scope.row.ratio / 100 }}
        </template>
      </el-table-column>
      <el-table-column label="分成金额" align="center">
        <template slot-scope="scope">{{ scope.row.amount | formatF2Y }}</template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status_name }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
              display: 'flex',
              justifyContent: 'flex-end',
              marginRight: '20px',
            }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes, prev, pager, next, jumper"></el-pagination>
  </m-card>
</template>

<script>
import {getAwardList, getDistributorAllLevel, exportAwardList} from "@/api/distributor";
import infoList from "@/mixins/infoList";

export default {
  name: "distributorLog",
  mixins: [infoList],
  data() {
    return {
      listApi: getAwardList,
      exApi: exportAwardList,
      optionList: [],
      divideList: [
        {name: "订单分成", value: 1},
        {name: "采购技术服务费分成", value: 2},
        {name: "供应商扣点分成", value: 3},
      ],
      orderList: [
        {name: "商城订单", value: 1},
        {name: "供应商订单", value: 2},
        {name: "供供应链订单", value: 3},
      ],
      stateList: [
        {name: "未结算", value: 1},
        {name: "已结算", value: 2},
        {name: "已失效", value: -1},
      ],
    }
  },
  mounted() {
    this.LevelOptionList()
    if (this.$route.query.uid) {
      this.searchInfo.uid = this.$route.query.uid
    }
    this.getTableData()
  },
  methods: {
    //重置
    resetSearch() {
      this.searchInfo = {}
      this.page = 1
    },
    //搜索
    search() {
      this.page = 1
      this.getTableData()
    },

    LevelOptionList() {
      getDistributorAllLevel().then(res => {
        this.optionList = res.data.levels
      })
    }
  }
}
</script>

<style scoped>
.w100 {
  width: 100%;
}
</style>
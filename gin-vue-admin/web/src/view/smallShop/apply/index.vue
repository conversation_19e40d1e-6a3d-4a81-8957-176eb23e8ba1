<template>
  <m-card>
    <el-table
        :data="tableData"
        style="width: 100%"
        class="mt25">
      <el-table-column align="center" prop="id" label="ID">
      </el-table-column>
      <el-table-column align="center" label="申请时间">
        <template slot-scope="scope">
          {{scope.row.created_at | formatDate}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="审核时间">
        <template slot-scope="scope">
          {{scope.row.operation_at | formatDate}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="会员头像">
        <template slot-scope="scope">
        <img :src="scope.row.user_info.avatar" style="width: 60px;height: 60px"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="手机号">
        <template slot-scope="scope">
          <p>{{scope.row.user_info.username}}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="会员等级">
        <template slot-scope="scope">
          <p>{{scope.row.user_info.level_info.name}}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="店铺名称" prop="title">
      </el-table-column>
      <el-table-column label="申请状态" align="center">
        <template slot-scope="scope">
          {{scope.row.status | chargetype}}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template slot-scope="scope">
          <div class="f fac fjc">
            <el-button type="text" v-if="scope.row.status === 0" @click="adopt(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">通过</el-button>
            <el-button class="color-red" type="text" v-if="scope.row.status === 0" @click="reject(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;">驳回</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
  </m-card>
</template>

<script>
import {getAppliersList,passApply,failApply} from "@/api/smallShop"
export default {
  name: "shopApplyIndex",
  data(){
    return{
      page: 1,
      pageSize: 10,
      total: 0,
      tableData:[]
    }
  },
  filters: {
    chargetype(type) {
      let s = ""
      switch (type) {
        case 0 :
          s = "待审核"
          break
        case 1 :
          s = "已通过"
          break
        case-1 :
          s = "已驳回"
          break
      }
      return s;
    }
  },
  mounted() {
    this.getAppliersList()
  },
  methods:{
    //驳回
    reject(id){
        this.$confirm('确认要驳回吗?, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          failApply({id:id}).then( res=>{
            if(res.code===0){
              this.$message.success(res.msg)
              this.getAppliersList()
            }
          })
        }).catch(() => {
        })
    },
    //通过
    adopt(id){
      this.$confirm('确认要通过吗?, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
      passApply({id:id}).then( res=>{
         if(res.code===0){
           this.$message.success(res.msg)
           this.getAppliersList()
         }
      })
      }).catch(() => {
      })
    },
    getAppliersList(){
      let params = {
        page: this.page,
        pageSize: this.pageSize
      }
      getAppliersList(params).then(res=>{
       if(res.code===0){
         this.tableData=res.data.list
         this.total=res.data.total
       }else {
         this.tableData=[]
         this.total=0
       }
      })
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.getAppliersList();
    },

    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.getAppliersList();
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
</style>
<template>
  <el-drawer
      title="详情"
      :visible="isShow"
      :close-on-press-escape="false"
      :wrapperClosable="false"
      :before-close="handleClose"
      size="calc(100% - 220px)"
      class="detail-ct"
  >
    <m-card style="margin-top: 20px">
      <div class="detail" style="padding-bottom: 10px">
        <div class="w100 f fac fjsb ">
          <div class="f fac " style="margin-left: 20px">
            <p style="color: orange;font-size: 22px">{{ DetailsTable.type | sendType }}<span
                v-if="this.auditStatus === 6">成功</span></p>
            <p style="margin-left: 10px;font-size: 16px">售后编号 :{{ DetailsTable.after_sale_sn }} </p>
            <p v-if="DetailsTable.order ? DetailsTable.order.third_order_sn !=='':'_'" style="margin-left: 10px;font-size: 16px">第三方订单编号 :{{DetailsTable.order ? DetailsTable.order.third_order_sn :"_"}} </p>
            <p v-if="DetailsTable.order ? DetailsTable.order.application.appName !=='':'_'" style="margin-left: 10px;font-size: 16px">采购端名称 :{{ DetailsTable.order ? DetailsTable.order.application.appName:"_"}} </p>
          </div>
          <div class="f fac">
            <p>申请人 : <span>{{ DetailsTable.user ? DetailsTable.user.username : "_" }}</span></p>
            <p style="margin-left: 20px">申请时间 :
              {{ DetailsTable.after_sales_audit ? DetailsTable.after_sales_audit.created_at : "" | formatDate }}</p>
          </div>
        </div>
        <div style="margin-left: 20px ; margin-top: 10px" v-if="DetailsTable.type !== 2">
          <span>商品退款金额 : <span style="color: red; font-size: 16px">￥{{ DetailsTable.amount |formatF2Y }}</span></span>
        </div>
        <!--        <el-button type="text" style="margin-left: 20px" v-if="this.auditStatus===0" @click="passAudit">审核通过</el-button>
                <el-button type="text" style="margin-left: 20px" v-if="this.auditStatus===0" @click="rejectRequest">驳回申请
                </el-button>
                <el-button type="text" style="margin-left: 20px" v-if="DetailsTable.type !== 2 &&this.auditStatus===4"
                           @click="afterService">售后收货
                </el-button>
                <el-button type="text" style="margin-left: 20px" v-if="this.auditStatus===8"
                           @click="confirmDelivery">确认收货
                </el-button>
                <el-button type="text" style="margin-left: 20px" v-if="DetailsTable.type === 2 &&this.auditStatus===4"
                           @click="receiveDeliver">确认收货并发货
                </el-button>
                <el-button type="text" style="margin-left: 20px" v-if="this.auditStatus===5" @click="afterRefund">售后退款
                </el-button>-->
        <el-tag type="success" style="margin: 20px;" v-if="this.auditStatus === 1">商家已同意</el-tag>
        <el-tag type="danger" style="margin: 20px;" v-if="this.auditStatus === 2">商家已驳回</el-tag>
        <el-tag style="margin: 20px;" v-if="this.auditStatus === 6">已完成</el-tag>
        <el-tag style="margin: 20px;" v-if="this.auditStatus === 3">待发货</el-tag>
        <el-tag style="margin: 20px;" v-if="this.auditStatus === -1">已关闭</el-tag>
      </div>
      <div class="detail_two">
        <p style="color: orange">退款提醒 : </p>
        <p class="color-grap">如通过 "微信支付" 付款订单,退款3-7个工作日到账</p>
      </div>
    </m-card>

    <m-card style="margin-top: 20px">
      <div class="detail_goods">
        <div>
          <h3>售后商品</h3>
          <img :src="DetailsTable.small_shop_order_item? DetailsTable.small_shop_order_item.image_url :''"
               style="width: 60px;height: 60px;margin-top: 10px"/>
          <p>{{ DetailsTable.small_shop_order_item ? DetailsTable.small_shop_order_item.title : '' }}</p>
        </div>
        <div>
          <h3>售后信息</h3>
          <p>售后方式 : <span style="color: red">{{ DetailsTable.type | sendType }}</span></p>
          <p v-if="DetailsTable.type !== 2">退款方式 : <span
              style="color: red"> {{ DetailsTable.refund_way |formatRefundWay }}</span></p>
          <p v-if="DetailsTable.type !== 2 && DetailsTable.refund_way === 1">退款商品个数 : <span
              style="color: red"> {{ DetailsTable.num }}</span></p>
          <p v-if="DetailsTable.type !== 2">退款金额 : <span
              style="color: red">￥ {{ DetailsTable.amount |formatF2Y }}</span></p>
          <p v-if="DetailsTable.type !== 2">运费 : <span
              style="color: red">￥ {{ DetailsTable.freight |formatF2Y }}</span></p>
          <p v-if="DetailsTable.type !== 2">技术服务费 : <span
              style="color: red">￥ {{ DetailsTable.technical_services_fee |formatF2Y }}</span></p>
          <p v-if="DetailsTable.type === 1">订单件数 : <span style="color: red">{{ DetailsTable.small_shop_order_item.qty }}</span></p>
          <p>联系方式 : <span>{{ DetailsTable.user ? DetailsTable.user.username : "" }}</span></p>
          <p v-if="DetailsTable.type !== 2">退款原因 : {{ DetailsTable.refund_reason_name }}</p>
          <p v-if="DetailsTable.type === 2">换货商品 : {{ DetailsTable.barter_num }}{{ DetailsTable.small_shop_order_item.unit }}
            {{ DetailsTable.barter_sku_title }}</p>
          <p>{{ DetailsTable.type === 2 ? '换货' : '退款' }}说明 : {{ DetailsTable.description }} </p>
          <p v-if="DetailsTable.type !== 2">合计退款金额 : <span  style="color: red"> ￥{{ DetailsTable.amount + DetailsTable.freight + DetailsTable.technical_services_fee   |formatF2Y }} </span> </p>
          <!--         <p>售后历史 : </p>-->
        </div>
        <div>
          <h3>购买信息</h3>
          <p>商品单价 : <span style="color: red"
                          v-if="DetailsTable.small_shop_order_item">￥ {{ DetailsTable.small_shop_order_item.amount | formatF2Y }}</span></p>
          <p>实付金额 : <span
              style="color: red">￥{{
              DetailsTable.small_shop_order_item ? $options.filters.formatF2Y(DetailsTable.small_shop_order_item.payment_amount) : 0.00
            }}</span>
            <span
                v-if="this.auditStatus === 6">(已退 ￥{{
                DetailsTable.small_shop_order_item ? $options.filters.formatF2Y(DetailsTable.small_shop_order_item.refund_amount) : 0.00
              }}) </span>
          </p>
          <!--         <p>发货件数 :  </p>-->
          <p>发货状态 : {{ DetailsTable.small_shop_order_item ? DetailsTable.small_shop_order_item.send_status : "" | sendStatus }}
            <el-button
                v-if="DetailsTable.type !==0 && DetailsTable.small_shop_order_item? DetailsTable.small_shop_order_item.send_status !==0 :'' "
                type="text" @click="seeLogistics">查看物流
            </el-button>
          </p>
          <p>订单编号 : <el-button type="text" @click="openShopping">{{ DetailsTable.order ? DetailsTable.order.order_sn : "" }}</el-button></p>
          <!--         <p>供应链 : </p>-->
        </div>
        <div v-if="DetailsTable.type !==0">
          <h3>退货地址</h3>
          <p>联系电话 :{{ DetailsTable.shop_address ? this.contactNumber(user) : "" }}
            <el-button type="text" @click="see">查看</el-button>
          </p>
          <p>联系人 : {{ DetailsTable.user ? DetailsTable.user.nickname : '' }} </p>
          <p>退货地址 : {{ DetailsTable.shop_address ? this.shop_address(address) : "" }}</p>
          <p>物流公司 : {{ DetailsTable.return_order_express ? DetailsTable.return_order_express.company_name : "" }} </p>
          <p>物流单号 : {{ DetailsTable.return_order_express ? DetailsTable.return_order_express.express_no : "" }}</p>
        </div>
        <div v-if="DetailsTable.type ===2">
          <h3>换货地址</h3>
          <p>联系电话 :{{ DetailsTable.order.shipping_address ? DetailsTable.order.shipping_address.mobile : '-' }}
            <!--            <el-button type="text" @click="see">查看</el-button>-->
          </p>
          <p>联系人 : {{ DetailsTable.order.shipping_address ? DetailsTable.order.shipping_address.realname : '-' }} </p>
          <p>退货地址 : {{ DetailsTable.order.shipping_address ? buyerAddress() : "-" }}</p>
          <p>物流公司 :
            {{ DetailsTable.after_sales_send && DetailsTable.after_sales_send.length ? DetailsTable.after_sales_send[0].company_name : '-' }}</p>
          <p>物流单号 :
            {{ DetailsTable.after_sales_send && DetailsTable.after_sales_send.length ? DetailsTable.after_sales_send[0].express_no : '-' }}</p>
        </div>
      </div>
    </m-card>
    <m-card style="margin-top: 20px;" v-if="this.Logistics">
      <div class="detail_wl">
        <h3 style=" margin-left: 20px;">物流状态 </h3>
        <div class="detail_wl_right">
          <el-steps direction="vertical" :active="1" finish-status="success"
                    style="margin-top: 10px;height: 200px;margin-left: 10px">
            <el-step class="wl" v-for="item in logisticsTable.data.list">
              <template slot="description">
                <div class="detail_wlxq">
                  <div class="detail_wlxq_left">
                    <p>{{ item.time }}</p>
                  </div>
                  <div class="detail_wlxq_right">
                    <p>{{ item.status }}</p>
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </m-card>
    <m-card style="margin-top: 20px">
      <div class="detail_xs">
        <div class="detail_xs_left">
          <h3>协商记录</h3>
        </div>
        <div class="detail_xs_right">
          <!--          <p class="color-grap">点击发布留言</p>-->
        </div>
      </div>
      <el-steps direction="vertical" :active="1" style="margin-top: 30px;margin-left: 10px">
        <el-step class="xc" v-for="item in DetailsTable.logs">
          <!--          <i slot="icon" style="font-style:normal;font-weight: 900;color:whitesmoke">买</i>-->
          <template slot="description">
            <div class="detail_xs_row" style="width: 100%">
              <div class="detail_xs_count">
                <div class="detail_xs_row_left">
                  <p> {{ item.content }}</p>
                </div>
                <div class="f fac">
                  <p style="color: gray">{{ item.created_at }}</p>
                </div>
              </div>
              <div class="detail_xs_reason" v-if="DetailsTable.type !== 2">
                <p style="color: gray">退款金额 :{{ item.price |formatF2Y }} </p>
                <p style="color: gray">运费 :{{ item.freight |formatF2Y }} </p>
                <p style="color: gray">技术服务费 :{{ item.technical_services_fee |formatF2Y }} </p>
              </div>
              <div class="detail_xs_reason" v-if="DetailsTable.type === 2">
                <p style="color: gray">换货商品 :{{ DetailsTable.barter_sku_title }} </p>
                <p style="color: gray">换货数量 :{{ DetailsTable.barter_num }} </p>
              </div>
            </div>
          </template>
        </el-step>
        <!--        <el-step class="f2">-->
        <!--          <i slot="icon" style="font-weight: 900;font-style:normal;color: white">买</i>-->
        <!--          <template slot="description" >-->
        <!--                  <div class="detail_xs_row" style="width: 1575px">-->
        <!--                    <div class="detail_xs_count">-->
        <!--                      <div class="detail_xs_row_left">-->
        <!--                        <p> <span>买家</span> 已退货,等待商家确认收货</p>-->
        <!--                      </div>-->
        <!--                      <div class="detail_xs_row_right">-->
        <!--                        <p>2022-03-24 18:18:00</p>-->
        <!--                      </div>-->

        <!--                    </div>-->
        <!--                    <div class="detail_xs_reason" >-->
        <!--                      <p>物流名称 : </p>-->
        <!--                      <p>物流编号 : </p>-->
        <!--                      <p>退款说明 : </p>-->
        <!--                      <p>联系电话 : </p>-->
        <!--                    </div>-->
        <!--                  </div>-->
        <!--          </template>-->
        <!--        </el-step>-->
        <!--        <el-step class="f1">-->
        <!--          <i slot="icon" style="font-weight: 900;font-style:normal;color: white">商</i>-->
        <!--          <template slot="description" >-->
        <!--                  <div class="detail_xs_row" style="width: 1575px">-->
        <!--                    <div class="detail_xs_count">-->
        <!--                      <div class="detail_xs_row_left">-->
        <!--                        <p> <span>商家</span> 商家拒绝确认收货</p>-->
        <!--                      </div>-->
        <!--                      <div class="detail_xs_row_right">-->
        <!--                        <p>2022-03-24 18:18:00</p>-->
        <!--                      </div>-->

        <!--                    </div>-->
        <!--                    <div class="detail_xs_reason" >-->
        <!--                      <p>拒绝原因 : </p>-->
        <!--                    </div>-->
        <!--                  </div>-->
        <!--          </template>-->
        <!--        </el-step>-->
        <!--        <el-step class="f2">-->
        <!--          <i slot="icon" style="font-weight: 900;font-style:normal;color: white">买</i>-->
        <!--          <template slot="description" >-->
        <!--                  <div class="detail_xs_row" style="width: 1575px">-->
        <!--                    <div class="detail_xs_count">-->
        <!--                      <div class="detail_xs_row_left">-->
        <!--                        <p> <span>买家</span> 已退款,等待商家确认收货</p>-->
        <!--                      </div>-->
        <!--                      <div class="detail_xs_row_right">-->
        <!--                        <p>2022-03-24 18:18:00</p>-->
        <!--                      </div>-->

        <!--                    </div>-->
        <!--                    <div class="detail_xs_reason">-->
        <!--                      <p>物流名称 : </p>-->
        <!--                      <p>物流编号 : </p>-->
        <!--                      <p>退款说明 : </p>-->
        <!--                      <p>联系电话 : </p>-->
        <!--                    </div>-->
        <!--                  </div>-->
        <!--          </template>-->
        <!--        </el-step>-->
        <!--        <el-step class="f1">-->
        <!--          <i slot="icon" style="font-weight: 900;font-style:normal;color: white">商</i>-->
        <!--          <template slot="description" >-->
        <!--                  <div class="detail_xs_row" style="width: 1575px">-->
        <!--                    <div class="detail_xs_count">-->
        <!--                      <div class="detail_xs_row_left">-->
        <!--                        <p> <span>商家</span> 已同意退款申请,等待买家退货</p>-->
        <!--                      </div>-->
        <!--                      <div class="detail_xs_row_right">-->
        <!--                        <p>2022-03-24 18:18:00</p>-->
        <!--                      </div>-->

        <!--                    </div>-->
        <!--                    <div class="detail_xs_reason" >-->
        <!--                      <p>退货地址 : </p>-->

        <!--                    </div>-->
        <!--                  </div>-->
        <!--          </template>-->
        <!--        </el-step>-->
        <!--        <el-step class="f2" >-->
        <!--          <i slot="icon" style="font-weight: 900;font-style:normal;color: white">买</i>-->
        <!--          <template slot="description" >-->
        <!--                  <div class="detail_xs_row">-->
        <!--                    <div class="detail_xs_count">-->
        <!--                      <div class="detail_xs_row_left">-->
        <!--                        <p> <span>买家</span> 发起来退款申请,等待商家处理</p>-->
        <!--                      </div>-->
        <!--                      <div class="detail_xs_row_right">-->
        <!--                        <p>2022-03-24 18:18:00</p>-->
        <!--                      </div>-->

        <!--                    </div>-->
        <!--                    <div  class="detail_xs_reason">-->
        <!--                      <p>售后方式 : </p>-->
        <!--                      <p>退款原因 : </p>-->
        <!--                      <p>退款金额 : </p>-->
        <!--                      <p>退款数量 : </p>-->
        <!--                      <p>退款说明 : </p>-->
        <!--                      <p>联系电话 : </p>-->
        <!--                    </div>-->
        <!--                  </div>-->
        <!--          </template>-->
        <!--        </el-step>-->
      </el-steps>

    </m-card>
    <el-dialog
        title="申请驳回"
        :visible="rejectShow"
        width="30%"
        :modal-append-to-body="false"
        :append-to-body="true"
        :before-close="dialogRejectShow">
      <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入内容"
          v-model="cause">
      </el-input>
      <span slot="footer" class="dialog-footer">
    <el-button @click="dialogRejectShow">取 消</el-button>
    <el-button type="primary" @click="dialogReject">确 定</el-button>
  </span>
    </el-dialog>
    <deliver-dialog ref="deliverDialog" @reload="afterSalesDetails"></deliver-dialog>
    <shopping-dialog ref="shoppingDialog"></shopping-dialog>
  </el-drawer>

</template>
<script>
import {user} from "../../../../store/module/user";
import {
  getByOrderItemId,
  getAfterSalesGet,
  shippingInfo,
  afterSalesAudit,
  afterSalesReceive,
  afterSalesRefund,
  afterSalesAuditReject, userReceive
} from "@/api/afterSale";
import {findByOrderItemId} from "@/api/smallShop"
import Detail from "@/view/goods/classify/components/detailPage/detail";
import DeliverDialog from "@/view/order/afterSale/components/deliverDialog";
import {confirm} from "@/decorators/decorators";
import shoppingDialog from "@/view/order/afterSale/components/shoppingDialog";

export default {
  name: "detailAfterSale",
  components: {Detail, DeliverDialog, shoppingDialog},
  data() {
    return {
      cause: "",//驳回原因
      auditStatus: null,
      DetailsTable: [],
      logisticsTable: [],
      isShow: false,
      user: "",
      address: "",
      xShow: true,
      rejectShow: false,
      Logistics: false,
    };
  },
  filters: {
    formatRefundWay: function (type) {
      let s = ""
      switch (type) {
        case 0:
          s = "手填金额"
          break;
        case 1:
          s = "按商品个数"
          break;
      }
      return s;
    },
    sendStatus: function (status) {
      let s = "";
      switch (status) {
        case 0:
          s = "待发货"
          break;
        case 1:
          s = "部分发货"
          break;
        case 2:
          s = "已发货"
          break;
      }
      return s;
    },
    sendType: function (type) {
      let s = "";
      switch (type) {
        case 0:
          s = "仅退款"
          break;
        case 1:
          s = "退货退款"
          break;
        case 2:
          s = "换货"
          break;
      }
      return s;
    }
  },
  methods: {
    openShopping(){
      this.$refs.shoppingDialog.init(this.DetailsTable.order.shipping_address,this.DetailsTable.user)
    },
    buyerAddress() {
      let s = this.DetailsTable.order.shipping_address.province + this.DetailsTable.order.shipping_address.city + this.DetailsTable.order.shipping_address.county + this.DetailsTable.order.shipping_address.town + this.DetailsTable.order.shipping_address.detail
      return s
    },
    shop_address() {
      let address = this.DetailsTable.shop_address.city_name.name + this.DetailsTable.shop_address.district_name.name + this.DetailsTable.shop_address.province_name.name + this.DetailsTable.shop_address.address
      return address
    },
    //查看物流
    seeLogistics() {
      this.Logistics = !this.Logistics
      this.shippingInfo()
    },
    //驳回申请
    dialogReject() {
      afterSalesAuditReject({id: this.DetailsTable.after_sales_audit.id, cause: this.cause}).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.afterSalesDetails(this.DetailsTable)
        }
      })
      this.dialogRejectShow()

    },
    //打开驳回申请
    rejectRequest() {
      this.rejectShow = true
    },
    //售后退款
    afterRefund() {
      this.$confirm('确定要退款?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        afterSalesRefund({id: this.DetailsTable.id}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.afterSalesDetails(this.DetailsTable)
          }
        })
      }).catch(() => {
      })

    },
    // 确认收货并发货
    receiveDeliver() {
      this.$refs.deliverDialog.init(this.DetailsTable)
    },
    // 确认收货
    @confirm("提示", "确定要收货?")
    confirmDelivery() {
      userReceive({id: this.DetailsTable.id}).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.afterSalesDetails(this.DetailsTable)
        }
      })
    },
    //售后收货
    afterService() {
      this.$confirm('确定要收货?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        afterSalesReceive({id: this.DetailsTable.id}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.afterSalesDetails(this.DetailsTable)
          }
        })
      }).catch(() => {
      })
    },
    //审核通过
    passAudit() {
      this.$confirm('确定要通过审核?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        afterSalesAudit({id: this.DetailsTable.after_sales_audit.id}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.afterSalesDetails(this.DetailsTable)
          }
        })
      }).catch(() => {
      })

    },
    // 拼装状态码
    auditStatusAssemble(status1, status2) {
      switch (status1) {
        case -1: // 已关闭
          this.auditStatus = -1
          break;
        case 0: // 待审核
          switch (status2) {
            case 0: // 等待商家处理
              this.auditStatus = 0
              break;
            case 1: // 商家已同意
              this.auditStatus = 1
              break;
            case -1: // 商家已拒绝
              this.auditStatus = 2
              break;
          }
          break;
        case 1: // 待买家发货
          this.auditStatus = 3
          break;
        case 2: // 待商家收货
          this.auditStatus = 4
          break;
        case 3: // 待退款
          this.auditStatus = 5
          break;
        case 4: // 已完成
          this.auditStatus = 6
          break;
        case 5: // 待商家发货
          this.auditStatus = 7
          break;
        case 6: // 待用户收货
          this.auditStatus = 8
          break;
      }
    },
    //获取物流
    shippingInfo() {
      shippingInfo().then(res => {
        if (res.code === 0) {
          this.logisticsTable = res.data.reshipping
        }
      })
    },

    afterSalesDetails(row) {
      getAfterSalesGet({id: row.id}).then(res => {
        if (res.code === 0) {
          this.DetailsTable = res.data.read
          this.auditStatusAssemble(res.data.read.status, res.data.read.after_sales_audit.status)
        }
      })
    },
    getByOrderItemId(id) {
      findByOrderItemId({id}).then(res => {
        if (res.code === 0) {
          this.DetailsTable = res.data.read
          this.auditStatusAssemble(res.data.read.status, res.data.read.after_sales_audit.status)
        }
      })
    },
    see() {
      this.xShow = !this.xShow
      this.contactNumber(user)
    },
    contactNumber() {
      if (this.xShow) {
        let user = this.DetailsTable.shop_address.tel.substr(0, 3) + '****' + this.DetailsTable.shop_address.tel.substr(7)
        return user
      } else {
        let user = this.DetailsTable.shop_address.tel
        return user
      }
    },
    dialogRejectShow() {
      this.rejectShow = false
      this.cause = ""
    },
    handleClose(reload = false) {
      try {
      } catch {
      } finally {
        this.isShow = false;
        this.auditStatus = null,
            this.DetailsTable = [],
            this.logisticsTable = [],
            this.user = "",
            this.address = "",
            this.xShow = true,
            this.Logistics = false
        this.rejectShow = false
      }
      if (reload) this.$emit("reload")

    },
  },
};
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";

.wl {
  ::v-deep
  .el-step__icon.is-text {
    width: 18px;
    height: 18px;
    margin-left: 3px;

  }

  ::v-deep
  .el-step__line {
    background: #f0f2f5;

  }
}

//.f1 {
//  ::v-deep
//  .el-step__icon.is-text {
//    background-color: #155BD4;
//    border: 2px solid #155BD4;
//  }
//    ::v-deep
//    .el-step__line {
//      background: #f0f2f5;
//    }
//
//}
//.f2{
//  ::v-deep
//  .el-step__icon.is-text{
//    background-color: gray;
//    border: 2px solid gray ;
//  }
//  ::v-deep
//  .el-step__line {
//    background: #f0f2f5;
//  }
//}
.detail {
  width: 100%;
  border-bottom: 3px solid #f0f2f5;
}

.detail_two {
  margin-left: 20px;
  margin-top: 10px;

  p {
    margin-top: 10px;
  }
}

.detail_goods {
  width: 100%;
  display: flex;

  div {
    flex: 1px;
    margin-left: 20px;

    p {
      margin-top: 10px;
      font-size: 14px;
      color: gray;
    }
  }
}

.detail_wl {
  width: 100%;

  .detail_wl_right {

    .detail_wlxq {
      display: flex;

      .detail_wlxq_left {
        flex: 1;
      }

      .detail_wlxq_right {
        flex: 4;
      }
    }
  }
}

.detail_xs {
  width: 100%;
  display: flex;
  border-bottom: 3px solid #f0f2f5;

  .detail_xs_left {
    padding-left: 20px;
    margin-bottom: 10px;
    flex: 1;
  }

  .detail_xs_right {
    p {
      margin-right: 10px;
    }
  }
}

.xc {
  ::v-deep
  .el-step__description {
    padding-right: 0px;
  }
}

.detail_xs_row {
  margin-top: -30px;
  margin-bottom: 20px;
  background-color: #f0f2f5;
  width: 100%;

  //border: 1px solid red;
  .detail_xs_count {
    margin-top: 10px;
    padding: 10px;
    display: flex;
    border-bottom: 1px solid #e1e3e4;

    .detail_xs_row_left {
      flex: 1;
      padding: 10px;

      p {
        color: black;
        font-weight: 900;

        span {
          color: gray;
        }
      }
    }
  }

  .detail_xs_reason {
    padding: 20px;
    margin-top: -18px;

    p {
      margin-top: 10px;
      color: gray;
      color: black;
    }
  }
}

</style>
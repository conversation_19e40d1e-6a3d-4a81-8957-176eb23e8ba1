<template>
  <el-dialog title="支付记录" :visible="isShow" width="50%" :before-close="handleClose">
    <el-table :data="tableData">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="支付单号">
        <template slot-scope="scope">
          {{scope.row.pay_sn}}
        </template>
      </el-table-column>
      <el-table-column label="支付金额">
        <template slot-scope="scope">
          {{ scope.row.amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          {{ scope.row.status | formatPayStatus }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="支付方式">
          <template slot-scope="scope">
              {{ scope.row.}}
          </template>
      </el-table-column> -->
      <el-table-column label="创建时间">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="支付时间">
        <template slot-scope="scope">
          {{ scope.row.paid_at | formatDate }}
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getPaymentRecord } from "@/api/smallShop"
export default {
  name: "paymentLog",
  data() {
    return {
      isShow: false,
      tableData: []
    }
  },
  filters: {
    formatPayStatus: function (status) {
      let s = "";
      switch (status) {
        case 0:
          s = "未支付"
          break;
        case 1:
          s = "已支付"
          break;
        case -2:
          s = "已退款"
          break;
        case -3:
          s = "部分退款"
          break;
        default:
          s = "未知状态"
          break;
      }
      return s
    }
  },
  methods: {
    getData(oid) {
      getPaymentRecord({ order_id: oid }).then(res => {
        if (res.code === 0) {
          this.tableData.push(res.data)
        }
      })
    },
    handleClose() {
      this.isShow = false
      this.tableData = []
    }
  }

}
</script>

<style scoped>

</style>
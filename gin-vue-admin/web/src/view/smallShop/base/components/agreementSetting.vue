<template>
  <el-form ref="formData" :model="formData" label-width="180px">
    <el-row>
      <el-col :span="15">
        <el-form-item label="用户服务协议标题:">
          <el-input placeholder="请输入" v-model="formData.service_title"></el-input>
          <p class="color-grap">默认为用户服务协议</p>
        </el-form-item>
      </el-col>
      <el-col :span="15">
        <el-form-item label="内容:">
          <m-editor style="height: 500px;" v-model="formData.service_content"></m-editor>
        </el-form-item>
      </el-col>
      <el-col :span="15">
        <el-form-item  label="用户隐私协议标题:" class="mt50">
          <el-input placeholder="请输入" v-model="formData.privacy_title"></el-input>
          <p class="color-grap">默认为用户隐私协议</p>
        </el-form-item>
      </el-col>
      <el-col :span="15">
        <el-form-item label="内容:">
          <m-editor style="height: 500px;" v-model="formData.privacy_content"></m-editor>
        </el-form-item>
      </el-col>
      <el-col :span="15">
        <el-form-item class="mt50">
          <el-button type="primary" @click="onSave">保 存</el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { findProtocolSetting, updateProtocolSetting } from "@/api/smallShop"
export default {
  name: "agreementSetting",
  data() {
    return {
      id: null,
      formData: {
        service_title:'',
        service_content:'',
        privacy_title:'',
        privacy_content:'',
      }
    }
  },
  mounted() {
    this.getSetting()
  },
  methods: {
    getSetting() {
      findProtocolSetting().then(res => {
        if (res.code === 0) {
          this.formData = res.data.setting.value
          this.id = res.data.setting.id
        }
      })
    },
    onSave() {
      console.log('mmm', this.id);
      let params = {
        id: this.id,
        value:{
          ...this.formData
        } 
      }
      updateProtocolSetting(params).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.getSetting()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mt50 {
  margin-top: 50px;
}
</style>
<template>
    <el-form ref="formData" :model="formData" label-width="180px">
        <el-form-item label="同步会员到中台">
            <el-switch v-model="formData.value.sync_users" :active-value="1" :inactive-value="0"></el-switch>
            <p class="color-grap">开启后，小商店会员具备微信开放平台unionid并且在中台未注册的，会自动注册为中台会员，会员推荐人为小商店会员推荐人！</p>
            <p class="color-grap" style="color: red;">
                开启该功能前，请务必确认您已经将小商店公众号、小程序绑定到和中台公众号、小程序同一个微信开放平台！
                <el-button type="text" @click="syncUserFun">手动同步！！</el-button>
            </p>
        </el-form-item>
        <el-form-item label="小商店会员列表">
            <el-switch
                v-model="formData.value.show_user_list"
                :active-value="1"
                :inactive-value="0"
            ></el-switch>
            <p class="color-grap">开启后，小商店前端显示会员列表！</p>
        </el-form-item>
        <el-form-item>
            <el-button type="primary" @click="save">保 存</el-button>
        </el-form-item>
    </el-form>
</template>

<script>
import { findSetting, syncUser, updateSetting } from '@/api/smallShop';
export default {
    name: 'memberSetting',
    data() {
        return {
            formData: {},
        };
    },
    methods: {
        // 获取设置
        async findSetting() {
            const res = await findSetting();
            if (res.code === 0) {
                this.formData = res.data.setting;
            }
        },
        // 手动同步
        async syncUserFun() {
            let res = await syncUser();
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
        // 保存
        async save() {
            const data = {
                id: this.formData.id,
                value: this.formData.value,
            };
            const res = await updateSetting(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.findSetting();
            }
        },
    },
};
</script>

<style>
</style>
<template>
  <div>
    <el-dialog 
      title="批量开票预览" 
      :visible="previewVisible"
      @open="onOpen"
      @close="onClose"
    >
      <div class="con-preview">
        <el-row :gutter="20">
          <el-col :span="12"><div>个人姓名：{{previewObj.person_name}}</div></el-col>
          <el-col :span="12"><div>开票内容: {{previewObj.amount_type | chargeAmountType}}</div></el-col>
          <el-col :span="12"><div>开票类型: {{previewObj.type | chargeType}}</div></el-col>
          <el-col :span="12"><div>发票抬头：{{previewObj.account_type | chargeAccountType}}</div></el-col>
          <el-col :span="12"><div>发票内容：{{previewObj.detail_type | chargeDetailType}}</div></el-col>
          <el-col :span="12"><div>收票人手机：{{previewObj.mobile}}</div></el-col>
          <el-col :span="12"><div>收票人邮箱：{{previewObj.email}}</div></el-col>
          <el-col :span="12"><div>单位名称：{{previewObj.company_name}}</div></el-col>
          <el-col :span="12"><div>纳税人识别号：{{previewObj.company_code}}</div></el-col>
          <el-col :span="12"><div>注册地址: {{previewObj.sign_address}}</div></el-col>
          <el-col :span="12"><div>注册电话：{{previewObj.sign_mobile}}</div></el-col>
          <el-col :span="12"><div>开户银行：{{previewObj.opening_bank}}</div></el-col>
          <el-col :span="12"><div>银行账号：{{previewObj.bank_account}}</div></el-col>
          <el-col :span="12"><div>地址id：{{previewObj.address_id}}</div></el-col>
        </el-row>
        <h3 class="mt20">异常订单</h3>
        <el-table
          :data="errOrders"
          class="mt20"
          style="width: 100%">
          <el-table-column
            prop="order_sn"
            align="center"
            label="订单号"
            width="180">
          </el-table-column>
          <el-table-column
            prop="amount"
            align="center"
            label="订单金额"
            width="180">
          </el-table-column>
          <el-table-column
            prop="continue"
            align="center"
            label="技术服务费">
          </el-table-column>
          <el-table-column
            prop="reason"
            align="center"
            label="异常原因">
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <template v-if="scope.row.continue === 1">
                <el-button type="text" @click="onContinue(scope.row)">继续开票</el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <h3 class="mt30">最终金额</h3>
        <el-row :gutter="20">
          <el-col :span="24">
            <div>开票订单数量：{{orderIds}}单</div>
          </el-col>
          <el-col :span="24"><div>开票金额合计：{{totalPrice}}元</div></el-col>
        </el-row>     
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="onClose">取 消</el-button>
        <el-button type="primary" @click="onSubmit">确认开票</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import operation from "@/utils/operation"
export default {
  name: "previewDialog",
  props: {
    previewVisible: {
      type: Boolean,
      default: false
    },
    previewObj: {
      type:Object,
      default: ()=>({}),
    },
    propErrOrders: {
      type: Array,
      default: () => [],
    },
    propOrderIds: {
      type: Array,
      default: () => [],
    },
    propTotalPrice: {
      type: String,
      default: '',
    }
  },
  data() {
    return {  
      acceptOrders: [], // “继续开票”时，需要被删的订单
      billItemIds: [], // “继续开票”时，需要被删的订单的id集合
      acceptPrice: 0, // “继续开票”时，需要重算的总价
      totalPrice: 0,
      orderIds:0,
    }
  },
  computed: {
    errOrders() {
      return this.propErrOrders.filter((v) => !this.acceptOrders.includes(v)) //propErrOrders和acceptOrders比较去重
    },
  },
  filters:{
    chargeAmountType(type){ //开票内容
      let s = ""
      switch (type){
        case 1 :
          s="普通订单"
          break
        case 2 :
          s="技术服务费"
          break
      }
      return s;
    },
    chargeType(type){ //开票类型
      let s = ""
      switch (type){
        case 1 :
          s="增值税普通发票"
          break
        case 2 :
          s="增值税专用发票"
          break
      }
      return s;
    },
    chargeAccountType(type){ //发票抬头
      let s = ""
      switch (type){
        case 1 :
          s="个人"
          break
        case 2 :
          s="单位"
          break
      }
      return s;
    },
    chargeDetailType(type){ //发票内容
      let s = ""
      switch (type){
        case 1 :
          s="商品明细"
          break
        case 2 :
          s="商品类别"
          break
      }
      return s;
    },
  },
  methods: {
    onOpen () {
      this.totalPrice = parseInt(this.propTotalPrice)
    },
    onClose () {
      this.$emit('update:previewVisible', false)
    },
    // 继续开票
    onContinue(row) {
      this.acceptOrders.push(row) //将订单移除出异常订单
      this.billItemIds.push(row.bill_item_id) //被移除订单的id集合
      this.acceptPrice = parseInt(row.amount)
      this.totalPrice = operation.accAdd(this.totalPrice, this.acceptPrice) // 更新开票预览金额
      if(this.propOrderIds) { // 修改“开票订单”数量
        this.orderIds = ++ this.propOrderIds.length 
      } else {
        this.orderIds = ++ this.orderIds 
      }
    },
    onSubmit(){
      this.$emit('reSubmit', this.billItemIds)
      this.$emit('close')
      this.onClose()
    },
  },
}
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";
.con-preview {
  margin: 0 30px;
  // width: 90%;
  .el-col {
    margin: 10px 0;
  }
}
.mt20 {
  margin-top:20px
}
.mt30 {
  margin-top:30px
}
</style>

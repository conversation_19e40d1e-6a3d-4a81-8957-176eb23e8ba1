<template>
  <m-card>
    <el-form :model="formData" label-width="130px" class="system">
      <el-form-item label="插件开关:">
        <el-radio-group v-model="formData.enable">
          <el-radio :label="'1'">开启</el-radio>
          <el-radio :label="'0'">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-row>
        <el-col :span="15">
          <el-form-item label="appKey:">
            <el-input v-model="formData.key"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="appSecret:">
            <el-input v-model="formData.secret" v-if="show"></el-input>
            <el-button type="primary" size="small" @click="reset" v-else>重置</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="公众号APPID:">
            <el-input v-model="formData.gzh_app_id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="小程序APPID:">
            <el-input v-model="formData.xcx_app_id"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="电签Url:">
            <el-input v-model="formData.contract_url"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="服务商ID:">
            <el-input v-model="formData.service_id"></el-input>
            <p class="serviceTip"> * 不填则使用默认服务商</p>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item label="接口地址:">
            <el-input v-model="formData.api_url"></el-input>
            <p class="serviceTip"> * 接口地址以 / 结尾</p>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item>
        <el-button type="primary" size="small" @click="save">保 存</el-button>
      </el-form-item>
    </el-form>
  </m-card>
</template>
<script>
import { getGongMallSetting, saveGongMallSetting } from '@/api/gongmall'

export default {
  name: 'withdrawSetting',
  data() {
    return {
      show: false,
      formData: {
        enable: '0',
        key: '',
        secret: '',
        gzh_app_id: '',
        xcx_app_id: '',
        contract_url: '',
        service_id: '',
        api_url: '',
      },
    }
  },
  mounted() {
    // 调用获取基础设置方法
    this.getGongMallSetting()
  },
  methods: {
    // 获取基础设置
    async getGongMallSetting() {
      const res = await getGongMallSetting()
      const value = JSON.parse(res.data.value)
      this.formData = value
      if (this.formData.secret === '') {
        this.show = true
      }
    },
    // 保存基础设置
    async save() {
      const { code, msg } = await saveGongMallSetting(this.formData)
      if (code === 0) {
        this.$message.success(msg)
      }
      if (this.formData.secret !== '') {
        this.show = false
      }
    },
    // 重置
    reset() {
      this.formData.secret = ''
      this.show = true
    },
  },
}
</script>
<style scoped lang="scss">
.serviceTip {
  color: #dc5d5d;
  font-size: 12px;
}
</style>

<template>
  <m-card>
    <el-form ref="formData" :model="formData" label-width="100px" inline>
      <el-form-item>
          <el-input placeholder="请输入" v-model="formData.order_sn" class="line-input" clearable>
              <span slot="prepend">提现单号</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="formData.uid" class="line-input" clearable>
              <span slot="prepend">会员ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="formData.nickname" class="line-input" clearable>
              <span slot="prepend">会员昵称</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >提现状态</span>
              </div>
              <el-select placeholder="请输入" v-model="formData.status">
                <el-option label="处理中" :value="3"></el-option>
                <el-option label="成功" :value="2"></el-option>
                <el-option label="失败" :value="1"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
        <div class="line-input" style="width: 586px;">
            <div class="line-box" style="min-width: auto;padding: 0 10px;">
                <span>打款时间</span>
            </div>
            <div class="f fac">
                <el-date-picker type="datetime" class="w100" value-format="yyyy-MM-dd HH:mm:ss" v-model="formData.pay_time_start" clearable placeholder="开始时间"></el-date-picker>
                <span class="zih-span">至</span>
                <el-date-picker type="datetime" class="w100" value-format="yyyy-MM-dd HH:mm:ss" v-model="formData.pay_time_end" clearable placeholder="截止时间"></el-date-picker>
            </div>
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" size="medium" @click="search">搜索</el-button>
        <el-button type="text" @click="reset">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <!-- 表格 -->
    <el-table :data="tableData">
      <el-table-column prop="id" label="ID" width="200"> </el-table-column>
      <el-table-column label="时间" width="300">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column prop="order_sn" label="提现单号" width="300"> </el-table-column>
      <el-table-column prop="uid" label="会员ID" width="200"> </el-table-column>
      <el-table-column label="会员" width="200">
        <template slot-scope="scope">
          <img :src="scope.row.user.avatar" class="avatar" />
          <div>{{ scope.row.user.nickname }}</div>
        </template>
      </el-table-column>
      <el-table-column label="打款金额" width="200">
        <template slot-scope="scope">
          {{ scope.row.amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 3">
            处理中
            <el-tooltip v-if="scope.row.err_msg" :content="scope.row.err_msg" placement="top">
              <i class="el-icon-warning" style="margin-left: 10px;"></i>
            </el-tooltip>
          </span>
          <span v-if="scope.row.status === 2">成功</span>
          <span v-if="scope.row.status === 1">失败</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <div class="pagination mt_25">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </div>
  </m-card>
</template>
<script>
// import mDaterange from '@/components/mDate/daterange'
import { getGongMallList } from '@/api/gongmall'
export default {
  name: 'withdrawRemitList',
  // components: { mDaterange },
  data() {
    return {
      formData: {
        order_sn: '', //提现单号
        uid: null, //会员ID
        nickname: '', //会员昵称
        status: null, //提现状态
        pay_time_start: '', //打款开始时间
        pay_time_end: '', //打款截止时间
      },
      tableData: [],
      page: 1,
      pageSize: 10,
      total: 0,
    }
  },
  mounted() {
    // 调用获取打款列表方法
    this.getList()
  },
  methods: {
    // 获取打款列表
    async getList() {
      let data = {
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await getGongMallList(data)
      if (res.code === 0) {
        this.tableData = res.data.list
        this.total = res.data.total
      }
    },
    // 每页条数切换
    handleSizeChange(val) {
      this.pageSize = val
      this.page = 1
      this.getList()
    },
    // 页数切换
    handleCurrentChange(val) {
      this.page = val
      this.getList()
    },
    // 搜索
    async search() {
      const order_sn = this.formData.order_sn
      const uid = parseInt(this.formData.uid)
      const nickname = this.formData.nickname
      const status = parseInt(this.formData.status)
      const pay_time_start = this.formData.pay_time_start
      const pay_time_end = this.formData.pay_time_end
      let data = {
        page: 1,
        pageSize: 10,
        order_sn,
        uid,
        nickname,
        status,
        pay_time_start,
        pay_time_end,
      }
      const res = await getGongMallList(data)
      if (res.code === 0) {
        this.tableData = res.data.list
        this.total = res.data.total
      }
    },
    // 重置搜索条件
    reset() {
      this.$refs.formData.resetFields()
      this.formData = {
        order_sn: '', 
        uid: null, 
        nickname: '', 
        status: null, 
        pay_time_start: '',
        pay_time_end: '',
      }
    },
  },
}
</script>
<style scoped>
::v-deep .cell {
  text-align: center;
}
::v-deep .el-table__cell {
  height: 84px;
  line-height: 84px;
}
::v-deep .el-date-editor {
  width: 275px;
}
</style>
<style scoped lang="scss">
.avatar {
  width: 45px;
  height: 45px;
}
.pagination {
  float: right;
}

.w100 {
  width: 100%;
}
</style>

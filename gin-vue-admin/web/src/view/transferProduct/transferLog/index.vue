<template>
  <m-card>
    <el-table :data="tableData">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="图片" align="center">
        <template slot-scope="scope">
          <m-image :src="scope.row.product.image_url" :style="{'width':'60px','height':'60px'}"></m-image>
        </template>
      </el-table-column>
      <el-table-column label="商品">
        <template slot-scope="scope">
          <p class="hiddenText2">{{scope.row.product.title}}</p>
        </template>
      </el-table-column>
      <el-table-column label="原供货渠道" prop="gather_supplier" align="center"></el-table-column>
      <el-table-column label="转移后供货渠道" prop="destination" align="center"></el-table-column>
      <el-table-column label="操作类型" align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.operation === 1">复制</p>
          <p v-else>转移</p>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100,200]"
            :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
    >
    </el-pagination>
  </m-card>
</template>

<script>
import {transferLoglist} from "@/api/transfer"
import infoList from "@/mixins/infoList";

export default {
  name: "transferLogIndex",
  mixins: [infoList],
  data() {
    return {
      listApi: transferLoglist,
    }
  },
  created() {
    this.getTableData()
  }
}
</script>

<style scoped>

</style>
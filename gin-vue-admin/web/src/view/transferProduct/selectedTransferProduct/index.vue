<template>
  <m-card>   
    <el-row>
      <el-form :model="formData" label-width="160px">
        <el-col :span="15">
          <el-form-item label="选择商品：">    
            <div class="demo-image">
              <div class="con-h block relative" v-for="(item,index) in chosenProducts" :key="item.id">
                <el-image class="media-img el-upload-list__item-thumbnail" :src="item.image_url" ></el-image>
                <div class="mask">
                  <span><i class="el-icon-delete" @click="onDeleteImage(index)"></i></span>
                </div>
                <p class="line22">{{item.title}}</p>
              </div>
              <div class="block relative">
                  <div class="img-upload"  @click="tableProduct = true">
                    <i class="el-icon-plus"></i>
                    <p class="icon-txt">可同时选择<br>多款商品</p>
                  </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="15">
            <el-form-item label="选择转移到：">
              <el-radio-group v-model="formData.resource">
                <el-radio v-model="formData.resource" :label="1" >自营</el-radio>
                <el-radio v-model="formData.resource" :label="2">
                  供应商
                  <el-input v-model="chosenSupplier[0].name" :disabled="isDisableSupplier" size="small" class="w200 ml30" placeholder="请选择供应商"></el-input>
                  <el-button type="primary" size="small"  @click="tableSupplier = true" :disabled="isDisableSupplier">选择供应商</el-button>
                </el-radio>            
              </el-radio-group>
            </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item>
            <el-button type="primary" @click="onSave">转移商品</el-button>
          </el-form-item>
        </el-col>
      </el-form>
      <el-drawer       
        title="可同时选择多款商品123"
        :visible.sync="tableProduct"
        @open="initGoodsList"
        @close="clearProductList"
        direction="rtl"
        class="detail-ct"
        size="calc(100% - 220px)">
        <el-row :gutter="20" class="mb30">
          <el-col :span="4"><el-input v-model="productName" placeholder="请输入商品名称" clearable></el-input></el-col>
          <el-col :span="4">
              <el-select v-model="supplier_id" clearable filterable>
                  <el-option label="平台自营" :value="0">
                  </el-option>
                  <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
              </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="gather_supply_id" placeholder="请选择供应链" clearable filterable>
                <el-option label="平台自营" :value="0">
                </el-option>
                <el-option
                    :label="item.name"
                    :value="item.id"
                    :key='item.id'
                    v-for="item in supplyOptions"
                >
                </el-option>
            </el-select>
          </el-col>
          <el-col :span="4"><el-button type="primary" @click="getList">搜索</el-button></el-col>
        </el-row>
        <el-table :data="productList" @selection-change="handleProductChange">
          <el-table-column type="selection" width="55" :selectable="handleSelectable"></el-table-column>
          <el-table-column label="ID" width="60">
            <template slot-scope="scope">
                <p>{{ scope.row.id }}</p>
            </template>
          </el-table-column>
          <el-table-column label="图片" width="140" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <el-popover
                    placement="right"
                    title=""
                    trigger="hover">
                    <m-image :src="scope.row.image_url" :style="{width:'160px',height:'160px'}"></m-image>
                    <m-image slot="reference" :src="scope.row.image_url" :alt="scope.row.image_url" :style="{width:'60px',height:'60px'}"></m-image>
                </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="商品" width="300">
            <template slot-scope="scope">
                <p>{{ scope.row.title }}</p>
            </template>
          </el-table-column>
          <el-table-column label="商品供货价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <span>￥{{ scope.row.price | formatF2Y }}</span>
            </template>
          </el-table-column>
          <el-table-column label="成本价" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <span>￥{{ scope.row.cost_price | formatF2Y }}</span>
            </template>
          </el-table-column>
          <el-table-column label="库存" align="center" prop="stock" show-overflow-tooltip></el-table-column>
          <el-table-column label=销量 align="center" prop="sales" show-overflow-tooltip></el-table-column>
          <el-table-column label="供应渠道" width="200" align="center" show-overflow-tooltip>
            <template slot-scope="scope">
                <span v-if="scope.row.supplier_id > 0">{{ scope.row.supplier.name }}</span>
                <span v-if="scope.row.gather_supply_id > 0">{{ scope.row.gather_supply.name }}</span>
                <span v-if="scope.row.supplier_id === 0 && scope.row.gather_supply_id === 0">自营</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="150" align="center">
            <template slot-scope="scope">
                <el-switch v-model="scope.row.is_display" :active-value="1" :inactive-value="0"
                    :inactive-text="switchStatusStr(scope.row.is_display,scope.row.status_lock)"
                    @change="handleStateChange(scope.row,'is_display')">
                </el-switch>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination 
          background 
          :current-page="page" 
          :page-size="pageSize" 
          :page-sizes="[1, 10, 30, 50, 100,200]"
          :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" 
          :total="total"
          @current-change="handleCurrentChange" 
          @size-change="handleSizeChange"
          layout="total, sizes,prev, pager, next, jumper">
        </el-pagination>
        <el-col :span="13" class="mt30">
          <el-button type="primary" @click="onChoseProduct">选中商品</el-button>
          <el-button @click="tableProduct = false">取消</el-button>   
        </el-col>
      </el-drawer>
      <el-drawer
        title="请选择供应商"
        :visible.sync="tableSupplier"
        @open="initSupplierList"
        @close="clearSupplierList"
        direction="rtl"
        class="detail-ct"
        size="calc(100% - 220px)">
        <el-row :gutter="20" class="mb30">
          <el-col :span="4"><el-input v-model="supplierName" placeholder="请输入供应商名称" clearable></el-input></el-col>
          <el-col :span="4"><el-button type="primary" @click="initSupplierList">搜索</el-button></el-col>
        </el-row>
        <el-table class="con-supplier" :data="supplierList" ref="supplierTable" @select="handleSupplierChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="ID" prop="id" align="center"></el-table-column>
          <el-table-column label="供应商名称" prop="name" align="center"></el-table-column>
          <el-table-column label="登录账户" prop="user_info.userName" align="center"></el-table-column>
          <el-table-column label="姓名" prop="realname" align="center"></el-table-column>
          <el-table-column label="手机号" prop="mobile" align="center"></el-table-column>
          <el-table-column label="分类名称" prop="category_info" align="center">
              <template slot-scope="scope">
                  {{ scope.row.category_info.name }}
              </template>
          </el-table-column>
          <el-table-column label="商品数量" prop="goods_count" align="center">
              <template slot-scope="scope">
                  {{ scope.row.goods_count }}
              </template>
          </el-table-column>
          <el-table-column label="订单总额" prop="order_price_total" align="center">
              <template slot-scope="scope">
                  {{ scope.row.order_price_total | formatF2Y }}
              </template>
          </el-table-column>
          <el-table-column label="结算余额" prop="order_price_total" align="center">
              <template slot-scope="scope">
                  {{ scope.row.settlement_balance | formatF2Y }}
              </template>
          </el-table-column>
          <el-table-column label="结算方式" align="center">
              <template slot-scope="scope">
                  <span v-if="scope.row.deduction_type === 1">独立设置</span>
                  <span v-if="scope.row.deduction_type === 2 || scope.row.deduction_type === 0">平台设置</span>
              </template>
          </el-table-column>
          <el-table-column label="比例(%)" align="center">
              <template slot-scope="scope">
                  {{ scope.row.deduction_ratio / 100 }}
              </template>
          </el-table-column>
          <el-table-column label="日期" align="center">
              <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
              </template>
          </el-table-column>
        </el-table>
        <el-pagination 
          background 
          :current-page="page" 
          :page-size="pageSize" 
          :page-sizes="[10, 30, 50, 100]"
          :style="{ float: 'right', padding: '20px' }" 
          :total="total" 
          @current-change="handleCurrentChangeSupplier"
          @size-change="handleSizeChangeSupplier" 
          layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
        <el-col :span="13" class="mt30">
          <el-button type="primary" @click="onChoseSupplier">选中供应商</el-button>
          <el-button @click="tableSupplier = false">取消</el-button>   
        </el-col>
      </el-drawer>
    </el-row>
  </m-card>
</template>

<script>
import {getProductList, getSupplierOptionList, upStatus} from "@/api/goods"
import { getSupplierList } from "@/api/supplier"
import { setTransferProduct } from "@/api/transfer"
import {getSupplyList} from "@/api/order"
export default {
  name: "selectedTransferProductIndex",
  data() {
    return {
      productName:'',
      supplier_id: null,
      gather_supply_id: null,
      supplierOption:[],
      supplyOptions: [],
      supplierName:'',
      tableProduct: false,
      tableSupplier: false,
      dialog: false,
      loading: false,
      templateSelection: {},//
      radioSupplier: '',//
      tableData: [],//
      formData: {
        resource: 1,
      },
      timer: null,
      //查询商品
      productList:[
        {
          "id": 8, // ID
          "title": "雅诗兰黛眼霜雅诗兰黛眼霜雅诗兰黛眼霜", // 商品
          "price": 15000, // 商品供货价
          "cost_price": 3000,// 成本价
          "activity_price": 32500,
          "stock": 20, //库存
          "sales": 0, // 销量
          "is_display": 0, //is_display === 0 ? '下架' : '上架'"
          "image_url": "http://localhost:8888/data/goSupply/uploads/file/39c52b5716a5b80bcfc4eb071fda0a57_20220510140708.jpg", //图片
          "supplier_id": 0, //供应渠道：supplier_id > 0，值为supplier.name；gather_supply_id > 0，值为gather_supply.name；supplier_id =0 && gather_supply_id = 0，自营
          "gather_supply_id": 0,//见上条
        }
      ],
      //查询供应商
      supplierList:[
        {
          "id": 1, //id
          "name": "农夫山泉股份有限公司", //供应商名称
          "realname": "农夫甲", //姓名
          "mobile": "18346054904", //手机号
          "order_price_total": 0,//订单总额
          "category_info": {
              "name": "饮料", //分类名称
          },
          "deduction_ratio": 0, //比例(%)
          "deduction_type": 2, //结算方式：1：独立设置；2：平台设置
          "user_info": {
              "created_at": 1654655006, //日期
              "userName": "", //登录账户
          },
          "settlement_balance": 0 //结算余额
        }
      ],
      tempProduct:[],
      tempSupplier:[],
      //选中的商品
      chosenProducts:[
        // {
        //   "id": 8, // ID
        //   "title": "雅诗兰黛眼霜雅诗兰黛眼霜雅诗兰黛眼霜", // 商品
        //   "price": 15000, // 商品供货价
        //   "cost_price": 3000,// 成本价
        //   "activity_price": 32500,
        //   "stock": 20, //库存
        //   "sales": 0, // 销量
        //   "is_display": 0, //is_display === 0 ? '下架' : '上架'"
        //   "image_url": "https://tse1-mm.cn.bing.net/th/id/OIP-C.aLXymPfEOjc5XpoQrL-MewHaFf?w=264&h=196&c=7&r=0&o=5&pid=1.7", //图片
        //   "supplier_id": 0, //供应渠道：supplier_id > 0，值为supplier.name；gather_supply_id > 0，值为gather_supply.name；supplier_id =0 && gather_supply_id = 0，自营
        //   "gather_supply_id": 0,//见上条
        // }
      ], 
      //选中的供应商
      chosenSupplier:[
        {
          "id": 0,
          "name":""
        }
      ],
      page:1,
      pageSize:10,
      total:50,
    };
  },
  computed: {
    isDisableSupplier() {
      return this.formData.resource === 1
    }
  },
  mounted() {
    this.getSupply() // 获取供应链
  },
  methods: {
    // 获取供应链
    getSupply() {
        getSupplyList().then(res => {
            this.supplyOptions = res.data.list
        })
    },
    handleSelectable(row,index){
        let v = true
        if(this.chosenProducts && this.chosenProducts.length){
            let obj = this.chosenProducts.find(item => item.id === row.id)
            v = !obj
        }
        return v
    },
    handleClose(done) {
      if (this.loading) {
        return;
      }
      this.$confirm('确定要提交表单吗？')
        .then(_ => {
          this.loading = true;
          this.timer = setTimeout(() => {
            done();
            // 动画关闭需要一定的时间
            setTimeout(() => {
              this.loading = false;
            }, 400);
          }, 2000);
        })
        .catch(_ => {});
    },
    cancelForm() {
      this.loading = false;
      this.dialog = false;
      clearTimeout(this.timer);
    },
    /**
     * 列表商品状态拼装
     * @param v1 商品状态 1上架 0下架
     * @param v2 锁定该状态 1锁定 0未锁定
     * @returns {string}
    */
    switchStatusStr(v1, v2) {
        let s1, s2 = ""
        switch (v1) {
            case 0:
                s1 = "下架"
                break;
            case 1:
                s1 = "上架"
                break;
        }
        switch (v2) {
            case 0:
                s2 = "未锁定"
                break;
            case 1:
                s2 = "已锁定"
                break;
        }
        return s1+s2;
    },
    // 上架, 新品,热卖,推荐,促销
    handleStateChange(row, name) {
        let data = {
            column: name, id: row.id
        }
        if (row.is_display === 1) data.status = 1
        let params = {
            column: "status_lock",
            id: row.id
        }
        upStatus(data).then(r => {
            if (r.code === 0) {
                this.$confirm("是否锁定上下架状态?", "提示", {
                    confirmButtonText: "锁定",
                    cancelButtonText: "不锁定",
                    type: "warning",
                }).then(() => {
                    params.status = 1
                    upStatus(params).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.getList()
                        }
                    })
                }).catch(() => {
                    params.status = 0
                    upStatus(params).then(res => {
                        if (res.code === 0) {
                            this.$message.success(res.msg)
                            this.getList()
                        }
                    })
                })
                // this.getList()
            } else {
                this.$message.error('更新失败')
            }
        })
    },
      initGoodsList(){
          getSupplierOptionList().then(r => {
              this.supplierOption = r.data.list
          })
          this.getList()
      },
    // 获取商品列表
    getList() {
        this.productList = [];
        let para = {
            "page": this.page,
            "pageSize": this.pageSize,
            "filter": 0,
            "title": this.productName,
            "category1_id": "",
            "category2_id": "",
            "category3_id": "",
            "maxPrice": "",
            "minPrice": "",
            "brand_id": "",
            "id": ""
        }
        if(this.supplier_id !== null){
            para.supplier_id = this.supplier_id
        }
        console.log(this.gather_supply_id);
        if (this.productList.gather_supply_id !== "") {
            para.gather_supply_id = this.productList.gather_supply_id
        }
        if (this.gather_supply_id !== null) {
          para.gather_supply_id = this.gather_supply_id;
        }

        /*if (this.productList.supplier_id !== "") {
            para.supplier_id = this.productList.supplier_id
        }  */
        getProductList(para).then(r => {
            if (r.code === 0) {
                this.productList = r.data.list;
                this.total = r.data.total;
                //zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
            } else {
                this.productList = [];
                this.total = 0;
            }
        });
        console.log('当前分页：this.page为',this.page, 'this.pageSize为',this.pageSize)
    },
    handleCurrentChange(page) {
      this.page = page;
      this.getList();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.getList();
    },
    handleCurrentChangeSupplier(page) {
      this.page = page;
      this.initSupplierList();
    },
    handleSizeChangeSupplier(size) {
      this.pageSize = size;
      this.initSupplierList();
    },
    //获取供应商列表
    initSupplierList() {
        let para = {
            "pageSize": this.pageSize,
            "page": this.page,
            "name": this.supplierName,
        }
        getSupplierList(para).then(res => {  
          if (res.code === 0) {
              this.supplierList = res.data.list;
              this.total = res.data.total;
          }
        });
    },
    clearProductList(){
      this.page = 1
      this.pageSize = 10
      this.total =50
        this.supplierOption = []
        this.supplier_id = null
      console.log('关闭时分页：this.page为',this.page, 'this.pageSize为',this.pageSize)
    },
    clearSupplierList(){
      this.page = 1
      this.pageSize = 10
      this.total =50
    },
    handleProductChange(val){
      this.tempProduct = []
      this.tempProduct.push(...val);
      console.log('this.tempProduct',this.tempProduct)
      console.log('val',val)
    },
    onChoseProduct(){
      this.chosenProducts.push(...this.tempProduct)
      this.tableProduct = false
    },
    onDeleteImage(index){
      this.chosenProducts.splice(index,1);
      console.log('删减后',this.chosenProducts)
    },
    handleSupplierChange(selection, row){
      this.$refs.supplierTable.clearSelection();
      this.$refs.supplierTable.toggleRowSelection(row, true)
      this.tempSupplier = [row]
      console.log('this.tempSupplier',this.tempSupplier)
    },
    onChoseSupplier(){
      this.chosenSupplier = this.tempSupplier
      console.log('this.chosenSupplier',this.chosenSupplier)
      this.tableSupplier = false
    },
    onSave(){
      try {
        let para = {
          "product_id":null,
          "type":1, // 1是自营，2是供应商
          "supplier_id":0, 
        }
        para.product_id = this.chosenProducts.map((item)=>item.id)
        para.type = this.formData.resource
        para.supplier_id = this.chosenSupplier[0].id
        if(para.product_id.length>0){
          setTransferProduct(para)
          this.$message.success('转移商品成功')
        }
      } catch (error) {
        this.$message.error('转移商品失败')
      }
    },
    
  },
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
::v-deep .detail-ct {
  .el-drawer__body {
    background-color: #fff;
    overflow: auto;
  }
}
.mt20{ margin-top:20px }
.mt30{ margin-top:30px }
.mb30{ margin-bottom:30px }
.ml30 { margin-left: 30px }
.w200 { width: 200px }
.block { display: block; }
.relative { position: relative;}
.line22 {line-height: 22px;}
.f-grey {color: #a5a4a4;}
.demo-image .block {
    margin: 10px 20px 10px 0;
    text-align: center;
    display: inline-block;
    // width: 20%;
    width: 132px;
    box-sizing: border-box;
    vertical-align: top;
}

.media-img {
    width: 132px;
    height: 132px;
    // width: 100%;
    // height: 100%;
    object-fit: fill;
    box-sizing: border-box;
    border-radius: 4px;
    transition: all .5s cubic-bezier(.55,0,.1,1);
    font-size: 14px;
    color: #606266;
    line-height: 1.8;
    position: relative;
    box-sizing: border-box;
    border-radius: 4px;
    width: 100%;
}
.con-h:hover .mask{
    display: flex; 
    justify-content: center;
    align-items: center;
}
.mask {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%);
    display: none;
    justify-content: center;
    align-items: center;
    width: 132px;
    height: 132px;
    border-radius: 4px;
    background-color: rgba(0,0,0,.5);
    transition: opacity .3s;
    z-index: 2;
    i {
        font-size: 18px;
        color:#fff;
        cursor: pointer;
    }
}

.img-upload {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 131px;
    height: 131px;
    border: 1px solid #e4e7ed;
    border-radius: 3px;
    text-align: center;
    cursor: pointer;
    .el-icon-plus {
        margin-top: 5px;
        font-size: 24px;
        font-weight: 700;
        color: #d9d9d9;
    }
    .icon-txt{
        margin-top: 10px;
        line-height: 1.5;
        font-size: 11px;
        color: rgba(0, 0, 0, 0.45);
    }
    img{
        width: 121px;
        height: 121px;
        vertical-align: middle;
    }
}

::v-deep.con-supplier .el-table__header-wrapper  .el-checkbox{
	display:none
}
</style>
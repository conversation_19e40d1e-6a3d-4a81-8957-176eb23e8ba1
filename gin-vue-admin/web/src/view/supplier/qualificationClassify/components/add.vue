<template>
  <el-dialog
      :title="formData.id?'编辑':'新增'"
      :visible="isShow"
      width="40%"
      :before-close="handleClose">
    <el-form :model="formData" label-width="120px" ref="form" :rules="rules">
      <el-form-item label="资质类型名称:" prop="title">
        <el-input v-model="formData.title" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="状态:" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">开启</el-radio>
          <el-radio :label="2">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="前端显示:" prop="is_display">
        <el-radio-group v-model="formData.is_display">
          <el-radio :label="1">显示</el-radio>
          <el-radio :label="0">隐藏</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="排序:" prop="sort">
        <el-input-number v-model="formData.sort" class="input-number-text-left w100" :controls="false" :precision="0"
                         :min="0"></el-input-number>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="save">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  createSupplierQualificationClassification,
  updateSupplierQualificationClassification
} from "@/api/adminSupplierQualification"

export default {
  name: "qualificationClassifyManageAdd",
  data() {
    return {
      isShow: false,
      formData: {
        title: "",
        status: 1,
        sort: 0,
        is_display: 1, // 1显示 0隐藏
      },
      rules: {
        title: {required: true, message: "请输入资质类型名称", trigger: "blur"},
        status: {required: true, message: "请选择状态", trigger: "change"},
      }
    }
  },
  methods: {
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.formData[element] = val[element];
      });
    },
    handleClose(status = false) {
      try {
        this.$refs.form.resetFields()
      } catch {
      } finally {
        this.isShow = false
        this.formData = {
          title: "",
          status: 1,
          sort: 0,
          is_display: 1
        }
        if (status) this.$emit("reload")
      }

    },
    save() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.formData.id) { // 编辑
            updateSupplierQualificationClassification(this.formData).then(res => {
              if (res.code === 0) {
                this.$message.success(res.msg)
                this.handleClose(true)
              } else {
                this.$message.error(res.msg)
              }
            })
          } else { // 新增
            createSupplierQualificationClassification(this.formData).then(res => {
              if (res.code === 0) {
                this.$message.success(res.msg)
                this.handleClose(true)
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
</style>
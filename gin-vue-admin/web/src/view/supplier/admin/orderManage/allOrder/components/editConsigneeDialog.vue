<!-- 修改收货人dialog -->
<template>
  <el-dialog
    title="修改收货信息"
    :visible="isShow"
    width="40%"
    :before-close="handleClose"
  >
    <el-form :model="formData" label-width="90px" ref="form" :rules="rules">
      <el-form-item label="收货人:" prop="realname">
        <el-input
          v-model="formData.realname"
          placeholder="收货人姓名"
        ></el-input>
      </el-form-item>

      <el-form-item label="联系方式:" prop="mobile">
        <el-input v-model="formData.mobile" placeholder="收货人电话"></el-input>
      </el-form-item>
      <el-form-item label="地址:" prop="selectedOptions">
        <el-cascader
          :options="options"
          v-model="formData.selectedOptions"
          class="w100"
          placeholder="省/市/区"
          clearable
          @change="handleAddrChange"
        >
        </el-cascader>
      </el-form-item>
      <el-form-item label="详细地址:" prop="detail">
        <el-input v-model="formData.detail" placeholder="详细地址"></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { regionData } from "element-china-area-data";
import verify from "@/utils/verify";
export default {
  data() {
    return {
      options: regionData,
      isShow: false,
      formData: {
        selectedOptions: [],
        id: null,
        // 收货人姓名
        realname: "",
        // 联系方式
        mobile: "",
        // 省
        province_id: null,
        // 市
        city_id: null,
        // 区
        country_id: null,
        // 详细地址
        detail: "",
      },
      rules: {
        realname: {
          required: true,
          message: "请输入收货人姓名",
          trigger: "blur",
        },
        mobile: {
          required: true,
          validator: (rule, value, callback) => {
            if (verify.checkPhone(value)) {
              callback();
            } else {
              return callback(new Error("手机号格式不正确"));
            }
          },
          trigger: "blur",
        },
        selectedOptions: {
          required: true,
          message: "请选择地址",
          trigger: "change",
        },
        detail: {
          required: true,
          message: "请输入详细地址",
          trigger: "blur",
        },
      },
    };
  },
  methods: {
    // 处理省/市/区
    handleAddrChange(value) {
      this.formData.province_id = print(value[0]);
      this.formData.city_id = print(value[1]);
      this.formData.country_id = print(value[2]);

      console.log(this.formData.selectedOptions);
    },
    // 确认
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
        } else {
          return false;
        }
      });
    },
    handleClose() {
      this.isShow = false;
      this.$refs.form.resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
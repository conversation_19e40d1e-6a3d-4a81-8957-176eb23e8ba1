<template>
  <el-dialog
      title="商品"
      :visible="isShow"
      width="600px"
      :before-close="handleClose">
    <el-table :data="products">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="图片" width="140" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <el-popover
              placement="right"
              title=""
              trigger="hover">
            <m-image :src="scope.row.thumb" :style="{width:'160px',height:'160px'}"></m-image>
            <m-image slot="reference" :src="scope.row.thumb" :alt="scope.row.thumb"
                     :style="{width:'60px',height:'60px'}"></m-image>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="商品" width="250">
        <template slot-scope="scope">
          <p>{{ scope.row.title }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>供货价</p>
        </template>
        <template slot-scope="scope">
          <p>￥{{ scope.row.price | formatF2Y }}</p>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>
  </el-dialog>

</template>

<script>
export default {
  name: "productsDialog",
  data() {
    return {
      products: [],
      isShow: false
    }
  },
  methods: {
    init(products) {
      this.isShow = true
      this.products = products
    },
    handleClose() {
      this.isShow = false
      this.products = []
    }
  }
}
</script>

<style scoped>

</style>
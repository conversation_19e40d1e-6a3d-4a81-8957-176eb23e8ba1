<template>
  <m-card>
    <el-button @click="openDialog" type="primary">新增</el-button>
    <el-form ref="form" :model="searchInfo" class="search-term mt25" inline>
      <el-form-item prop="companyName">
        <el-input placeholder="请输入" v-model="searchInfo.companyName" class="line-input" clearable>
            <span slot="prepend">公司名称</span>
        </el-input>
      </el-form-item>
      <el-form-item prop="legalPersonName">
        <el-input placeholder="请输入" v-model="searchInfo.legalPersonName" class="line-input" clearable>
            <span slot="prepend">法人姓名</span>
        </el-input>
      </el-form-item>
      <el-form-item prop="member_name">
        <el-input placeholder="请输入" v-model="searchInfo.member_name" class="line-input" clearable>
            <span slot="prepend">会员</span>
        </el-input>
      </el-form-item>
      <el-form-item prop="selectedOptions">
        <div class="line-input">
            <div class="line-box ">
                <span >省市区</span>
            </div>
            <el-cascader clearable class="w100" size="large" :options="options" v-model="searchInfo.selectedOptions"
              @change="handleChangeSearch">
            </el-cascader>
        </div>
    </el-form-item>
    <el-form-item  prop="appLevelId">
      <div class="line-input">
          <div class="line-box ">
              <span >采购端等级</span>
          </div>
          <el-select v-model="searchInfo.appLevelId" value-key="value" placeholder="请选择" label="采购端等级:" class="w100"
          clearable>
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
      </div>
    </el-form-item>
      <!-- <el-row :gutter="10">
        <el-col :span="6">
          <el-form-item label="公司名称:" prop="companyName">
            <el-input placeholder="请输入" v-model="searchInfo.companyName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="法人姓名:" prop="legalPersonName">
            <el-input placeholder="请输入" v-model="searchInfo.legalPersonName"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="会员:" prop="member_name">
            <el-input placeholder="请输入" v-model="searchInfo.member_name"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="省市区:" prop="selectedOptions">
            <el-cascader clearable class="w100" size="large" :options="options" v-model="searchInfo.selectedOptions"
              @change="handleChangeSearch">
            </el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购端等级:" prop="appLevelId">
            <el-select v-model="searchInfo.appLevelId" value-key="value" placeholder="请选择" label="采购端等级:" class="w100"
              clearable>
              <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
      <br/>
      <el-form-item>
        <el-button @click="onSubmit" type="primary">查询</el-button>
        <el-button type="text" @click="resetSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="tableData" @selection-change="handleSelectionChange" class="mt25">
      <!-- <el-table-column label="日期" align="center">
        <template slot-scope="scope"
          >{{ scope.row.created_at | formatDate }}
        </template>
</el-table-column>

<el-table-column label="公司名称" prop="companyName" align="center"></el-table-column>

<el-table-column label="信用代码" prop="creditCode" align="center"></el-table-column>

<el-table-column label="法人姓名" prop="legalPersonName" align="center"></el-table-column>

<el-table-column label="联系人姓名" prop="contactsName" align="center"></el-table-column>

<el-table-column label="联系人电话" prop="contactsPhontnumber" align="center"></el-table-column> -->
      <el-table-column label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="会员/手机号" align="center">
        <template slot-scope="scope">
          <p>{{ scope.row.user.nickname }}</p>
          <p>{{ scope.row.user.username }}</p>
        </template>
      </el-table-column>
      <el-table-column label="订单数量" prop="order_count" align="center"></el-table-column>

      <el-table-column label="订单金额" align="center">
        <template slot-scope="scope">{{ scope.row.order_amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center" prop="companyName"></el-table-column>
      <el-table-column label="采购端名称" prop="appName" align="center"></el-table-column>

      <el-table-column label="采购端等级" prop="appLevelId" align="center">
        <template slot-scope="scope">{{ scope.row.applicationLevel.levelName }}
        </template>
      </el-table-column>

      <el-table-column label="站内余额" align="center" prop="balance">
        <template slot-scope="scope">
          <p> {{ scope.row.user.balance.purchasing_balance | formatF2Y }} </p>
          <p v-if="isChargeBill"><el-button type="text" class="p0" @click="topUp(scope.row)">充值</el-button></p>
        </template>
      </el-table-column>

      <el-table-column label="运营状态" prop="status_code" align="center">
        <template slot-scope="scope">{{ scope.row.status_code == 0 ? '测试中' : '运营中' }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="360" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="getKey(scope.row)">重新生成密钥</el-button>
          <el-button type="text" @click="checkKey(scope.row)">查看密钥</el-button>
          <!--<el-button type="text" @click="syncData(scope.row)">同步</el-button>-->
          <el-button type="text" @click="detail(scope.row)">查看</el-button>
          <el-button @click="updateApplication(scope.row)" type="text">编辑</el-button>
          <!--<el-button type="text" class="color-red" @click="deleteRow(scope.row)">删除</el-button> -->
          <!--          <el-button type="text" @click="onBindSupplier(scope.row.id)">解绑</el-button>-->
        </template>
      </el-table-column>
    </el-table>

    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
      :style="{ float: 'right', padding: '20px' }" :total="total" @current-change="handleCurrentChange"
      @size-change="handleSizeChange" layout="total, sizes, prev, pager, next, jumper"></el-pagination>

    <el-dialog :before-close="closeDialog" :visible.sync="dialogFormVisible" :title="titleDialog">
      <el-form :model="formData" ref="form" :rules="rules" label-position="right" label-width="120px">
        <el-form-item label="公司名称:">
          <el-input v-model="formData.companyName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="公司介绍:">
          <el-input v-model="formData.companyIntro" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="省市区:">
          <el-cascader :options="options" v-model="selectedOptions" @change="handleChange" titleDialog class="w100">
          </el-cascader>
        </el-form-item>

        <el-form-item label="详细地址:">
          <el-input v-model="formData.address" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="信用代码:">
          <el-input v-model="formData.creditCode" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="营业执照:">
          <upload-image v-model="formData.businessLicense" :fileSize="512" :maxWH="1080" />
        </el-form-item>

        <el-form-item label="法人姓名:">
          <el-input v-model="formData.legalPersonName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="法人身份证号码:">
          <el-input v-model="formData.idCardNumber" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="法人身份证正面:">
          <upload-image v-model="formData.idCardFront" :fileSize="512" :maxWH="1080" />
        </el-form-item>

        <el-form-item label="法人身份证背面:">
          <upload-image v-model="formData.idCardBackend" :fileSize="512" :maxWH="1080" />
        </el-form-item>

        <el-form-item label="联系人姓名:">
          <el-input v-model="formData.contactsName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="联系人电话:">
          <el-input v-model="formData.contactsPhontnumber" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="联系人邮箱:">
          <el-input v-model="formData.contactsEmail" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="采购端名称:">
          <el-input v-model="formData.appName" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="采购端等级:" prop="appLevelId">
          <el-select v-model="formData.appLevelId" value-key="value" placeholder="请选择" label="采购端等级:" titleDialog
            class="w100">
            <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="回调地址:" prop="callBackLink">
          <div style="display: flex;">
            <el-input v-model="formData.callBackLink" clearable placeholder="请输入"></el-input>
            <el-button class="verify" type="primary" @click="verifyfun">校验</el-button>
          </div>
        </el-form-item>
        <el-form-item label="运营状态:" prop="status_code">
          <el-radio-group v-model="formData.status_code" @change="statusCode('callBackLink')">
            <el-radio :label="0">测试中</el-radio>
            <el-radio :label="1">运营中</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="IP白名单:">
          <el-input v-model="formData.ipList" type="textarea" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="会员:" prop="memberId">
          <!-- <el-input
            v-model.number="formData.memberId"
            clearable
            placeholder="请输入"
          ></el-input> -->
          <el-select v-model="formData.memberId" filterable class="w100">
            <el-option v-for="item in userOption" :key="item.id" :label="item.username" :value="item.id"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="云仓供应商:" prop="supplier_id">
          <el-select v-if="this.titleDialog == '编辑'" v-model="formData.supplier_id" class="w120">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-select v-else v-model="this.resData.SupplierID" disabled="">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商:" prop="pet_supplier_id">
          <el-select v-if="this.titleDialog == '编辑'" v-model="formData.pet_supplier_id" class="w120">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
          <el-select v-else v-model="this.resData.SupplierID" disabled="">
            <el-option v-for="item in supplierOption" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div class="dialog-footer" slot="footer">
        <el-button @click="enterDialog" type="primary">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </el-dialog>

    <applicationDetail ref="applicationDetail"></applicationDetail>

    <!-- key dialog -->
    <el-dialog title="查看密钥" :visible="keyDialogIsShow" width="40%" :before-close="handleKeyDialogClose">
      <el-form :model="keyData" label-width="90px">
        <el-form-item label="appKey:">
          <p>
            {{ keyData.appKey }}
            <a href="javascript:;" class="ml10 color-red" @click="$fn.copy(keyData.appKey)">复制</a>
          </p>
        </el-form-item>
        <el-form-item label="appSecret:">
          <p>
            {{ keyData.appSecret | formatSecret }}
            <a href="javascript:;" class="ml10 color-red" @click="$fn.copy(keyData.appSecret)">复制</a>
          </p>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleKeyDialogClose">关 闭</el-button>
      </div>
    </el-dialog>
    <!-- 充值组件 -->
    <recharge-dialog ref="rechargeDialog" @callBackInitMermberList="getTableData"></recharge-dialog>
  </m-card>
</template>

<script>
import {
  createApplication,
  deleteApplication,
  deleteApplicationByIds,
  updateApplication,
  findApplication,
  getApplicationList,
  getApplicationLevelOption,
  createApplicationKeySecret,
  bindSupplier,
  validateCallback
} from "@/api/application"; //  此处请自行替换地址
import { findSetting } from "@/api/supplierSetting";
import { getSupplierOptionList } from "@/api/order";
import { syncPriduct } from "@/api/goods";
import { formatTimeToStr } from "@/utils/date";
import infoList from "@/mixins/infoList";
import UploadImage from "@/components/upload/image.vue";
import { regionData } from "element-china-area-data";
import applicationDetail from "./components/applicationDetail";
import { getUserList } from "@/api/member";
import RechargeDialog from "./components/rechargeDialog"

export default {
  name: "SupplierAdminPurchaseManageIndex",
  mixins: [infoList],
  components: {
    UploadImage,
    applicationDetail,
    RechargeDialog
  },
  data() {
    return {
      rules: {
        appLevelId: {
          required: true,
          message: "请选择采购端等级",
          trigger: "change",
        },
        callBackLink: {
          required: false,
          message: "请输入回调地址",
          trigger: "blur",
        },
        memberId: { required: true, message: "请选择会员", trigger: "change" },
      },
      userOption: [],
      supplierOption: [],
      titleDialog: "新增",
      listApi: getApplicationList,
      levelApi: getApplicationLevelOption,
      isChargeBill: false,
      dialogFormVisible: false,
      type: "",
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        companyName: "",
        companyIntro: "",
        provinceId: 0,
        cityId: 0,
        districtId: 0,
        address: "",
        creditCode: "",
        businessLicense: "",
        legalPersonName: "",
        idCardNumber: "",
        idCardFront: "",
        idCardBackend: "",
        contactsName: "",
        contactsPhontnumber: "",
        contactsEmail: "",
        appName: "",
        appLevelId: "",
        callBackLink: "",
        ipList: "",
        memberId: null,
        supplier_id: null,
        pet_supplier_id: null,
        status_code: 0
      },
      levelOptions: [],
      options: regionData,
      selectedOptions: [],
      keyData: {},
      keyDialogIsShow: false,
    };
  },
  mounted() {
    this.getTableData();
    this.onChargeBill()
  },
  filters: {
    formatSecret: function (str) {
      if (str) {
        let leng = str.length;
        let cent = Math.ceil(leng / 2);
        return str.replace(str.slice(cent - 5, cent + 5), "**********");
      }
    },
    formatDate: function (time) {
      if (time != null && time != "") {
        var date = new Date(time);
        return formatTimeToStr(date, "yyyy-MM-dd hh:mm:ss");
      } else {
        return "";
      }
    },
    formatBoolean: function (bool) {
      if (bool != null) {
        return bool ? "是" : "否";
      } else {
        return "";
      }
    },
  },
  methods: {
    //充值按钮是否禁用
    onChargeBill() {
      findSetting().then((res) => {
        const purviewCode = res.data.resupplierSetting.value.recharge_switch
        if (purviewCode == 1) {
          this.isChargeBill = true
        }
      });
    },
    // 充值
    topUp(row) {
      this.$refs.rechargeDialog.isShow = true
      this.$nextTick(() => {
        this.$refs.rechargeDialog.formData.uid = row.user.id
      })
    },
    // 获取供应资源
    getSupplier() {
      getSupplierOptionList().then((res) => {
        this.supplierOption = res.data.list;
      });
    },
    getUserOption() {
      getUserList({ page: 1, pageSize: 9999 }).then((res) => {
        if (res.code === 0) {
          this.userOption = res.data.list;
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    syncData(row) {
      syncPriduct({ id: row.id }).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg);
        }
      });
    },
    // 关闭查看密钥dialog
    handleKeyDialogClose() {
      this.keyDialogIsShow = false;
      this.keyData = {};
    },
    // 生成密钥
    getKey(row) {
      this.$confirm("是否确认重新生成密钥?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        createApplicationKeySecret({ id: row.id }).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            this.getTableData();
          }
        });
      });
    },
    // 查看密钥
    checkKey(row) {
      this.keyDialogIsShow = true;
      this.keyData.appKey = `application${row.id}`;
      this.keyData.appSecret = row.appSecret;
    },
    detail(row) {
      this.$refs.applicationDetail.isShow = true;
      this.$refs.applicationDetail.getLevelOption(row.id);
      this.$refs.applicationDetail.getApplication(row.id);
    },
    handleChangeSearch(value) {
      this.searchInfo.provinceId = parseInt(value[0] == null ? "0" : value[0]);
      this.searchInfo.cityId = parseInt(value[1] == null ? "0" : value[1]);
      this.searchInfo.districtId = parseInt(value[2] == null ? "0" : value[2]);
    },
    handleChange(value) {
      this.formData.provinceId = parseInt(value[0]);
      this.formData.cityId = parseInt(value[1]);
      this.formData.districtId = parseInt(value[2]);
    },
    resetSearch() {
      this.$refs.form.resetFields();
      this.page = 1;
      this.total = 0;
      this.pageSize = 10;
      this.searchInfo = {};
      this.getTableData();
    },
    //条件搜索前端看此方法
    onSubmit() {
      this.page = 1;
      this.pageSize = 10;
      this.getTableData();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    onBindSupplier(cid) {
      this.$confirm("确定要解绑吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        bindSupplier({ id: cid }).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg);
            //this.getTableData();
          }
        });
      });
    },
    deleteRow(row) {
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.deleteApplication(row);
      });
    },
    async onDelete() {
      const ids = [];
      if (this.multipleSelection.length == 0) {
        this.$message({
          type: "warning",
          message: "请选择要删除的数据",
        });
        return;
      }
      this.multipleSelection &&
        this.multipleSelection.map((item) => {
          ids.push(item.id);
        });
      const res = await deleteApplicationByIds({ ids });
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length == ids.length) {
          this.page--;
        }
        this.deleteVisible = false;
        this.getTableData();
      }
    },
    async updateApplication(row) {
      this.getUserOption();
      this.titleDialog = "编辑";
      const res = await findApplication({ id: row.id });
      this.type = "update";
      if (res.code == 0) {
        this.formData = res.data.reapplication;
        this.selectedOptions = [
          String(this.formData.provinceId),
          String(this.formData.cityId),
          String(this.formData.districtId),
        ];
        this.dialogFormVisible = true;
      }
    },
    closeDialog() {
      try {
        this.$refs.form.resetFields();
      } catch {
      } finally {
        this.dialogFormVisible = false;
        this.titleDialog = "新增";
        this.formData = {
          companyName: "",
          companyIntro: "",
          provinceId: 0,
          cityId: 0,
          districtId: 0,
          address: "",
          creditCode: "",
          businessLicense: "",
          legalPersonName: "",
          idCardNumber: "",
          idCardFront: "",
          idCardBackend: "",
          contactsName: "",
          contactsPhontnumber: "",
          contactsEmail: "",
          appName: "",
          appLevelId: "",
          callBackLink: "",
          ipList: "",
          memberId: null,
          status_code: 0
        };
        this.selectedOptions = [];
      }
    },
    async deleteApplication(row) {
      const res = await deleteApplication({ id: row.id });
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length == 1) {
          this.page--;
        }
        this.getTableData();
      }
    },
    async enterDialog() {
      console.log(this.formData);
      let valid = await this.$refs.form.validate();
      if (valid) {
        let res;
        this.formData.appLevelId = parseInt(this.formData.appLevelId);
        switch (this.type) {
          case "create":
            res = await createApplication(this.formData);
            break;
          case "update":
            res = await updateApplication(this.formData);
            break;
          default:
            res = await createApplication(this.formData);
            break;
        }

        if (res.code == 0) {
          this.$message({
            type: "success",
            message: "创建/更改成功",
          });
          this.closeDialog();
          this.getTableData();
        }
      } else {
        return false;
      }
    },
    openDialog() {
      this.getUserOption();
      this.type = "create";
      this.dialogFormVisible = true;
    },
    async setLevelOption() {
      const options = await this.levelApi();
      if (options.code == 0) {
        options.data.list.map((item) => {
          const option = {
            value: item.id,
            label: item.levelName,
          };
          this.levelOptions.push(option);
        });
      }
    },
    statusCode(res) {
      console.log(this.formData.status_code, 1);
      if (this.formData.status_code == 0) {
        this.rules.callBackLink = {
          required: false,
          message: "请输入回调地址",
          trigger: "blur",
        }
      } else {
        this.rules.callBackLink = {
          required: true,
          message: "请输入回调地址",
          trigger: "blur",
        }
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate(res);
      });
    },
    async verifyfun() {
      const res = await validateCallback({ link: this.formData.callBackLink })
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: res.msg,
        })
      }
    },
  },
  async created() {
    await this.getTableData();
    await this.setLevelOption();
    this.getSupplier();
  },
};
</script>


<style lang="scss" scoped>
@import "@/style/base.scss";

.p0 {
  padding: 0;
}

.verify {
  margin-left: 10px;
}
</style>
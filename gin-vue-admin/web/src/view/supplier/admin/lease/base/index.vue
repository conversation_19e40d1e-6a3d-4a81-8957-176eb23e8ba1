<template>
  <m-card>
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane name="1" label="基础设置">
        <el-form ref="elForm" class="mt25" :model="formData"  label-width="120px">
          <el-form-item label="租赁商品:">
            <div class="f fac">
              <el-form-item>
                <el-radio :label="0" v-model="formData.is_open">关闭</el-radio>
              </el-form-item>
              <el-form-item style="margin-left: 50px" prop="sellt_type">
                <el-radio :label="1" v-model="formData.is_open">开启</el-radio>
              </el-form-item>
            </div>
          </el-form-item>
<!--          <el-form-item label="聚合页PC端链接:">-->
<!--            <el-button @click="$router.push({name:'supplierLeaseReleaseGoods'})">复制链接</el-button>-->
<!--          </el-form-item>-->
          <el-form-item label="起租金额:"  class="form-box">
            <el-input-number  :min="0" :controls="false" v-model="formData.start_amount"></el-input-number>
            <span class="paixutext">如果总租金小于起租金额,则无法下单</span>
          </el-form-item>
          <el-form-item label="租赁协议:">
            <m-editor v-model="formData.leasing_agreement"></m-editor>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">保存设置</el-button>
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </m-card>
</template>
<script>
import {getSysLeaseSetting,saveSysLeaseSetting,getLeaseTenancyTermList} from "@/api/supplier/rent";
import {mapGetters} from "vuex";

const path = process.env.VUE_APP_BASE_API;
export default {
  name: "SupplierSetting",
  components: {},
  props: [],
  data() {
    return {
      activeName: "1",
      sellt_mode: null,
      formData: {
        is_open:0,
        // pc_url:"http://localhost:8888",
        start_amount:10,
        leasing_agreement:""
      },
      formData2:{},
    };
  },
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  watch: {},
  mounted() {
    this.getSetting()
  },
  async created() {

  },
  methods: {
    submitForm() {
      if(this.formData2.value.start_amount){
        this.formData2.value.start_amount = this.formData2.value.start_amount * 100
      }
      console.log(this.formData2.value.start_amount)
      saveSysLeaseSetting(this.formData2).then((res) => {
        if(res.code === 0){
          this.$message.success(res.msg)
          this.getSetting()
        }else{
          this.$message.error(res.msg)
        }
      })
    },
    getSetting(){
      getSysLeaseSetting().then((res) => {
        if(res.code === 0){
          this.formData = res.data.value
          this.formData.start_amount = res.data.value.start_amount / 100
          this.formData2 = res.data
        }
      })
    }
  },
};
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";
.el-upload__tip {
  line-height: 1.2;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
::v-deep .form-box {
  .el-input-number {
    width: 100%;
    .el-input__inner {
      text-align: left;
    }
  }
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.paixutext{
  color:#AFAFAF
}
</style>
<template>
  <el-drawer title="详情" :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
             :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
    <m-card :titIsShow="true">
      <p class="h4l4" slot="title">公司信息</p>
      <p class="h4l4">公司名称: {{ appInfo.application.companyName }}</p>
      <p class="h4l4">信用代码: {{ appInfo.application.creditCode }}</p>
      <div class="f fac">
        <p class="h4l4">公司地址:</p>
        <el-cascader style="margin-left: 5px;width: 250px" :options="options" v-model="selectedOptions" titleDialog
                     disabled>
        </el-cascader>
      </div>
      <p class="h4l4">详细地址: {{ appInfo.application.address }}</p>
      <p class="h4l4">公司介绍: {{ appInfo.application.companyIntro }}</p>
      <div class="f">
        <p class="h4l4">营业执照:</p>
        <div style="margin-left: 10px" @click="openDialogImg(appInfo.application.businessLicense)">
          <m-image :src="appInfo.application.businessLicense"
                   :style="{width:'250px',height:'180px'}"></m-image>
        </div>
      </div>

    </m-card>
    <m-card :titIsShow="true" class="mt25">
      <p class="h4l4" slot="title">法人信息</p>
      <p class="h4l4">法人姓名: {{ appInfo.application.legalPersonName }}</p>
      <p class="h4l4">身份证号: {{ appInfo.application.idCardNumber }}</p>
      <div class="f">
        <p class="h4l4">身份证正反面:</p>
        <div style="margin-left: 10px" class="f fac">
          <div @click="openDialogImg(appInfo.application.idCardFront)">
            <m-image :src="appInfo.application.idCardFront"
                     :style="{width:'250px',height:'180px'}"></m-image>
          </div>
          <div @click="openDialogImg(appInfo.application.idCardBackend)">
            <m-image :src="appInfo.application.idCardBackend"
                     :style="{width:'250px',height:'180px',marginLeft:'20px'}">
            </m-image>
          </div>
        </div>
      </div>
    </m-card>
    <m-card :titIsShow="true" class="mt25">
      <p class="h4l4" slot="title">联系人信息</p>
      <p class="h4l4">姓名: {{ appInfo.application.contactsName }}</p>
      <p class="h4l4">邮箱: {{ appInfo.application.contactsEmail }}</p>
      <p class="h4l4">联系电话: {{ appInfo.application.contactsPhontnumber }}</p>
    </m-card>
    <m-card :titIsShow="true" class="mt25">
      <p class="h4l4" slot="title">采购端信息</p>
      <!--      <p>采购端名称: {{ appInfo.appName }}</p>-->
      <p class="h4l4" v-for="item in levelOption" :key="item.id" v-if="item.id === appInfo.application.appLevelId">采购端等级:
        {{ item.levelName }}</p>
      <!--      <p>回调地址: {{appInfo.callBackLink}}</p>-->
      <!--      <p>IP白名单: {{appInfo.ipList}}</p>-->
      <!--      <p>支付方式顺序: <span class="pay-sort-span" v-for="item in application_pay_sort" :key="item.id">{{item.name}}<span>-></span></span></p>-->
      <!--        <p>会员: {{appInfo.memberId}}</p>-->
    </m-card>
    <m-card :titIsShow="true" class="mt25">
      <p class="h4l4" slot="title">申请状态</p>
      <template v-if="detailShow">
        <el-button type="primary" @click="audit">通过审核</el-button>
        <el-button type="danger" @click="reject">驳回</el-button>
      </template>
      <p class="h4l4" v-if="appInfo.status === 1">申请状态:
        <el-tag type="success">已通过</el-tag>
      </p>
      <p class="h4l4" v-if="appInfo.status === 2">申请状态:
        <el-tag type="danger">已驳回</el-tag>
      </p>
    </m-card>
    <reject-dialog ref="rejectDialog" @reload="btnEmit"></reject-dialog>
    <el-dialog :visible="dialogVisible" @close="handleDialogImgClose" :modal-append-to-body="false"
               :append-to-body="true">
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </el-drawer>
</template>

<script>
import {regionData} from "element-china-area-data";
import {updateApplicationApply} from "@/api/applicationAudit"
import {getApplicationLevelOption} from "@/api/application";
import RejectDialog from "./rejectDialog";

export default {
  name: "appApplyDetail",
  components: {RejectDialog},
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: "",
      isShow: false,
      options: regionData,
      appInfo: {
        application: {}
      },
      levelOption: [],
      selectedOptions: [],
      detailShow: true
    }
  },
  methods: {
    openDialogImg(url) {
      console.log(url, '???');
      if (url) {
        this.dialogVisible = true
        this.dialogImageUrl = url
      }
    },
    handleDialogImgClose() {
      this.dialogVisible = false
      this.dialogImageUrl = ""
    },
    btnEmit() {
      this.detailShow = false
      this.appInfo.status = 2
    },
    //驳回
    reject() {
      this.$refs.rejectDialog.isShow = true
      this.$refs.rejectDialog.setForm(this.appInfo)
    },
    audit() {
      let obj = {
        id: this.appInfo.id,
        user_id: this.appInfo.user_id,
        application_id: this.appInfo.application_id,
        status: 1
      }
      updateApplicationApply(obj).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.appInfo.status = 1
          this.detailShow = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    init() {
      if (this.appInfo.status !== 0) {
        this.detailShow = false
      }
      this.selectedOptions = [
        this.appInfo.application.provinceId.toString(),
        this.appInfo.application.cityId.toString(),
        this.appInfo.application.districtId.toString(),
      ];
      getApplicationLevelOption().then(res => {
        if (res.code === 0) {
          this.levelOption = res.data.list

        } else {
          this.$message.error(res.msg)
        }

      })
    },
    handleClose(reload = true) {
      this.isShow = false
      this.appInfo = {
        application: {}
      }
      if (reload === true) {
        this.$emit("reload")
      }
    },
  }
}
</script>

<style scoped>
</style>
<template>
  <div class="big">
    <!--    <SelectClassify v-if="step === 1" @handleNext="handleNext"></SelectClassify>-->
    <m-card>
      <el-button type="primary" @click="submitGoods">保存商品</el-button>
      <div class="addGoods-box mt25">
        <el-form label-width="130px">
          <el-tabs type="card" v-model="activeName" @tab-click="handleTabClick">
            <el-tab-pane label="基本信息" name="1">
              <!-- 基本信息 -->
              <el-row>
                <el-col :span="16">
                  <!--                                <el-form-item>
                                                      <span slot="label">已选分类/品牌 <span class="color-red">*</span></span>
                                                      <p>{{ goodsClassifyStr }}</p>
                                                  </el-form-item>-->
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>分类:</span>
                    <el-form-item>
                      <el-select filterable v-model="classifyCheck1"
                                 @change="getClassify2o3(2,classifyCheck1)">
                        <el-option v-for="item in classifyList1" :key="item.id" :label="item.name"
                                   :value="item.id">
                        </el-option>
                      </el-select>
                      <el-select filterable class="ml10" clearable v-model="classifyCheck2"
                                 @change="getClassify2o3(3,classifyCheck2)">
                        <el-option v-for="item in classifyList2" :key="item.id" :label="item.name"
                                   :value="item.id">
                        </el-option>
                      </el-select>
                      <el-select filterable class="ml10" clearable v-model="classifyCheck3">
                        <el-option v-for="item in classifyList3" :key="item.id" :label="item.name"
                                   :value="item.id">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-form-item>
                  <el-form-item label="品牌:">
                    <el-select v-model="brand_id" clearable filterable remote
                               :remote-method="remoteMethod" :loading="brandsOptiosData.loading">
                      <el-option v-for="item in brandsOptiosData.brandOptios" :key="item.id" :label="item.name"
                                 :value="item.id"></el-option>
                      <div class="text-center">
                        <el-pagination background small class="pagination"
                                       style="padding-top:10px !important;padding-bottom: 0 !important;"
                                       :current-page="brandsOptiosData.page"
                                       :page-size="brandsOptiosData.pageSize"
                                       :total="brandsOptiosData.total"
                                       @current-change="handleBrandPage"
                                       layout="prev,pager, next"/>
                      </div>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="排序:">
                    <el-input-number :min="0" :controls="false" v-model="goodsSort"></el-input-number>
                  </el-form-item>
                  <div class="f fac" v-if="supplier_id">
                    <el-form-item label="商品来源:">
                      <el-select v-model="source_id" clearable filterable>
                        <el-option v-for="item in source_options" :key="item.id" :label="item.name"
                                   :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item label="分类:">
                      <el-select v-model="source_classify_id" clearable filterable>
                        <el-option v-for="item in source_classify_options" :key="item.id" :label="item.name"
                                   :value="item.id"></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>商品名称:</span>
                    <el-input v-model="goodsTitle"></el-input>
                  </el-form-item>
                  <el-form-item label="商品描述:">
                    <el-input v-model="goodsDesc"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>商品主图:</span>
                    <el-upload class="avatar-uploader" :show-file-list="false"
                               :action="path+'/fileUploadAndDownload/upload'"
                               :headers="{'x-token':token}"
                               :on-success="handleMainImgSuccess" :before-upload="$fn.beforeAvatarUpload"
                               accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                      <img v-if="goodsImageUrl" :src="goodsImageUrl" class="avatar"/>
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <p class="color-grap">建议尺寸：400*400像素,图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
                  </el-form-item>
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>商品图片:</span>
                    <div class="f fac">
                      <p>({{ goodsGallery.length }}/9)</p>
                      <el-button type="text" class="ml10" @click="openDialogSort(1)">点击进行排序
                      </el-button>
                    </div>
                    <div class="f fw">
                      <div class="productImgBox" v-for="(item,index) in goodsGalleryFileList"
                           :key="item.id">
                        <m-image v-if="item.type === 1" :src="item.url"
                                 style="width:180px;height:180px"/>
                        <div class="del-box">
                          <el-button type="text" icon="el-icon-delete"
                                     @click="removeGoodsGalleryList(index)"></el-button>
                        </div>
                      </div>
                      <el-upload v-if="goodsGalleryFileList.length < 9" class="avatar-uploader" multiple
                                 list-type="picture-card"
                                 :action="path+'/fileUploadAndDownload/upload'"
                                 :headers="{'x-token':token}" :on-success="handleGoodsGallerySuccess"
                                 :before-upload="$fn.beforeAvatarUpload"
                                 :show-file-list="false"
                                 accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                        <i class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                      <!--                                        <el-upload :limit="9" class="avatar-uploader" multiple
                                                                         :on-exceed="handle9Exceed"
                                                                         list-type="picture-card"
                                                                         :action="path+'/fileUploadAndDownload/upload'"
                                                                         :headers="{'x-token':token}" :on-success="handleGoodsGallerySuccess"
                                                                         :before-upload="beforeAvatarUpload"
                                                                         :show-file-list="false"
                                                                         accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                                                                  <i class="el-icon-plus avatar-uploader-icon"></i>
                                                              </el-upload>-->
                    </div>
                    <p class="color-grap">建议尺寸：400*400像素,最多上传9个素材,单张图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
                  </el-form-item>
                  <el-form-item label="首图视频:">
                    <div class="f fac">
                      <el-progress v-if="progressIsShow" type="circle" :width="178"
                                   :percentage="progressNum" style="margin-right: 8px;"></el-progress>
                      <div v-if="videoUrl && !progressIsShow" class="video-box">
                        <video controls="controls" :src="videoUrl"
                               style="width: 100%;height: 100%"></video>
                      </div>
                      <el-upload class="avatar-uploader" :show-file-list="false"
                                 :action="path+'/fileUploadAndDownload/upload'"
                                 :headers="{'x-token':token}"
                                 :on-success="handleMainVideoSuccess" :on-progress="videoPrigress"
                                 :on-error="videoError" :before-upload="$fn.beforeVideoUpload"
                                 accept=".mp4,.3gp,.avi">
                        <i v-if="videoUrl" class="el-icon-edit-outline avatar-uploader-icon"></i>
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>

                    </div>
                    <el-button v-if="videoUrl" type="text" @click="removeVideo">移除视频</el-button>
                    <p class="color-grap">设置后商品详情首图默认显示视频,建议时长9-30秒</p>
                  </el-form-item>
                  <el-form-item label="商品状态:">
                    <el-radio-group v-model="is_display">
                      <el-radio :label="1">上架</el-radio>
                      <el-radio :label="0">下架</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="锁定上下架:">
                    <el-radio-group v-model="status_lock">
                      <el-radio :label="1">是</el-radio>
                      <el-radio :label="0">否</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="历史销售:">
                    <el-input-number :min="0" style="max-width: 200px" :controls="false"
                                     v-model="goodsSales"></el-input-number>
                    <p>前端真实销售 = 历史销售 + 系统销售</p>
                  </el-form-item>
                  <el-form-item label="售后及服务:" v-show="false">
                    <el-input type="textarea" :rows="6" placeholder="填写特殊说明,不超过100字"
                              v-model="goodsService"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>运费设置:</span>
                    <el-radio  v-model="goodsFreightType" label="0" v-if="">
                      <span>统一运费</span>
                      <el-input-number :min="0" :controls="false" class="ml10" :precision="2"
                                       :disabled="goodsFreightType === '3' || goodsFreightType === '2' || goodsFreightType === '1' ? true : false"
                                       style="max-width: 200px" v-model="goodsFreightPrice">
                      </el-input-number>
                      <span class="ml10">元</span>
                    </el-radio>
                    <br>
                    <el-radio v-model="goodsFreightType" label="1"
                              class="mt10">
                      <span>运费模板</span>
                      <el-select v-model="freight_template_id" filterable
                                 :disabled="goodsFreightType === '3' || goodsFreightType === '2' || goodsFreightType === '0' ? true : false"
                                 class="ml10 w200">
                        <el-option v-for="item in freightTemplateOption" :key="item.id"
                                   :label="item.name" :value="item.id"></el-option>
                      </el-select>
                      <el-button type="text" class="ml10" @click="addTemp">新建</el-button>
                      <el-button type="text" class="ml10" :loading="btnLoadIsShow"
                                 @click="getFreightTemp(true)">刷新
                      </el-button>
                    </el-radio>
                    <br>
                    <el-radio v-model="goodsFreightType"  label="3"
                              class="mt10">
                      <span>包邮</span>
                    </el-radio>
                    <br>
                    <!--                <el-radio v-if="goodsFreightType === '2'" v-model="goodsFreightType" label="2"-->
                    <!--                          class="mt10">-->
                    <!--                  <span>第三方运费</span>-->
                    <!--                </el-radio>-->
                  </el-form-item>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="商品详情" name="2">
              <!-- 商品详情 -->
              <!--                        <quill-editor ref="myTextEditor" v-model="goodsDetailImages" :options="editorOption"-->
              <!--                            style="height:400px;width: 100%;padding-bottom: 100px"></quill-editor> -->
              <m-editor v-model="goodsDetailImages" style="height: 700px;"></m-editor>


              <!-- <el-form-item>
                  <span slot="label"><span class="color-red"></span>商品详情:</span>
                  <div class="f fac">
                      <p>({{ goodsDetailImages.length }}/9)</p>
                      <el-button type="text" class="ml10" @click="openDialogSort(2)">点击进行排序
                      </el-button>
                  </div>
                  <el-upload :limit="9" class="avatar-uploader" list-type="picture-card"
                      :action="path+'/fileUploadAndDownload/upload'" :headers="{'x-token':token}"
                      :on-success="handleGoodsDetailImagesSuccess" :on-remove="removeGoodsDetailImagesList"
                      :before-upload="beforeAvatarUpload" :file-list="goodsDetailImagesFileList"
                      accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <p class="sug-p">
                      最多上传9个素材,单张图片需限制在10M以内
                  </p>
              </el-form-item> -->

            </el-tab-pane>
            <el-tab-pane label="商品参数" name="3">
              <!-- 商品参数 -->
              <el-table :data="goodsParamList" border>
                <el-table-column label="属性名称">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="属性值">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.value"></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <!-- <el-popconfirm title="确定删除吗？" class="ml10" @confirm="delGoodsParam(scope.$index)"> -->
                    <el-button type="text" slot="reference" class="color-red"
                               @click="delGoodsParam(scope.$index)">删除
                    </el-button>
                    <!-- </el-popconfirm> -->
                  </template>
                </el-table-column>
              </el-table>
              <div class="add-btn-box">
                <el-button type="primary" @click="addGoodsParam">添加属性
                </el-button>
                <el-button @click="goodsParamList = []">清空
                </el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="销售属性" name="4">
              <!-- 销售属性 -->
              <div class="sale-box">
                <div>
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>商品单位:</span>
                    <el-input class="w287" v-model="goodsUnit"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <span slot="label">最小起订量:</span>
                    <el-input-number class="w287" :precision="0" :min="0" :controls="false"
                                     v-model="minBuyQty">
                    </el-input-number>
                    <el-tooltip style="margin-left: 10px">
                      <div slot="content">不满足最小起订量的，将无法下单；<br/>为空、为零则不限制。</div>
                      <el-button type="text" icon="el-icon-question" class="tool-btn"></el-button>
                    </el-tooltip>
                  </el-form-item>

                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>属性类型:</span>
                    <el-radio-group v-model="typeRadio">
                      <el-radio :label="1">单规格商品</el-radio>
                      <el-radio :label="0">多规格商品</el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1" label="规格图片:">
                    <el-upload class="avatar-uploader" :show-file-list="false"
                               :action="path+'/fileUploadAndDownload/upload'"
                               :headers="{'x-token':token}"
                               :on-success="handleSingSkuImgSuccess" :before-upload="$fn.beforeAvatarUpload"
                               accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                      <img v-if="singleSkuImg" :src="singleSkuImg" class="avatar"/>
                      <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <p class="color-grap">建议尺寸：400*400像素,图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1" label="商品编号:">
                    <el-input :controls="false" class="w287" v-model="sn"></el-input>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1" label="商品条码:">
                    <el-input :controls="false" class="w287" v-model="barcode"></el-input>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>商品供货价:</span>
                    <el-input-number class="w287" :precision="2" :min="0" :controls="false"
                                     v-model="goodsPrice" :disabled="priceIsEdit === 1">
                    </el-input-number>
                    <span style="margin-left: 10px">元</span>
                    <el-tooltip>
                      <div slot="content">无会员折扣下，卖给中台会员的价格（包括采购端绑定会员和pc端会员）</div>
                      <el-button type="text" icon="el-icon-question" class="tool-btn"></el-button>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>建议零售价:</span>
                    <el-input-number class="w287" :precision="2" :min="0" :controls="false"
                                     v-model="goodsOriginPrice" :disabled="priceIsEdit === 1">
                    </el-input-number>
                    <span style="margin-left: 10px">元</span>
                    <el-tooltip>
                      <div slot="content">指该品牌厂家制定的线上线下零售指导价格，此价格作为线上实际销 售价格对比，参考作用。</div>
                      <el-button type="text" icon="el-icon-question" class="tool-btn"></el-button>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>商品成本价:</span>
                    <el-input-number class="w287" :precision="2" :min="0" :controls="false"
                                     v-model="goodsCostPrice" :disabled="priceIsEdit === 1">
                    </el-input-number>
                    <span style="margin-left: 10px">元</span>
                    <el-tooltip>
                      <div slot="content">中台进货的价格，统计中台利润时使用</div>
                      <el-button type="text" icon="el-icon-question" class="tool-btn"></el-button>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>商品指导价:</span>
                    <el-input-number class="w287" :precision="2" :min="0" :controls="false"
                                     v-model="goodsGuidePrice" :disabled="priceIsEdit === 1">
                    </el-input-number>
                    <span style="margin-left: 10px">元</span>
                    <el-tooltip>
                      <div slot="content">中台建议采购端销售的价格</div>
                      <el-button type="text" icon="el-icon-question" class="tool-btn"></el-button>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>商品营销价:</span>
                    <el-input-number class="w287" :precision="2" :min="0" :controls="false"
                                     v-model="goodsActivityPrice" :disabled="priceIsEdit === 1">
                    </el-input-number>
                    <span style="margin-left: 10px">元</span>
                    <el-tooltip>
                      <div slot="content">指同一商品在阿里、京东、淘宝、天猫等对应电商平台促销时段 <br/>如 双 11、双
                        12、618、年货节等营销节日让利价格，或参与秒杀、砍价、 拼团、新品发布等营销活动短时促销价格。（目前只有京东有营销价）
                      </div>
                      <el-button type="text" icon="el-icon-question" class="tool-btn"></el-button>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>库存:</span>
                    <el-input-number class="w287" :precision="0" :controls="false" v-model="goodsStock">
                    </el-input-number>
                  </el-form-item>
                  <el-form-item v-if="typeRadio === 1">
                    <span slot="label"><span class="color-red">*</span>重量:</span>
                    <el-input-number class="w287" :precision="0" :min="0" :controls="false"
                                     v-model="goodsWeight">
                    </el-input-number>
                    <span style="margin-left: 10px">g</span>
                  </el-form-item>
                  <!-- 无规格部分 -->
                  <!-- <div v-if="typeRadio === 1">
                      <el-form-item label="商品编码:">
                          <el-input class="w287" v-model="goodsSn"></el-input>
                      </el-form-item>
                      <el-form-item label="商品条形码:">
                          <el-input class="w287" v-model="goodsBarcode"></el-input>
                      </el-form-item>
                      <el-form-item label="商品重量:">
                          <el-input-number :min="0" :precision="2" :controls="false" class="w287"
                              v-model="goodsWeight">
                          </el-input-number>
                          <span style="margin-left: 10px">克</span>

                      </el-form-item>
                      <el-form-item label="商品体积:">
                          <el-input-number :min="0" :precision="3" :controls="false" class="w287"
                              v-model="goodsVolume">
                          </el-input-number>
                          <span style="margin-left: 10px">立方米</span>

                      </el-form-item>
                  </div> -->
                </div>
                <!-- 多规格部分 -->
                <div class="multi-spe-box" v-if="typeRadio === 0">
                  <div class="form-padding">
                    <!-- 添加规格 循环 -->
                    <div v-show="speIsShow">
                      <div v-for="(item,index) in spec" :key="item.id" class="spe-box-item">
                        <i class="el-icon-error close-i" @click="deleteSpecItem(index)"></i>
                        <el-form-item style="padding-right: 24px;">
                          <span slot="label"><span class="color-red">*</span>规格名:</span>
                          <el-input v-model="item.name" @change="specNameVerify(index,item.name)">
                          </el-input>
                        </el-form-item>
                        <el-form-item>
                          <span slot="label"><span class="color-red">*</span>规格项:</span>
                          <el-row>
                            <el-col :span="6" class="mr10" style="padding-top:5px"
                                    v-for="(option,index2) in item.options" :key="item.id">
                              <el-input class="item-spe-input" v-model="option.name"
                                        @change="specOptionNameVerify(index,index2,option.name)">
                                <el-button slot="append" icon="el-icon-close"
                                           @click="deleteSpecSubdataItem(index,index2,option)">
                                </el-button>
                              </el-input>
                            </el-col>
                            <el-col :span="3">
                              <el-button type="primary" @click="addSpecSubdataItem(index,'')">
                                添加规格项
                              </el-button>
                            </el-col>
                          </el-row>
                        </el-form-item>
                      </div>
                    </div>
                    <el-form-item label-width="130px">
                      <el-button type="primary" :disabled="spec.length <= 1?false:true"
                                 @click="addSpecItem('')">
                        添加规格
                      </el-button>
                      <el-button @click="assemblySpec()">
                        刷新规格项目表
                      </el-button>
                    </el-form-item>
                    <el-form-item label-width="130px">
                      <p class="color-red">注: 新增或删除规格项需重新填写发票信息</p>
                    </el-form-item>
                  </div>
                  <table cellspacing="0" cellpadding="0" width="100%" class="spe-table">
                    <thead>
                    <tr>
                      <td :colspan="spec.length+12" class="text-left">
                        <div class="f fac batck-btn-box">
                          <span class="batch-span">批量操作: </span>
                          <template v-if="batchInputIsShow">
                            <el-input-number class="w120" :min="0"
                                             :precision="batchPrecision" :controls="false"
                                             size="mini"
                                             v-model="batchInputValue"></el-input-number>
                            <el-button type="text" class="conf-btn" @click="confirmBatch">确定
                            </el-button>
                            <el-button type="text" class="conf-btn" @click="cancelBatch">取消
                            </el-button>
                          </template>
                          <template v-else>
                            <el-button type="text" @click="openatchInput('batchStock')">库存
                            </el-button>
                            <el-button type="text"
                                       @click="openatchInput('batchOriginPrice')">
                              建议零售价
                            </el-button>
                            <el-button type="text" @click="openatchInput('batchPrice')">供货价
                            </el-button>
                            <el-button type="text" @click="openatchInput('batchCostPrice')">
                              成本价格
                            </el-button>
                            <el-button type="text"
                                       @click="openatchInput('batchGuidePrice')">指导价
                            </el-button>
                            <el-button type="text"
                                       @click="openatchInput('batchActivityPrice')">营销价
                            </el-button>
                            <el-button type="text" @click="openatchInput('batchWeight')">重量
                            </el-button>
                            <!-- <el-button type="text" @click="openatchInput('batchVolume')">体积
                        </el-button> -->
                          </template>
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td v-for="item in spec">{{ item.name }}
                      </td>
                      <td>
                        <p>
                          图片
                        </p>
                      </td>
                      <td width="10%">
                        <p>标题</p>
                      </td>
                      <td width="10%">
                        <p>
                          <span class="color-red">*</span> 库存
                        </p>
                      </td>
                      <td width="10%">
                        预扣库存
                      </td>
                      <td width="10%">
                        <p><span class="color-red">*</span> 建议零售价</p>
                      </td>
                      <td width="10%">
                        <p><span class="color-red">*</span> 供货价</p>
                      </td>
                      <td width="10%">
                        <p><span class="color-red">*</span> 成本价格</p>
                      </td>
                      <td width="10%">
                        <p><span class="color-red">*</span> 指导价</p>
                      </td>
                      <td width="10%">
                        <p>营销价 <span class="color-red"></span></p>
                        <p>重量(克) <span class="color-red"></span></p>
                      </td>
                      <!-- <td width="10%">
                      <p>商品编码 <span class="color-red"></span></p>
                  </td> -->
                      <td width="10%">
                        <p>商品编码 <span class="color-red"></span></p>
                        <p>商品条码 <span class="color-red"></span></p>
                      </td>
                      <!--                                        <td width="10%">
                                                                  <p>重量(克) <span class="color-red"></span></p>
                                                              </td>-->
                      <td width="10%">
                        <p>操作 <span class="color-red"></span></p>
                      </td>
                      <!-- <td width="10%">
                      <p>体积(m²) <span class="color-red"></span></p>
                  </td> -->
                    </tr>
                    </thead>

                    <tbody>
                    <tr v-for="(item,index) in skus" :key="item.id">
                      <td v-for="(a,index3) in spec" rowspan="1">
                        {{ getRowName(index3, item.optionName) }}
                      </td>
                      <td rowspan="1">
                        <el-upload
                            class="sku_avatar-uploader"
                            :show-file-list="false"
                            :action="path + '/fileUploadAndDownload/upload'"
                            :headers="{ 'x-token': token }"
                            :on-success="function (res,file) {return handleSkusImgSuccess(res,file,index)}"
                            :before-upload="$fn.beforeAvatarUpload"
                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
                        >
                          <img
                              v-if="item.image_url"
                              :src="item.image_url"
                              class="avatar"
                          />
                          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                        </el-upload>
                        <p class="color-grap" style="margin-top: 5px">建议:400*400</p>
                      </td>
                      <td rowspan="1">
                        <el-input :placeholder="item.optionName" v-model="item.title">
                        </el-input>
                      </td>
                      <td rowspan="1">
                        <el-input-number :min="0" :controls="false" v-model="item.stock">
                        </el-input-number>
                      </td>
                      <td rowspan="1">
                        <el-input disabled v-if="item.lock_stock && item.lock_stock > 0" v-model="item.lock_stock"></el-input>
                        <el-input disabled v-else></el-input>
                      </td>
                      <td rowspan="1">
                        <el-input-number :min="0" :precision="2" :controls="false"
                                         v-model="item.origin_price" :disabled="priceIsEdit === 1">
                        </el-input-number>
                      </td>
                      <td rowspan="1">
                        <el-input-number :min="0" :precision="2" :controls="false"
                                         v-model="item.price" :disabled="priceIsEdit === 1">
                        </el-input-number>
                      </td>
                      <td rowspan="1">
                        <el-input-number :min="0" :precision="2" :controls="false"
                                         v-model="item.cost_price" :disabled="priceIsEdit === 1">
                        </el-input-number>
                      </td>
                      <td rowspan="1">
                        <el-input-number :min="0" :precision="2" :controls="false"
                                         v-model="item.guide_price" :disabled="priceIsEdit === 1">
                        </el-input-number>
                      </td>
                      <td rowspan="1">
                        <el-input-number :min="0" :precision="2" :controls="false"
                                         v-model="item.activity_price" placeholder="营销价" :disabled="priceIsEdit === 1">
                        </el-input-number>
                        <el-input-number :precision="0" :min="0" :controls="false" placeholder="重量"
                                         v-model="item.weight">
                        </el-input-number>
                      </td>
                      <!-- <td rowspan="1">
                      <el-input v-model="item.commodity"></el-input>
                  </td> -->
                      <td rowspan="1">
                        <el-input v-model="item.sn" style="margin-bottom: 5px;"
                                  placeholder="商品编码"></el-input>
                        <el-input v-model="item.barcode" placeholder="商品条码"></el-input>
                      </td>
                      <!--                                        <td rowspan="1">
                                                                  <el-input-number :precision="0" :min="0" :controls="false"
                                                                                   v-model="item.weight">
                                                                  </el-input-number>
                                                              </td>-->
                      <td rowspan="1">
                        <el-button type="text" @click="editDescribe(item.describe,index)">编辑描述
                        </el-button>
                      </td>
                      <!-- <td rowspan="1">
                      <el-input-number :min="0" :precision="3" :controls="false"
                          v-model="item.volume">
                      </el-input-number>
                  </td> -->
                    </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="商品特殊资质" name="5">
              <!-- 商品特殊资质 -->
              <!-- <quill-editor ref="myTextEditor" v-model="goodsAffiche" :options="editorOption"
                style="height:400px;width: 100%;padding-bottom: 100px"></quill-editor> -->
              <span style="display: none">{{ goodsAffiche }}</span>
              <div class="qualifications-row" v-for="(item,index) in goodsAffiche" :key="item.id">
                <el-form-item>
                  <span slot="label"><span class="color-red">*</span>特殊资质名称:</span>
                  <el-row :gutter="10" style="padding: 0">
                    <el-col :span="6">
                      <el-input v-model="item.title"></el-input>
                    </el-col>
                    <el-col :span="6" class="f fac">
                      <el-upload :show-file-list="false"
                                 :action="path+'/fileUploadAndDownload/upload'"
                                 :headers="{'x-token':token}"
                                 :data="{index:index}" :on-success="handleGoodsAfficheSuccess"
                                 :before-upload="$fn.beforeAvatarUpload"
                                 accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                        <el-button>上传图片</el-button>
                      </el-upload>
                      <el-button class="ml10" @click="removeItem(index)">移除</el-button>
                    </el-col>
                  </el-row>
                  <p class="color-grap">建议尺寸宽度：900像素，图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
                  <div class="qualifications-img-box">
                    <img v-if="item.url" :src="item.url">
                  </div>
                </el-form-item>
              </div>

              <div class="add-btn-box">
                <!-- <el-button icon="el-icon-plus" class="btn2" @click="addGoodsAffiche">添加特殊资质
                </el-button> -->
                <el-button type="primary" @click="addGoodsAffiche">添加特殊资质
                </el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="发票管理" name="6">
              <el-row>
                <!-- 新增部分 -->
                <el-col :span="24">
                  <el-form-item label="赋码方式:" label-width="80px">
                    <el-radio-group v-model="bill_position">
                      <el-radio :label="1" :disabled="typeRadio === 0">按商品赋码</el-radio>
                      <el-radio :label="2" :disabled="typeRadio === 1">按规格赋码</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <!-- 修改部分 -->
                <template v-if="bill_position === 1">
                  <el-col :span="8" class="tree-box">
                    <div class="f fac">
                      <el-input size="small" v-model="treeInput"></el-input>
                      <el-button size="small" type="primary" @click="searchTree">搜索</el-button>
                    </div>
                    <el-tree
                        :filter-node-method="filterNode"
                        highlight-current
                        ref="tree"
                        class="mt10"
                        :data="treeData"
                        :props="defaultProps"
                        @node-click="handleNodeClick">
                    </el-tree>
                  </el-col>
                  <el-col :span="16" class="right-form-box">
                    <el-row>
                      <el-col :span="12">
                        <el-form-item label="税收分类编码:">
                          <el-input v-model="tax_code"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="发票商品名称:">
                          <el-input v-model="tax_product_name"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="税收分类简称:">
                          <el-input v-model="tax_short_name"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="商品简码:">
                          <el-input v-model="short_code"></el-input>
                        </el-form-item>
                      </el-col>
                      <!--                                    <el-col :span="12">
                                                              <el-form-item label="规格型号:">
                                                                  <el-input v-model="tax_option"></el-input>
                                                              </el-form-item>
                                                          </el-col>-->
                      <!--                                    <el-col :span="12">
                                                              <el-form-item label="计量单位(元):">
                                                                  <el-input-number v-model="goodsPrice" :controls="false" :min="0"
                                                                                   :precision="2"
                                                                                   class="w100 input-number-text-left"></el-input-number>
                                                              </el-form-item>
                                                          </el-col>-->
                      <!--                                    <el-col :span="12">
                                                              <el-form-item label="单位:">
                                                                  <el-input v-model="tax_unit"></el-input>
                                                              </el-form-item>
                                                          </el-col>-->
                      <el-col :span="12">
                        <el-form-item label="使用优惠政策:">
                          <el-radio-group v-model="is_favorable_policy">
                            <el-radio :label="1">是</el-radio>
                            <el-radio :label="0">否</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item>
                                                <span slot="label"><span class="color-red"
                                                                         v-if="is_favorable_policy === 1">* </span>优惠政策类型:</span>
                          <el-select v-model="favorable_policy" clearable class="w100">
                            <el-option v-for="item in favorablePolicyOptios" :key="item.id"
                                       :label="item" :value="item"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item>
                          <span slot="label"><span class="color-red">*</span>税率:</span>
                          <div class="f fac">
                            <!--                                                tax_rate-->
                            <el-select v-model="tax_rate">
                              <el-option v-for="item in taxRateOptios" :key="item.id"
                                         :label="item.label" :value="item.value"></el-option>
                            </el-select>
                          </div>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item>
                          <span slot="label"><span class="color-red">*</span>免税类型:</span>
                          <el-select v-model="free_of_tax" class="w100">
                            <el-option v-for="item in freeOfTaxOptions" :key="item.id"
                                       :label="item.label" :value="item.value"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="含税标志:">
                          <el-radio-group v-model="is_tax_logo">
                            <el-radio :label="1">含税</el-radio>
                            <el-radio :label="0">不含税</el-radio>
                          </el-radio-group>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-col>
                </template>
                <!-- 新增部分 -->
                <template v-if="bill_position === 2">
                  <el-col>
                    <el-form-item label="批量操作" label-width="80px">
                      <el-button type="text" @click="openCodeTypeDialog('all')">赋码</el-button>
                    </el-form-item>
                  </el-col>
                  <table cellspacing="0" cellpadding="0" class="bill-table">
                    <thead>
                    <tr>
                      <td v-for="item in spec">{{ item.name }}
                      <td>
                        <p>标题</p>
                      </td>
                      <td>
                        <p>赋码状态</p>
                      </td>
                      <td>
                        <p>发票赋码</p>
                      </td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(item,index) in skus" :key="item.id">
                      <td v-for="(a,index3) in spec" rowspan="1">
                        {{ getRowName(index3, item.optionName) }}
                      </td>
                      <td rowspan="1">
                        <el-input :placeholder="item.optionName" v-model="item.title">
                        </el-input>
                      </td>
                      <td>
                                            <span>{{
                                                item.tax_code != '' ? '已赋码' : '未赋码'
                                              }}</span>
                      </td>
                      <td>
                        <el-button type="text" @click="openCodeTypeDialog(index)">赋码</el-button>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </template>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="租赁设置" name="7">
              <!-- 销售属性 -->
              <div class="sale-box">
                <div>
                  <el-form-item>
                    <span slot="label"><span class="color-red">*</span>可服务区域:</span>
                    <!--                <el-cascader-->
                    <!--                    v-model="regionArray"-->
                    <!--                    :options="cityOptions"-->
                    <!--                    :props="props"-->
                    <!--                    @getCheckedNodes="getCheckedNodes"-->
                    <!--                    @change="handleChange($event)"-->
                    <!--                    clearable></el-cascader>-->
                    <el-button icon="el-icon-map-location" @click="openRegion">新增区域</el-button>
                    <p class="cityPrompt">不选则服务区域为:全国</p>
                  </el-form-item>
                  <div v-for="(item,index) in cityChoosr" class="cityBtn">
                    <el-button class="cityBtn">{{item}}</el-button>
                    <!--                <el-button class="cityBtn"  >{{item}}</el-button>-->
                    <!--                <div v-for="a  in item.lease_coverage_regions">-->
                    <!--                <el-button class="cityBtn"  v-for="b  in a.lease_coverage_regions">{{b.name}}</el-button>-->
                    <!--                </div>-->
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-form>
      </div>
    </m-card>
    <el-dialog title="商品图 片排序" :close-on-click-modal="false" :close-on-press-escape="false" :visible="dialogSortIsShow"
               width="50%" :before-close="dialogSortClose">
      <draggable v-model="goodsGallery" chosenClass="chosen" forceFallback="true" group="people" animation="500"
                 @start="onStart" @end="onEnd(1)" class="draggable-box">
        <transition-group>
          <div class="item" v-for="element in goodsGallery" :key="element.src">
            <img :src="element.url">
          </div>
        </transition-group>
      </draggable>
    </el-dialog>
    <el-dialog title="新增区域" :close-on-click-modal="false" :close-on-press-escape="false" :visible="addregionShow"
               width="50%" style="height:800px" :before-close="dialogSortClose2">
      <el-cascader
          v-model="chooseCityArr"
          style="width:100%"
          :options="regionOptions"
          :props="props"
          @getCheckedNodes="getCheckedNodes"
          @change="handCascaderChange"
          clearable></el-cascader>
      <div class="drawer__footer">
        <el-button type="primary" @click="regionSave">保 存</el-button>
        <el-button @click="regionhandleClose">取 消</el-button>
      </div>
    </el-dialog>
    <!-- <el-dialog title="商品详情排序" :close-on-click-modal="false" :close-on-press-escape="false" :visible="dialogSortIsShow2"
        width="50%" :before-close="dialogSortClose2">
        <draggable v-model="goodsDetailImages" chosenClass="chosen" forceFallback="true" group="people" animation="500"
            @start="onStart" @end="onEnd(2)" class="draggable-box">
            <transition-group>
                <div class="item" v-for="element in goodsDetailImages" :key="element.url">
                    <img :src="element.url">
                </div>
            </transition-group>
        </draggable>
    </el-dialog> -->
    <TempDialog ref="tempDialog" @load="getFreightTemp"></TempDialog>
    <SkusDescribeDialog ref="skusDescribeDialog" @getDescribe="getDescribe"></SkusDescribeDialog>
    <CodeTypeDialog ref="codeTypeDialog" @resSkus="resSkus"></CodeTypeDialog>
  </div>
</template>
<script>
import SelectClassify from "./components/selectClassify"
/* import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import { quillEditor, Quill } from 'vue-quill-editor';
import { container, ImageExtend, QuillWatch } from 'quill-image-extend-module' */
import {regionData} from "element-china-area-data";
import {createProduct, findProduct, updateProduct,leasefindProduct} from "@/api/product";
import {leaseSupplyCreateProduct,leaseSupplyUpdateProduct,adminSupplierFindProduct} from "@/api/supplier/goods";
import {mapGetters} from "vuex";
import {getClassify} from "@/api/classify";
//导入draggable组件
import draggable from "vuedraggable";
import cnchar from 'cnchar';
import {getExpressTemplateList} from "@/api/expressTemplate"
import TempDialog from "@/view/order/distributionTemp/components/templateDialog";
import SkusDescribeDialog from "./components/skusDescribeDialog";
// Quill.register('modules/ImageExtend', ImageExtend)
import {getBillCategory} from "@/api/bill";
import CodeTypeDialog from "./components/codeTypeDialog";
import {getBrandOptionList} from "@/api/brands";
import {findApplicationSetting} from "@/api/application";
import {findSetting} from "@/api/supplierSetting";
import {getSupplySetting} from "@/api/goods";
import {getBrandsList} from "@/api/brands"
import {getSupplierSourceCategoryOptionList, getSupplierSourceOptionList} from "@/api/supplier/source";
import { getRegion,regiongetAreas  } from "@/api/region";

/**
 * 获取数组对象指定k的下标
 */
Array.prototype.indexOfJSON = function (kName, value) {
  for (var i = 0; i < this.length; i++) {
    if (this[i][kName] == value) return i;
  }
  return -1;
};
export default {
  name: "releaseGoods",
  // components: { SelectClassify, quillEditor, draggable, TempDialog },
  components: {SelectClassify, draggable, TempDialog, SkusDescribeDialog, CodeTypeDialog},
  data() {
    return {
      //新增区域
      addregionShow:false,
      regionOptions:[],
      //选中城市数组
      chooseCityArr:[],
      //传后arr
      chooseCityArr2:[],
      //储存id
      chuchun:[],
      props: {
        multiple: true,
        // children: 'lease_coverage_regions',
        // lazyLoad (node, resolve) {
        //   console.log(node)
        //   const { data,level} = node;
        //   if(level === 1){
        //     getRegion({parent_id:data.id}).then((res) => {
        //       if(res.code === 0) {
        //         var nodes =  res.data.list.map(item => {
        //           return {
        //             value:item.id,
        //             label:item.name,
        //             // level:item.level,
        //             leaf: false
        //           }
        //         })
        //         resolve(nodes);
        //       }
        //     })
        //   }else if(level === 2){
        //     getRegion({parent_id:node.value}).then((res) => {
        //       if(res.code === 0) {
        //         var nodes =  res.data.list.map(item => {
        //           return {
        //             value:item.id,
        //             label:item.name,
        //             // level:item.level,
        //             leaf: true
        //           }
        //         })
        //         resolve(nodes);
        //       }
        //     })
        //   }
        //
        // }
      },
      //
      supplier_id: null,
      source_id: null,
      source_classify_id: null,
      source_options: [],
      source_classify_options: [],
      formData: {
        items: [
        ],
      },
      // 选种城市信息
      cityChoosr:[],
      cityOptions:[],
      // 选中城市地区数组
      regionArray:[],
      priceIsEdit: 0, // 0允许改动价格  1不允许改动价格
      original_sku_id: 0,
      singleSkuImg: "",// 单规格图片
      minBuyQty: 0, // 最少起批量
      brandsOptiosData: {
        name: "",
        brandsOptios: [],
        loading: false,
        page: 1,
        pageSize: 10,
        total: 0
      },
      /* 发票部分开始 */
      treeInput: "", // 搜索字段
      treeData: [], // 分类数据
      taxRateOptios: [// 税率optios
        {label: "0%", value: 0},
        {label: "1%", value: 1},
        {label: "3%", value: 3},
        {label: "6%", value: 6},
        {label: "9%", value: 9},
        {label: "10%", value: 10},
        {label: "11%", value: 11},
        {label: "13%", value: 13},
        {label: "17%", value: 17},
      ],
      freeOfTaxOptions: [
        {label: "正常税率", value: 1},
        {label: "出口免税和其他免税优惠政策", value: 2},
        {label: "不征增值税", value: 3},
        {label: "普通零税率", value: 4},
      ],
      favorablePolicyOptios: [
        "免税", "100%先征后退", "50%先征后退不征税", "先征后退", "即征即退100%", "即征即退30%", "即征即退50%", "即征即退70%", "按3%简易征收", "按5%简易征收", "按5%简易征收减按1.5%计征", "稀土产品", "简易征收", "超税负12%即征即退", "超税负3%即征即退", "超税负8%即征即退"
      ],
      defaultProps: {
        label: 'mc',
        short_name: "short_name",
        id: "bm",
        children: "children"
      },
      tax_code: "", //税收分类编码
      tax_short_name: "", //税收分类简称
      // tax_option: "", //规格型号
      tax_unit: "", //单位
      favorable_policy: "", //优惠政策
      is_favorable_policy: 0,//是否使用优惠政策
      free_of_tax: 1, //1正常税率2出口免税和其他免税优惠政策3不征增值税4普通零税率
      tax_product_name: "", // 发票商品名称
      short_code: "", // 商品简码
      tax_measure_price: 0,//税收计量单价
      tax_rate: 0, //税率
      is_tax_logo: 1, //含税标志
      bill_position: 1,// 赋码方式 1=按商品 2按规格  新增部分
      /* 发票部分结束 */

      brand_id: "",// 品牌
      progressIsShow: false,
      progressNum: 0,
      videoUrl: "", // 首图视频
      // 刷新按钮状态
      btnLoadIsShow: false,
      // 运费模板id
      freight_template_id: null,
      // 运费模板option
      freightTemplateOption: [],
      // 商品图片排序dialog
      dialogSortIsShow: false,
      dialogSortIsShow2: false,
      drag: false,
      goodsImgList: [],
      // 批量input是否显示
      batchInputIsShow: false,
      // 批量input绑定值
      batchInputValue: "",
      batchPrecision: 0,
      batchValueName: "",
      path: this.$path,
      select: 1,
      // 规格显示
      speIsShow: true,
      step: 1,
      // tabs默认选中项
      activeName: "1",
      //------商品id-------//
      goodsId: 0,
      //------分类信息-------//
      classify: {},
      //------基本信息-------//
      classifyList1: [], //类目1
      classifyList2: [], //类目2
      classifyList3: [], //类目3
      classifyCheck1: null, // 选中类目1
      classifyCheck2: null, // 选中类目2
      classifyCheck3: null, // 选中类目3
      goodsClassifyStr: "",
      goodsSort: 0,
      goodsTitle: "",
      sn: "",
      barcode: "",
      goodsDesc: "",
      goodsImageUrl: "",//产品主图
      goodsGallery: [],//产品图册  - type 类型（1图片2视频） - src 资源链接
      goodsGalleryFileList: [],
      goodsSales: 0,
      goodsService: "",
      //运费模式类型
      // 0 统一运费 1 运费模板 2 第三方运费
      goodsFreightType: "0",
      goodsFreightPrice: 0,
      hide_uniform_freight:'',
      //------商品详情-------//
      // goodsDetailImages: [],
      goodsDetailImages: "",
      goodsDetailImagesFileList: [],
      //------商品参数-------//
      goodsParamList: [],
      //------销售属性-------//
      typeRadio: 1,// 属性类型 1=单规格 0=多规格
      goodsUnit: "",
      goodsSn: "",
      goodsBarcode: "",
      goodsWeight: 0.00,
      goodsVolume: 0.00,
      goodsStock: 0,
      goodsPrice: 0.00,
      goodsOriginPrice: 0.00,
      goodsCostPrice: 0.00,
      goodsGuidePrice: 0.00,//商品指导价
      goodsActivityPrice: 0.00,//商品营销价
      //------商品特殊资质-------//
      goodsAffiche: [],
      is_display: 0,
      //------批量处理-------//
      batchStock: 0,
      batchOriginPrice: 0.00,
      batchPrice: 0.00,
      batchCostPrice: 0.00,
      batchSn: "",
      batchBarcode: "",
      batchWeight: 0.00,
      batchVolume: 0.00,
      shop_level: 0,
      des_level: 0,
      express_level: 0,
      level: 0,
      //租赁设置
      isRentGoods:0,
      /* editorOption: {
          modules: {
              ImageExtend: {
                  name: 'file',  // 图片参数名
                  size: 3,  // 可选参数 图片大小，单位为M，1M = 1024kb
                  action: this.$path + "/fileUploadAndDownload/upload",
                  response: (res) => {
                      return res.data.file.url
                  },
                  headers: (xhr) => {
                      xhr.setRequestHeader('x-token', this.token)
                  },
              },
              toolbar: {  // 如果不上传图片到服务器，此处不必配置
                  container: container,  // container为工具栏，此次引入了全部工具栏，也可自行配置
                  handlers: {
                      'image': function () {  // 劫持原来的图片点击按钮事件
                          QuillWatch.emit(this.quill.id)
                      }
                  }
              }
          },
          placeholder: '编辑文章内容'
      }, */

      //规格项数据
      spec: [],
      skus: [],
      sku_id: null,
      arrayList: [],
      results: [],
      status_lock: 0, // 锁定状态  1锁定 0不锁定
      regions:[{}],//修改的数组
      regionsIndex:'',//下标
      chooseArr:[],
    }
  },
  watch: {
    typeRadio(val) {
      switch (val) { // 新增部分
        case 1:
          this.bill_position = 1
          break;
        case 0:
          this.bill_position = 2
          break;
      }
      if (val === 1 && this.skus[0]) {
        let sku = this.skus[0]
        this.goodsPrice = sku.price
        this.goodsOriginPrice = sku.origin_price
        this.singleSkuImg = sku.image_url
        this.goodsCostPrice = sku.cost_price
        this.goodsGuidePrice = sku.guide_price
        this.goodsActivityPrice = sku.activity_price
        this.goodsStock = sku.stock
        this.sn = sku.sn
        this.barcode = sku.barcode
        this.goodsWeight = sku.weight
      } else if (val === 0 && this.skus.length <= 1) {
        if (!this.skus[0]) {
          this.addSpecItem("默认")
          this.addSpecSubdataItem(0, '默认')
          this.assemblySpec()
        }
        this.skus[0].price = this.goodsPrice
        this.skus[0].origin_price = this.goodsOriginPrice
        this.skus[0].cost_price = this.goodsCostPrice
        this.skus[0].guide_price = this.goodsGuidePrice
        this.skus[0].activity_price = this.goodsActivityPrice
        this.skus[0].stock = this.goodsStock
        this.skus[0].sn = this.sn
        this.skus[0].barcode = this.barcode
        this.skus[0].weight = this.goodsWeight
      }
    },
  },
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  created() {
    this.getArea()

  },
  mounted() {
    //获取一级类目
    getClassify(1, 0).then((r) => {
      this.classifyList1 = r.data.list;
    });
    // this.getArea()
    // this.getBrandsOpstios()
    this.getBrandsOptios()
    //获取商品信息
    this.initProduct();
    // 获取运费模板
    this.getFreightTemp()
  },
  methods: {
    openRegion(){
      this.addregionShow = true
      // this.$refs.SelectRegion.isShow=true
      // this.$refs.SelectRegion.getArea()
      // this.$refs.SelectRegion.getProvince()
      // console.log(this.formData.items)
      // if(this.formData.items){
      //   let formItem = this.$fn.deepClone(this.formData.items)
      //   console.log(formItem)
      //   formItem.splice(formItem.indexOfJSON('id',this.formData.items.id),1)
      //   let disIds = this.regroupItems(formItem)
      //   this.$refs.SelectRegion.disposeData(this.formData.items,this.regionsIndex,disIds)
      // }
    },
    //选择地区
    regionChange(){
      console.log(11)
    },
    regroupItems(formItem) {
      let newArr = []
      let ids = []
      if(!formItem){
        return []
      }
      formItem.forEach(province => {
        newArr.push(province.lease_coverage_regions)
      })
      newArr.forEach(item => {
        item.forEach(item1 => {
          if (item1.lease_coverage_regions.length === 0) {
            ids.push(item1.id)
          } else {
            item1.lease_coverage_regions.forEach(item2 => {
              if (item2.lease_coverage_regions.length === 0) {
                ids.push(item2.id)
              } else {
                item2.lease_coverage_regions.forEach(item3 => {
                  if (item3.lease_coverage_regions.length === 0 || item3.lease_coverage_regions === null) {
                    ids.push(item3.id)
                  } else {
                    item3.lease_coverage_regions.forEach(item4 => {
                      ids.push(item4.id)
                    })
                  }
                })
              }
            })
          }
        })
      })
      let newIds = ids.filter((item, index, newIds) => newIds.indexOf(item) === index)
      return newIds;
    },
    // 获取地区
    async getArea(parent_id = 0, checked = false) {
      let res = await regiongetAreas();
      let list = [];
      if (res.code === 0) {
        let sheng = []
        let shi = []
        let qu = []
        res.data.forEach((item) => {
          if(item.level === 1){
            sheng.push(item)
          }
          if(item.level === 2){
            shi.push(item)
          }
          if(item.level === 3){
            qu.push(item)
          }
        })
        //处理省市区数组字段
        let sheng2 = []
        let shi2 = []
        let qu2 = []
        sheng.forEach((item) => {
          sheng2.push({ value: item.id,label:item.name,id:item.id});
        });
        shi.forEach((item) => {
          shi2.push({ value: item.id,label:item.name,parent_id:item.parent_id});
        });
        qu.forEach((item) => {
          qu2.push({ value: item.id,label:item.name,parent_id:item.parent_id});
        });
        sheng2.forEach((shengItem,shengIndex) => {
          let cc  = shi2.filter(function (value) {
            return value.parent_id === shengItem.id
          })
          sheng2[shengIndex].children = cc
        })
        sheng2.forEach(sheng2Item => {
          sheng2Item.children.forEach((sheng2children,index) => {
            let bb  = qu2.filter(function (value) {
              return value.parent_id === sheng2children.value
            })
            sheng2Item.children[index].children = bb
          })
        })
        this.regionOptions = sheng2
      } else {
        list = [];
      }
    },
    handCascaderChange(){
      console.log(this.chooseCityArr)
    },
    getCheckedNodes(){
      console.log(this.chooseCityArr)
    },
    //获取选中的地市
    getCheckedRegion(ids, regionNameArr, index) {
      console.log(ids)
      console.log(regionNameArr)
      console.log(index)
      if (index >= 0 && index !== null) {
        this.formData.items[index].region_names = regionNameArr;
        this.formData.items[index].regions = ids;
      }
    },
    //获取选中的地市
    // getCheckedRegion(ids, regionNameArr, index) {
    //   console.log(ids)
    //   console.log(regionNameArr)
    //   console.log(index)
    //   this.regions = ids
    //   this.cityChoosr = regionNameArr
    //   if (index >= 0 && index !== null) {
    //     this.formData.items[index].region_names = regionNameArr;
    //     this.formData.items[index].lease_coverage_regions = ids;
    //   } else {
    //     this.formData.items.push({
    //       lease_coverage_regions: ids,
    //       region_names: regionNameArr,
    //     });
    //     this.regions = this.formData.items
    //   }
    // },
    async getSourceOption() {
      const {code, data} = await getSupplierSourceOptionList({supplier_id: this.supplier_id})
      if (code === 0) {
        this.source_options = data.list
      }
    },
    async getSourceClassifyOption() {
      const {code, data} = await getSupplierSourceCategoryOptionList({supplier_id: this.supplier_id})
      if (code === 0) {
        this.source_classify_options = data.list
      }
    },
    handleBrandPage(val) {
      this.brandsOptiosData.page = val
      this.getBrandsOptios()
    },
    remoteMethod(query) {
      this.brandsOptiosData.name = query
      this.brandsOptiosData.page = 1
      this.getBrandsOptios()
    },
    async getBrandsOptios() {
      let params = {
        page: this.brandsOptiosData.page,
        pageSize: this.brandsOptiosData.pageSize
      }
      if (this.brandsOptiosData.name) params.name = this.brandsOptiosData.name
      this.brandsOptiosData.loading = true
      let res = await getBrandsList(params)
      this.brandsOptiosData.loading = false
      if (res.code === 0) {
        this.brandsOptiosData.total = res.data.total
        this.brandsOptiosData.brandOptios = res.data.list.sort(function (a, b) {
          return (a.name.spell().toLowerCase() - b.name.spell().toLowerCase())
        })
      }
    },
    // 获取品牌option
    /*async getBrandsOpstios() {
        let res = await getBrandOptionList()
        if (res.code === 0) {
            this.brandOptios = res.data.list
        }
    },*/
    handleTabClick(tab) {
      if (tab.name === '6' && this.bill_position === 1) {
        // 获取发票分类
        this.getBillClassify()
      }
    },
    /* 发票部分开始 */
    resSkus(skus) { // 新增部分
      this.skus = skus
      skus.forEach((item, index) => {
        this.$set(this.skus, index, {
          ...skus[index]
        })
      })
    },
    openCodeTypeDialog(type = "") { // 新增部分
      this.$refs.codeTypeDialog.isShow = true
      this.$refs.codeTypeDialog.getBillClassify()
      this.$nextTick(() => {
        this.$refs.codeTypeDialog.goodsTitle = this.goodsTitle
        this.$refs.codeTypeDialog.skus = this.skus
        this.$refs.codeTypeDialog.type = type
        if (typeof type === 'number') {
          this.$refs.codeTypeDialog.setForm()
        }
      })
    },
    // 过滤tree
    searchTree() {
      this.$refs.tree.filter(this.treeInput);
    },
    filterNode(value, data, node) {
      if (!value) {
        node.expanded = false;
        return true;
      }
      let val = value.toLowerCase();
      return this.chooseNode(val, data, node);
      /*if (!value) return true;
      return data.mc.indexOf(value) !== -1;*/
    },
    chooseNode(value, data, node) {
      if (data.mc.indexOf(value) !== -1) {
        return true;
      }
      const level = node.level;
      if (level === 1) {
        return false;
      }
      let parentData = node.parent;
      let index = 0;
      while (index < level - 1) {
        if (parentData.data.mc.indexOf(value) !== -1) {
          return true;
        }
        parentData = parentData.parent;
        index++;
      }
      return false;
    },
    // 选中发票分类
    handleNodeClick(data) {
      if (!data.children) {
        let value = parseInt(data.zzssl.split("%")[0])
        this.taxRateOptios = [// 税率optios
          {label: "0%", value: 0},
          {label: "1%", value: 1},
          {label: "3%", value: 3},
          {label: "6%", value: 6},
          {label: "9%", value: 9},
          {label: "10%", value: 10},
          {label: "11%", value: 11},
          {label: "13%", value: 13},
          {label: "17%", value: 17},
        ]
        if (this.taxRateOptios.indexOfJSON("value", value) === -1) {
          this.taxRateOptios.push({label: data.zzssl, value: value})
        }
        this.tax_rate = value
        this.tax_code = data.bm
        this.tax_short_name = data.spbmjc
      }
    },
    // 获取发票分类
    async getBillClassify() {
      let res = await getBillCategory()
      if (res.code === 0) {
        this.treeData = res.data.list
      }
    },
    /* 发票部分结束 */

    // 编辑描述dialog回调
    getDescribe(data) {
      this.skus[data.index].describe = data.describe
    },
    editDescribe(describe, index) {
      this.$refs.skusDescribeDialog.isShow = true
      this.$refs.skusDescribeDialog.setData(describe, index)
    },
    handle9Exceed(files, fileList) {
      this.$message.warning(`当前限制选择 9 个图片，本次选择了 ${files.length} 个图片，超出了 ${files.length + fileList.length - 9} 个图片`);
    },
    addTemp() {
      this.$refs.tempDialog.isShow = true
      this.$nextTick(() => {
        this.$refs.tempDialog.title = "新增";
      });
    },
    // 获取运费模板
    getFreightTemp(flg = false) {
      this.btnLoadIsShow = flg
      getExpressTemplateList({page: 1, pageSize: 999}).then(res => {
        if (res.code === 0) {
          this.freightTemplateOption = res.data.list
          this.btnLoadIsShow = false
        }
      })
    },
    // 关闭排序dialog
    dialogSortClose() {
      this.dialogSortIsShow = false
    },
    dialogSortClose2() {
      this.addregionShow = false
    },
    // 打开排序dialog
    openDialogSort(type) {
      if (type === 1) {
        if (this.goodsGallery.length > 0) {
          this.dialogSortIsShow = true
        } else {
          this.$message.error("请上传图片")
        }
      } else if (type === 2) {
        if (this.goodsDetailImages.length > 0) {
          this.dialogSortIsShow2 = true
        } else {
          this.$message.error("请上传图片")
        }
      }

    },
    //开始拖拽事件
    onStart() {
      this.drag = true;
    },
    //拖拽结束事件
    onEnd(type) {
      this.drag = false;
      this.$message.success("排序成功!")

      switch (type) {
        case 1:
          this.goodsGalleryFileList = [...this.goodsGallery];
          break;
        case 2:
          this.goodsDetailImagesFileList = [...this.goodsDetailImages];
          break;
      }
    },
    //获取商品信息
    initProduct() {
      if (!this.$route.query.id) {
        return;
      }
      leasefindProduct({id: this.$route.query.id}).then(res => {
        if (res.code == 0) {
          this.recoverGoods(res.data.reproduct);
          // this.chooseCityArr = res.data.reproduct.lease_coverage.lease_coverage_regions
          // this.chooseCityArr = res.data.reproduct.lease_coverage.lease_coverage_regions
          if(res.data.reproduct.lease_coverage.lease_coverage_regions){
            let aa = [];
            if (res.data.reproduct.lease_coverage.lease_coverage_regions_data){
              res.data.reproduct.lease_coverage.lease_coverage_regions_data.forEach((item) => {
                aa.push(item.name)
              })
              this.cityChoosr = aa
            }
          }
          if(res.data.reproduct.lease_coverage.lease_coverage_regions){
            this.chuchun = res.data.reproduct.lease_coverage.lease_coverage_regions
          }
          this.formData.items = res.data.reproduct.lease_coverage.lease_coverage_regions
          this.regionsIndex =  res.data.reproduct.id
        } else {

        }
      });
    },
    //显示批量处理input
    openatchInput(valueName) {
      this.batchInputIsShow = true;
      this.batchValueName = valueName;
      if (valueName === 'batchStock') {
        this.batchPrecision = 0
      } else if (valueName === 'batchVolume') {
        this.batchPrecision = 3
      } else {
        this.batchPrecision = 2
      }
    },
    // 确定
    confirmBatch() {
      this[this.batchValueName] = this.batchInputValue;
      this.skusBatch();
    },
    // 取消
    cancelBatch() {
      this.batchInputIsShow = false;
      this.batchValueName = "";
      this.batchInputValue = "";
    },
    // 获取二三级类目
    async getClassify2o3(level, pid) {
      if (level === 3) {
        this.classifyList3 = [];
        this.classifyCheck3 = null
      } else {
        this.classifyList2 = [];
        this.classifyCheck2 = null
        this.classifyList3 = [];
        this.classifyCheck3 = null
      }
      let r = await getClassify(level, pid)
      switch (level) {
        case 2:
          this.classifyList2 = r.data.list;
          break;
        case 3:
          this.classifyList3 = r.data.list;
          break;
      }
    },
    beforeVideoUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 20;
      if (!isLt10M) {
        this.$message.error('上传视频大小不能超过 20MB!');
      }
      return isLt10M;
    },
    beforeAvatarUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
      }
      return isLt10M;
    },
    // 单规格图片
    handleSingSkuImgSuccess(res) {
      if (res.code === 0) {
        this.singleSkuImg = res.data.file.url
      }
    },
    // 商品相册图片
    handleGoodsGallerySuccess(res) {
      if (res.code === 0) {
        this.goodsGallery.push({
          type: 1,
          src: res.data.file.url,
          url: res.data.file.url
        })
        this.goodsGalleryFileList = [...this.goodsGallery];

      } else {
        this.$message.error(res.msg)
      }

    },
    // 上传首图视频
    handleMainVideoSuccess(res) {
      if (res.code === 0) {

        this.progressIsShow = false;
        this.videoUrl = res.data.file.url;

      } else {
        this.$message.error(res.msg)
      }
    },
    videoPrigress(event, file, fileList) {
      this.progressNum = 0;
      this.progressIsShow = true
      if (Math.floor(event.percent) >= 100) {
        setTimeout(() => {
          this.progressNum = 99;
        }, 500)
      } else {
        setTimeout(() => {
          this.progressNum = Math.floor(event.percent);
        }, 500)
      }

    },
    removeVideo() {
      this.videoUrl = ""
    },
    videoError(err, file, fileList) {
      this.progressNum = 0;
      this.progressIsShow = true
      this.$message.error("上传失败")
    },
    // 删除商品相册图片
    removeGoodsGalleryList(index) {
      this.goodsGallery.splice(index, 1)
      this.goodsGalleryFileList.splice(index, 1)
      /*this.goodsGallery = [];
      fileList.forEach(item => {
          this.goodsGallery.push({
              type: 1,
              src: item.url,
              url: item.url
          })
      })*/
    },
    // 上传主图
    handleMainImgSuccess(res) {
      if (res.code === 0) {
        this.goodsImageUrl = res.data.file.url;
      } else {
        this.$message.error(res.msg)
      }
    },

    // 商品详情
    handleGoodsDetailImagesSuccess(res) {
      this.goodsDetailImages.push({
            url: res.data.file.url
          }
      );
    },
    // 删除商品详情图片
    removeGoodsDetailImagesList(file, fileList) {
      this.goodsDetailImages = [];
      fileList.forEach(item => {
        this.goodsDetailImages.push({
          type: 1,
          src: item.url,
          url: item.url
        })
      })
    },
    // 上传特殊资质名称
    handleGoodsAfficheSuccess(res) {
      if (res.code === 0) {
        this.goodsAffiche[res.data.file.index].url = res.data.file.url;
        this.goodsAffiche[res.data.file.index].src = res.data.file.url;
      } else {
        this.$message.error(res.msg)
      }
    },
    // 移除特殊资质item
    removeItem(index) {
      this.goodsAffiche.splice(index, 1)
    },
    //添加特殊资质item
    addGoodsAffiche() {
      this.goodsAffiche.push({
        title: "",
        url: ""
      })
    },
    //初始化分类选项
    initClassifyTitle(classify) {
      let that = this;
      that.classify = classify;
      that.goodsClassifyStr = classify.category1_name + "/" + classify.category2_name + "/" + classify.category3_name;
    },
    //添加规格项数据
    addSpecByName(name) {
      let newSpec = {};
      newSpec.name = name;
      newSpec.options = [];
      return newSpec;
    },

    //添加规格项
    addSpecItem(name) {
      let that = this;
      let newSpec = that.addSpecByName(name);
      that.spec.push(newSpec);
    },

    //删除规格项
    deleteSpecItem(index) {
      let that = this;
      if (that.spec.length > 0) {
        that.spec.splice(index, 1);
      }
      that.assemblySpec();
    },

    //添加规格项子数据
    addSpecSubdataByName(name) {
      let newSpecSubdata = {};
      newSpecSubdata.name = name;
      return newSpecSubdata;
    },

    //添加规格项子数据
    addSpecSubdataItem(index, name) {
      let that = this;
      let tempSpec = that.spec[index];
      tempSpec.options.push(that.addSpecSubdataByName(name));
      that.spec[index] = tempSpec;
    },

    //更新规格项子数据
    updateSpecSubdataItemByNames(index, names) {
      let that = this;
      let tempSpec = that.spec[index];
      let tempOptions = [];
      names.forEach(name => {
        tempOptions.push(that.addSpecSubdataByName(name));
      });
      tempSpec.options = tempOptions;
      that.spec[index] = tempSpec;
    },

    //删除规格项子数据
    deleteSpecSubdataItem(specIndex, specSubdataIndex) {
      let that = this;
      that.spec[specIndex].options.splice(specSubdataIndex, 1);
      if (that.spec[specIndex].options.length == 0 || !that.spec[specIndex].options) {
        that.spec.splice(specIndex, 1);
      }
      that.assemblySpec();
    },

    //规格名验证
    specNameVerify(specIndex, name) {
      let that = this;
      if (that.spec.length == 1 || !name) {
        that.assemblySpec();
        return;
      }
      for (let i = 0; i < that.spec.length; i++) {
        if (that.spec[i].name == name && i != specIndex) {
          that.spec[specIndex].name = "";
          that.$message.error("已经添加了相同的规格名");
          return;
        }
      }
      that.assemblySpec();
    },

    //规格项验证
    specOptionNameVerify(specIndex, specSubdataIndex, name) {
      let that = this;
      if (that.spec[specIndex].options.length == 1 || !name) {
        that.assemblySpec();
        return;
      }
      for (let i = 0; i < that.spec[specIndex].options.length; i++) {
        if (that.spec[specIndex].options[i].name == name && i != specSubdataIndex) {
          that.spec[specIndex].options[specSubdataIndex].name = "";
          that.$message.error("已经添加了相同的规格项名");
          return;
        }
      }
      that.assemblySpec();
    },

    //规格项验证
    verifySpec() {
      let that = this;
      if (that.spec.length == 0) {
        that.skus = [];
        that.$message.error("请添加规格");
        return false;
      }
      return true;
    },

    //skus验证
    verifySkus() {
      let that = this;
      if (that.skus.length == 0) {
        that.$message.error("请添加规格");
        return false;
      }
      return true;
    },

    //组装数据
    assemblySpec() {
      let that = this;
      if (!that.verifySpec()) {
        return;
      }
      that.skus = [];
      that.arrayList = [];
      that.results = [];

      for (let i = 0; i < that.spec.length; i++) {
        if (that.spec[i].options.length > 0) {
          let temp = that.filterOptions(that.spec[i].options);
          if (temp.length > 0) {
            that.arrayList.push(that.filterOptions(that.spec[i].options));
          }
        }
      }
      that.forIn(0, null);
      that.results.forEach(item => {
        that.skus.push(that.assemblySku(item));
      });
    },

    //过滤Options
    filterOptions(options) {
      for (let i = 0; i < options.length; i++) {
        if (options[i] == null || options[i].name == null || options[i].name == '' || options[i].name == undefined) {
          options.splice(i, 1);
        }
      }
      return options;
    },

    forIn(index, subContext) {
      let that = this;
      let stringList = that.arrayList[index];
      for (let i in stringList) {
        let e = stringList[i].name;
        if (e) {
          if (index !== 0) {
            e = subContext + "+" + e;
          }
          if (index === (that.arrayList.length - 1)) {
            that.results.push(e);
          } else {
            that.forIn(index + 1, e);
          }
        }
      }
    },

    //组装sku 单项数据 提交
    assemblySku(val) {
      let that = this;
      let skuItem = {};
      skuItem.optionName = val;
      skuItem.title = val;
      skuItem.sn = "";
      skuItem.barcode = "";
      skuItem.weight = 0.0;
      skuItem.volume = 0.00;
      skuItem.price = 0.00;
      skuItem.cost_price = 0.00;
      skuItem.origin_price = 0.00;
      skuItem.guide_price = 0.00;
      skuItem.stock = 0;

      skuItem.spec_items = [];
      for (let i = 0; i < that.spec.length; i++) {
        let specItem = {};
        specItem.id = 0;
        specItem.value = that.getRowName(i, val);
        specItem.spec_id = 0;
        let spec = {};
        spec.id = 0;
        spec.title = that.spec[i].name;
        specItem.spec = spec;
        skuItem.spec_items.push(specItem);
      }
      return skuItem;
    },

    //获取rowName
    getRowName(index, item) {
      if (item) {
        let arr = item.split('+');
        return arr[index];
      }
      return ""
    },
    // 添加属性
    addGoodsParam() {
      this.goodsParamList.push({name: "", value: ""});
    },
    // 删除属性
    delGoodsParam(index) {
      this.goodsParamList.splice(index, 1);
    },
    //提交商品
    submitGoods() {
      let that = this;
      if (!that.verifyGoods()) {
        return;
      }
      if(this.formData.items){
          this.formData.items.forEach(item => {
              console.log(item.lease_coverage_regions)
              this.chooseArr = item.lease_coverage_regions
          })
      }

      if(this.chooseCityArr2.length === 0){
        this.chooseCityArr2 = this.chuchun
      }
      let goodsJson = {
        "id": that.goodsId,
        //
        "category1_id": that.classifyCheck1,
        "category2_id": that.classifyCheck2,
        "category3_id": that.classifyCheck3,
        //
        "sort": that.goodsSort,
        "title": that.goodsTitle,
        "sn": that.sn,
        "barcode": that.barcode,
        "desc": that.goodsDesc,
        "image_url": that.goodsImageUrl,
        "video_url": that.videoUrl,
        "gallery": that.goodsGallery,
        "sales": parseInt(that.goodsSales),
        "service": that.goodsService,//待定字段
        "freight_type": parseInt(that.goodsFreightType),
        "freight": that.$changeMoneyY2F(that.goodsFreightPrice),
        "freight_template_id": that.freight_template_id,
        //
        // "detail_images": that.transformGoodsDetailImages(),
        "detail_images": that.goodsDetailImages,
        //
        "attrs": that.goodsParamList,
        //
        "unit": that.goodsUnit,
        "min_buy_qty": that.minBuyQty,
        "skus": [],
        //
        "qualifications": that.goodsAffiche,
        "shop_level": this.shop_level,
        "des_level": this.des_level,
        "express_level": this.express_level,
        "level": this.level,
        "single_option": this.typeRadio,
        "is_display": this.is_display,
        "status_lock": this.status_lock,
        // 发票
        /*"tax_code": that.tax_code,
        "tax_short_name": that.tax_short_name,
        "tax_unit": that.goodsUnit,
        "favorable_policy": that.favorable_policy,
        "is_favorable_policy": that.is_favorable_policy,
        "free_of_tax": that.free_of_tax,
        "short_code": that.short_code,
        "tax_measure_price": that.$changeMoneyY2F(that.goodsPrice),
        "tax_rate": that.tax_rate,
        "is_tax_logo": that.is_tax_logo,*/
        "bill_position": that.bill_position, // 新增部分
        // 租赁部分
        "lease_coverage": {
          "lease_coverage_regions":this.chooseCityArr2
        }
      };
      if (this.supplier_id) {
        if (this.source_id) {
          goodsJson.supplier_source_id = this.source_id
        }
        if (this.source_classify_id) {
          goodsJson.supplier_source_category_id = this.source_classify_id
        }
      }

      // 新增部分
      if (that.bill_position === 1) {
        goodsJson.tax_code = that.tax_code
        goodsJson.tax_short_name = that.tax_short_name
        goodsJson.tax_unit = that.goodsUnit
        goodsJson.favorable_policy = that.favorable_policy
        goodsJson.is_favorable_policy = that.is_favorable_policy
        goodsJson.free_of_tax = that.free_of_tax
        goodsJson.short_code = that.short_code
        goodsJson.tax_measure_price = that.$changeMoneyY2F(that.goodsPrice)
        goodsJson.tax_rate = that.tax_rate
        goodsJson.is_tax_logo = that.is_tax_logo
        goodsJson.tax_product_name = that.tax_product_name
      }
      if (this.brand_id) {
        goodsJson.brand_id = this.brand_id
      }
      if (that.typeRadio === 1) {
        goodsJson.skus = [];
        // goodsJson.sn = that.goodsSn;
        // goodsJson.barcode = that.barcode;
        goodsJson.volume = that.goodsVolume;
        goodsJson.price = that.$changeMoneyY2F(that.goodsPrice);
        goodsJson.origin_price = that.$changeMoneyY2F(that.goodsOriginPrice);
        goodsJson.sku_image = that.singleSkuImg;
        goodsJson.cost_price = that.$changeMoneyY2F(that.goodsCostPrice);
        goodsJson.guide_price = that.$changeMoneyY2F(that.goodsGuidePrice);
        goodsJson.activity_price = that.$changeMoneyY2F(that.goodsActivityPrice);
        goodsJson.stock = that.goodsStock;
        goodsJson.sku_id = that.sku_id;
        goodsJson.weight = that.goodsWeight;
        goodsJson.original_sku_id = that.original_sku_id;
      } else {
        goodsJson.price = that.$changeMoneyY2F(that.goodsPrice);
        goodsJson.origin_price = that.$changeMoneyY2F(that.goodsOriginPrice);
        goodsJson.cost_price = that.$changeMoneyY2F(that.goodsCostPrice);
        goodsJson.guide_price = that.$changeMoneyY2F(that.goodsGuidePrice);
        goodsJson.activity_price = that.$changeMoneyY2F(that.goodsActivityPrice);
        goodsJson.stock = that.goodsStock;
        goodsJson.skus = that.transformSkusY2F();
      }
      that.$loading({"text": "提交中..."});
      if (that.goodsId == 0) {
        leaseSupplyCreateProduct(goodsJson).then((res) => {
          if (res.code == 0) {
            that.$message.success(res.msg);
            that.$loading().close();
            that.$router.back();
          } else {
            that.$message.error(res.msg);
            that.$loading().close();
          }
        });
      } else {
          leaseSupplyUpdateProduct(goodsJson).then((res) => {
          if (res.code == 0) {
            that.$message.success(res.msg);
            that.$loading().close();
            this.initProduct()
            that.$router.back();
          } else {
            that.$message.error(res.msg);
            that.$loading().close();
          }
        });
      }
    },

    //转换格式
    transformGoodsDetailImages() {
      let goodsDetailImagesPara = [];
      this.goodsDetailImages.forEach(item => {
        goodsDetailImagesPara.push(item.url);
      });
      return goodsDetailImagesPara;
    },

    //元转分sku
    transformSkusY2F() {
      let that = this;
      let newSkus = [];
      that.skus.forEach(sku => {
        let tempSku = {...sku};
        tempSku.price = that.$changeMoneyY2F(tempSku.price);
        tempSku.cost_price = that.$changeMoneyY2F(tempSku.cost_price);
        tempSku.origin_price = that.$changeMoneyY2F(tempSku.origin_price);
        tempSku.guide_price = that.$changeMoneyY2F(tempSku.guide_price);
        tempSku.activity_price = that.$changeMoneyY2F(tempSku.activity_price);
        newSkus.push(tempSku);
      });
      return newSkus;
    },
    //验证字段
    verifyGoods() {
      let that = this;
      if (!that.classifyCheck1 || !that.classifyCheck2 || !that.classifyCheck3) {
        that.$message.error("请选择分类");
        return false;
      }
      // if (!that.classifyCheck1) {
      //     that.$message.error("请选择分类");
      //     return false;
      // }
      if (!that.goodsTitle) {
        that.$message.error("请填写商品名称");
        return false;
      }
      if (!this.goodsImageUrl) {
        that.$message.error("请上传商品主图");
        return false;
      }
      if (this.goodsGallery.length <= 0) {
        that.$message.error("请上传商品图片");
        return false;
      }
      if (!this.goodsFreightType) {
        that.$message.error("请设置运费");
        return false;
      }

      if (this.goodsFreightType == "0") {
        if (this.goodsFreightPrice === "" || this.goodsFreightPrice === '' || this.goodsFreightPrice == undefined) {
          that.$message.error("请填写统一运费");
          return false;
        }
      } else if (this.goodsFreightType == "1") {
        if (!this.freight_template_id) {
          that.$message.error("请选择运费模版");
          return false;
        }
      }
      if (!that.goodsUnit) {
        that.$message.error("请填写商品单位");
        return false;
      }

      if (!that.verifyPrice(that.goodsPrice)) {
        that.$message.error("请填写商品供货价");
        return false;
      }

      if (!that.verifyPrice(that.goodsOriginPrice)) {
        that.$message.error("请填写建议零售价");
        return false;
      }

      if (!that.verifyPrice(that.goodsCostPrice)) {
        that.$message.error("请填写商品成本价");
        return false;
      }

      if (!that.verifyPrice(that.goodsGuidePrice)) {
        that.$message.error("请填写商品指导价");
        return false;
      }

      if (!that.verifyPrice(that.goodsActivityPrice)) {
        that.$message.error("请填写商品营销价");
        return false;
      }

      if (!that.verifyPrice(that.goodsStock)) {
        that.$message.error("请填写商品库存");
        return false;
      }
      if (that.typeRadio === 0) {
        if (!that.verifySkus()) {
          return false;
        }
      }
      if (that.bill_position === 1 && that.tax_code) { // 新增部分
        if (that.tax_rate === '') {
          that.$message.error("商品未赋码请选择税率");
          return false
        }
        if (that.is_favorable_policy === 1 && !that.favorable_policy) {
          that.$message.error("商品未赋码请选择优惠政策类型");
          return false
        }
        if ([1, 2, 3, 4].indexOf(that.free_of_tax) === -1) {
          that.$message.error("商品未赋码请选择免税类型")
          return false
        }
      }
      // 新增部分
      let flg = true;
      if (that.bill_position === 2) {
        for (let i = 0; i < that.skus.length; i++) {
          if (that.skus[i].tax_code) {
            if (typeof that.skus[i].tax_rate === 'undefined' || that.skus[i].tax_rate === '') {
              that.$message.error(`[${that.skus[i].title}] 规格未赋码请选择税率`);
              flg = false
              break;
            }
            if (that.skus[i].is_favorable_policy && that.skus[i].is_favorable_policy === 1) {
              if (!that.skus[i].favorable_policy) {
                that.$message.error(`[${that.skus[i].title}] 规格未赋码请选择优惠政策类型`);
                flg = false
                break;
              }
            }
            if ([1, 2, 3, 4].indexOf(that.skus[i].free_of_tax) === -1) {
              that.$message.error(`[${that.skus[i].title}] 规格未赋码请选择免税类型`)
              flg = false
              break;
            }
          }

        }
        if (!flg) {
          return false
        }
      }
      return true;
    },

    //验证价格
    verifyPrice(price) {
      if (price == null || price == '') {
        let priceInt = parseInt(price);
        if (priceInt == 0) {
          return true;
        }
        return false;
      }
      return true;
    },

    //批量处理skus
    skusBatch() {
      let that = this;
      switch (that.batchValueName) {
        case "batchStock":
          that.skus.forEach(sku => {
            sku.stock = that.batchInputValue;
          });
          break;
        case "batchOriginPrice":
          that.skus.forEach(sku => {
            sku.origin_price = that.batchInputValue;
          });
          break;
        case "batchPrice":
          that.skus.forEach(sku => {
            sku.price = that.batchInputValue;
          });
          break;
        case "batchCostPrice":
          that.skus.forEach(sku => {
            sku.cost_price = that.batchInputValue;
          });
          break;
        case "batchGuidePrice":
          that.skus.forEach(sku => {
            sku.guide_price = that.batchInputValue;
          });
          break;
        case "batchActivityPrice":
          that.skus.forEach(sku => {
            sku.activity_price = that.batchInputValue;
          });
          break;
        case "batchWeight":
          that.skus.forEach(sku => {
            sku.weight = that.batchInputValue;
          });
          break;
        case "batchVolume":
          that.skus.forEach(sku => {
            sku.volume = that.batchInputValue;
          });
          break;
        default:
          break;
      }
      that.cancelBatch();
    },

    regionSave() {
      this.initProduct()
      let shengarr = []
      let shiarr = []
      let quarr = []
      //省数组
      let shengArr = []
      // 市数组
      let shiArr = []
      // 区数组
      let quArr = []
      this.chooseCityArr.forEach(item => {
        shengarr.push({id:item[0]})
        shiarr.push({id:item[1],parent_id:item[0]})
        quarr.push({id:item[2],parent_id:item[1]})
        const res = new Map();
        shengArr = shengarr.filter((shengarr) => !res.has(shengarr.id) && res.set(shengarr.id, 1));
        shiArr = shiarr.filter((shiarr) => !res.has(shiarr.id) && res.set(shiarr.id, 1));
        quArr = quarr.filter((quarr) => !res.has(quarr.id) && res.set(quarr.id, 1));
      })
      shengArr.forEach((shengItem,shengIndex) => {
        let cc  = shiArr.filter(function (value) {
          return value.parent_id === shengItem.id
        })
        shengArr[shengIndex].lease_coverage_regions = cc
      })
      console.log(shengArr)
      shengArr.forEach(sheng2Item => {
        sheng2Item.lease_coverage_regions.forEach((sheng2children,index) => {
          let bb  = quArr.filter(function (value) {
            return value.parent_id === sheng2children.id
          })
          sheng2Item.lease_coverage_regions[index].lease_coverage_regions = bb
        })
      })
      this.chooseCityArr2 = shengArr
      this.addregionShow = false;
    },
    regionhandleClose(){
      this.addregionShow = false;
    },

    // 下一步
    handleNext(data) {
      this.step = 2
      this.initClassifyTitle(data);
    },
    handleSkusImgSuccess(res, file, index) {
      if (res.code === 0) {
        // this.skus[index].image_url = res.data.file.url
        this.$set(this.skus, index, {...this.skus[index], image_url: res.data.file.url})
      }
    },
    //恢复数据
    recoverGoods(goods) {
      let that = this;
      // 恢复发票数据
      if (goods.bill_position && goods.bill_position === 1) { // 修改部分
        that.tax_code = goods.tax_code
        that.tax_short_name = goods.tax_short_name
        // that.tax_option = goods.tax_option
        that.tax_unit = goods.tax_unit
        that.favorable_policy = goods.favorable_policy
        that.is_favorable_policy = goods.is_favorable_policy
        that.free_of_tax = goods.free_of_tax || 0
        that.short_code = goods.short_code
        that.tax_measure_price = that.$changeMoneyF2Y(goods.tax_measure_price)

        that.taxRateOptios = [// 税率optios
          {label: "0%", value: 0},
          {label: "1%", value: 1},
          {label: "3%", value: 3},
          {label: "6%", value: 6},
          {label: "9%", value: 9},
          {label: "10%", value: 10},
          {label: "11%", value: 11},
          {label: "13%", value: 13},
          {label: "17%", value: 17},
        ]
        if (that.taxRateOptios.indexOfJSON("value", goods.tax_rate) === -1) {
          that.taxRateOptios.push({label: `${goods.tax_rate}%`, value: goods.tax_rate})
        }
        that.tax_rate = goods.tax_rate
        that.is_tax_logo = goods.is_tax_logo
        that.tax_product_name = goods.tax_product_name
      }
      //that.bill_position = goods.bill_position ?? ""; // 新增部分
      if (goods.supplier_id) {
        this.supplier_id = goods.supplier_id
        this.source_id = goods.supplier_source_id ? goods.supplier_source_id : null
        this.source_classify_id = goods.supplier_source_category_id ? goods.supplier_source_category_id : null
        this.getSourceOption()
        this.getSourceClassifyOption()
      }
      that.typeRadio = goods.single_option
      if (that.typeRadio === 1) {
        that.sku_id = goods.sku_id
      }
      if (goods.brand_id) {
        that.brand_id = goods.brand_id
      }
      that.shop_level = goods.shop_level || 0
      that.des_level = goods.des_level || 0
      that.express_level = goods.express_level || 0
      that.level = goods.level || 0
      //商品id
      that.goodsId = goods.id;
      //恢复分类
      that.getClassify2o3(2, goods.category1_id);
      that.getClassify2o3(3, goods.category2_id);
      setTimeout(() => {
        that.classifyCheck1 = goods.category1_id;
        that.classifyCheck2 = goods.category2_id;
        that.classifyCheck3 = goods.category3_id;
      }, 700)

      //基本信息
      that.minBuyQty = goods.min_buy_qty
      that.goodsTitle = goods.title;
      that.sn = goods.sn;
      that.barcode = goods.barcode;
      that.goodsDesc = goods.desc;
      that.goodsImageUrl = goods.image_url;
      that.videoUrl = goods.video_url;
      that.goodsSort = goods.sort
      that.goodsGallery = [];
      that.goodsGalleryFileList = [];
      that.status_lock = goods.status_lock ?? 0
      that.is_display = goods.is_display ?? 0
      if (goods.gallery) {
        goods.gallery.forEach(item => {
          that.goodsGallery.push({
            url: item.src,
            src: item.src,
            type: item.type
          })
        });
      }
      that.goodsGalleryFileList = [...that.goodsGallery];
      that.goodsSales = goods.sales;
      that.goodsFreightType = goods.freight_type || goods.freight_type == 0 ? goods.freight_type.toString() : "";
      getSupplySetting({id: goods.gather_supply_id}).then(res => {
        if (res.code === 0) {
          this.priceIsEdit = res.data.setting.data.setting.edit_price
        }
      })
      findSetting().then(res => {
        if (res.code === 0) {
          this.hide_uniform_freight = res.data.resupplierSetting.value.hide_uniform_freight
        }
      })

      that.goodsFreightPrice = that.$changeMoneyF2Y(goods.freight);
      that.freight_template_id = goods.freight_template_id;
      //商品详情
      that.goodsDetailImages = goods.detail_images
      /* that.goodsDetailImages = [];
      that.goodsDetailImagesFileList = [];
      goods.detail_images.forEach(item => {
          that.goodsDetailImages.push({
              url: item
          })
      });
      that.goodsDetailImagesFileList = [...that.goodsDetailImages]; */
      //商品参数
      that.goodsParamList = goods.attrs || [];
      //销售属性
      that.goodsUnit = goods.unit;
      that.goodsPrice = that.$changeMoneyF2Y(goods.price);
      that.goodsOriginPrice = that.$changeMoneyF2Y(goods.origin_price);
      that.goodsCostPrice = that.$changeMoneyF2Y(goods.cost_price);
      that.goodsGuidePrice = that.$changeMoneyF2Y(goods.guide_price);
      that.goodsActivityPrice = that.$changeMoneyF2Y(goods.activity_price);
      that.skus = [...goods.skus];
      if (that.typeRadio === 1) {
        that.goodsSn = goods.sn;
        that.goodsBarcode = goods.barcode;
        that.goodsWeight = goods.skus[0].weight;
        that.goodsVolume = goods.volume;
        that.goodsStock = goods.stock;
        that.singleSkuImg = goods.skus[0].image_url;
        that.original_sku_id = goods.skus[0].original_sku_id;
      } else {
        that.goodsStock = goods.stock;
        that.skus.forEach(item => {
          if (item.options) {
            item.optionName = that.recoverSkuItemOptionName(item.options);
          }
          item.cost_price = that.$changeMoneyF2Y(item.cost_price);
          item.origin_price = that.$changeMoneyF2Y(item.origin_price);
          item.price = that.$changeMoneyF2Y(item.price);
          item.guide_price = that.$changeMoneyF2Y(item.guide_price);
          item.activity_price = that.$changeMoneyF2Y(item.activity_price);
        });
        if (that.skus[0].options) {
          that.recoverSpec(that.skus[0].options);
        }
      }
      that.goodsAffiche = goods.qualifications ? [...goods.qualifications] : [];
      that.goodsAffiche.forEach(item => {
        item.url = item.src;
        item.src = item.src;
      });

    },
    //组装spec
    recoverSpec(options) {
      let that = this;
      let specItemsMap = new Map();
      for (let i = 0; i < options.length; i++) {
        that.addSpecItem(options[i].spec_name);
        //创建map值
        specItemsMap.set(options[i].spec_name, []);
        that.skus.forEach(sku => {
          let arr = sku.optionName.split('+');
          let tempMapItems = specItemsMap.get(options[i].spec_name);
          tempMapItems.push(arr[i]);
          const set = new Set(tempMapItems);
          tempMapItems = [...set];//去重操作
          specItemsMap.set(options[i].spec_name, tempMapItems);
        });
        that.updateSpecSubdataItemByNames(i, specItemsMap.get(options[i].spec_name));
      }
    },

    //组装optionName
    recoverSkuItemOptionName(options) {
      let optionName = "";
      for (let i = 0; i < options.length; i++) {
        if (i === options.length - 1) {
          optionName = optionName + options[i].spec_item_name;
        } else {
          optionName = optionName + options[i].spec_item_name + "+";
        }
      }
      return optionName;
    }
  }


}

</script>
<style lang="scss" scoped>
@import "@/style/base.scss";

.productImgBox {
  width: 180px;
  height: 180px;
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;

  &:hover {
    .del-box {
      display: inline-block;
    }
  }

  .del-box {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.6);
    text-align: center;

    .el-button {
      margin-top: 70px;
      font-size: 18px;
      color: #fff;
    }

  }
}

.addGoods-box {
  /* ::v-deep .el-tabs {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        .el-tabs__active-bar {
          display: none;
        }

        .el-tabs__item {
          font-weight: bold;
          font-size: 16px;
          margin-left: 30px;

          &:hover {
            color: $green1;
          }

          &.is-active {
            color: $green1;
          }
        }
      }
    }
  } */

  .qualifications-row {
    .el-row {
      padding: 0;
    }

    .qualifications-img-box {
      img {
        width: 500px;
      }
    }
  }
}

.pl20 {
  padding-left: 5%;
}

::v-deep .ql-editor {
  img {
    max-width: 100%;
  }
}

::v-deep .avatar-uploader {
  .el-upload-list {
    li {
      width: 178px;
      height: 178px;
    }
  }

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 178px;
    height: 178px;

    img {
      width: 178px;
      height: 178px;
    }
  }
}

::v-deep .sku_avatar-uploader {
  .el-upload-list {
    li {
      width: 75px;
      height: 75px;
      min-width: 75px;
      min-height: 75px;
    }
  }

  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 75px;
    height: 75px;
    min-width: 75px;
    min-height: 75px;

    .avatar-uploader-icon {
      line-height: 75px;
    }

    img {
      width: 75px;
      height: 75px;
      min-width: 75px;
      min-height: 75px;
    }
  }
}

.video-box {
  width: 178px;
  height: 178px;
  border: 1px dashed #d9d9d9;
  margin-right: 8px;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;

  line-height: 178px;
  text-align: center;
}

.sug-p {
  font-size: 12px;
  color: #c1c1c1;
}

// 商品参数部分
.goods-param.table-div {
  .close-btn {
    font-size: 18px;
    color: #4692b1;
    margin-left: 10px;
    margin-right: 10px;
  }

  .table-head {
    border-bottom: 1px solid #dfdfdf;
    padding: 10px 0;

    .head-span {
      display: inline-block;
      margin-left: 40px;
      color: #979ca4;
    }
  }

  .table-body {
    .tr-div {
      border-bottom: 1px solid #f6f5f5;

      .td-div {
        padding: 10px 30px 10px 0;
      }
    }

    .add-btn-box {
      margin-top: 20px;
    }
  }
}

.w287 {
  width: 287px;
}

// 销售属性部分
.sale-box {
  /*   .form-padding {
    padding-left: 10%;
    padding-right: 5%;
  } */

  //  多规格部分
  ::v-deep .multi-spe-box {
    .el-row {
      padding: 0;

      .el-col {
        padding-top: 0 !important;
      }
    }

    .spe-box-item {
      position: relative;

      &:hover {
        i.close-i {
          display: inline-block;
        }
      }

      .item-spe-input {
        .el-input-group__append {
          .el-button {
            i.el-icon-close {
              font-weight: bold;
              // color: #6ba6be;
            }
          }
        }
      }

      i.close-i {
        cursor: pointer;
        display: none;
        position: absolute;
        top: -8px;
        right: 0px;
        z-index: 999;
        font-size: 18px;
      }
    }

    .add-spe-btn {
      border-color: #52b9d1;
      background-color: #52b9d1;
      color: white;

      &.discolor {
        opacity: 0.5;
      }
    }

    .del-spe-btn {
      border-color: #e15141;
      background-color: #e15141;
      color: white;
    }

    //表格部分
    .spe-table {
      width: 100%;
      border-color: #e4e4e4;

      .el-input-number {
        width: 100%;
      }

      tr {
        td {
          border-style: solid;
          border-color: #e4e4e4;
          padding: 10px;
          text-align: center;

          &:last-child {
            border-right-width: 1px;
          }

          &.green-color {
            background-color: #e2efda;
          }

          &.yellow-color {
            background-color: #fbf8e5;
          }

          &.pink-color {
            background-color: #efdfde;
          }

          &.gray-color {
            background-color: #f5f5f5;
          }
        }
      }

      thead {
        tr {
          td {
            border-width: 1px 0 1px 1px;
          }
        }
      }

      tbody {
        tr {
          td {
            border-width: 0 0 1px 1px;
          }
        }
      }
    }
  }
}

.ml10 {
  margin-left: 10px;
}

.mr10 {
  margin-right: 10px;
}

.mt10 {
  margin-top: 10px;
}

.w100 {
  width: 100%;
}

.w120 {
  width: 120px !important;
}

.w200 {
  width: 200px;
}

.text-left {
  text-align: left !important;
}

::v-deep .el-input-number {
  .el-input__inner {
    text-align: left;
  }
}

.batck-btn-box {
  span.batch-span {
    margin-right: 10px;
  }

  .el-button {
    padding: 0;
    margin: 10px 5px;

    &.conf-btn {
      color: #000000;
    }
  }
}

.draggable-box {
  span {
    display: flex;
    flex-wrap: wrap;
  }

  /*被拖拽对象的样式*/
  .item {
    width: 180px;
    height: 180px;
    padding: 6px;
    margin-left: 10px;
    margin-bottom: 10px;
    cursor: move;

    &:nth-child(1) {
      margin-left: 0;
    }
  }

  .item img {
    width: 100%;
    height: 100%;
  }

  /*选中样式*/
  .chosen {
    border: solid 1px #269d88 !important;
  }
}

.tree-box {
  height: 368px;
  overflow-y: auto;
}

.right-form-box {
  //border-left: 1px solid #e4e7ed;
}

// 发票管理表格部分
.bill-table {
  width: 100%;
  border-color: #e4e4e4;

  .el-input-number {
    width: 100%;
  }

  tr {
    td {
      border-style: solid;
      border-color: #e4e4e4;
      padding: 10px;
      text-align: center;

      &:last-child {
        border-right-width: 1px;
      }

      &.green-color {
        background-color: #e2efda;
      }

      &.yellow-color {
        background-color: #fbf8e5;
      }

      &.pink-color {
        background-color: #efdfde;
      }

      &.gray-color {
        background-color: #f5f5f5;
      }
    }
  }

  thead {
    tr {
      td {
        border-width: 1px 0 1px 1px;
      }
    }
  }

  tbody {
    tr {
      td {
        border-width: 0 0 1px 1px;
      }
    }
  }
}
.cityBtn{
  margin-top: 5px;
  display: inline-block;
  margin-left:8px;
}
.cityPrompt{
  color: rgba(175, 175, 175, 1);
  font-size: 14px;
  text-align: left;
  font-family: SourceHanSansSC-regular;
}
.drawer__footer {
  text-align: right;
  margin-top: 20px;
  padding-bottom: 20px;
}
</style>

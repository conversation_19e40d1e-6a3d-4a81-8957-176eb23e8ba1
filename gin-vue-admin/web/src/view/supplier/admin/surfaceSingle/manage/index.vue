<template>
    <m-card>
        <el-button type="primary" @click="openAddEdit(null)">新增</el-button>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="ID" align="center" prop="id"></el-table-column>
            <el-table-column label="电子面单模板名称" align="center" prop="name"></el-table-column>
            <el-table-column label="快递类型" align="center" prop="shipper_name"></el-table-column>
            <el-table-column label="是否默认(只能设置一个)" align="center">
                <template slot-scope="scope">
                    <el-switch
                        @change="handleDefaultChange(scope.row)"
                        v-model="scope.row.is_default"
                        :active-value="1"
                        :inactive-value="0"
                    ></el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openAddEdit(scope.row)">编辑</el-button>
                    <el-button type="text" class="color-red" @click="del(scope.row)">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <AddEdit ref="addEdit" @reload="getTableData(1, 10)"></AddEdit>
    </m-card>
</template>

<script>
import infoList from '@/mixins/infoList';
import AddEdit from './components/add';
import { getSurfaceSinglesList, changeDefault, deleteSurfaceSingle } from '@/api/adminPrint';
export default {
    name: 'SupplierSurfaceSingleManageIndex',
    mixins: [infoList],
    components: { AddEdit },
    data() {
        return {
            listApi: getSurfaceSinglesList,
        };
    },
    mounted() {
        this.getTableData();
    },
    methods: {
        del(row) {
            this.$fn.clicks(async function () {
                const { code, msg } = await deleteSurfaceSingle({ id: row.id });
                if (code === 0) {
                    this.$message.success(msg);
                    this.getTableData();
                }
            }, '确定删除?');
        },
        async handleDefaultChange(row) {
            let params = {
                id: row.id,
                is_default: row.is_default,
            };
            const { code, msg } = await changeDefault(params);
            if (code === 0) {
                this.$message.success(msg);
                this.getTableData();
            }
        },
        openAddEdit(row) {
            this.$refs.addEdit.open(row);
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/style/base.scss';
</style>

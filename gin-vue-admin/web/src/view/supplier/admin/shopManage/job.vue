<template>
  <m-card>
    <el-button @click="openDialog" type="primary">新增岗位</el-button>
    <el-form class="search-term mt25" :model="searchInfo">
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.name" class="line-input" clearable>
            <span slot="prepend">岗位名称</span>
        </el-input>
      </el-form-item> 
      <el-form-item>
        <el-button @click="onSubmit" type="primary">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-popover placement="top" v-model="deleteVisible" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin: 0">
            <el-button @click="deleteVisible = false" size="mini" type="text"
              >取消</el-button
            >
            <el-button @click="onDelete" size="mini" type="primary"
              >确定</el-button
            >
          </div>
          <!--            <el-button icon="el-icon-delete" size="mini" slot="reference" type="danger">批量删除</el-button>-->
        </el-popover>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData"
      @selection-change="handleSelectionChange"
      class="mt25"
    >
      <!--    <el-table-column type="selection" width="55"></el-table-column>-->
      <el-table-column label="日期" align="center">
        <template slot-scope="scope">{{
          scope.row.created_at | formatDate
        }}</template>
      </el-table-column>
      <el-table-column
        label="岗位名称"
        prop="name"
        align="center"
      ></el-table-column>

      <el-table-column
        label="备注"
        prop="remark"
        align="center"
      ></el-table-column>

      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button @click="updateSupplierJob(scope.row)" type="text"
            >编辑</el-button
          >
          <el-button type="text" class="color-red" @click="deleteRow(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="page"
      :page-size="pageSize"
      :page-sizes="[10, 30, 50, 100]"
      :style="{ float: 'right', padding: '20px' }"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>

    <el-dialog
      :before-close="closeDialog"
      :visible.sync="dialogFormVisible"
      title="操作"
    >
      <el-form :model="formData" label-position="right" label-width="90px">
        <el-form-item>
          <span slot="label"><span class="color-red">* </span>岗位名称:</span>
          <el-input
            v-model="formData.name"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>

        <el-form-item label="备注:">
          <el-input
            v-model="formData.remark"
            clearable
            placeholder="请输入"
          ></el-input>
        </el-form-item>

        <el-form-item label="权限菜单:">
          <el-checkbox-group v-model="formData.menus" size="mini">
            <el-checkbox
              v-for="(item, index) in initMenus"
              :key="index"
              :label="item.id"
              :disabled="item.disabled"
              border
              >{{ item.title }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="权限api:">
          <el-checkbox-group v-model="formData.apis" size="mini">
            <el-checkbox
              v-for="(item, index) in initApis"
              :key="index"
              :label="item.id"
              :disabled="item.disabled"
              border
              >{{ item.title }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <div class="dialog-footer" slot="footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button @click="enterDialog" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </m-card>
</template>

<script>
import {
  createSupplierJob,
  deleteSupplierJob,
  deleteSupplierJobByIds,
  updateSupplierJob,
  findSupplierJob,
  getSupplierJobList,
  getInitJobMenus,
  getInitJobApis,
} from "@/api/adminSupplierJob"; //  此处请自行替换地址
import { formatTimeToStr } from "@/utils/date";
import infoList from "@/mixins/infoList";
export default {
  name: "SupplierJob",
  mixins: [infoList],
  data() {
    return {
      listApi: getSupplierJobList,
      initMenuApi: getInitJobMenus,
      initJobApi: getInitJobApis,
      dialogFormVisible: false,
      type: "",
      deleteVisible: false,
      multipleSelection: [],
      initMenus: [],
      initApis: [],
      formData: {
        name: "",
        remark: "",
        menus: [],
        apis: [],
      },
    };
  },
  filters: {
    formatDate: function (time) {
      if (time != null && time != "") {
        var date = new Date(time);
        return formatTimeToStr(date, "yyyy-MM-dd hh:mm:ss");
      } else {
        return "";
      }
    },
    formatBoolean: function (bool) {
      if (bool != null) {
        return bool ? "是" : "否";
      } else {
        return "";
      }
    },
  },
  methods: {
    //条件搜索前端看此方法
    onSubmit() {
      this.page = 1;
      this.pageSize = 10;
      this.getTableData();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    deleteRow(row) {
      this.$confirm("此岗位关联的成员也会同时删除，确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.deleteSupplierJob(row);
      });
    },
    async onDelete() {
      const ids = [];
      if (this.multipleSelection.length == 0) {
        this.$message({
          type: "warning",
          message: "请选择要删除的数据",
        });
        return;
      }
      this.multipleSelection &&
        this.multipleSelection.map((item) => {
          ids.push(item.ID);
        });
      const res = await deleteSupplierJobByIds({ ids });
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length == ids.length) {
          this.page--;
        }
        this.deleteVisible = false;
        this.getTableData();
      }
    },
    async updateSupplierJob(row) {
      const res = await findSupplierJob({ id: row.id });
      this.type = "update";
      if (res.code == 0) {
        this.formData = res.data.resupplierJob;
        this.dialogFormVisible = true;
      }
    },
    closeDialog() {
      this.dialogFormVisible = false;
      this.formData = {
        name: "",
        remark: "",
        menus: [],
        apis: [],
      };
    },
    async deleteSupplierJob(row) {
      const res = await deleteSupplierJob({ ID: row.id });
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length == 1) {
          this.page--;
        }
        this.getTableData();
      }
    },
    async enterDialog() {
      if(!this.formData.name){
        this.$message.error("请填写岗位名称")
        return false
      }
      let res;
      switch (this.type) {
        case "create":
          this.formData.menus.push("1")
          res = await createSupplierJob(this.formData);
          break;
        case "update":
          res = await updateSupplierJob(this.formData);
          break;
        default:
          res = await createSupplierJob(this.formData);
          break;
      }
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "创建/更改成功",
        });
        this.closeDialog();
        this.getTableData();
      }
    },
    openDialog() {
      this.type = "create";
      this.dialogFormVisible = true;
    },
    async setInitMenus() {
      const options = await this.initMenuApi();
      if (options.code == 0) {
        options.data.list.map((item) => {
          const option = {
            id: item.ID,
            title: item.Title,
          };
          this.initMenus.push(option);
        });
      }
    },
    async setInitApis() {
      const options = await this.initJobApi();
      if (options.code == 0) {
        options.data.list.map((item) => {
          const option = {
            iid: item.ID,
            id: item.path + "," + item.method,
            title: item.description,
          };
          this.initApis.push(option);
        });
      }
    },
  },
  async created() {
    await this.getTableData();
    await this.setInitMenus();
    await this.setInitApis();
  },
};
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
::v-deep .el-checkbox-group {
  .el-checkbox {
    margin-left: 0 !important;
    margin-right: 20px !important;
  }
}
</style>

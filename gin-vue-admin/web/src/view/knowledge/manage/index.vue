<template>
    <m-card>
        <el-button type="primary" @click="onCreate">新增</el-button>
        <el-form class="search-term mt25" label-width="135px" inline>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchForm.name"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">知识库名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input">
                    <div class="line-box">
                        <span>状态</span>
                    </div>
                    <el-select
                        v-model="searchForm.status"
                        clearable
                        filterable
                        class="w100"
                    >
                        <template v-for="item in statusList">
                            <el-option
                                :label="item.name"
                                :value="item.value"
                                :key="item.value"
                            ></el-option>
                        </template>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="onSearch">搜索</el-button>
                <el-button type="text" @click="onClear">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" style="margin-top: 30px">
            <el-table-column
                prop="id"
                label="ID"
                align="center"
            ></el-table-column>
            <el-table-column
                label="排序"
                align="center"
                prop="sort"
            ></el-table-column>
            <el-table-column prop="created_at" label="创建时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column
                prop="name"
                label="知识库名称"
                align="center"
            ></el-table-column>
            <el-table-column prop="cover" label="封面" align="center">
                <template slot-scope="scope">
                    <m-image
                        style="width: 60px; height: 60px"
                        :src="scope.row.cover"
                    ></m-image>
                </template>
            </el-table-column>
            <el-table-column prop="articles" label="文档数量" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.num }}篇</p>
                    <el-button type="text" @click="openArticle(scope.row)">
                        查看
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column prop="status" label="启用" align="center">
                <template slot-scope="scope">
                    <el-switch
                        v-model="scope.row.status"
                        :active-value="1"
                        :inactive-value="0"
                        @change="onChangeDefault(scope.row)"
                    >
                    </el-switch>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template v-slot="scope">
                    <el-button
                        type="text"
                        @click="onEdit(scope.row)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        >编辑</el-button
                    >
                    <el-button
                        type="text"
                        slot="reference"
                        class="btn-del-text"
                        @click="onDelete(scope.row)"
                        style="
                            padding: 0 !important;
                            margin-left: 10px !important;
                        "
                        >删除</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        ></el-pagination>
        <KnowledgeDialog
            :visible.sync="dialog.visible"
            :pageStatus="dialog.pageStatus"
            :propRow="dialog.propRow"
            @submitted="onSubmitted"
        >
        </KnowledgeDialog>
        <ArticleDrawer :visible.sync="drawer.visible" :propRow="drawer.propRow">
        </ArticleDrawer>
    </m-card>
</template>
<script>
import KnowledgeDialog from './components/kownledgeDialog.vue';
import ArticleDrawer from './components/articleDrawer.vue';
import {
    getKnowledgeBaseList,
    deleteKnowledgeBase,
    setAttributeStatus,
} from '@/api/knowledge.js';
const PAGE_STATUS = {
    CREATE: 0,
    EDIT: 1,
};
export default {
    name: 'kownledgeManageIndex',
    components: {
        KnowledgeDialog,
        ArticleDrawer,
    },
    data() {
        return {
            statusList: [
                { name: '启用', value: 1 },
                { name: '不启用', value: 0 },
            ],
            dialog: {
                visible: false,
                pageStatus: 0,
                propRow: {
                    id: '',
                },
            },
            drawer: {
                visible: false,
                propRow: {
                    id: '',
                },
            },
            searchForm: {
                name: '',
                status: null,
            },
            tableData: [],
            page: 1,
            pageSize: 10,
            total: 0,
        };
    },
    created() {
        this.init();
    },
    methods: {
        openArticle(row) {
            this.drawer.visible = true;
            this.drawer.propRow = { ...row };
        },
        async init() {
            try {
                const params = {
                    name: this.searchForm.name,
                    status: this.searchForm.status,
                    page: this.page,
                    pageSize: this.pageSize,
                };
                const res = await getKnowledgeBaseList(params);
                if (res.code === 0 && res.data.list.length > 0) {
                    this.tableData = res.data.list;
                }
            } catch (error) {
                console.log(error);
                this.$message.error('初始化失败');
            }
        },
        async onDelete(row) {
            try {
                await this.$confirm('是否确认删除?', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                });
            } catch (error) {
                this.$message({
                    type: 'info',
                    message: '已取消删除',
                });
                return;
            }
            try {
                await deleteKnowledgeBase({ id: row.id });
                this.$message.success('删除成功');
                this.init();
            } catch (error) {
                this.$message.error('删除失败');
            }
        },
        // 新增
        onCreate() {
            this.dialog.visible = true;
            this.dialog.pageStatus = PAGE_STATUS.CREATE;
        },
        onSearch() {
            this.init();
        },
        onClear() {
            this.searchForm.name = '';
            this.searchForm.status = '';
            this.init();
        },
        onEdit(row) {
            this.dialog.visible = true;
            this.dialog.pageStatus = PAGE_STATUS.EDIT;
            this.dialog.propRow = { ...row };
        },
        // 提交
        onSubmitted() {
            this.init();
        },
        onChangeDefault(row) {
            const params = {
                id: row.id,
                status: row.status,
            };
            setAttributeStatus(params).then((res) => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                } else {
                    this.$message.error(res.msg);
                }
            });
        },
        handleCurrentChange(page) {
            this.page = page;
            this.init();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.init();
        },
    },
};
</script>

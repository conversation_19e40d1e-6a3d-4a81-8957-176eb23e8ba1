<template>
  <el-dialog
    width="700px"
    :title="`知识库${dialogTitle}`"
    :visible="visible"
    @open="onOpen"
    @close="onClose"
    @closed="onClosed">
    <el-form :model="formData" label-width="90px">
      <el-form-item label="排序:" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="知识库名称:" prop="name">
        <el-input v-model="formData.name" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="是否启用:" prop="status">
        <el-switch v-model="formData.status" active-color="#155BD4" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="封面:" prop="cover">
        <el-upload
            class="logo-upload"
            :show-file-list="false"
            :action="`${$path}/fileUploadAndDownload/upload`"
            :headers="{'x-token':token}"
            :on-success="handleLogoSuccess"
            :before-upload="$fn.beforeAvatarUpload"
            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
          <img v-if="formData.cover" :src="formData.cover" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <p class="color-grap">建议尺寸: 100*100,或正方形图片</p>
      </el-form-item>
      <el-form-item label="简介:" prop="desc">
        <el-input
          v-model="formData.desc"
          type="textarea"
          :autosize="{ minRows: 6 }"
          clearable
          placeholder="请输入">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取 消</el-button>
      <el-button type="primary" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {mapGetters} from "vuex"
import { createKnowledgeBase, updateKnowledgeBase } from '@/api/knowledge.js'
const PAGE_STATUS = {
  CREATE: 0,
  EDIT: 1
}
export default {
  name: 'KnowledgeDialog',
  props: {
    // 弹窗显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 页面状态：新增/编辑
    pageStatus: {
      type: Number,
      default: PAGE_STATUS.CREATE
    },
    propRow: {
      type: Object,
      default: () => { return {} }
    }
  },
  data() {
    return {
      formData:{
        id: null,
        sort: null,
        name: "",
        status: 1,
        cover: "",
        desc: null, // 描述
      }
    }
  },
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
    // 页面标题
    dialogTitle () {
      switch (this.pageStatus) {
        case PAGE_STATUS.CREATE:
          return '新增'
        case PAGE_STATUS.EDIT:
        default:
          return '编辑'
      }
    },
    // 提交接口
    submitApi () {
      switch (this.pageStatus) {
        case PAGE_STATUS.CREATE:
          return createKnowledgeBase
        case PAGE_STATUS.EDIT:
        default:
          return updateKnowledgeBase
      }
    },

  },
  methods: {
    onOpen () {
      this.$nextTick(() => {
        switch (this.pageStatus) {
          case PAGE_STATUS.CREATE:
            break
          case PAGE_STATUS.EDIT:
            this.formData = {...this.propRow}
            break
        }
      })
    },
    onClose () {
      this.$emit('update:visible', false)
    },
    onClosed () {
      this.formData.id = null
      this.formData.sort = null
      this.formData.name = ''
      this.formData.status = null
      this.formData.cover = ''
      this.formData.desc = ''
    },
    async onSubmit(){
      try {
        let params = {...this.formData}
        params.sort = Number(params.sort)
        if(this.pageStatus === PAGE_STATUS.CREATE){
          delete params.id
        }      
        const { code, msg } = await this.submitApi(params)
        if(+code === 7){
          throw new Error(msg)         
        }
        this.$emit('update:visible', false)
        this.$emit('submitted')
        this.$message.success(`${this.dialogTitle}成功`)
      } catch (error) {
        this.$alert(error.message, { type:"error"})
      }
    },
    handleLogoSuccess(res) {
      if (res.code === 0) {
        this.formData.cover = res.data.file.url;
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeLogoUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
      }
      return isLt10M;
    },
  },
}
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";
::v-deep .logo-upload {
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 1px dashed #d9d9d9;
    img {
      width: 100px;
      height: 100px;
    }
  }
}
.dialog-footer {
  margin-top: 25px;
}
</style>
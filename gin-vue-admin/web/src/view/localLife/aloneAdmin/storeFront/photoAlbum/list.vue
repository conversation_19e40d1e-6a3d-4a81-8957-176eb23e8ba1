<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="相册" name="images">
                <images></images>
            </el-tab-pane>
            <el-tab-pane label="标签相册" name="tagImages">
                <tag-images></tag-images>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>
<script>
import images from './components/images.vue';
import tagImages from './components/tagImages.vue';
export default {
    name: 'localLifeASFPL',
    components: { images, tagImages },
    data() {
        return {
            activeName: 'images',
        };
    },
};
</script>
<style scoped lang="scss"></style>

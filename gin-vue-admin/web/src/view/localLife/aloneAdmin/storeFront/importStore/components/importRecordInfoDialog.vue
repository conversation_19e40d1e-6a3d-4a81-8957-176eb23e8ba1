<template>
    <el-drawer title="导入详情" :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
        :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
        <m-card>
            <el-form :model="searchForm" label-width="80px" class="search-term" inline>
                <el-form-item prop="store_name">
                    <el-input placeholder="请输入" v-model="searchForm.store_name" class="line-input" clearable>
                        <span slot="prepend">门店名称</span>
                    </el-input>
                </el-form-item>
                <el-form-item prop="user_mobile">
                    <el-input placeholder="请输入" v-model="searchForm.user_mobile" class="line-input" clearable>
                        <span slot="prepend">会员手机号</span>
                    </el-input>
                </el-form-item>
                <el-form-item prop="status">
                    <div class="line-input">
                        <div class="line-box">
                            <span>导入状态</span>
                        </div>
                        <el-select class="w100" v-model="searchForm.status" clearable>
                            <el-option label="成功" :value="1"></el-option>
                            <el-option label="失败" :value="-1"></el-option>
                        </el-select>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="handleSearchClick">搜索</el-button>
                    <el-button type="text" @click="resetData">重置搜索条件</el-button>
                </el-form-item>
            </el-form>
            <el-table :data="list" class="mt25">
                <el-table-column label="门店名称" prop="store_name" align="center"></el-table-column>
                <el-table-column label="会员手机号" prop="user_mobile" align="center"></el-table-column>
                <el-table-column label="省市区" align="center">
                    <template slot-scope="scope">
                        <p>{{ scope.row.province_name }}/{{ scope.row.city_name }}/{{ scope.row.area_name }}</p>
                    </template>
                </el-table-column>
                <el-table-column label="详细地址" prop="address_detail" align="center"></el-table-column>
                <el-table-column label="经纬度" align="center">
                    <template slot-scope="scope">
                        <p>经度{{ scope.row.longitude }}</p>
                        <p>纬度{{ scope.row.latitude }}</p>
                    </template>
                </el-table-column>
                <el-table-column label="经纬度" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.status | formatStatus }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                        <el-button type="text" v-if="scope.row.status === 0"
                            @click="checkerror(scope.row)">查看失败原因</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }" :total="total"
                @current-change="handleCurrentChange" @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        </m-card>
    </el-drawer>
</template>
<script>
import { importRecordInfo } from '@/api/localLife';


export default {
    name: "importRecordInfoDialog",
    data() {
        return {
            isShow: false,
            searchForm: {
                store_name: '',
                user_mobile: '',
                status: '',
            },
            list: [],
            page: 1,
            pageSize: 10,
            total: 0,
            id: null,
        }
    },
    filters: {
        formatStatus: function (v) {
            let s = "";
            switch (v) {
                case 0:
                    s = "失败"
                    break;
                case 1:
                    s = "成功"
                    break;
            }
            return s
        }
    },
    methods: {
        //查看失败原因
        checkerror(row) {
            this.$message.error(row.fail_reason)
        },
        // 搜索按钮
        handleSearchClick() {
            this.page = 1;
            this.search()
        },
        // 重置
        resetData() {
            this.searchForm = {
                store_name: '',
                user_mobile: '',
                status: '',
            }
            this.handleSearchClick()
        },
        // 关闭
        handleClose() {
            this.isShow = false
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.id = null;
            this.searchForm = {
                store_name: '',
                user_mobile: '',
                status: '',
            }
        },
        info(id = null) {
            this.isShow = true;
            if (id) {
                this.id = id
            };
            this.search()
        },
        // 搜索
        async search() {
            let data = {
                record_id: this.id,
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchForm
            }
            let res = await importRecordInfo(data)
            if (res.code === 0) {
                this.list = res.data.list
                this.total = res.data.total
            }
        },
        // 分页
        handleCurrentChange(page) {
            this.page = page;
            this.search();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.search();
        },
    }
}
</script>
<style scoped></style>
<template>
  <m-card>
    <el-form :model="formData" class="search-term" label-width="80px" inline>
      <el-form-item>
        <el-input placeholder="请输入" v-model="formData.value" class="line-input-width" clearable>
          <el-select v-model="orderSearchListTag" slot="prepend" @change="orderchange">
            <el-option :label="item.name" :value="item.value" v-for="item in orderSearchList">
            </el-option>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" v-model="formData.product_title" class="line-input" clearable>
          <span slot="prepend">商品名称</span>
        </el-input>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input">
          <div class="line-box ">
            <span>商品类型</span>
          </div>
          <el-select class="w100" filterable clearable v-model="formData.product_type">
            <el-option label="团购卷" :value="1"></el-option>
            <el-option label="代金卷" :value="2"></el-option>
            <el-option label="次卡" :value="3"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input">
          <div class="line-box ">
            <span>使用状态</span>
          </div>
          <el-select class="w100" filterable clearable v-model="formData.order_status">
            <el-option label="待使用" :value="1"></el-option>
            <el-option label="部分使用" :value="2"></el-option>
            <el-option label="已完成" :value="3"></el-option>
            <el-option label="已关闭" :value="-1"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input">
          <div class="line-box ">
            <span>售后状态</span>
          </div>
          <el-select class="w100" filterable clearable v-model="formData.status">
            <el-option :key="item.id" :label="item.name" :value="item.value" v-for="item in orderStatusConditions">
            </el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input">
          <div class="line-box ">
            <span>采购端</span>
          </div>
          <el-select class="w100" filterable clearable v-model="formData.application_id">
            <el-option v-for="item in orderPaymentTypeConditions" :key="item.id" :label="item.app_name"
              :value="item.id">
            </el-option>
          </el-select>
        </div>
      </el-form-item>
      <br />
      <el-form-item>
        <div class="line-input-date line-input">
          <div class="line-box">
            <span>申请时间</span>
          </div>
          <div class="f fac">
            <el-date-picker class="w100" placeholder="开始日期" type="datetime" v-model="formData.d1">
            </el-date-picker>
            <p class="title-3">至</p>
            <el-date-picker class="w100" placeholder="结束日期" type="datetime" v-model="formData.d2">
            </el-date-picker>
          </div>
        </div>
      </el-form-item>
      <el-form-item label-width="0px">
        <div class="f fac dateBtnBox">
          <span :class="dateActive === item.value ? 'is_active' : ''" :key="item.id" @click="handleDateTab(item)"
            v-for="item in dateList">{{ item.name }}</span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button @click="searchOrder()" type="primary">搜索</el-button>
        <el-button @click="clearSearchCondition()" type="text">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-tabs @tab-click="handleTabsClick" class="mt25 order-tabs" type="card" v-model="orderStatus">
      <el-tab-pane :key="item.id" :label="`${item.name} ${item.total !== null ? item.total : ''}`" :name="item.value"
        v-for="item in orderStatusConditions">
      </el-tab-pane>
    </el-tabs>
    <div class="table-box">
      <el-table :data="[{}]" class="table-head" :header-cell-style="{ height: '56px' }">
        <el-table-column label="商品" width="300"></el-table-column>
        <el-table-column align="center" label="使用状态" width="190"></el-table-column>
        <el-table-column align="center" label="订单金额(元)" width="190"></el-table-column>
        <el-table-column align="center" label="退款金额(元)" width="190"></el-table-column>
        <el-table-column align="center" label="技术服务费(元)" width="190"></el-table-column>
        <el-table-column align="center" label="申请时间" width="190"></el-table-column>
        <el-table-column align="center" label="售后状态" width="200"></el-table-column>
        <el-table-column align="center" label="操作"></el-table-column>
      </el-table>
      <div v-for="item in tableData" :key="item.id" class="mt25">
        <el-table :data="[item]" class="table-cont">
          <el-table-column>
            <template slot="header">
              <div class="w100 f fac fjsb">
                <div class="f fac">
                  <p>售后编号: {{ item.after_sale_sn }}</p>
                  <p>订单编号: {{ item.order_sn }}</p>
                  <p>第三方订单号: {{ item.order.third_order_sn }}</p>
                  <p v-if="item.application_id">采购端名称:{{ item.application.app_name }}</p>
                  <el-tag type="warning" class="ml10">{{ item.order.order_type | ordertype }}</el-tag>
                </div>
              </div>
            </template>
            <el-table-column width="300">
              <template slot-scope="scope">
                {{ scope.row.product_title }}
              </template>
            </el-table-column>
            <!-- 使用状态 -->
            <el-table-column width="190" align="center">
              <template slot-scope="scope">
                {{ scope.row.order.status | formatStatus }}
              </template>
            </el-table-column>
            <!-- 订单金额 -->
            <el-table-column width="190" align="center">
              <template slot-scope="scope">
                {{ scope.row.order.amount | formatF2Y }}
              </template>
            </el-table-column>
            <!-- 退款金额 -->
            <el-table-column width="190" align="center">
              <template slot-scope="scope">
                {{ scope.row.amount | formatF2Y }}
              </template>
            </el-table-column>
            <!-- 技术服务费 -->
            <el-table-column width="190" align="center">
              <template slot-scope="scope">
                {{ scope.row.technical_services_fee | formatF2Y }}
              </template>
            </el-table-column>
            <!-- 申请时间 -->
            <el-table-column width="190" align="center">
              <template slot-scope="scope">
                {{ scope.row.created_at | formatDate }}
              </template>
            </el-table-column>
            <!-- 售后状态 -->
            <el-table-column width="190" align="center">
              <template slot-scope="scope">
                {{ scope.row.status_name }}
              </template>
            </el-table-column>
            <el-table-column align="center">
              <template slot-scope="scope">
                <el-button type="text" @click="viewDetails(scope.row.id)">查看详情</el-button>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
      display: 'flex',
      justifyContent: 'flex-end',
      marginRight: '20px',
    }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
      layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    <detailAfterSale ref='detailAfterSale' @handleClose="searchOrder"></detailAfterSale>
  </m-card>
</template>
<script>
import { getBrandAfterSalesList } from '@/api/localLife';
import { formatTimeToStr } from "@/utils/date";
import { getApplicationOption } from "@/api/order"
import detailAfterSale from './components/detailAfterSale.vue';

export default {
  components: { detailAfterSale },
  name: "localLifeAOASL",
  data() {
    return {
      formData: {
        value: '',
        brand_name: '', // 品牌名称
        product_title: '', // 商品名称
        product_type: '', // 商品类型 1团购券2代金券3次卡
        order_status: '', // 使用状态
        status: "", // 售后订单状态
        application_id: '', // 采购端
        start_at: '',
        end_at: ''
      },
      orderSearchListTag: 'order_sn',
      orderSearchList: [
        { name: '订单编号', value: 'order_sn' },
        { name: '售后编号', value: 'after_sale_sn' },
        { name: '第三方订单号', value: 'third_order_sn' },
      ],
      orderPaymentTypeConditions: [],
      dateActive: "",
      dateList: [
        { name: "今", value: 0 },
        { name: "昨", value: 1 },
        { name: "近7天", value: 2 },
        { name: "近30天", value: 3 },
      ],
      orderStatus: "-100",
      //订单状态
      orderStatusConditions: [
        {
          name: "全部",
          value: '-100',
          total: null,
        },
        {
          name: "待审核",
          value: '0',
          total: 0
        },
        {
          name: "待退款",
          value: '1',
          total: 0
        },
        {
          name: "已完成",
          value: '2',
          total: 0
        },
        {
          name: "已关闭",
          value: '-2',
          total: 0
        },
        {
          name: "已驳回",
          value: '-1',
          total: 0
        },
      ],
      tableData: [],
      page: 1,
      pageSize: 10,
      total: null,
    }
  },
  filters: {
    // 商品状态
    ordertype: function (status) {
      let name = ""
      switch (status) {
        case 1:
          name = "团购卷"
          break;
        case 2:
          name = "代金卷"
          break;
        case 3:
          name = "次卡"
          break;
      }
      return name;
    },
    // 格式化订单状态
    formatStatus: function (status) {
      let name = ""
      switch (status) {
        case 0:
          name = "待支付"
          break;
        case 1:
          name = "待使用"
          break;
        case 2:
          name = "部分使用"
          break;
        case 3:
          name = "已完成"
          break;
        case -1:
          name = "已关闭"
          break;
      }
      return name;
    }
  },
  mounted() {
    getBrandAfterSalesList({ page: 1, pageSize: 10 }).then(res => {
      this.total = res.data.total
      this.tableData = res.data.list
      this.setTotalNum(res.data.statistic)
    })
    this.getApplication()
  },
  methods: {
    // 获取采购端
    getApplication() {
      getApplicationOption().then(res => {
        this.orderPaymentTypeConditions = res.data.list;
        this.orderPaymentTypeConditions.unshift(
          { app_name: "全部", id: 0 }
        );
      })
    },
    // 获取售后列表
    async searchOrder() {
      let data = this.searchlist()
      let res = await getBrandAfterSalesList(data)
      if (res.code === 0) {
        this.total = res.data.total
        this.tableData = res.data.list
        this.setTotalNum(res.data.statistic)
      }
    },
    searchlist() {
      let data = {
        page: this.page,
        pageSize: this.pageSize
      }
      if (this.formData.value && this.formData.value !== '') {
        data[this.orderSearchListTag] = this.formData.value
      }
      if (this.formData.product_title && this.formData.product_title !== '') {
        data.product_title = this.formData.product_title
      }
      if (this.formData.product_type && this.formData.product_type !== '') {
        data.product_type = parseInt(this.formData.product_type)
      }
      if (this.formData.order_status && this.formData.order_status !== '') {
        data.order_status = parseInt(this.formData.order_status)
      }
      if (this.formData.status && this.formData.status !== '') {
        if (this.formData.status !== '-100') {
          data.status = parseInt(this.formData.status)
        }
      }
      if (this.formData.application_id && this.formData.application_id !== '') {
        data.application_id = parseInt(this.formData.application_id)
      }
      if (this.formData.d1 && this.formData.d1 !== '') {
        data.start_at = formatTimeToStr(this.formData.d1.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
      }
      if (this.formData.d2 && this.formData.d2 !== '') {
        data.end_at = formatTimeToStr(this.formData.d2.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
      }
      return data
    },
    setTotalNum(data) {
      this.orderStatusConditions[1].total = data.Audit_count
      this.orderStatusConditions[2].total = data.wait_refund_count
      this.orderStatusConditions[3].total = data.completed_count
      this.orderStatusConditions[4].total = data.close_count
      this.orderStatusConditions[5].total = data.reject_count
    },
    // 重置搜索条件
    clearSearchCondition() {
      this.formData = {
        value: '',
        brand_name: '', // 品牌名称
        product_title: '', // 商品名称
        product_type: '', // 商品类型 1团购券2代金券3次卡
        order_status: '', // 使用状态
        status: "", // 售后订单状态
        application_id: '', // 采购端
        start_at: '',
        end_at: ''
      }
      this.orderSearchListTag = 'order_sn';
      this.dateActive = '';
      this.orderStatus = '-100'
      this.page = 1;
      this.pageSize = 10;
      this.searchOrder()
    },
    // 查看详情
    viewDetails(id) {
      this.$refs.detailAfterSale.info(id)
    },
    // tabs切换
    handleTabsClick() {
      this.formData.status = this.orderStatus;
      this.page = 1;
      this.pageSize = 10;
      this.searchOrder();
    },
    // 分页
    handleSizeChange(page) {
      this.pageSize = page;
      this.searchOrder();
    },
    handleCurrentChange(size) {
      this.page = size;
      this.searchOrder();
    },
    // 切换订单搜索条件清空内容
    orderchange() {
      this.formData.value = ''
    },
    // 切换日期
    handleDateTab(item) {
      this.dateActive = this.dateActive === item.value ? "" : item.value;
      const todayDate = new Date();
      switch (this.dateActive) {
        case 0:
          const dateToday1 = new Date();
          dateToday1.setHours(0);
          dateToday1.setMinutes(0);
          dateToday1.setSeconds(0);
          this.formData.d1 = dateToday1;
          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.d2 = todayDate;
          break;
        case 1:
          const dateYesterday1 = new Date();
          dateYesterday1.setTime(dateYesterday1.getTime() - 3600 * 1000 * 24 * 1);
          dateYesterday1.setHours(0);
          dateYesterday1.setMinutes(0);
          dateYesterday1.setSeconds(0);
          this.formData.d1 = dateYesterday1;

          const dateYesterday2 = new Date();
          dateYesterday2.setTime(dateYesterday2.getTime() - 3600 * 1000 * 24 * 1);
          dateYesterday2.setHours(23);
          dateYesterday2.setMinutes(59);
          dateYesterday2.setSeconds(59);
          this.formData.d2 = dateYesterday2;
          break;
        case 2:
          const date7Day1 = new Date();
          date7Day1.setTime(date7Day1.getTime() - 3600 * 1000 * 24 * 7);
          date7Day1.setHours(0);
          date7Day1.setMinutes(0);
          date7Day1.setSeconds(0);
          this.formData.d1 = date7Day1;

          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.d2 = todayDate;
          break;
        case 3:
          const date30Day1 = new Date();
          date30Day1.setTime(date30Day1.getTime() - 3600 * 1000 * 24 * 30);
          date30Day1.setHours(0);
          date30Day1.setMinutes(0);
          date30Day1.setSeconds(0);
          this.formData.d1 = date30Day1;

          todayDate.setHours(23);
          todayDate.setMinutes(59);
          todayDate.setSeconds(59);
          this.formData.d2 = todayDate;
          break;

        default:
          break;
      }
    },
  }
}
</script>
<style scoped lang="scss">
// 搜索部分
.search-term {
  .dateBtnBox {
    height: 36px;
    line-height: 36px;

    span {
      height: 38px;
      line-height: 38px;
      display: inline-block;
      margin-right: 10px;
      padding: 0 7px;
      border: 1px solid #dcdee0;
      color: #c8c9cc;
      cursor: pointer;
      box-sizing: border-box;

      &:last-child {
        margin-right: 0;
      }

      &:hover {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }

      &.is_active {
        color: #155bd4;
        border-color: #155bd4;
        background-color: #fff;
      }
    }
  }
}

/**tab**/
::v-deep .order-tabs {
  .el-tabs__header {
    margin-bottom: 0px;

    .el-tabs__item {
      background-color: #f7f8fa;

      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }

      &:hover {
        color: #303133;
      }
    }
  }
}

/***************************** 表格部分 *******************************/
.table-box {
  margin-top: 25px;
}

::v-deep .el-table.table-head {
  margin-bottom: 25px;

  &::before {
    display: none;
  }

  .el-table__header-wrapper {
    tr th {
      background-color: #f7f8fa !important;
      border-bottom: 0;
    }
  }

  .el-table__body-wrapper {
    display: none;
  }
}

::v-deep .el-table.table-cont.el-table--border {
  border: 1px solid #efefef !important;
}

::v-deep .el-table.table-cont {
  margin-bottom: 0;

  thead {
    tr th {
      background-color: #f7f8fa !important;
    }

    tr:last-child {
      display: none;
    }

    tr:first-child {
      th {
        p {
          margin-left: 20px;

          &.supplier-p {
            //background-color: rgb(74, 197, 156);
            padding: 10px;
            color: #0a3cdc;
            //border-radius: 3px;
          }

          &:first-child {
            margin-left: 0;
          }
        }
      }
    }
  }

  .el-table__body-wrapper {
    .goods-box {
      p {
        margin-left: 10px;
      }
    }

    .comm-box {
      width: 50%;
      margin: 0 auto;

      p {
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.table-foot-box {
  border: 1px solid #ebeef5;
  border-top: 0;
  margin-bottom: 20px;
  padding: 10px;

  p {
    margin-left: 10px;

    &.addr-p {
      span {
        margin-right: 5px;
      }
    }

    &:first-child {
      margin-left: 0;
    }
  }
}

.copy {
  margin-left: 10px;
  font-size: 14px;
  color: #155bd4;
  cursor: pointer;
}

.line-input-date .line-box {
  color: #1E1E1E;
  min-width: 70px
}
</style>
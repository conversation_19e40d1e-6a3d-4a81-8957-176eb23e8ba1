<template>
    <m-card>
        <el-dialog title="选择门店" :visible.sync="dialogVisible" width="48%">
            <el-form :model="storeForm" ref="form" inline>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>门店ID:</span>
                        </div>
                        <el-input
                            placeholder="请输入"
                            v-model="storeForm.id"
                            clearable
                        ></el-input>
                    </div>
                </el-form-item>
                <el-form-item label="">
                    <div class="line-input">
                        <div class="line-box">
                            <span>门店名称:</span>
                        </div>
                        <el-input
                            placeholder="请输入"
                            v-model="storeForm.name"
                            clearable
                        ></el-input>
                    </div>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="searchForm">
                        搜索
                    </el-button>
                    <el-button type="text" @click="resetForm">
                        重置搜索条件
                    </el-button>
                </el-form-item>
            </el-form>
            <el-table
                :data="storeList"
                @selection-change="handleSelectionChange"
            >
                <el-table-column type="selection" width="110"></el-table-column>
                <el-table-column
                    label="ID"
                    prop="id"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    label="门店信息"
                    prop="name"
                    align="center"
                ></el-table-column>
            </el-table>
            <el-pagination
                background
                :current-page="page"
                :page-size="pageSize"
                :page-sizes="[10, 30, 50, 100]"
                :style="{
                    display: 'flex',
                    justifyContent: 'flex-end',
                    marginRight: '20px',
                }"
                :total="total"
                @current-change="handleCurrentChange"
                @size-change="handleSizeChange"
                layout="total, sizes, prev, pager, next, jumper"
            ></el-pagination>
            <span slot="footer">
                <el-button type="primary" @click="confirm"> 确 定 </el-button>
                <el-button @click="dialogVisible = false"> 取 消 </el-button>
            </span>
        </el-dialog>
    </m-card>
</template>

<script>
import { storelist } from '@/api/localLife';

export default {
    name: 'storeDialog',

    data() {
        return {
            dialogVisible: false, // 弹出层显示
            storeForm: {
                id: null, // ID
                name: '', // 名称
            },
            storeList: [], // 门店列表
            chooseStoreList: [], // 选中门店列表
            page: 1,
            pageSize: 10,
            total: 0,
        };
    },

    mounted() {},

    methods: {
        // 获取门店列表
        async getStorelist() {
            const params = {
                page: this.page,
                pageSize: this.pageSize,
                id: this.storeForm.id ? parseInt(this.storeForm.id) : null,
                name: this.storeForm.name,
            };
            const res = await storelist(params);
            if (res.code === 0) {
                this.storeList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        searchForm() {
            this.getStorelist();
        },
        // 重置搜索
        resetForm() {
            this.storeForm.id = null;
            this.storeForm.name = '';
        },
        // 选择门店
        handleSelectionChange(row) {
            this.chooseStoreList = row;
            // this.chooseStoreList = row.map((item) => ({ store_id: item.id }));
        },
        // 确认选择
        confirm() {
            if (this.chooseStoreList.length === 0) {
                this.$message.error('请选择门店');
                return;
            }
            this.dialogVisible = false;
            this.$emit('choose-list', this.chooseStoreList);
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getStorelist();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.getStorelist();
        },
    },
};
</script>

import {
    getCategoryChildList,
    createCategory,
    deleteCategory,
    moveCategory,
    displayByIds,
} from "@/api/localLife"
import AddClassify from "../add"

export default {
    name: "detail",
    components: {AddClassify},
    data() {
        return {
            categoryName: "",
            level2: {
                categoryName: "二级类目",
                drawer: false,
                page: 1,
                pageSize: 10,
                total: 0,
                tableData: [],
                rootIsShow: false,
                parent_id: 0,
                level: 2,
                searchForm: {},
                searchInputWidth: "160px",
                selectTables: []
            },
            level3: {
                categoryName: "三级子类目",
                innerDrawer: false,
                page: 1,
                pageSize: 10,
                total: 0,
                tableData: [],
                rootIsShow: false,
                parent_id: 0,
                level: 3,
                searchForm: {},
                searchInputWidth: "160px",
                selectTables: []
            },
        }
    },
    filters: {
        formatStatus: function (status) {
            return status ? "启用" : "关闭"
        },
    },
    methods: {
        // 上移下移
        async updateSort(type, row) {
            let params = {
                id: row.id,
                parent_id: row.parent_id,
                move_operate: type
            }
            let res = await moveCategory(params)
            if (res.code === 0) {
                this.$message.success("操作成功")
                this.fetch()
            }
        },
        handleSelectionChange2(val) {
            this.level2.selectTables = val;
        },
        handleSelectionChange3(val) {
            this.level3.selectTables = val;
        },
        // search input 获取焦点
        searchInputFocus(level = 2) {
            if (level === 2) {
                this.level2.searchInputWidth = "300px";
            } else if (level === 3) {
                this.level3.searchInputWidth = "300px";
            }
        },
        // search input 失去焦点
        searchInputBlur(level = 2) {
            if (level === 2) {
                this.level2.searchInputWidth = "160px";
            } else if (level === 3) {
                this.level3.searchInputWidth = "160px";
            }
        },
        // 打开添加商品分类
        openAddClassify() {
            let that = this;
            if (that.level3.innerDrawer) {
                this.$refs.addClassify.intent(0, that.level3.parent_id, that.level3.level);
                return;
            }

            this.$refs.addClassify.intent(0, that.level2.parent_id, that.level2.level);

        },

        //回调刷新
        fetch() {
            let that = this;
            if (that.level3.innerDrawer) {
                this.level3.rootIsShow = false;
                this.fetchClassifyLevel3(this.level3.parent_id);
                return;
            }

            this.level2.rootIsShow = false;
            this.fetchClassifyLevel2(this.level2.parent_id);
        },

        // 打开三级分类
        openDetail3(row) {
            this.level3.innerDrawer = true;
            this.level3.parent_id = row.id;
            this.level3.categoryName = row.name;
            this.level3.level = 2;
            this.fetchClassifyLevel3(row.id);
        },
        handleClose1() {
            this.level2.drawer = false;
        },
        handleClose2() {
            this.level3.innerDrawer = false;
        },
        // 获取二级分类
        fetchClassifyLevel2(pid) {
            let that = this;
            that.level2.parent_id = pid
            that.level2.level = 2;

            let para = {
                page: this.level2.page,
                pageSize: this.level2.pageSize,
                parent_id: pid,
                name: this.level2.searchForm.title
            }

            getCategoryChildList(para).then(res => {
                if (res.code === 0) {
                    this.level2.total = res.data.total
                    this.level2.tableData = res.data.list
                } else {
                    this.level2.total = 0
                    this.level2.tableData = []
                }
            })
        },

        // 获取三级分类
        fetchClassifyLevel3(pid) {
            let that = this;
            that.level3.parent_id = pid
            that.level3.level = 3;

            let para = {
                page: this.level3.page,
                pageSize: this.level3.pageSize,
                parent_id: pid,
                name: this.level3.searchForm.title
            }

            getCategoryChildList(para).then(res => {
                if (res.code === 0) {
                    this.level3.total = res.data.total;
                    this.level3.tableData = res.data.list;
                } else {
                    this.level3.total = 0;
                    this.level3.tableData = [];
                }
            })
        },

        handleCurrentChange1(page) {
            this.level2.page = page
            this.fetch()
        },
        handleSizeChange1(size) {
            this.level2.pageSize = size
            this.fetch()
        },
        handleCurrentChange2(page) {
            this.level3.page = page
            this.fetch()
        },
        handleSizeChange2(size) {
            this.level3.pageSize = size
            this.fetch()
        },


        //快速添加分类
        quickCreateCategory() {
            let that = this;
            if (!that.categoryName) {
                return;
            }
            let para = {
                "parent_id": that.level2.parent_id,
                "level": that.level2.level,
                "name": that.categoryName
            }

            if (that.level3.innerDrawer) {
                para = {
                    "parent_id": that.level3.parent_id,
                    "level": that.level3.level,
                    "name": that.categoryName
                }
            }

            createCategory(para).then(res => {
                if (res.code === 0) {
                    that.fetch();
                    that.$message.success(res.msg);
                    that.categoryName = "";
                } else {
                    that.$message.error(res.msg);
                }
            });
        },

        //删除分类
        deleteCategoryDialog(item) {
            let that = this;
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                that.deleteCategoryById(item);
            });
        },
        deleteCategoryById(item) {
            let that = this;
            let para = {
                "id": item.id
            }
            deleteCategory(para).then(res => {
                if (res.code === 0) {
                    that.fetch();
                    that.$message.success(res.msg);
                } else {
                    that.$message.error(res.msg);
                }
            });
        },

        //编辑
        editCategoryById(item) {
            let that = this;
            if (that.level3.innerDrawer) {
                this.$refs.addClassify.intent(item.id, that.level3.parent_id, that.level3.level);
                return;
            }
            this.$refs.addClassify.intent(item.id, that.level2.parent_id, that.level2.level);
        },

        //搜索
        search() {
            let that = this;
            if (that.level3.innerDrawer) {
                this.level3.rootIsShow = false;
                this.level3.tableData = [];
                this.level3.page = 1;
                this.level3.pageSize = 10;
                this.fetchClassifyLevel3(this.level3.parent_id);
                return;
            }

            this.level2.rootIsShow = false;
            this.level2.tableData = [];
            this.level2.page = 1;
            this.level2.pageSize = 10;
            this.fetchClassifyLevel2(this.level2.parent_id);

        },

        //状态修改
        upCategoryDisplays(item, value) {
            let para = {
                "ids": [item.id],
                "value": value
            }
            displayByIds(para).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.fetch();
                }
            });
        },

        //批量处理状态
        batchUpCategoryDisplays2(value) {
            if (this.level2.selectTables.length == 0) {
                this.$message.warning("请选择要处理的数据");
                return;
            }

            let ids = [];
            this.level2.selectTables.forEach(item => {
                ids.push(item.id);
            })
            let para = {
                "ids": ids,
                "value": value
            }
            displayByIds(para).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.fetch();
                }
            });
        },

        //批量处理状态
        batchUpCategoryDisplays3(value) {
            if (this.level3.selectTables.length == 0) {
                this.$message.warning("请选择要处理的数据");
                return;
            }

            let ids = [];
            this.level3.selectTables.forEach(item => {
                ids.push(item.id);
            })
            let para = {
                "ids": ids,
                "value": value
            }
            displayByIds(para).then(r => {
                if (r.code === 0) {
                    this.$message.success(r.msg);
                    this.fetch();
                }
            });
        }

    }
}
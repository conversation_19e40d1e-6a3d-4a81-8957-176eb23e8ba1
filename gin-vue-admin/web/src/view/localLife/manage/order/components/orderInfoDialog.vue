<template>
    <div>
        <el-drawer title="订单详情" :visible="isShow" :close-on-press-escape="false" :append-to-body="true"
            :wrapperClosable="false" :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
            <div class="f fac">
                <p>订单编号: {{ orderData.order_sn }}</p>
                <p class="ml10">第三方订单编号: {{ orderData.third_order_sn }}</p>
                <p class="ml10">下单时间: {{ orderData.created_at | formatDate }}</p>
            </div>
            <!-- 订单状态部分 -->
            <el-row class="order-status-box mt25">
                <el-col :span="9" class="left">
                    <h1 class="order-status-title">{{ orderData.status | formatStatus }}</h1>
                    <div>
                        <el-button type="text" v-for="btn in orderData.button" :key="btn.id"
                            @click="buttonfun(btn, orderData.id)">{{ btn.title }}
                        </el-button>
                    </div>
                    <el-button type="text" class="remark-btn" @click="openDialog">备注</el-button>
                </el-col>
                <el-col :span="15" class="right f fac">
                    <el-steps :active="status" align-center finish-status="success" class="f1">
                        <el-step title="买家下单" :description="orderData.created_at | formatDate"></el-step>
                        <el-step title="买家付款" :description="orderData.paid_at | formatDate"></el-step>
                        <el-step v-if="orderData.status === 3" title="交易成功"
                            :description="orderData.received_at | formatDate"></el-step>
                        <el-step v-else-if="orderData.status === 2" title="完成"
                            :description="orderData.sent_at | formatDate"></el-step>
                        <el-step v-else title="待使用" :description="orderData.sent_at | formatDate"></el-step>
                    </el-steps>
                </el-col>
            </el-row>
            <!-- 收货人信息,配送信息,付款信息,买家信息 -->
            <div class="synthesize-box mt25 f">
                <div class="f1">
                    <p class="head-p">联系人信息</p>
                    <ul>
                        <li>预留手机号: {{ orderData.user_mobile }}</li>
                    </ul>
                </div>
                <div class="f1">
                    <p class="head-p">买家信息</p>
                    <ul>
                        <li>买家:
                            <el-button type="text" @click="toUserInfo(orderData.user.id)">
                                {{ orderData.user.nickname }}
                            </el-button>
                            <el-button type="text" v-if="orderData.status !== 0" @click="openPaymentLog">查看支付记录
                            </el-button>
                        </li>
                    </ul>
                </div>
            </div>
            <!--售后商品-->
            <template>
                <p class="mt25">售后商品</p>
                <el-table :data="afterSalesList" style="margin-top: 10px">
                    <el-table-column label="商品">
                        <template slot-scope="scope">
                            <div class="f fa">
                                <m-image :src="scope.row.image_url"
                                    style="width: 69px;min-width: 69px; height: 69px;min-height: 69px"></m-image>
                                <div class="ml10 table-goods-imgName">
                                    <p class="hiddenText1">
                                        <el-button type="text">
                                            {{ scope.row.title }}
                                        </el-button>
                                    </p>
                                    <p class="hiddenText2">规格: {{ scope.row.sku_title }}</p>
                                    <p>数量: {{ scope.row.qty }}{{ scope.row.unit }}</p>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="使用状态" align="center">
                        <template slot-scope="scope">
                            <span>{{ scope.row.status_name }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="订单金额(元)" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.order_amount | formatF2Y }}
                        </template>
                    </el-table-column>
                    <el-table-column label="退款金额(元)" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.amount | formatF2Y }}
                        </template>
                    </el-table-column>
                    <el-table-column label="申请时间" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.created_at | formatDate }}
                        </template>
                    </el-table-column>
                    <el-table-column label="售后状态" align="center">
                        <template slot-scope="scope">
                            {{ scope.row.status | formatStatus3 }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="right">
                        <template slot-scope="scope">
                            <el-button type="text" @click="openAfterDetail(scope.row)">查看详情</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </template>
            <!-- 商品部分 -->
            <p class="mt25">订单商品</p>
            <el-table :data="orderData.order_items" style="margin-top: 10px">
                <el-table-column label="商品">
                    <template slot-scope="scope">
                        <div class="f fa">
                            <m-image :src="scope.row.image_url"
                                style="width: 69px;min-width: 69px; height: 69px;min-height: 69px"></m-image>
                            <div class="ml10 table-goods-imgName">
                                <p class="hiddenText1">
                                    <el-button type="text">
                                        {{ scope.row.title }}
                                    </el-button>
                                </p>
                                <p class="hiddenText2">规格: {{ scope.row.sku_title }}</p>
                                <p>数量: {{ scope.row.qty }}{{ scope.row.unit }}</p>
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="单价(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.amount / scope.row.qty | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="优惠(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.discount_amount ? scope.row.discount_amount : 0 | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="供货金额(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.supply_amount ? scope.row.supply_amount : 0 | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="小计(元)" align="center">
                    <template slot-scope="scope">
                        {{ scope.row.amount | formatF2Y }}
                    </template>
                </el-table-column>
                <el-table-column label="售后状态" align="center">
                    <template slot-scope="scope">
                        <el-button v-if="can_refund" size="small" @click="refund(can_refund_Url,scope.row.id)">主动退款
                        </el-button>
                    </template>
                </el-table-column>
                <el-table-column label="" align="right">
                    <template slot-scope="scope">
                    </template>
                </el-table-column>
            </el-table>
            <!-- 统计部分 -->
            <div class="statistics-box f fjend mt25">
                <div class="statistics">
                    <ul>
                        <li>
                            <span class="title-span text-right">商品总价:</span><span class="sum-span text-right">￥{{
                                orderData.item_amount | formatF2Y }}</span>
                        </li>
                        <li>
                            <span class="title-span text-right">技术服务费:</span><span class="sum-span text-right">￥{{
                                orderData.technical_services_fee | formatF2Y }}</span>
                        </li>
                    </ul>
                    <p class="sum-total">
                        <span class="title-span text-right">实收金额:</span>
                        <span class="sum-span text-right color-red">￥{{ orderData.amount | formatF2Y }}</span>
                    </p>
                </div>
            </div>
        </el-drawer>
        <!-- 确认使用弹窗 -->
        <el-dialog :before-close="affirmHandleDialogClose" :visible="affirmShow" title="确认使用" top="40vh" width="700px">
            <el-form :model="affirmData" ref="affirmData" label-width="130px">
                <el-form-item label="核销门店:" prop="store_id">
                    <el-select class="w400" v-model="affirmData.store_id" clearable placeholder="请选择核销门店">
                        <el-option v-for="item in shoplist" :key="item.id" :label="item.name"
                            :value="item.id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="核销次数:" prop="count">
                    <el-input class="w400" v-model="affirmData.count" placeholder="请输入"></el-input>
                </el-form-item>
            </el-form>
            <div class="dialog-footer" slot="footer">
                <el-button @click="affirmfun" type="primary">确 定</el-button>
                <el-button @click="affirmHandleDialogClose">取 消</el-button>
            </div>
        </el-dialog>
        <!-- 备注弹窗 -->
        <el-dialog :before-close="handleDialogClose" :visible="noteIsShow" title="备注" top="40vh" width="600px">
            <el-input :rows="6" placeholder="请输入内容" type="textarea" v-model="dialogForm.note">
            </el-input>
            <div class="dialog-footer" slot="footer">
                <el-button @click="confirmNote" type="primary">确 定</el-button>
                <el-button @click="handleDialogClose">取 消</el-button>
            </div>
        </el-dialog>
        <!-- 用户信息 -->
        <user-detail ref="userDetail"></user-detail>
        <!-- 支付记录 -->
        <paymentLog ref="paymentLog"></paymentLog>
        <!-- 售后详情 -->
        <detailAfterSale ref="detailAfterSale" @handleClose="orderInfo(id)"></detailAfterSale>
    </div>
</template>
<script>
import {
    orderInfo,
    getOrder,
    getStoreOptionList,
    orderNote,
    getOrderStore
} from '@/api/localLife';
import userDetail from "../../../../member/components/userDetail"
import paymentLog from "./paymentLog"
import detailAfterSale from "../../afterSale/components/detailAfterSale.vue"


export default {
    components: { userDetail, paymentLog,detailAfterSale },
    data() {
        return {
            isShow: false,
            orderData: {
                user: {}
            },
            id: null,
            afterSalesList: [], // 售后
            status: null,

            // 确认使用
            affirmDataUrl: '',
            affirmShow: false,
            affirmData: {
                order_id: null, // 订单id
                store_id: null,  // 核销id
                count: null // 核销次数
            }, // 确认使用数据
            shoplist: [], // 订单列表

            noteIsShow: false,
            dialogForm: {  //备注字段
                id: null,
                note: ""
            },
            can_refund: false,
            can_refund_Url: ''
        }
    },
    filters: {
        formatStatus: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "商品已拍下,等待买家付款"
                    break;
                case 1:
                    name = "买家已付款,等待买家核销"
                    break;
                case 2:
                    name = "买家已付款,等待买家核销"
                    break;
                case 3:
                    name = "卖家已核销,订单已完成"
                    break;
            }
            return name;
        },
        formatStatus2: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "未发货"
                    break;
                case 1:
                    name = "发货中"
                    break;
                case 2:
                    name = "已发货"
                    break;
            }
            return name;
        },
        // 格式化售后订单状态
        formatStatus3: function (status) {
            let name = ""
            switch (status) {
                case 0:
                    name = "待审核"
                    break;
                case 1:
                    name = "待退款"
                    break;
                case 2:
                    name = "已完成"
                    break;
                case -1:
                    name = "已驳回"
                    break;
                case -2:
                    name = "已关闭"
                    break;
            }
            return name;
        }
    },
    methods: {
        info(id) {
            this.isShow = true
            this.id = id
            this.orderInfo(id)
        },
        handleClose() {
            this.isShow = false;
            this.id = null;
            this.orderData = {
                user: {}
            };
            this.afterSalesList = []; // 售后
            this.status = null;
            this.can_refund = false
            this.$emit('handleClose')
        },
        async orderInfo(id) {
            let res = await orderInfo({ id })
            if (res.code === 0) {
                this.orderData = res.data.order
                res.data.order.button.forEach(e => {
                    if (e.title === '全额退款') {
                        this.can_refund = true;
                        this.can_refund_Url = e.url
                    }
                })
                switch (this.orderData.status) {
                    case 0:
                        this.status = 1
                        break;
                    case 1:
                        this.status = 2
                        break;
                    case 2:
                        this.status = 2
                        break;
                    case 3:
                        this.status = 3
                        break;
                    case -1:
                        this.status = -1
                }
                // 获取售后数据
                this.setAfterSalesList()
            }
        },
        // 售后数据
        setAfterSalesList() {
            const { order_items } = this.orderData
            this.afterSalesList = []
            if (order_items && order_items.length > 0) {
                order_items.forEach(item => {
                    if (item.localLifeAfterSale && item.localLifeAfterSale.id) {
                        this.afterSalesList.push({
                            status: item.localLifeAfterSale.status,
                            title: item.title,
                            image_url: item.image_url,
                            sku_title: item.sku_title,
                            qty: item.qty,
                            unit: item.unit,
                            order_amount: item.amount,
                            status_name: this.orderData.status_name,
                            created_at: item.localLifeAfterSale.created_at,
                            order_item_id: item.localLifeAfterSale.order_item_id,
                            id: item.localLifeAfterSale.id,
                            amount: item.localLifeAfterSale.amount
                        })
                    }
                })
            }
        },
        // 订单操作按钮
        buttonfun(item, id) {
            switch (item.title) {
                case '确认付款':
                    this.pay(item.url, id)
                    break;
                case '关闭订单':
                    this.close(item.url, id)
                    break;
                case '确认使用':
                    this.confirmedUse(item.url, id)
                    break;
                case '全额退款':
                    this.refund(item.url, id)
                    break;
            }
        },
        // 打开确认使用弹窗
        async confirmedUse(url, id) {
            this.affirmData.order_id = id
            this.affirmDataUrl = url
            this.affirmShow = true
            let res = await getOrderStore({id})
            if (res.code === 0) {
                this.shoplist = res.data
            }
        },
        // 关闭确认使用弹窗
        affirmHandleDialogClose() {
            this.affirmShow = false
            this.affirmDataUrl = ''
            this.affirmData = {
                order_id: null, // 订单id
                store_id: null,  // 核销id
                count: null // 核销次数
            }
        },
        // 确认使用
        async affirmfun() {
            if (this.affirmData.count === null) {
                this.$message.error('核销次数不能为空')
                return
            }
            if (this.affirmData.store_id === null) {
                this.$message.error('请选择核销门店')
                return
            }
            this.affirmData.count = parseInt(this.affirmData.count)
            let res = await getOrder(this.affirmDataUrl, this.affirmData)
            if (res.code === 0) {
                this.$message.success(res.msg)
                this.orderInfo(this.id)
                this.affirmHandleDialogClose()
            }
        },
        // 关闭订单
        close(url, order_id) {
            this.$confirm("确认要关闭订单吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await getOrder(url, { order_id })
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.orderInfo(this.id)
                }
            })
        },
        // 付款
        pay(url, order_id) {
            this.$confirm("确认要支付吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await getOrder(url, { order_id })
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.orderInfo(this.id)
                }
            })
        },
        // 退款
        refund(url, order_id) {
            this.$confirm("确认要退款吗?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(async () => {
                let res = await getOrder(url, { order_id })
                if (res.code === 0) {
                    this.$message.success(res.msg)
                    this.orderInfo(this.id)
                }
            })
        },
        // 打开备注
        openDialog() {
            this.noteIsShow = true
            this.dialogForm.id = this.orderData.id
            this.dialogForm.note = this.orderData.note
        },
        // 关闭备注弹窗
        handleDialogClose() {
            this.noteIsShow = false
            this.dialogForm = {
                id: null,
                note: ""
            }
        },
        // 确认备注
        async confirmNote() {
            let res = await orderNote(this.dialogForm)
            if (res.code === 0) {
                this.$message.success(res.msg)
                this.handleDialogClose()
                this.orderInfo(this.orderData.id)
            }
        },
        // 查看会员用户信息
        toUserInfo(id) {
            this.$refs.userDetail.isShow = true;
            this.$refs.userDetail.is_back = 1;
            this.$nextTick(() => {
                this.$refs.userDetail.getUserDetail(id);
            });
        },
        // 查看支付记录
        openPaymentLog() {
            this.$refs.paymentLog.getData(this.orderData.id)
        },
        // 查看详情
        openAfterDetail(row){
            this.$refs.detailAfterSale.info(row.id)
        },
    }
}
</script>
<style scoped lang="scss">
@import "@/style/base.scss";

// 订单状态部分
.order-status-box {
    border: 1px solid #f0f2f5;
    padding: 0;
    height: 150px;

    .left {
        border-right: 1px solid #f0f2f5;
        padding: 15px 10px;
        height: 100%;

        h1.order-status-title {
            font-size: 18px;
        }

        p.remind-p {
            margin-top: 20px;
            line-height: 20px;
            color: #72767b;

            span.remind-span {
                color: #e6a23c;
            }
        }

        /* .order-status-marked-p {
            margin-top: 20px;
            line-height: 20px;
            color: #72767b;
        } */
        .remark-btn {
            padding: 0;
        }
    }

    .right {
        height: 100%;

        ::v-deep .el-steps {
            .el-step {
                .el-step__head {
                    &.is-success {
                        color: $primary-color;
                        border-color: $primary-color;
                    }

                    &.is-process {
                        color: #c0c4cc;
                        border-color: #c0c4cc;
                    }
                }

                .el-step__main {
                    .el-step__title {
                        font-size: 14px;
                        color: #000000;

                        &.is-wait,
                        &.is-process {
                            color: #c0c4cc;
                        }
                    }

                    .el-step__description {
                        padding: 0;
                        color: #72767b;

                        &.is-wait,
                        &.is-process {
                            color: #c0c4cc;
                        }
                    }
                }
            }
        }
    }
}

.order-status-remind {
    border: 1px solid #f0f2f5;
    border-top: 0;
    padding: 15px 10px;

    p.remind-p {
        color: #72767b;

        span.remind-span {
            color: #e6a23c;
        }
    }
}

// 包裹部分
.parcel-tabs {
    .tab-item {
        border: 1px solid #f0f2f5;
        padding: 20px;

        .el-row {
            padding: 0;

            ul li {
                line-height: 25px;
                color: #72767b;

                span {
                    display: inline-block;
                }

                span.title-span {
                    min-width: 70px;
                }

                .goods-info {
                    margin-top: 10px;
                    line-height: 20px;

                    p {
                        font-size: 12px;
                        width: 70px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        color: #a8a9ab;
                    }
                }
            }
        }
    }
}

// 收货人信息,配送信息,付款信息,买家信息
.synthesize-box {
    background-color: #fafafa;
    padding: 15px 10px;
    padding-top: 10px;

    p.head-p {
        height: 36px;
        line-height: 36px;
    }

    ul li {
        color: #72767b;
        line-height: 25px;
    }
}

.table-goods-imgName {
    width: calc(100% - 79px);

    .el-button {
        padding: 0;
        margin: 0;
    }
}

// 发票信息
.invoice-box {
    display: flex;
    justify-content: space-between;

    .left-invoice-box {
        flex: 1;
        border: 1px solid #d7dae2;
        text-align: left;
        padding: 15px 10px;
        color: #a8a9ab;

        .el-empty {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        p {
            margin-bottom: 10px;
        }
    }

    .right-upload {
        flex: 1;
        margin-left: 20px;
        padding: 15px 10px;
        border: 1px solid #d7dae2;
        text-align: center;

        .title {
            font-weight: bold;
            font-size: 14px;
            text-align: left;
        }

        ::v-deep .img-uploader {
            .el-upload {
                border: 1px dashed #d9d9d9;
                border-radius: 6px;
                width: 178px;
                height: 178px;

                .img-uploader-icon {
                    font-size: 28px;
                    color: #8c939d;

                    line-height: 178px;
                    text-align: center;
                }
            }
        }

        .save-invoice {
            margin-top: 10px;
            border-radius: 5px;
        }
    }
}

// 统计金额部分
.statistics-box {
    .statistics {
        ul li {
            line-height: 20px;
            color: #72767b;

            span {
                display: inline-block;
            }

            span.title-span {
                width: 110px;
            }

            span.sum-span {
                width: 120px;
            }
        }

        text-right {
            text-align: right;
        }

        .sum-total {
            margin-top: 10px;

            span {
                display: inline-block;
            }

            span.title-span {
                width: 110px;
            }

            span.sum-span {
                width: 120px;
            }
        }
    }
}

.w400 {
    width: 400px;
}
</style>
<div>
    <el-drawer title="包裹列表" :visible="isShow" :close-on-press-escape="false" :wrapperClosable="false"
               :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
        <m-card>
            <el-tabs v-model="activeName" type="card" v-if="order_express.length > 0">
                <el-tab-pane :label="`包裹${index+1}`" :name="item.id+''" v-for="(item,index) in order_express"
                             :key="item.id">
                    <el-table :data="item.order_items">
                        <el-table-column label="商品图片" align="center">
                            <template slot-scope="scope">
                                <m-image style="width: 60px;height: 60px;" :src="scope.row.image_url"></m-image>
                            </template>
                        </el-table-column>
                        <el-table-column label="商品名称" prop="title"></el-table-column>
                        <el-table-column label="物流单号" align="center">
                            <template slot-scope="scope">
                                {{ item.express_no }}
                            </template>
                        </el-table-column>
                        <el-table-column label="物流公司" align="center">
                            <template slot-scope="scope">
                                {{ item.company_name }}
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button type="text" @click="openDialog(item)">修改物流</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-tab-pane>
            </el-tabs>
        </m-card>
    </el-drawer>
    <el-dialog
            title="修改物流"
            :visible="dialogIsShow"
            width="40%"
            :before-close="handleDialogClose">
        <el-form :model="logisticsFormData" ref="form" :rules="rules" label-width="90px">
            <el-form-item label="物流公司:" prop="company_code">
                <el-select v-model="logisticsFormData.company_code" class="input200" filterable clearable>
                    <el-option :value="item.code" :label="item.name" :key="item.name" v-for="item in expressList">
                        {{ item.name }}
                    </el-option>
                </el-select>
                <el-button type="text" @click="getExpress()" class="ml10">
                    刷新
                </el-button>
            </el-form-item>

            <el-form-item label="物流单号:" prop="express_no">
                <el-input v-model="logisticsFormData.express_no" placeholder="输入真实有效的物流单号" class="input200"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="confirm">确 定</el-button>
            <el-button @click="handleDialogClose">取 消</el-button>
        </div>
    </el-dialog>
</div>
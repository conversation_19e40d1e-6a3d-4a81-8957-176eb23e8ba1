<template>
  <el-dialog
      title="修改记录"
      :visible="isShow"
      width="800px"
      :before-close="handleClose">
    <el-table
        :data="tableData"
        style="width: 100%">
      <el-table-column
          align="center"
          label="修改时间">
        <template slot-scope="scope">
          {{scope.row.created_at | formatDate}}
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="修改前收货信息">
        <template slot-scope="scope">
         <p>{{scope.row.old_realname}}</p>
          <p>{{scope.row.old_mobile}}</p>
          <p>{{scope.row.old_province}}{{scope.row.old_city}}{{scope.row.old_county}}{{scope.row.old_town}}{{scope.row.old_detail}}</p>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="修改后收货信息">
        <template slot-scope="scope">
          <p>{{scope.row.realname}}</p>
          <p>{{scope.row.mobile}}</p>
          <p>{{scope.row.province}}{{scope.row.city}}{{scope.row.county}}{{scope.row.town}}{{scope.row.detail}}</p>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>

import {getShippingAddressLog} from "@/api/order"
export default {
  data(){
  return{
    isShow:false,
    tableData:[]
  }
  },
  methods:{
    getShippingAddressLog(order_id){
      getShippingAddressLog({order_id:order_id}).then(res=>{
      if(res.code===0){
        this.tableData=res.data
      }
      })
    },
    handleClose() {
      try {
      } catch {
      } finally {
        this.isShow = false
        this.tableData=[]
      }
    },
  }
}
</script>

<style scoped>

</style>
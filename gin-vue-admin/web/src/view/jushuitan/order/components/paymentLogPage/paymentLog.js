import { getPaymentRecord } from "@/api/order"
export default {
    data() {
        return {
            isShow: false,
            tableData: []
        }
    },
    filters: {
        //0待支付，1已支付，-1已失效，-2已退款
        formatPayStatus: function (status) {
            let s = "";
            switch (status) {
                case 0:
                    s = "待支付"
                    break;
                case 1:
                    s = "已支付"
                    break;
                case -1:
                    s = "已失效"
                    break;
                case -2:
                    s = "已退款"
                    break;
            }
            return s
        }
    },
    methods: {
        getData(oid) {
            getPaymentRecord({ order_id: oid }).then(res => {
                if (res.code === 0) {
                    this.tableData.push(res.data)
                }
            })
        },
        handleClose() {
            this.isShow = false
            this.tableData = []
        }
    }
};

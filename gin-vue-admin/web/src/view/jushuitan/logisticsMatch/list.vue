<template>
    <m-card>
        <el-button type="primary" @click="openDialog">新增</el-button>
        <el-table class="mt25" :data="tableData">
            <el-table-column label="本地" prop="self_express_code"></el-table-column>
            <el-table-column label="本地名称" prop="self_express_name"></el-table-column>
            <el-table-column label="聚水潭" prop="jushuitan_express_code"></el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" class="color-red" @click="del(scope.row.id)">
                        删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100, 200]"
            :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px' }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        ></el-pagination>
        <add-dialog ref="addDialog" @reLoad="getTableData"></add-dialog>
    </m-card>
</template>
<script>
import AddDialog from './components/addDialog.vue';
import infoList from '@/mixins/infoList';
import { getExpressMatchList, deleteExpressMatch } from '@/api/jushuitan';
export default {
    name: 'jushuitanLogisticsMatchList',
    components: { AddDialog },
    mixins: [infoList],
    data() {
        return {
            listApi: getExpressMatchList,
        };
    },
    mounted() {
        this.getTableData();
    },
    methods: {
        openDialog() {
            this.$refs.addDialog.init();
        },
        del(id) {
            this.$fn.confirm(async () => {
                const { code, msg } = await deleteExpressMatch({ id });
                if (code === 0) {
                    this.$message.success(msg);
                    this.getTableData()
                }
            }, '确定删除?');
        },
    },
};
</script>
<style lang="scss">
@import '@/style/base.scss';
</style>

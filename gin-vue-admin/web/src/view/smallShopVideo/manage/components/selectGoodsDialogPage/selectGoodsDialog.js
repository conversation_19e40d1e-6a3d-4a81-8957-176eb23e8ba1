import { getProductList } from "@/api/goods";
export default {
    data() {
        return {
            loading: false,
            // dialog是否显示
            isShow: false,
            // 搜索条件
            searchForm: {
                title: "",
            },
            // 商品列表数据
            goodsList: [],
            // 选中的商品数据
            checkGoods: {},
            // 已选中的goods数据列表
            selectGoodsList: [],
            // 分页信息
            page: 1,
            pageSize: 10,
            total: 0,
        };
    },
    methods: {
        // 获取商品列表
        fetch() {
            this.loading = true;
            getProductList({
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchForm,
            }).then((res) => {
                this.loading = false;
                if (res.code === 0) {
                    this.goodsList = res.data.list;
                    this.page = res.data.page;
                    this.pageSize = res.data.pageSize;
                    this.total = res.data.total;
                } else {
                    this.$message.error(res.msg);
                    this.page = 1;
                    this.pageSize = 10;
                    this.total = 0;
                }
            });
        },
        // 关闭
        handleClose() {
            this.isShow = false;
            this.checkGoods = {};
            this.goodsList = [];
            this.searchForm.title = "";
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
        },
        // 确定
        confirm() {
            let flg = 1;
            if (this.selectGoodsList.length > 0) {
                for (let i = 0; i < this.selectGoodsList.length; i++) {
                    if (this.selectGoodsList[i].id === this.checkGoods.id || this.selectGoodsList[i].product_id === this.checkGoods.id) {
                        // 停止循环弹出重复提示
                        this.$message.error("已选择该商品,请勿重复选择!");
                        flg = 2;
                        break;
                    }
                }
            }
            if (flg === 1) {
                this.$emit("addGoodsList", this.checkGoods);
                this.handleClose();
            }
        },
        // 查询
        search() {
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.goodsList = [];
            this.fetch();
        },
        // 选中
        checkCurrentChange(val) {
            this.checkGoods = val;
            this.confirm()
        },
        // 分页部分
        handleCurrentChange(page) {
            this.page = page;
            this.fetch();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.fetch();
        },
    },
};
<template>
  <div>
    <el-button type="primary" @click="onCreate">新增</el-button>
    <el-form class="search-term mt25" label-width="135px" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >中台快递</span>
              </div>
              <el-select v-model="searchForm.name" clearable filterable class="w100">
                <template v-for="item in shippingList">
                  <el-option :label="item.name" :value="item.name" :key="item.name">
                  </el-option>
                </template>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >云仓快递</span>
              </div>
              <el-select v-model="searchForm.wx_name" clearable filterable class="w100">
                <template v-for="item in deliverList">
                  <el-option :label="item.delivery_name" :value="item.delivery_id" :key="item.delivery_id"></el-option>
                </template>
              </el-select>
          </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="onSearch">搜索</el-button>
        <el-button type="danger" @click="onSend" plain>重新发货</el-button>
        <el-button type="text" @click="onClear">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="matchingList" style="margin-top: 30px">
      <el-table-column prop="id" label="ID" align="center"></el-table-column>
      <el-table-column prop="code" label="中台code" align="center"></el-table-column>
      <el-table-column prop="name" label="中台快递名称" align="center"></el-table-column>
      <el-table-column prop="wx_code" label="云仓code" align="center"></el-table-column>
      <el-table-column prop="wx_name" label="云仓快递名称" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template v-slot="scope">
          <el-button type="text"  @click="onEdit(scope.row)">编辑</el-button>
          <el-button type="text" slot="reference" class="btn-del-text" @click="onDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <ExpressMatchDialog
        :visible.sync="dialog.visible"
        :pageStatus="dialog.pageStatus"
        :propRow="dialog.propRow"
        :deliverList="deliverList"
        :shippingList="shippingList"
        @submitted="onSubmitted"
    />
    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{ display: 'flex', justifyContent: 'flex-end', marginRight: '20px', }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>
  </div>
</template>
<script>
import ExpressMatchDialog from './expressMatchDialog.vue'
import { getDeliverList, getShippingList, getMatchingList, deleteMatching, getSend } from '@/api/expressMatch.js'
import {smallShopVideoMatchingList,smallShopVideoCompanyList,smallShopVideoMatchingDelete,againSend} from '@/api/smallShopVideo'
const PAGE_STATUS = {
  CREATE: 0,
  EDIT: 1
}
export default {
  name: 'smallShopVideoFastMatchList',
  components: {
    ExpressMatchDialog
  },
  data () {
    return {
      dialog: {
        visible: false,
        pageStatus: 0,
        propRow: {
          id: '',
          code: '', // 中台code
          name: '', // 中台名称
          wx_code: '', // 云仓code
          wx_name: '' // 云仓快递名称
        }
      },
      searchForm: {
        name: '',
        cloud_name: ''
      },
      deliverList: [], // 云仓快递公司列表
      shippingList: [], // 中台快递公司列表
      matchingList: [],// 中台与云仓快递公司关联关系列表
      page: 1,
      pageSize:50,
      total:0
    }
  },
  created () {
    this.init()
    // console.log('getGatherID',this.$ls.getGatherID())
  },
  methods: {
    async init () {
      try {
        const deliverTemp = await smallShopVideoCompanyList({ gather_supply_id: this.$ls.getGatherID() })
        this.deliverList = deliverTemp.data.list
        console.log('deliverList', this.deliverList)
        const shippingTemp = await getShippingList()
        this.shippingList = shippingTemp.data.list
        const matchingTemp = await smallShopVideoMatchingList()
        this.matchingList = matchingTemp.data.list
        console.log('matchingList', this.matchingList)
        this.total = matchingTemp.data.total
      } catch (error) {
        console.log(error)
        this.$message.error('初始化失败')
      }
    },
    // 新增
    onCreate () {
      this.dialog.visible = true
      this.dialog.pageStatus = PAGE_STATUS.CREATE
    },
    async onSearch () {
      //this.page = 1;
      const param = {
        page:this.page,
        pageSize:this.pageSize,
        name: this.searchForm.name,
        wx_name: this.searchForm.wx_name,
      }
      try {
        const matchingTemp = await smallShopVideoMatchingList(param)
        this.matchingList = matchingTemp.data.list
        console.log('matchingList', this.matchingList)
      } catch (error) {
        this.$message.error('查询失败')
      }
    },
    // 编辑
    onEdit (row) {
      this.dialog.visible = true
      this.dialog.pageStatus = PAGE_STATUS.EDIT
      this.dialog.propRow.id = row.id
      this.dialog.propRow.code = row.code
      this.dialog.propRow.name = row.name
      this.dialog.propRow.wx_code = row.wx_code
      this.dialog.propRow.wx_name = row.wx_name
    },
    // 删除
    async onDelete (row) {
      // 确认是否删除
      try {
        await this.$confirm('是否确认删除?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
        return
      }
      // 执行删除请求
      try {
        await smallShopVideoMatchingDelete({ id: row.id })
        this.$message.success('删除成功')
        this.init()
      } catch (error) {
        this.$message.error('删除失败')
      }
    },
    // 提交
    onSubmitted () {
      this.init()
    },
    // 重新发货
    async onSend () {
      // 是否重新发货
      try {
        await this.$confirm('是否重新发货?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        this.$message({
          type: 'info',
          message: '已取消重新发货'
        })
        return
      }
      try {
        await againSend()
        this.$message.success('您已成功发货')
      } catch (error) {
        this.$message.error('重新发货失败')
      }
    },
    // 重置查询条件
    onClear () {
      this.searchForm.name = ''
      this.searchForm.cloud_name = ''
      this.init()
    },
    handleCurrentChange(page) {
      this.page = page
      this.onSearch()
    },

    handleSizeChange(size) {
      this.pageSize = size
      this.onSearch()
    },
  }
}
</script>
::v-deep .el-drawer {
    .el-drawer__body {
        overflow: auto;
    }
}
::v-deep .drawer_content {
    height: auto;
    padding-bottom: 0;
    .cont-div {
        display: flex;
        flex-direction: column;
        height: 100%;
        .main {
            display: flex;
            .item {
                flex: 1;
                height: 70vh;
                margin-left: 10px;
                border: 1px solid #e3e3e3;
                background: #fff;
                &:first-child {
                    margin-left: 0;
                }
                .selectList {
                    height: calc(100% - 45px);
                    padding: 5px;
                    overflow: hidden;
                    overflow-y: auto;
                    .ul {
                        .li {
                            display: flex;
                            .border {
                                width: 100%;
                                box-sizing: border-box;
                                line-height: 12px;
                                height: 24px;
                                display: flex;
                                background-color: #f7f9fa;
                                align-items: center;
                                margin-top: 3px;
                                padding-right: 5px;
                                .left {
                                    margin: 7px 5px;
                                    border-radius: 2px;
                                    width: 2px;
                                    height: 10px;
                                    background: #f24e41;
                                }
                                .name {
                                    flex: auto;
                                    font-size: 12px;
                                }
                                .del {
                                    width: 20px;
                                    text-align: center;
                                    flex: none;
                                    color: #acacac;
                                    cursor: pointer;
                                    i {
                                        font-size: 12px;
                                    }
                                    &:hover {
                                        i {
                                            color: #f56c6c;
                                        }
                                    }
                                }
                            }
                        }
                        .ul {
                            margin-left: 14px;
                            .li {
                                .left {
                                    background: #57b382;
                                }
                            }
                            .ul {
                                margin-left: 14px;
                                .li {
                                    .left {
                                        background: #f6a623;
                                    }
                                }
                                .ul {
                                    margin-left: 14px;
                                    .li {
                                        .left {
                                            background: #4466cf;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .list {
                    height: calc(100% - 35px);
                    overflow: hidden;
                    overflow-y: auto;
                    .li {
                        padding: 10px;
                        border-top: 1px solid #e3e3e3;
                        display: flex;
                        margin-left: -1px;
                        align-items: center;
                        justify-content: center;
                        line-height: 18px;
                        cursor: pointer;
                        &.true {
                            background: #f7f9fa;
                        }
                        &:first-child {
                            border-top: 0;
                        }
                        .select {
                            flex: none;
                            border: 1px solid #b2aebc;
                            border-radius: 4px;
                            width: 18px;
                            height: 18px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            box-sizing: border-box;
                            i {
                                display: none;
                            }
                            &.true {
                                background: #4466cf;
                                border-color: #4466cf;
                                color: #fff;
                                i {
                                    display: inline-block;
                                    font-size: 12px;
                                    margin-left: -1px;
                                }
                            }
                            &.disabled{
                                background-color: #f3f3f3;
                                cursor: not-allowed;
                            }
                        }
                        .name {
                            font-size: 12px;
                            padding: 0 10px;
                            flex: auto;
                        }
                        .code {
                            font-size: 12px;
                            text-align: right;
                            color: #999;
                            flex: none;
                        }
                        .go {
                            opacity: 0.7;
                            flex: none;
                            padding-left: 5px;
                        }
                    }
                }
                .title {
                    width: 100%;
                    /* position: sticky;
            left: 0;
            top: 0; */
                    line-height: 34px;
                    background: #f7f9fa;
                    border-bottom: 1px solid #e3e3e3;
                    z-index: 1;
                    font-size: 12px;
                    text-align: center;
                }
            }
        }
        .drawer__footer {
            text-align: right;
            margin-top: 20px;
            padding-bottom: 20px;
        }
    }
}

::v-deep .el-checkbox {
    .el-checkbox__label {
        display: none;
    }
}

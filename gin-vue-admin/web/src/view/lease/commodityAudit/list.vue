<!-- 商品审核 -->
<template>
  <m-card>
    <el-form :model="searchInfo" class="search-term" label-width="90px" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >供应商</span>
              </div>
              <el-select clearable filterable v-model="searchInfo.suppliers_id" class="w100">
                <el-option v-for="item in supplierList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.title" class="line-input" clearable>
              <span slot="prepend">商品名称</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >状态</span>
              </div>
              <el-select clearable v-model="searchInfo.is_display" class="w100">
                <el-option v-for="item in statusOptions" :key="item.id" :label="item.label" :value="item.val"></el-option>
              </el-select>
          </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <div class="mt25">
      <el-button @click="batchClick(1)">批量通过</el-button>
      <el-button @click="batchClick(-1)">批量驳回</el-button>
    </div>
    <el-table :data="tableData" class="mt25" ref="table">
      <el-table-column type="selection" align="center" :selectable='selectInit'></el-table-column>
      <el-table-column label="图片" width="140" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <m-image style="width: 60px;height:60px" :src="scope.row.product.image_url">
          </m-image>
        </template>
      </el-table-column>
      <el-table-column label="商品" width="300">
        <template slot-scope="scope">
          <p>{{ scope.row.product.title }}</p>
        </template>
      </el-table-column>
      <el-table-column label="价格" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>￥{{ scope.row.product.price | formatF2Y }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成本价" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>￥{{ scope.row.product.cost_price | formatF2Y }}</span>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="product.stock" show-overflow-tooltip></el-table-column>
      <el-table-column label=销量 align="center" prop="product.sales" show-overflow-tooltip></el-table-column>
      <el-table-column label="供应商" align="center" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.product.supplier.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.status === 0">待审核</span>
          <span v-if="scope.row.status === -1 ">驳回</span>
          <span v-if="scope.row.status === 1">通过</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="150px">
        <template slot-scope="scope">
          <el-button type="text" @click="$_blank('/layout/leaseBaseSettingIndex/leaseReleaseGoods',{id:scope.row.product_id})">详情
          </el-button>
          <el-button type="text" v-if="scope.row.status === 0" @click="handleApplyClick(scope.row,1)">通过</el-button>
          <el-button type="text" v-if="scope.row.status === 0" @click="handleApplyClick(scope.row,-1)" class="color-red">
            驳回
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes,prev, pager, next, jumper"></el-pagination>
  </m-card>
</template>

<script>
import infoList from "@/mixins/infoList";
import {getSupplierOptionList} from "@/api/goods";
import {productVerifyList,leaseAuditLeaseAuditProduct,leasegetLeaseAuditProductList} from "@/api/product";

export default {
  name: "goodsVerifyList",
  mixins: [infoList],
  data() {
    return {
      supplierList: [],
      searchInfo:{},
      //分页
      page: 1,
      pageSize: 10,
      total: 0,
      tableData:[],
      listApi: productVerifyList,
      statusOptions: [
        {label: "驳回", val: -1},
        {label: "通过", val: 1},
      ],
    }
  },
  mounted() {
    // this.getTableData()
    this.leaseSupplygetList()
    // 获取供应商
    getSupplierOptionList().then(res => {
      if (res.code === 0) {
        this.supplierList = res.data.list
      } else {
        this.$message.error(res.msg)
      }
    })
  },
  methods: {
    // 租赁商品列表
    leaseSupplygetList(){
      let data ={
        'page':this.page,
        'pageSize':this.pageSize,
        'title':this.searchInfo.title,
        'suppliers_id':this.searchInfo.suppliers_id,
        'status':this.searchInfo.is_display,
      }
      leasegetLeaseAuditProductList(data).then((res) => {
        if(res.code === 0){
          this.tableData = res.data.list
          this.page = res.data.page
          this.total = res.data.total
          this.pageSize = res.data.pageSize
        }
      })
    },
    selectInit(row, index) {
      if (row.status === 0) {
        return true
      } else {
        return false
      }
    },
    /**
     * 批量操作
     * @param type 1 通过 2驳回
     */
    batchClick(type) {
      if (Array.isArray(this.$refs.table.selection) && this.$refs.table.selection.length > 0) {
        let s = ""
        s = type === 1 ? "批量通过" : "批量驳回"
        this.$confirm("是否确认" + s + "?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
// 获取选中的数据
          let selectedArr = this.$refs.table.selection
          let params = {
            is_display: null,
            ids: []
          }
          switch (type) {
            case 1:
              params.is_display = 1
              break;
            case 2:
              params.is_display = 0
              break;
          }
          selectedArr.forEach(item => {
            params.ids.push({
              id: item.id,
              product_id: item.product_id
            })
          })
          let res = await leasegetLeaseAuditProductList(params)
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getTableData()
          }
        }).catch(()=>{})
      } else {
        this.$message.error("请勾选商品")
      }
    },
    // 重置搜索条件
    reSearch() {
      this.page = 1
      this.searchInfo = {}
    },
    // 搜索
    search() {
      this.page = 1;
      this.leaseSupplygetList();
    },
    handleSizeChange(size) {
      this.pageSize = size
      this.leaseSupplygetList();
    },

    handleCurrentChange(page) {
      this.page = page
      this.leaseSupplygetList();
    },
    // 审核操作
    handleApplyClick(row, status) {
      let s = ""
      switch (status) {
        case -1:
          s = "驳回"
          break;
        case 1:
          s = "通过"
          break;
      }
      this.$confirm("是否确认" + s + "?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        let data = {
          ids: [row.id],
          // product_id: row.product_id,
          status: status
        }
        leaseAuditLeaseAuditProduct(data).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.leaseSupplygetList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
      })

    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
</style>
<template>
  <el-form :model="formData" label-width="160px">
    <p class="title-p">推送云仓定价策略</p>
    <el-divider></el-divider>
    <el-form-item label="下单用户:">
      <el-select v-model="formData.cloud_user_id" clearable filterable>
        <el-option v-for="item in cloudUserOption" :key="item.id" :value="item.id"
                   :label="item.username">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="云仓订单同步:">
      <el-radio-group v-model="formData.cloud_is_syn_order">
        <el-radio :label="1">开启</el-radio>
        <el-radio :label="2">关闭</el-radio>
      </el-radio-group>
      <p class="hint-p">关闭后不会自动同步订单,不设置默认开启!</p>
    </el-form-item>
    <el-form-item label="自动下架云仓商品:">
      <el-radio-group v-model="formData.cloud_is_auto_undercarriage">
        <el-radio :label="0">关闭</el-radio>
        <el-radio :label="1">开启</el-radio>
      </el-radio-group>
      <p class="hint-p">商品下架时自动下架云仓商品</p>
    </el-form-item>
    <el-form-item label="推送成功自动申请上架:">
      <el-radio-group v-model="formData.cloud_is_push_auto_shelf">
        <el-radio :label="0">关闭</el-radio>
        <el-radio :label="1">开启</el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="云仓市场价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_marketing" :label="0"></el-radio>
        <el-form-item label="建议零售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_marketing_marketing_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_marketing" :label="1"></el-radio>
        <el-form-item label="供货价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_marketing_price_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_marketing" :label="2"></el-radio>
        <el-form-item label="成本价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_marketing_cost_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="云仓指导价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_guide" :label="0"></el-radio>
        <el-form-item label="建议零售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_guide_marketing_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_guide" :label="1"></el-radio>
        <el-form-item label="供货价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_guide_price_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_guide" :label="2"></el-radio>
        <el-form-item label="成本价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_guide_cost_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_guide" :label="3"></el-radio>
        <el-form-item label="指导价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_guide_guide_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="云仓结算价:">
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_settlement" :label="0"></el-radio>
        <el-form-item label="建议零售价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_settlement_marketing_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_settlement" :label="1"></el-radio>
        <el-form-item label="供货价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_settlement_price_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
      <div class="f fac blockRadio-box">
        <el-radio v-model="formData.cloud_settlement" :label="2"></el-radio>
        <el-form-item label="成本价 X 定价系数" label-width="135px">
          <div class="f fac">
            <el-input v-model="formData.cloud_settlement_cost_coefficient"></el-input>
            <p class="f1 ml10">%</p>
          </div>
        </el-form-item>
      </div>
    </el-form-item>
  </el-form>
</template>
<script>
import { getUserList } from "@/api/cloud"
export default {
  name: "stbzCloud",
  data() {
    return {
      page:1,
      pageSize:9999,
      cloudUserOption:[],//下单用户列表
      formData: {
        cloud_user_id:0,//下单用户
        cloud_is_syn_order:1, //是否同步订单 1是 2否
        cloud_is_auto_undercarriage: 0, //是否中台下架云仓自动下架 1是 0否
        cloud_is_push_auto_shelf: 0, // 推送成功自动上架  1是  0否

        cloud_marketing: 0, //云仓市场价设置 0 市场价 1现价 2 成本价
        cloud_marketing_marketing_coefficient: "100", // 市场价订价系数
        cloud_marketing_price_coefficient: "100", // 现价订价系数
        cloud_marketing_cost_coefficient: "100", // 成本价订价系数

        cloud_guide: 0, // 云仓指导价设置 0 市场价 1现价 2 成本价
        cloud_guide_marketing_coefficient: "100", // 市场价订价系数
        cloud_guide_price_coefficient: "100", // 现价订价系数
        cloud_guide_cost_coefficient: "100", // 成本价订价系数
        cloud_guide_guide_coefficient:"100",//

        cloud_settlement: 0, // 云仓结算价设置 0 市场价 1现价 2 成本价
        cloud_settlement_marketing_coefficient: "100", // 市场价订价系数
        cloud_settlement_price_coefficient: "100", // 现价订价系数
        cloud_settlement_cost_coefficient: "100", // 成本价订价系数
      },
    };
  },
  mounted() {
    this.getUserList()
  },
  methods: {
    //下单用户
    getUserList(){
      getUserList({page:this.page,pageSize:this.pageSize}).then(res=>{
        if(res.code===0){
          this.cloudUserOption=res.data.list
        }
      })
    },
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        if(val.cloud_user_id === null){
          that.formData.cloud_user_id = 0
        }else{
          that.formData[element] = val[element];
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}
.blockRadio-box {
  margin-top: 15px;

  & :first-child {
    margin-top: 0;
  }

  ::v-deep .el-radio {
    margin: 0;

    .el-radio__label {
      display: none;
    }
  }
}
p.hint-p {
  font-size: 12px;
  color: #c0c4cc;
  line-height: 20px;
}
</style>
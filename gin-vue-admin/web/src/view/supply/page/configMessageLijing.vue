<!-- 丽晶供应链配置信息 -->
<template>
    <m-card>
        <el-tabs v-model="activeName" type="card">
            <el-tab-pane label="基础设置" name="baseSetting">
                <el-form :model="formData" label-width="130px">
                    <p class="title-p">基础信息</p>
                    <el-divider></el-divider>
                    <el-row>
                        <el-col :span="15">
                            <el-form-item label="appKey:">
                                <el-input v-model="formData.baseInfo.appKey"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item label="appSecret:" >
                                <el-input v-model="formData.baseInfo.appSecret"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item label="access_token:" >
                                <el-input v-model="formData.baseInfo.access_token"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="15">
                            <el-form-item label="apiUrl:" >
                                <el-input v-model="formData.baseInfo.apiUrl"></el-input>
                            </el-form-item>
                        </el-col> 

                        <el-col :span="15">
                            <el-form-item label="渠道编号:" >
                                <el-input v-model="formData.baseInfo.ks_channel_no"></el-input>
                            </el-form-item>
                        </el-col>      
                        <el-col :span="15">
                            <el-form-item label="默认仓库编号:" >
                                <el-input v-model="formData.baseInfo.default_unit_no"></el-input>
                            </el-form-item>
                        </el-col>                   
                        <!-- <el-col :span="15">
                            <el-form-item label="供应链名称:">
                                <el-input v-model="formData.baseInfo.storeName"></el-input>
                            </el-form-item>
                        </el-col> -->
                    </el-row>

                    <p class="title-p">等级限制</p>
                    <el-divider></el-divider>
                    <el-form-item
                        v-for="(platformItem, index) in platforms"
                        :key="platformItem.id"
                        :label="platformItem.name"
                    >
                    <el-checkbox
                        v-model="platformItem.isCheckAll"
                        @change="checkAll(platformItem.isCheckAll, index)"
                    >全选
                    </el-checkbox
                    >
                    <el-checkbox-group
                        v-model="platformItem.application_level_ids"
                        @change="checkItem(platformItem.application_level_ids, index)"
                    >
                        <el-checkbox
                            v-for="item in sserLevelOptionList"
                            :key="item.id"
                            :label="item.id"
                        >{{ item.levelName }}
                        </el-checkbox
                        >
                    </el-checkbox-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="saveSetting">提交</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>

        </el-tabs>
    </m-card>
</template>

<script>
// 配置信息
import {mapGetters} from "vuex";
import {getSetting, saveSetting, setSupplySetting, getSupplySetting} from "@/api/gatherSupply";
import {getApplicationLevelOption} from "@/api/application";
// import aljxTokenDialog from "./components/aljxTokenDialog.vue"
// import yzh from "./configComponents/yzh";
// import stbz from "./configComponents/stbz";
// import self from "./configComponents/self";
// import szbao from "./configComponents/szbao";
// import stbzCloud from "./configComponents/stbzCloud";
// import cross from "./configComponents/cross";
// import dwd from "./configComponents/dwd";
// import yzhNew from "./configComponents/yzh_new";
// import hehe from "./configComponents/hehe";
// import zyhx from "./configComponents/zyhx";
// import aljx from "./configComponents/aljx";

export default {
    name: "SupplyConfigMessageLijing",
    // components: {aljxTokenDialog, yzh, stbz, self, szbao, stbzCloud, cross, dwd, yzhNew, hehe, zyhx, aljx},
    data() {
        return {
            platforms: [
                {name: "采购端等级:", id: 117, application_level_ids: [], isCheckAll: false},
            ],
            sserLevelOptionList: [],
            activeName: "baseSetting",
            callBackUrl: "",
            domain: "",
            routeKey: this.$route.query.key || "",
            rescode: 9,
            formData: {
                baseInfo: {},
            },
            // 店铺设置、店铺执照
            shopData: {
                value: {
                    shop_announcement: '', // 店铺公告
                    ShopStoreLicenses: [], // 营业执照
                    shop_store_returns_instructions: '', // 退换货说明
                    shop_logo_square: '', //供应链logo
                }
            },
            path: this.$path,
            tempId: 0,
            tempKey: ''
        };
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),
    },
    created() {
        getSetting({key: "gatherSupply" + parseInt(this.$route.query.id)}).then(
            (res) => {
                try {
                    this.formData = JSON.parse(res.data.data.value);
                    // console.log(this.formData, "????");
                    this.rescode = res.code;
                    // console.log("rescode", res.code);
                    this.disposeLevelData(
                        this.formData.storage_auth.application_level_ids
                    );
                    if (this.routeKey) {
                        this.$refs[this.routeKey].setFrom(this.formData.pricing);
                        if (this.routeKey === "stbz") {
                            this.$refs.stbzCloud.setFrom(this.formData.cloud);
                        }
                    }
                    
                    this.formData.update.currentPrice =
                        this.formData.update.currentPrice || 2;
                    this.formData.update.originalPrice =
                        this.formData.update.originalPrice || 2;
                    this.formData.update.costPrice = this.formData.update.costPrice || 2;

                    this.formData.update.baseInfo = this.formData.update.baseInfo || 2;
                    this.formData.update.imageWatermark =
                        this.formData.update.imageWatermark || 2;
                    this.formData.update.createBrand =
                        this.formData.update.createBrand || 2;
                } catch {
                } finally {
                    let ishttps =
                        "https:" == document.location.protocol ? "https" : "http";
                    let domain = document.domain;

                    //res.data.url

                    let proto = res.data.proto ? res.data.proto : "https";

                    this.domain = proto + "://" + res.data.domain;
                    this.callBackUrl =
                        proto +
                        "://" +
                        res.data.domain +
                        "/supplyapi/api/gatherSupply/notificationCallBack";
                }

                // console.log("数据：", res.data.url)
            }
        );
        this.getShop()
        this.getUserLevel();
    },
    methods: {
        // 处理等级限制回显
        disposeLevelData(data) {
            console.log('data',data);
            data.forEach((item) => {
                this.platforms.forEach((item2) => {
                if (item.source_id === item2.id) {
                    item2.application_level_ids = item.application_level_ids;
                    item2.isCheckAll =
                        item.application_level_ids.length ===
                        this.sserLevelOptionList.length;
                }
                });
            });
        },
        // return等级限制数据
        getLevelData() {
            let returnData = [];
            this.platforms.forEach((item) => {
                if (item.application_level_ids.length > 0) {
                returnData.push({
                    source_id: item.id,
                    application_level_ids: item.application_level_ids,
                });
                }
            });
            return {application_level_ids: returnData};
        },
        async getUserLevel() {
            let res = await getApplicationLevelOption();
            if (res.code === 0) {
                this.sserLevelOptionList = res.data.list;
            }
        },       
        checkAll(isCheckAll, index) {
            if (isCheckAll) {
                this.sserLevelOptionList.forEach((item) => {
                this.platforms[index].application_level_ids.push(item.id);
                });
            } else {
                this.platforms[index].application_level_ids = [];
            }
        },
        checkItem(value, index) {
            this.platforms[index].isCheckAll =
                value.length === this.sserLevelOptionList.length;
        },
        getCode() {
            // this.$refs.aljxCodeDialog.init()
            let domain = this.domain
            window.open(`https://auth.1688.com/oauth/authorize?client_id=${this.formData.baseInfo.appKey}&site=1688&redirect_uri=https://yx.gz.cn&state=1`)
        },
        openTokenDialog() {
            this.$refs.aljxTokenDialog.init(this.$route.query.id)
        },
        getToken(token) {
          console.log("token:"+token)
            this.formData.baseInfo.token = token
        },
        handleLicenses(res) {
            // this.shopData.value.ShopStoreLicenses = res.data.file.url;
            this.shopData.value.ShopStoreLicenses.push(res.data.file.url)
        },
        handleSupplier(res) {
            this.shopData.value.shop_logo_square = res.data.file.url;
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
        // 删除封面
        delCover(index) {
            this.shopData.value.ShopStoreLicenses.splice(index, 1)
        },
        async saveShop() {
            const params = {
                id: this.tempId,
                key: this.tempKey,
                ...this.shopData
            }
            console.log('params', params)
            const res = await setSupplySetting(params)
            if (res.code === 0) {
                this.$message.success(res.msg)
            } else {
                this.$message.error(res.msg)
            }
        },
        async getShop() {
            const params = {
                gather_supply_id: parseInt(this.$route.query.id)
            }
            const res = await getSupplySetting(params)
            this.tempKey = res.data.key
            this.tempId = res.data.id
            if (res.code === 0) {
                this.shopData.value = res.data.value
                if (this.shopData.value.ShopStoreLicenses === null) this.shopData.value.ShopStoreLicenses = []
            } else {
                this.$message.error(res.msg)
            }
        },
        async saveSetting() {
            // if (this.routeKey) {
            //     // console.log(this.$refs[this.routeKey], "??????");
            //     this.formData.pricing = {...this.$refs[this.routeKey].formData};
            //     if (this.routeKey === "stbz") {
            //         this.formData.cloud = {...this.$refs.stbzCloud.formData};
            //     }
            //     let storage_auth = this.$refs[this.routeKey].getLevelData();
            //     this.formData.storage_auth = storage_auth;
            // }

            // this.formData.management.products = parseInt(
            //     this.formData.management.products
            // );
            // this.formData.management.profit = parseInt(
            //     this.formData.management.profit
            // );
            /* if (this.routeKey === "stbz") {
              let storage_auth = this.$refs.stbz.getLevelData();
              this.formData.storage_auth = storage_auth;
            }
            if (this.routeKey === "yzh") {
              let storage_auth = this.$refs.yzh.getLevelData();
              this.formData.storage_auth = storage_auth;
            } */
            console.log("this.$route.query.id：" + this.$route.query.id)
            let storage_auth = this.getLevelData();
            this.formData.storage_auth = storage_auth;
            let data = {
                key: "gatherSupply" + parseInt(this.$route.query.id),

                value: JSON.stringify(this.formData),
            };

            // console.log("请求数据：" + JSON.stringify(data))
            let res = await saveSetting(data);

            // console.log("返回数据：" + JSON.stringify(res))
            if (res.code === 0) {
                this.$message({
                    type: "success",
                    message: "保存成功",
                });
                // console.log("成功");
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.el-form {
  .el-row {
    padding: 0;
  }

  .blockRadio-box {
    margin-top: 15px;

    & :first-child {
      margin-top: 0;
    }

    ::v-deep .el-radio {
      margin: 0;

      .el-radio__label {
        display: none;
      }
    }
  }

  p.hint-p {
    font-size: 12px;
    color: #c0c4cc;
    line-height: 20px;
  }
}

p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}

.w270 {
  width: 270px;
}

::v-deep .avatar-uploader {
  .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    width: 178px;
    height: 178px;

    .avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      line-height: 178px;
      text-align: center;
    }
  }
}


.covers-uploader, .cover-image {
  width: 178px;
  height: 178px;
}

.cover-list-box {
  width: 178px;
  height: 178px;
  margin-right: 10px;
  margin-bottom: 10px;
  position: relative;

  &:hover {
    .del-box {
      display: block;
      line-height: 178px;
    }
  }

  .del-box {
    display: none;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.3);

    .el-button.el-button--text {
      font-size: 18px;
      color: #fff;
      margin-top: 10px;
    }
  }
}


.covers-uploader {
  border: 1px dashed #D9DCDF;
  color: #ABABAB;
  text-align: center;

  i {
    // margin-top: 24px;
    // font-size: 40px;
    margin-top: 20px;
    font-size: 20px;
  }
}

.avatar-txt {
  margin-top: 12px;
  font-size: 12px;
}
</style>
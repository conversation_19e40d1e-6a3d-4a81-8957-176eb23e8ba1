<template>
    <el-form :model="searchForm" ref="searchForm" label-width="90px" class="search-term" inline>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >一级分类</span>
              </div>
              <el-select v-model="searchForm.fl1" filterable class="w100" @change="operationCategory(2, searchForm.fl1)">
                <el-option v-for="item in category1" :key="item.category_id" :label="item.category_name" :value="item.category_id"> </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >二级分类</span>
              </div>
              <el-select v-model="searchForm.fl2" filterable class="w100" @change="operationCategory(3, searchForm.fl2)">
                <el-option v-for="item in category2" :key="item.category_id" :label="item.category_name" :value="item.category_id"> </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >三级分类</span>
              </div>
              <el-select v-model="searchForm.fl3" filterable class="w100">
                <el-option v-for="item in category3" :key="item.category_id" :label="item.category_name" :value="item.category_id"> </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >区间类型</span>
              </div>
              <el-select v-model="searchForm.sectionType" class="w100">
                <el-option v-for="item in sectionType" :key="item.id" :label="item.name" :value="item.type"> </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input">
            <div class="line-box ">
                <span v-if="searchForm.sectionType === 'promotion_rate'">利润百分比:</span>
                <span v-else-if="searchForm.sectionType === 'gross_profit_rate'">利率百分比:</span>
                <span v-else>区间价格:</span>
            </div>
            <div class="f fac">
                <el-form-item prop="range_from" class="f1">
                  <el-input v-model="searchForm.range_from" placeholder="请输入"></el-input>
                </el-form-item>
                <span class="zih-span mr_10">至</span>
                <el-form-item prop="range_to" class="f1">
                  <el-input v-model="searchForm.range_to" placeholder="请输入"></el-input>
                </el-form-item>
            </div>
        </div>
      </el-form-item>
      <el-form-item label="">
            <div class="line-input">
                <div class="line-box">
                    <span>利润率:</span>
                </div>
                <div class="f fac">
                    <el-form-item class="f1">
                        <el-input
                            v-model="searchForm.promotion_from"
                            placeholder="请输入"
                        >
                        </el-input>
                    </el-form-item>
                    <span class="zih-span mr_10">至</span>
                    <el-form-item class="f1">
                        <el-input
                            v-model="searchForm.promotion_to"
                            placeholder="请输入"
                        >
                        </el-input>
                    </el-form-item>
                </div>
            </div>
        </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >是否已导入</span>
              </div>
              <el-select v-model="is_import" class="w100">
                <el-option label="全部" value="0"></el-option>
                <el-option label="已导入" value="1"></el-option>
                <el-option label="未导入" value="2"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >商品名称</span>
              </div>
              <el-input placeholder="商品名称" v-model="searchForm.goodsName"></el-input>
          </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getGoodsList()">查询</el-button>
        <el-button type="text" @click="resetForm('searchForm')">重置搜索条件 </el-button>
        <!-- <el-button type="primary" @click="synchronizationGoodsList()">同步拉取供应链商品 </el-button> -->
      </el-form-item>
    </el-form>
  </template>
  
  <script>
  import {
    getCategoryChild,
    getGoodsList,
    getGroup,
    syncGoodsList,
    updateImportGoods,
    updateGoodsMd5Empty,
    updateImportGoodsYouxuan,
    getTypes,
  } from '@/api/gatherSupply'
  
  export default {
    name: 'goodsImportSearch',
    data() {
      return {
        categoryList: [],
        // 是否已导入
        is_import: '0',
        newSearchForm: {},
        keyName: '',
        searchForm: {
          fl1: null,
          fl2: null,
          fl3: null,
        },
        category1: [],
        category2: [],
        category3: [],
        groupList: [],
        sectionType: [
          {
            name: '协议价',
            type: 'agreement_price',
          },
          {
            name: '指导价',
            type: 'guide_price',
          },
        ],
      }
    },
    mounted() {
      this.keyName = this.$route.query.key
      this.getCategory()
    },
    methods: {
      async getGroup() {
        let params = {
          gather_supply_id: parseInt(this.$route.query.id),
          key: this.$route.query.key,
        }
        let res = await getGroup(params)
        if (res.code === 0) {
          this.groupList = res.data
        }
      },
  
      async synchronizationGoodsList() {
        let data = {
          gather_supply_id: parseInt(this.$route.query.id),
          key: 'guanaitong',
        }
  
        let info = await syncGoodsList(data)
  
        if (info.code === 0) {
          this.$message.success('后台正在同步,预计5-10分钟完成,请稍后刷新页面查看')
          console.log(info)
        }
      },
  
      async getGoodsList() {
        let searchForm = {}
        searchForm.source = parseInt(this.searchForm.terrace)
        searchForm.is_free_shipping = parseInt(this.searchForm.isPostage)
        searchForm.shop_words = this.searchForm.store
        searchForm.search_words = this.searchForm.goodsName
        searchForm.range_from = parseInt(this.searchForm.range_from)
        searchForm.range_to = parseInt(this.searchForm.range_to)
        searchForm.range_type = this.searchForm.sectionType
        searchForm.group_id = this.searchForm.marketingCampaign
        searchForm.search_words = this.searchForm.goodsName
        searchForm.is_import = parseInt(this.is_import)
        searchForm.promotion_rate = {
            from: parseInt(this.searchForm.promotion_from),
            to: parseInt(this.searchForm.promotion_to),
        };
        if (this.searchForm.fl1) {
          searchForm.category1_id = parseInt(this.searchForm.fl1)
        }
        if (this.searchForm.fl2) {
          searchForm.category2_id = parseInt(this.searchForm.fl2)
        }
        if (this.searchForm.fl3) {
          searchForm.category3_id = parseInt(this.searchForm.fl3)
        }
        this.newSearchForm = searchForm
        this.$emit('handleSearch', searchForm)
      },
  
      // 重置
      resetForm(formName) {
        this.searchForm = {}
        // this.$refs[formName].resetFields()
        this.$emit('pageReload')
        this.is_import = '0'
      },
      async operationCategory(level = 1, pid = '') {
        switch (level) {
          case 1:
            this.category1 = []
            break
          case 2:
            this.category2 = []
            this.searchForm.fl2 = ''
            this.category3 = []
            this.searchForm.fl3 = ''
            break
          case 3:
            this.category3 = []
            this.searchForm.fl3 = ''
            break
        }
        let params = {
          gather_supply_id: parseInt(this.$route.query.id),
          key: this.$route.query.key,
        }
        if (pid) {
          params.pid = parseInt(pid)
        }
        let { data } = await getCategoryChild(params)
        data.forEach(item => {
          this[`category${level}`].push({
            ...item,
          })
        })
      },
      async getCategory() {
        let searchForm = {
          gather_supply_id: parseInt(this.$route.query.id),
          key: this.$route.query.key,
        }
        let { data } = await getCategoryChild(searchForm)
        this.categoryList = data
        this.operationCategory()
      },
    },
  }
  </script>
  
  <style scoped></style>
  
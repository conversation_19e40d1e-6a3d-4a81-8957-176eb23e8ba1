<template>
  <el-form :model="searchForm" ref="searchForm" label-width="90px" class="search-term">
    <el-row :gutter="10">
      <el-col :xl="8" :lg="12">
        <el-form-item label="商品:" prop="goodsName">
          <el-input placeholder="商品名称" v-model="searchForm.goodsName"></el-input>
        </el-form-item>
      </el-col>
      <!--      <el-col :xl="8" :lg="12">-->
      <!--        <el-form-item label="一级分类:" prop="fl1">-->
      <!--          <el-select-->
      <!--              v-model="searchForm.fl1"-->
      <!--              filterable-->
      <!--              class="w100"-->
      <!--              @change="getCategory(2, 0)"-->
      <!--          >-->
      <!--            <el-option-->
      <!--                v-for="item in category1"-->
      <!--                :key="item.c_id"-->
      <!--                :label="item.name"-->
      <!--                :value="item.c_id"-->
      <!--            >-->
      <!--            </el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--      </el-col>-->
      <!--    </el-row>-->
      <!--    <el-row :gutter="10">-->
      <!--      <el-col :xl="8" :lg="12">-->
      <!--        <el-form-item label="二级分类:" prop="fl2">-->
      <!--          <el-select-->
      <!--              v-model="searchForm.fl2"-->
      <!--              filterable-->
      <!--              class="w100"-->
      <!--              @change="getCategory(3, 0)"-->
      <!--          >-->
      <!--            <el-option-->
      <!--                v-for="item in category2"-->
      <!--                :key="item.c_id"-->
      <!--                :label="item.name"-->
      <!--                :value="item.c_id"-->
      <!--            >-->
      <!--            </el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--      </el-col>-->
      <!--      <el-col :xl="8" :lg="12">-->
      <!--        <el-form-item label="三级分类:" prop="fl3">-->
      <!--          <el-select v-model="searchForm.fl3" filterable class="w100">-->
      <!--            <el-option-->
      <!--                v-for="item in category3"-->
      <!--                :key="item.c_id"-->
      <!--                :label="item.name"-->
      <!--                :value="item.c_id"-->
      <!--            >-->
      <!--            </el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--      </el-col>-->
      <el-col :xl="8" :lg="12">
        <el-form-item label="状态:" prop="isDisplay">
          <el-select v-model="searchForm.isDisplay" class="w100">
            <el-option value="" label="不限"></el-option>
            <el-option value="0" label="上架"></el-option>
            <el-option value="1" label="下架"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xl="8" :lg="12">
        <el-form-item label="是否已导入:" prop="is_import">
          <el-select v-model="searchForm.is_import" class="w100">
            <el-option label="全部" value="0"></el-option>
            <el-option label="已导入" value="1"></el-option>
            <el-option label="未导入" value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xl="8" :lg="12">
        <el-row :gutter="10">
          <el-col :xl="10" :lg="12">
            <el-form-item label="区间类型:" prop="sectionType">
              <el-select v-model="searchForm.sectionType" class="w100">
                <el-option v-for="item in sectionType" :key="item.id" :label="item.name" :value="item.type">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xl="14" :lg="18">
            <el-form-item label="区间价格:">
              <div class="f fac">
                <el-form-item prop="minPrice" class="f1">
                  <el-input v-model="searchForm.minPrice" placeholder="请输入"></el-input>
                </el-form-item>
                <span class="zih-span ml10 mr10">至</span>
                <el-form-item prop="maxPrice" class="f1">
                  <el-input v-model="searchForm.maxPrice" placeholder="请输入"></el-input>
                </el-form-item>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-col>
      <el-col :xl="8" :lg="12">
        <el-form-item label="店铺:" prop="wdt_shop_id">
          <el-select v-model="searchForm.wdt_shop_id" class="w100" filterable @focus="changeShop" @change="changeShopId">
            <el-option v-for="item in shopList" :key="item.id" :label="item.name" :value="item.shop_id"> </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>

    <el-form-item>
      <el-button type="primary" @click="getGoodsList()">查询</el-button>
      <el-button type="text" @click="resetForm('searchForm')">重置搜索条件 </el-button>
      <el-button type="warning" @click="updateGoodsInventory">批量更新商品库存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
// import { getCategoryChild } from '@/api/gatherSupply'
import { list,updateImportGoods } from '@/api/wangdiantong'
import {confirm} from "@/decorators/decorators";

export default {
  name: 'goodsImportSearch',
  data() {
    return {
      // 店铺列表
      shopList: [],
      // 店铺ID
      wdt_shop_id: '',
      // 是否已导入
      newSearchForm: {},
      keyName: '',
      searchForm: {
        fl1: '',
        fl2: '',
        fl3: '',
      },
      category1: {},
      category2: {},
      category3: {},
      groupList: [],
      sectionType: [
        {
          name: '协议价',
          type: 'agreement_price',
        },
        {
          name: '指导价',
          type: 'guide_price',
        },
      ],
    }
  },
  mounted() {
    this.keyName = this.$route.query.key
    // this.getCategory(1, 0)
  },
  methods: {
      // 批量更新商品库存
      @confirm("提示","是否批量更新商品库存?")
      async updateGoodsInventory(){
          let id = parseInt(this.$route.query.id)
          if(!id){
              this.$message.error("非法操作")
              return;
          }
        const {code,msg} = await updateImportGoods({id})
          if(code === 0){
              this.$message.success(msg)
              this.$emit('reLoad')
          }
      },
    async getGoodsList() {
      let searchForm = {}
      searchForm.category_id = parseInt(this.searchForm.fl2)
      searchForm.search_words = this.searchForm.goodsName
      searchForm.range_from = parseInt(this.searchForm.minPrice)
      searchForm.range_to = parseInt(this.searchForm.maxPrice)
      searchForm.range_type = this.searchForm.sectionType
      searchForm.is_free_shipping = parseInt(this.searchForm.isPostage)
      searchForm.is_display = parseInt(this.searchForm.isDisplay)
      searchForm.is_import = parseInt(this.searchForm.is_import)
      searchForm.wdt_shop_id = this.searchForm.wdt_shop_id
      if (this.searchForm.fl1) {
        searchForm.category_id = parseInt(this.searchForm.fl1)
        let category1 = this.category1.find(item => {
          return item.id === this.searchForm.fl1
          //筛选出匹配数据，是对应数据的整个对象
        })
        searchForm.categorys = category1.name
      }
      if (this.searchForm.fl2) {
        searchForm.category_id = parseInt(this.searchForm.fl2)
        let category2 = this.category2.find(item => {
          return item.id === this.searchForm.fl2
          //筛选出匹配数据，是对应数据的整个对象
        })
        searchForm.categorys += ',' + category2.name
      }
      if (this.searchForm.fl3) {
        searchForm.category_id = parseInt(this.searchForm.fl3)
        let category3 = this.category3.find(item => {
          return item.id === this.searchForm.fl3
          //筛选出匹配数据，是对应数据的整个对象
        })
        searchForm.categorys += ',' + category3.title
      }
      this.newSearchForm = searchForm
      this.$emit('handleSearch', searchForm)
    },

    // 重置
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.$emit('pageReload')
    },
    // 获取店铺
    async changeShop() {
      const res = await list({
        strategy: this.$route.query.key,
      })
      this.shopList = res.data.list
    },
    // 获取店铺ID
    changeShopId(id) {
      this.wdt_shop_id = id
    }
    // async getCategory(level, pid) {
    //   switch (level) {
    //     case 1:
    //       pid = 0;
    //       break;
    //     case 2:
    //       pid = this.searchForm.fl1;
    //       console.log('pid 2', pid);
    //       this.category2 = [];
    //       this.category3 = [];
    //       this.searchForm.fl2 = "";
    //       this.searchForm.fl3 = "";
    //       break;
    //     case 3:
    //       pid = this.searchForm.fl2;
    //       this.category3 = [];
    //       this.searchForm.fl3 = "";
    //       break;
    //   }
    //
    //   let searchForm = {
    //     pid: pid,
    //     source: parseInt(this.searchForm.terrace),
    //     gather_supply_id: parseInt(this.$route.query.id),
    //     key: this.$route.query.key
    //   };
    //   let list = await getCategoryChild(searchForm);
    //   if (list.code === 0) {
    //     switch (level) {
    //       case 1:
    //         this.category1 = list.data;
    //         console.log('this.category1', this.category1);
    //         break;
    //       case 2:
    //         this.category2 = list.data;
    //         console.log('this.category2', this.category2);
    //         break;
    //       case 3:
    //         this.category3 = list.data;
    //         console.log('this.category3', this.category3);
    //         break;
    //     }
    //   }
    // },
  },
}
</script>

<style scoped></style>

<template>
    <el-row>
        <el-col>
            <div class="f fac fjsb">
                <p class="title-p">采购端API同步指定支付</p>
                <el-button type="primary" @click="paySave">保 存</el-button>
            </div>
            <el-divider></el-divider>
        </el-col>
        <el-col :span="12">
            <el-table :data="payFormData">
                <el-table-column label="支付方式" align="center" prop="account_name"></el-table-column>
                <el-table-column label="是否支持" align="center">
                    <template slot-scope="scope">
                        <el-checkbox v-model="scope.row.enable" :true-label="1" :false-label="0"></el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column label="优先级" align="center">
                    <template slot-scope="scope">
                        <el-input v-model.number="scope.row.sort_level"></el-input>
                    </template>
                </el-table-column>
            </el-table>
        </el-col>
    </el-row>
</template>
<script>
import {confirm} from "@/decorators/decorators";
import {createSupplyAccount, getAccount} from "@/api/purchase";

export default {
    name: "simultaneousAssignmentPayment",
    data() {
        return {
            gather_supply_id: this.$route.query.id ? parseInt(this.$route.query.id) : 0,
            payFormData: [],
        }
    },
    mounted() {
        this.getAccountSetting()
    },
    methods:{
        @confirm("提示", "是否确认保存?")
        async paySave() {
            this.payFormData = this.payFormData.map(item => ({
                ...item,
                gather_supply_id: this.gather_supply_id
            }))
            const {code, msg} = await createSupplyAccount(this.payFormData)
            if (code === 0) {
                this.$message.success(msg)
            }
        },
        async getAccountSetting() {
            const {code, data} = await getAccount({gather_supply_id:this.gather_supply_id})
            if (code === 0) {
                this.payFormData = data
            }
        },
    }
}
</script>
<style scoped lang="scss">
p.title-p {
  font-size: 16px;
  font-weight: bold;
}

.el-divider {
  margin: 15px 0;
}

::v-deep .el-input-number {
  .el-input__inner {
    width: 100%;
  }
}

p.hint-p {
  margin-top: 10px;
  font-size: 12px;
  color: #c0c4cc;
  line-height: 20px;
}
</style>
<!--
 * @Author: jwt <EMAIL>
 * @Date: 2022-06-01 20:59:28
 * @LastEditors: jwt <EMAIL>
 * @LastEditTime: 2022-06-07 11:45:27
 * @FilePath: \element-ui-demo\src\view\express-match\express-match-dialog.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <el-dialog
    :title="`${dialogTitle}关联关系`"
    :visible="visible"
    @open="onOpen"
    @close="onClose"
    @closed="onClosed"
  >
    <el-form :model="form" ref="ruleForm">
      <el-form-item prop="code" label="中台code" :label-width="formLabelWidth">
        <el-input
          v-model="form.code"
          autocomplete="off"
          class="w400"
          size="small"
          :disabled="true"
        ></el-input>
      </el-form-item>
      <el-form-item prop="code" label="中台快递名称" :label-width="formLabelWidth">
        <el-select
          v-model="form.code"
          filterable
          placeholder="请选择中台快递"
          class="w400"
          size="small"
        >
          <template v-for="item in shippingList">
            <el-option :label="item.name" :value="item.code" :key="item.name">
            </el-option>
          </template>
        </el-select>
      </el-form-item>
      <el-form-item prop="cloud_code" label="云仓code" :label-width="formLabelWidth">
        <el-input
          v-model="form.cloud_code"
          autocomplete="off"
          class="w400"
          size="small"
          :disabled="true"
        ></el-input>
      </el-form-item>
      <el-form-item prop="cloud_code" label="云仓快递名称" :label-width="formLabelWidth">
        <el-select
          v-model="form.cloud_code"
          filterable
          placeholder="请选择云仓快递"
          class="w400"
          size="small"
        >
          <template v-for="item in deliverList">
            <el-option :label="item.name" :value="item.code" :key="item.name"></el-option>
          </template>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onClose">取 消</el-button>
      <el-button type="primary" @click="onSubmit">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createMatching, updateMatching } from '@/api/expressMatch.js'
const PAGE_STATUS = {
  CREATE: 0,
  EDIT: 1
}
export default {
  name: 'ExpressMatchDialog',
  props: {
    // 弹窗显示/隐藏
    visible: {
      type: Boolean,
      default: false
    },
    // 页面状态：新增/编辑
    pageStatus: {
      type: Number,
      default: PAGE_STATUS.CREATE
    },
    //云仓快递
    deliverList: {
      type: Array,
      default: () => []
    },
    //中台快递
    shippingList: {
      type: Array,
      default: () => []
    },
    propRow: {
      type: Object,
      default: () => { return {} }
    }
  },
  data () {
    return {
      formLabelWidth: '120px',
      form: {
        id: '',
        code: '', // 中台code
        name: '', // 中台名称
        cloud_code: '', // 云仓code
        cloud_name: '' // 云仓快递名称
      }
    }
  },
  computed: {
    // 页面标题
    dialogTitle () {
      switch (this.pageStatus) {
        case PAGE_STATUS.CREATE:
          return '新增'
        case PAGE_STATUS.EDIT:
        default:
          return '编辑'
      }
    },
    // 提交接口
    submitApi () {
      switch (this.pageStatus) {
        case PAGE_STATUS.CREATE:
          return createMatching
        case PAGE_STATUS.EDIT:
        default:
          return updateMatching
      }
    }

  },
  methods: {
    // 确定
    async onSubmit () {
      try {
        //中台
        this.form.name = this.shippingList.find(item => item.code === this.form.code).name
        //云仓
        this.form.cloud_name = this.deliverList.find(item => item.code === this.form.cloud_code).name
        const params = {...this.form}
        if(this.pageStatus === PAGE_STATUS.CREATE){
          delete params.id
        }      
        const { code, msg } = await this.submitApi(params)
        if(+code === 7){
          throw new Error(msg)         
        }
        this.$emit('update:visible', false)
        this.$emit('submitted')
        this.$message.success(`${this.dialogTitle}成功`)
      } catch (error) {
        this.$alert(error.message, { type:"error"})
      }
    },
    onOpen () {
      this.$nextTick(() => {
        switch (this.pageStatus) {
          case PAGE_STATUS.CREATE:
            break
          case PAGE_STATUS.EDIT:
            this.form.id = this.propRow.id
            this.form.code = this.propRow.code
            this.form.name = this.propRow.name
            this.form.cloud_code = this.propRow.cloud_code
            this.form.cloud_name = this.propRow.cloud_name
            break
        }
      })
    },
    onClose () {
      this.$emit('update:visible', false)
    },
    onClosed () {
      this.$refs.ruleForm.resetFields()
      this.form.name = ''
      this.form.cloud_name = ''
      this.form.id = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.w400 {width: 400px;}
</style>

<template>
  <div>
    <el-button type="primary" @click="openAddressDialog">添加售后地址</el-button>
    <el-table :data="tableData" class="mt25">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="地址" prop="info" align="center"></el-table-column>
      <el-table-column label="是否默认"  align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.is_default===0">否</p>
          <p v-else-if="scope.row.is_default===1" >是</p>
        </template>
      </el-table-column>
      <el-table-column label="操作"  align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="edit(scope.row)" >修改</el-button>
          <el-button type="text" @click="del(scope.row)"  class="color">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]" :style="{
              display: 'flex',
              justifyContent: 'flex-end',
              marginRight: '20px',
            }" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    <address-dialog ref="addressDialog" @reload="addressData"></address-dialog>
  </div>
</template>
<script>
import addressDialog from "./components/addressDialog";
import {getAddressList , deleteRefundAddress} from "@/api/cloud";

export default {
  name: "cloudAfterSaleAddressIndex",
  components:{
    addressDialog
  },
  data(){

    return{
      tableData:[],
      page: 1,
      pageSize: 10,
      total: 0,
      gather_supply_id:this.$ls.getGatherID(),
    }
  },
  mounted() {
    this.getAddressList()
  },
  methods:{
    //列表重新加载
    addressData(){
      this.page = 1
      this.getAddressList()
    },
    //修改
    edit(row){
     this.$refs.addressDialog.isShow=true
     this.$refs.addressDialog.setAddress(row)
    },
    //删除
    del(row){
      this.$confirm("确定删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deleteRefundAddress({gather_supply_id:this.gather_supply_id, id: row.id}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getAddressList()
          }
        })
      }).catch(() => {
      })
    },
    getAddressList(){
      let data={
        "page":this.page,
        "pageSize":this.pageSize,
        "gather_supply_id":this.gather_supply_id

      }
      getAddressList(data).then(res=>{
        if (res.code === 0) {
          this.tableData = res.data.list.list;
          this.total = res.data.list.count;
        } else {
          this.tableData = [];
          this.total = 0;
        }
      })
    },
    openAddressDialog(){
      this.$refs.addressDialog.isShow=true
    },
    handleCurrentChange(page) {
      this.page = page;
      this.getAddressList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.getAddressList();
    },
  }
};
</script>
<style>
.color{
  color: red;
}
</style>
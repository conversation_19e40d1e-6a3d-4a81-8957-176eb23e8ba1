<template>
    <m-card>
        <el-button type="primary" @click="openDialog">新增</el-button>
        <el-table class="mt25" :data="tableData">
            <el-table-column label="ID" prop="id" align="center"></el-table-column>
            <el-table-column label="时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column
                label="供应链名称"
                prop="name"
                align="center"
                show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="预存款余额" align="center">
                <template slot-scope="scope">
                    <div v-if="scope.row.hasOwnProperty('new_balance')">
                        <span>{{ scope.row.new_balance | formatF2Y }}</span>
                        <el-button
                            type="text"
                            class="ml10"
                            :loading="scope.row.loading"
                            @click="handleCheckBlance(scope.row, scope.$index)"
                        >
                            刷新
                        </el-button>
                    </div>
                    <el-button
                        v-else
                        type="text"
                        :loading="scope.row.loading"
                        @click="handleCheckBlance(scope.row, scope.$index)"
                    >
                        点击查看
                    </el-button>
                </template>
            </el-table-column>
            <el-table-column label="商品数量" align="center">
                <template slot-scope="scope">
                    {{ scope.row.goods_count }}
                </template>
            </el-table-column>
            <el-table-column label="订单数量" align="center">
                <template slot-scope="scope">
                    {{ scope.row.order_count }}
                </template>
            </el-table-column>
            <el-table-column label="订单金额" align="center">
                <template slot-scope="scope">
                    {{ scope.row.order_amount | formatF2Y }}
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="250" fixed="right">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        @click.native="
                            toTarget('/layout/cloudIndexManage/cloudSupplySetting', scope.row)
                        "
                        style="padding: 0 !important;margin-left: 10px !important;"
                    >
                        配置
                    </el-button>

                    <el-button
                        type="text"
                        @click="jumpCloud(scope.row.id)"
                        v-if="scope.row.category.key === 'stbz'"
                        style="padding: 0 !important;margin-left: 10px !important;"
                    >
                        云仓
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{ float: 'right', padding: '20px' }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <cloudSupplyDialog ref="cloudSupplyDialog" @reload="fetch"></cloudSupplyDialog>
    </m-card>
</template>
<script>
import { getList, getBalance } from '@/api/gatherSupply';
import cloudSupplyDialog from './cloudSupplyDialog';
export default {
    name: 'cloudSupplyManage',
    components: { cloudSupplyDialog },
    data() {
        return {
            page: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
        };
    },
    mounted() {
        this.fetch();
    },
    methods: {
        openDialog() {
            this.$refs.cloudSupplyDialog.init();
        },
        handleCurrentChange(page) {
            this.page = page;
            this.fetch();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.fetch();
        },
        // 跳转至云仓
        jumpCloud(id) {
            this.$ls.setGatherID(id);
            this.$router.push({
                name: 'cloudManageIndex',
                query: {
                    gather_supply_id: id,
                },
            });
        },
        // 点击查看预存款余额
        async handleCheckBlance(row, index) {
            row.loading = true;
            this.$set(this.tableData, index, row);
            let res = await getBalance({ id: row.id });
            row.loading = false;
            if (res.code === 0) {
                row.new_balance = res.data ?? '';
                this.$set(this.tableData, index, row);
            }
        },
        async fetch() {
            let params = {
                category_id: 1,
                page: this.page,
                pageSize: this.pageSize,
            };
            const { code, data } = await getList(params);
            if (code === 0) {
                this.tableData = data.list;
                this.total = data.total;
            }
        },
        toTarget(name, row) {
            this.$router.push({ path: name, query: { id: row.id, key: row.category.key } });
        },
    },
};
</script>
<style lang="scss" scoped></style>

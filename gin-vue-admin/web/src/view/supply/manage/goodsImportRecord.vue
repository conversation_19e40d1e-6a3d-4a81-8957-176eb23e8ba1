<!-- 供应链管理 -->
<template>
    <m-card>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="时间" align="center">
                <template slot-scope="scope"
                    >{{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column
                label="批次"
                prop="batch"
                align="center"
                show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
                label="操作人"
                prop="sys_user.userName"
                align="center"
            >
            </el-table-column>
            <el-table-column
                label="预计数量"
                prop="estimated_quantity"
                align="center"
            >
            </el-table-column>
            <el-table-column
                label="重复数量"
                prop="repeat_quantity"
                align="center"
            >
            </el-table-column>
            <el-table-column
                label="完成数量"
                prop="completed_quantity"
                align="center"
            >
            </el-table-column>

            <el-table-column label="导入类型" prop="status" align="center">
                <template slot-scope="scope">
                    <div v-if="scope.row.status == 2">重复导入</div>
                    <div v-else>正常</div>
                </template>
            </el-table-column>

            <el-table-column
                label="运行状态"
                prop="completion_status"
                align="center"
            >
                <template slot-scope="scope">
                    <div v-if="scope.row.completion_status == 1">已完成</div>
                    <div v-else-if="scope.row.completion_status == 2">失败</div>
                    <div v-else>导入中</div>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="retry(scope.row.id)" style="padding: 0 !important;margin-left: 10px !important;"
                        >重试</el-button
                    >
                    <el-button v-if="scope.row.failed_reason" type="text" @click="openErrDialog(scope.row.failed_reason)" style="padding: 0 !important;margin-left: 10px !important;">失败原因</el-button>
                    <el-button
                        type="text"
                        @click="
                            openImportGoodsList(scope.row.id, scope.row.source,scope.row.batch)
                        "
                        style="padding: 0 !important;margin-left: 10px !important;"
                        >详情</el-button
                    >
                    <el-button
                        class="color-red"
                        type="text"
                        @click="deleteRecord(scope.row.id)"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{ float: 'right', padding: '20px' }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <el-dialog
            title="新增"
            :visible.sync="openOff"
            :append-to-body="true"
            :lock-scroll="false"
            @close="clearInfo"
            width="30%"
        >
            <div>
                <el-form label-width="100px">
                    <el-row :gutter="10">
                        <el-col>
                            <el-form-item label="供应链名称:">
                                <el-input
                                    placeholder="请输入"
                                    v-model="supply_name"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="createdSupply" type="primary"
                    >确 定</el-button
                >
                <el-button @click="clearInfo">取 消</el-button>
            </span>
        </el-dialog>
        <importGoodsList ref="importGoodsList"></importGoodsList>
    </m-card>
</template>

<script>
import {
    createSupply,
    getSupplyGoodsList,
    getImportRecordList,
    getCounts,
    deleteImportSupplyGoodsRecord,
    retryImportSupplyGoodsRecord,
} from '@/api/gatherSupply';
import importGoodsList from './components/importGoodsList';
import { confirm } from '@/decorators/decorators';

export default {
    name: 'SupplyManageIndex',
    components: { importGoodsList },
    data() {
        return {
            searchInputWidth: '150px',
            searchName: '',
            page: 1,
            openOff: false,
            pageSize: 10,
            supply_name: '',
            total: 0,
            tableData: [],
            tableGoodsData: [],
        };
    },

    mounted() {
        this.getList();
    },
    methods: {
        openErrDialog(msg){
            this.$alert(msg, '失败原因', {
                confirmButtonText: '确定',
                callback: action => {
                    this.$message({
                        type: 'info',
                        message: `action: ${ action }`
                    });
                }
            });
        },
        // 重试
        @confirm('提示', '确定重试?')
        async retry(id) {
            const { code, msg } = await retryImportSupplyGoodsRecord({ id });
            if (code === 0) {
                this.$message.success(msg);
                this.getList();
            }
        },

        async getSupplyGoodsLIst(id) {
            let data = {
                id: id,
                page: this.page,
                pageSize: this.pageSize,
            };
            let res = await getSupplyGoodsList(data);

            if (res.code === 0) {
                /* this.$refs.importGoodsList.tableData = res.data.list;
                this.$refs.importGoodsList.total = res.data.total; */
                this.$refs.importGoodsList.importId = id;
                this.tableGoodsData = res.data.list;
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg,
                });
            }
        },
        // 打开导入商品列表
        openImportGoodsList(id, source,batch) {
            // this.getSupplyGoodsLIst(id)

            this.$refs.importGoodsList.isShow = true;
            this.$refs.importGoodsList.importId = id;
            this.$refs.importGoodsList.source = source;
            this.$refs.importGoodsList.batch = batch;
            this.$refs.importGoodsList.getSuccessGoodsList();
        },
        clearInfo() {
            this.supply_name = '';
            this.openOff = false;
        },

        async deleteRecord(id) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            })
                .then(() => {
                    let data = {
                        id: id,
                    };

                    deleteImportSupplyGoodsRecord(data).then((res) => {
                        if (res.code === 0) {
                            this.$message({
                                type: 'success',
                                message: res.msg,
                            });
                            this.getList();
                        } else {
                            this.$message({
                                type: 'error',
                                message: res.msg,
                            });
                        }
                    });
                })
                .catch(() => {});
        },

        async createdSupply() {
            if (!this.supply_name) {
                this.$message.error('请输入供应链名称');
                return false;
            }
            let data = {
                name: this.supply_name,
            };

            let res = await createSupply(data);
            if (res.code === 0) {
                this.$message({
                    type: 'success',
                    message: res.msg,
                });
                this.clearInfo();
                this.getList();
            } else {
                this.$message({
                    type: 'error',
                    message: res.msg,
                });
            }
        },
        toTarget(name, id) {
            this.$router.push({ path: name, query: { id: id } });
        },

        handleclick() {
            this.openOff = true; //默认页面不显示为false,点击按钮将这个属性变成true
        },

        async getCount() {
            let res = await getCounts();
            if (res.code === 0) {
                console.log(res);

                this.order_amount = res.data['amount_count'];
                this.goods_count = res.data['goods_count'];
                this.order_count = res.data['order_count'];
            }
        },

        async getList() {
            let data = {
                page: this.page,
                pageSize: this.pageSize,
            };
            let res = await getImportRecordList(data);
            if (res.code === 0) {
                this.tableData = res.data.list;
                this.total = res.data.total;
            }
        },
        searchSubmit() {
            this.getList();
            console.log('ddd', this.searchName);
        },
        // search input 获取焦点
        searchInputFocus() {
            this.searchInputWidth = '300px';
        },
        // search input 失去焦点
        searchInputBlur() {
            this.searchInputWidth = '150px';
        },
        fetch() {},
        handleCurrentChange(page) {
            this.page = page;
            this.getList();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.getList();
        },
    },
};
</script>
<style lang="scss" scoped>
@import '@/style/base.scss';

.search-input {
    transition: width 0.25s linear;
}
</style>

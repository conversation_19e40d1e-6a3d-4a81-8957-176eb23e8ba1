<template>
  <el-dialog
      :title="formData.id ? '编辑' : '新增'"
      :visible="isShow"
      width="700px"
      :before-close="handleClose">
    <el-form ref="form" :rules="rules" :model="formData" label-width="90px">
      <el-form-item label="活动名称:" prop="title">
        <el-input v-model="formData.title" placeholder="请输入"></el-input>
      </el-form-item>
<!--      <el-form-item label="活动分类:" prop="category_id">-->
<!--        <el-select v-model="formData.category_id" class="w100" clearable filterable>-->
<!--          <el-option :label="item.name" :value="item.value" v-for="item in supplyOptions">-->
<!--          </el-option>-->
<!--        </el-select>-->
<!--      </el-form-item>-->
        <el-form-item label="联盟平台:" prop="type">
          <el-select v-model="formData.type" class="w100" clearable>
            <el-option :label="item.name" :value="item.value" v-for="item in allianceConditions"></el-option>
          </el-select>
        </el-form-item>
      <el-form-item label="第三方ID:" prop="activity_id">
        <el-input v-model="formData.activity_id" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="活动主图:" prop="image">
        <el-upload
            class="logo-upload"
            :show-file-list="false"
            :action="`${$path}/fileUploadAndDownload/upload`"
            :headers="{'x-token':token}"
            :on-success="handleLogoSuccess"
            :before-upload="$fn.beforeAvatarUpload"
            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF"
        >
          <img v-if="formData.image" :src="formData.image" class="avatar">
          <i v-else class="el-icon-plus avatar-uploader-icon"></i>
        </el-upload>
        <p class="color-grap">建议尺寸: 630*390,或正方形图片</p>
      </el-form-item>
      <el-form-item label="结算节点:" prop="settle_type">
        <el-select v-model="formData.settle_type" class="w100" clearable filterable>
          <el-option :label="item.name" :value="item.value" v-for="item in jiesuanOptins">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结算日期:" prop="settle_time" v-if="formData.settle_type != 2  ">
        <el-select v-model="formData.settle_time" class="w100" clearable filterable>
          <el-option :label="item.name" :value="item.value" v-for="item in supplyOptions">
          </el-option>
        </el-select>
      </el-form-item>
        <el-form-item label="到期时间:" prop="expire_time">
            <el-date-picker
                    value-format="timestamp"
                    v-model="formData.expire_time"
                    type="datetime"
                    placeholder="选择日期时间">
            </el-date-picker>
        </el-form-item>
      <el-form-item label="佣金比例:" prop="commission_rate">
        <el-input v-model="formData.commission_rate" placeholder="请输入"></el-input>
      </el-form-item>
<!--      <el-form-item label="是否推荐:" prop="isRecommend">-->
<!--        <el-switch v-model="formData.isRecommend"></el-switch>-->
<!--      </el-form-item>-->
      <el-form-item label="活动说明:" prop="description">
        <m-editor v-model="formData.description"></m-editor>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {updateActivity, createActivity, findBrand} from "@/api/pushAlliance";
import {mapGetters} from "vuex";
export default {
  name: "activityDialog",
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  data() {
    return {
      formData: {
        id: null,
        title: "", // 活动名称
        // category_id: "", // 活动分类
        type:"", //联盟平台
        activity_id: "", //第三方活动ID
        image: "",//活动主图
        settle_type: "", //结算节点
        settle_time: 0, //结算日期
        commission_rate:'', //佣金比例
        description: "", // 描述
        expire_time: null
      },
      supplyOptions:[
        {name:"1",value:1},
        {name:"2",value:2},
        {name:"3",value:3},
        {name:"4",value:4},
        {name:"5",value:5},
        {name:"6",value:6},
        {name:"7",value:7},
        {name:"8",value:8},
        {name:"0",value:9},
        {name:"10",value:10},
        {name:"11",value:11},
        {name:"12",value:12},
        {name:"13",value:13},
        {name:"14",value:14},
        {name:"15",value:15},
        {name:"16",value:16},
        {name:"17",value:17},
        {name:"18",value:18},
        {name:"19",value:19},
        {name:"20",value:20},
        {name:"21",value:21},
        {name:"22",value:22},
        {name:"23",value:23},
        {name:"24",value:24},
        {name:"25",value:25},
        {name:"26",value:26},
        {name:"27",value:27},
        {name:"28",value:28},
        {name:"29",value:29},
        {name:"30",value:30},
        {name:"31",value:31},

      ],
      //结算
      jiesuanOptins:[
        {name:"月结",value:1},
        {name:"订单完成",value:2},
      ],
      //联盟平台
      allianceConditions:[
        {name:"美团",value:"meituan"},
        {name:"滴滴",value:"didi"},
        {name:"饿了么",value:"eleme"},
      ],
      isShow: false,
      rules: {
        title: {required: true, message: "请输入活动名称", trigger: "blur"}
      }
    }
  },
  methods: {
    async init(id) {
      this.isShow = true
      const {code, data} = await findBrand({id})
      if(code === 0){
        this.formData = this.$fn.deepClone(data.rebrands)
      }
    },
    confirm() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return false;
        let res = {}
          let params = {
            ...this.formData
          }
          if(this.formData.expire_time){
              params.expire_time = parseInt(this.formData.expire_time / 1000)
          }
        if (this.formData.id) {
          res = await updateActivity(params)
        } else {
          res = await createActivity(params)
        }
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.$emit("reload")
          this.handleClose()
        }
      })
    },
    handleClose() {
      try {
        this.$refs.form.activityManagementList()
      } catch {

      } finally {
        this.isShow = false
        this.formData = {
          id: null,
          title: "", // 活动名称
          // category_id: "", // 活动分类
          type: "", //联盟平台
          activity_id: "", //第三方活动ID
          image: "",//活动主图
          settle_type: "", //结算节点
          settle_time: "", //结算日期
          commission_rate:0, //佣金比例
          description: "", // 描述
          expire_time: null
        }
      }

    },
    handleLogoSuccess(res) {
      if (res.code === 0) {
        this.formData.image = res.data.file.url;
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeLogoUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
      }
      return isLt10M;
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .logo-upload {
  .el-upload {
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 1px dashed #d9d9d9;

    img {
      width: 100px;
      height: 100px;
    }
  }
}
.number-text-left{
  width:100%;
}
.dialog-footer {
  margin-top: 25px;
}
</style>
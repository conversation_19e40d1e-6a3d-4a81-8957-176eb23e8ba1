<template>
  <el-container>
    <el-main>
      <el-form ref="form" :model="form" :rules="formRules" label-width="100px" style="width: 700px;">
        <h3>数据库配置</h3>
        <el-row>
          <el-col :span="12">
            <el-form-item label="主机名" prop="db_host">
              <el-input v-model="form.db_host"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="db_port">
              <el-input v-model.number="form.db_port"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="db_username">
              <el-input v-model="form.db_username"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="db_password">
              <el-input v-model="form.db_password" type="password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据库名称" prop="db_name">
              <el-input v-model="form.db_name" placeholder="如果数据库不存在, 需提前创建"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <h3>管理员账号配置</h3>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账号" prop="admin_username">
              <el-input v-model="form.admin_username"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="密码" prop="admin_password">
              <el-input v-model="form.admin_password" type="password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="确认密码" prop="confirm_admin_password">
              <el-input v-model="form.confirm_admin_password" type="password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="submit()">安装</el-button>
        </el-form-item>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
import {installStore,} from "@/api/install";

export default {
  name: 'index',
  data() {
    return {
      form: {
        db_host: '127.0.0.1',
        db_port: 3306,
        db_username: '',
        db_password: '',
        db_name: '',
        admin_username: '',
        admin_password: '',
        confirm_admin_password: '',
      },
      formRules: {
        db_host: [
          {required: true, message: '请输入数据库主机名', trigger: 'blur'}
        ],
        db_port: [
          {type: 'number', required: true, message: '请输入数据库端口', trigger: 'blur'}
        ],
        db_username: [
          {required: true, message: '请输入数据库用户名', trigger: 'blur'}
        ],
        db_password: [
          {required: true, message: '请输入数据库密码', trigger: 'blur'}
        ],
        db_name: [
          {required: true, message: '请输入数据库名称', trigger: 'blur'}
        ],
        admin_username: [
          {required: true, message: '请输入管理员账号', trigger: 'blur'}
        ],
        admin_password: [
          {required: true, message: '请输入管理员密码', trigger: 'blur'},
          {min: 6, message: '长度至少6个字符', trigger: 'blur'}
        ],
        confirm_admin_password: [
          {required: true, message: '请再次输入管理员密码', trigger: 'blur'},
          {min: 6, message: '长度至少6个字符', trigger: 'blur'}
        ]
      },
      default_ports: {
        'mysql': 3306,
      }
    }
  },
  methods: {
    submit() {
      this.$refs['form'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.save()
      })
    },
    async save() {
      const res = await installStore(this.form)
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "配置成功，请重启服务器",
          showClose: true,
        });
      } else {
        this.$message({
          type: "error",
          message: res.msg,
          showClose: true,
        });
      }
    }
  }
}
</script>

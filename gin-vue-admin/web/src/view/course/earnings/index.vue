<template>
  <m-card>
    <el-form class="search-term" :model="searchInfo" label-width="120px" inline>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.lecturer_name" class="line-input" clearable>
              <span slot="prepend">讲师名称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.uid" class="line-input" clearable>
              <span slot="prepend">会员ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.mobile" class="line-input" clearable>
              <span slot="prepend">会员昵称/手机号</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >状态</span>
              </div>
              <el-select v-model="searchInfo.status" class="w100">
                <el-option label="全部" value=""></el-option>
                <el-option label="已分成" value="1"></el-option>
                <el-option label="未分成" value="2"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.curriculum_name" class="line-input" clearable>
              <span slot="prepend">课程名称</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.curriculum_id" class="line-input" clearable>
              <span slot="prepend">课程ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.order_sn" class="line-input" clearable>
              <span slot="prepend">订单号</span>
          </el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
        <el-button @click="reSearch" type="text">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <div class="mt25 search-term f fac">
      <p>总条数:{{ total }}</p>
      <p class="ml10">累计订单金额: ￥{{resData.total_order_amount | formatF2Y}}</p>
      <p class="ml10">累计分成金额:￥{{resData.total_amount | formatF2Y}}</p>
      <p class="ml10">已结算分成:￥{{resData.settled | formatF2Y}}</p>
      <p class="ml10">未结算分成:￥{{resData.unsettled | formatF2Y}}</p>
    </div>
    <el-table class="mt25" :data="tableData">
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="讲师名称" prop="lecturer.lecturer_name" align="center"></el-table-column>
      <el-table-column label="会员信息" align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.lecturer">
            {{
              scope.row.lecturer.user_info.nickname ? scope.row.lecturer.user_info.nickname : scope.row.lecturer.user_info.username
            }}</p>
          <p v-if="scope.row.lecturer">
            {{
              scope.row.lecturer.user_info.mobile ? scope.row.lecturer.user_info.mobile : scope.row.lecturer.user_info.username
            }}</p>
        </template>
      </el-table-column>
      <el-table-column label="课程信息" align="center">
        <template slot-scope="scope">
          <div class="f" v-if="scope.row.curriculum">
            <m-image :src="scope.row.curriculum.curriculum_img" :style="{minWidth:'80px',minHeight:'80px',maxWidth:'80px',maxHeight:'80px'}"></m-image>
            <p class="ml10">{{ scope.row.curriculum.curriculum_name }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="订单号" prop="order_sn" align="center"></el-table-column>
      <el-table-column label="订单金额" align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.order">￥{{ scope.row.order.amount | formatF2Y }}</p>
        </template>
      </el-table-column>
      <el-table-column label="分成金额" align="center">
        <template slot-scope="scope">
          <p v-if="scope.row.curriculum">￥{{ scope.row.curriculum.reward | formatF2Y }}</p>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | statusFormat }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100,200]"
        :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes,prev, pager, next, jumper"
    >
    </el-pagination>
  </m-card>
</template>
<script>
import infoList from "@/mixins/infoList";
import {findLecturerAward} from "@/api/curriculum";

export default ({
  name: "earnings",
  mixins: [infoList],
  components: {},
  filters: {
    statusFormat(status) {
      let s = "未分成"
      if (status === 1) {
        s = "已分成"
      }
      return s;
    }
  },
  data() {
    return {
      listApi: findLecturerAward
    };
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    search() {
      this.searchInfo.page = 1
      if(this.searchInfo.uid){
        this.searchInfo.uid = parseInt(this.searchInfo.uid)
      }
      if(this.searchInfo.curriculum_id){
        this.searchInfo.curriculum_id = parseInt(this.searchInfo.curriculum_id)
      }
      this.getTableData(1)
    },
    reSearch() {
      this.searchInfo = {}
    }
  },
})
</script>
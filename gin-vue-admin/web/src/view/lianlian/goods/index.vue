<template>
  <m-card>
    <el-form :model="searchInfo" ref="from" label-width="90px" class="search-term" inline>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.id" class="line-input" clearable>
            <span slot="prepend">商品ID</span>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.title" class="line-input" clearable>
            <span slot="prepend">商品名称</span>
        </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >商品状态</span>
              </div>
              <el-select v-model="goodsStatus" class="w100" clearable>
                <el-option v-for="item in goodsStatusList" :key="item.id" :label="item.name"
                          :value="item.value">
                </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >所在城市</span>
              </div>
              <el-select v-model="searchInfo.goodsCity" class="w100" clearable  filterable>
                <el-option v-for="item in goodsCityList" :key="item.city_id" :label="item.city_name"
                          :value="item.city_id">
                </el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >商品排序</span>
              </div>
              <el-select v-model="searchInfo.sortMode" class="w100" filterable clearable>
                <el-option label="商品ID" value="id"></el-option>
              </el-select>
          </div>
      </el-form-item>
      <el-form-item label="">
        <div class="line-input" >
          <el-select v-model="searchInfo.sortType" class="w100" filterable clearable>
            <el-option label="升序" value="0"></el-option>
            <el-option label="降序" value="1"></el-option>
          </el-select>
          </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search">搜索</el-button>
      </el-form-item>
      <el-form-item>
        <el-button @click="exTable">导出</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="searchDeleteAll">删除所有筛选商品</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="text" @click="reSearch">重置搜索条件</el-button>
      </el-form-item>
    </el-form>
    <el-tabs v-model="tabGoodsStatus" @tab-click="handleTabsClick" type="card" class="mt25 goods-tabs">
      <el-tab-pane v-for="item in goodsStatusList" :key="item.id" :label="item.name" :name="item.value">
      </el-tab-pane>
    </el-tabs>

    <div class="table-top">
      <el-button @click="batchPutaway()" v-if="[0,2].includes(searchInfo.filter)">批量上架</el-button>
      <el-button @click="batchSoldOut()" v-if="[0,1,3].includes(searchInfo.filter)">批量下架</el-button>
      <el-button slot="reference" @click="batchDeleteDialog()">批量删除</el-button>
<!--      <el-button @click="onOpenBatch()">批量分类</el-button>-->
      <el-button @click="onCheckedHot()">批量热卖</el-button>
      <el-button @click="onCheckedPromotion()">批量促销</el-button>
      <el-button @click="onCheckedNew()">批量新品</el-button>
    </div>

    <el-table
        :data="tableData"
        style="width: 100%"
        class="mt25"
        @selection-change="handleSelectionChange">
      <el-table-column type="expand" width="25" align="center">
        <template slot-scope="scope" >
          <el-table
              :data="tableData"
              style="width: 100%">
            <el-table-column label="产品名称" prop="onlyName"></el-table-column>
            <el-table-column label="封面图片">
              <template slot-scope="scope">
                <m-image style="width: 60px;height:60px" :src="scope.row.faceImg">
                </m-image>
              </template>
            </el-table-column>
            <el-table-column label="产品主标题" prop="productTitle"></el-table-column>
            <el-table-column label="产品副标题" prop="title"></el-table-column>
            <el-table-column label="产品简介" prop="shareText"></el-table-column>
            <el-table-column label="抢购开始时间">
              <template slot-scope="scope">
                {{scope.row.beginTime/1000 | formatDate}}
              </template>
            </el-table-column>
            <el-table-column label="抢购结束时间">
              <template slot-scope="scope">
                {{scope.row.endTime/1000 | formatDate}}
              </template>
            </el-table-column>
            <el-table-column label="渠道库存" prop="channelStock"></el-table-column>
            <el-table-column label="渠道销量" prop="channelSaleAmount"></el-table-column>
            <el-table-column label="预约方式">
              <template slot-scope="scope">
                {{scope.row.bookingType |formatShipments}}
              </template>
            </el-table-column>
            <el-table-column label="预约须知" prop="bookingText"></el-table-column>
          </el-table>
        </template>

      </el-table-column>
      <el-table-column type="selection" width="55" align="center"></el-table-column>
      <el-table-column width="80" align="center"
          prop="productId"
          label="商品ID"
        >
      </el-table-column>
      <el-table-column
          label="图片"
          align="center"
         >
        <template slot-scope="scope">
          <m-image style="width: 60px;height:60px" :src="scope.row.product.image_url">
          </m-image>
        </template>
      </el-table-column>
      <el-table-column width="280" align="center"
          prop="title"
          label="商品标题">
      </el-table-column>

      <el-table-column align="center" show-overflow-tooltip width="150">
        <template slot="header">
          <p>导入时间</p>
          <p>更新时间</p>
        </template>
        <template slot-scope="scope">
          <p>{{ scope.row.created_at | formatDate }}</p>
          <p>{{ scope.row.updated_at  | formatDate }}</p>
        </template>
      </el-table-column>

      <el-table-column align="center" show-overflow-tooltip width="160">
        <template slot="header">
          <p>供货价</p>
          <p>成本价</p>
          <p>零售价</p>
        </template>
        <template slot-scope="scope">
          <p>￥{{ scope.row.product.minPrice | formatF2Y }} -￥{{ scope.row.product.maxPrice | formatF2Y }}</p>
          <p>￥{{ scope.row.min_cost  | formatF2Y }} -￥{{ scope.row.max_cost | formatF2Y }} </p>
          <p>￥{{ scope.row.min_retail | formatF2Y }} -￥{{ scope.row.max_retail | formatF2Y }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip width="110">
        <template slot="header">
          <p>利润率</p>
        </template>
        <template slot-scope="scope">
          <p>{{ scope.row.product.profit_rate | formatF2Y }}%</p>
        </template>
      </el-table-column>
      <el-table-column label="库存" align="center" prop="product.stock" show-overflow-tooltip></el-table-column>
      <el-table-column label=销量 align="center" prop="product.sales" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" width="150">
        <template slot="header">
          <p>售卖截止时间</p>
        </template>
        <template slot-scope="scope"  >
          <!-- <p>{{ scope.row.beginTime/1000 | formatDate }}</p>
          <p>{{ scope.row.endTime/1000 | formatDate }}</p> -->
          <p>{{ scope.row.beginTime }}</p>
          <p>{{ scope.row.endTime }}</p>
        </template>
      </el-table-column>
<!--      <el-table-column label="供应渠道" align="center" show-overflow-tooltip>-->
<!--        <template slot-scope="scope">-->
<!--          <span v-if="scope.row.supplier_id > 0">{{ scope.row.supplier.name }}</span>-->
<!--          <span v-else-if="scope.row.gather_supply_id > 0">{{ scope.row.gather_supply.name }}</span>-->
<!--          <span v-else-if="scope.row.supplier_id === 0 && scope.row.gather_supply_id === 0">自营</span>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column align="center"
          label="状态">
        <template slot="header">
          <p>上下架状态</p>
          <p>锁定状态</p>
        </template>
        <template slot-scope="scope">
          <p>
            <el-switch v-model="scope.row.product.is_display" :active-value="1" :inactive-value="0"
                       @change="handleStatus(scope.row.product,'is_display')">
            </el-switch>
          </p>
          <p>
            <el-switch v-model="scope.row.product.status_lock" :active-value="1" :inactive-value="0"
                       @change="handleStatus(scope.row.product,'status_lock')">
            </el-switch>
          </p>
          <!--                <el-switch v-model="scope.row.is_display" :active-value="1" :inactive-value="0"
                              :inactive-text="switchStatusStr(scope.row.is_display,scope.row.status_lock)"
                              @change="handleStateChange(scope.row,'is_display')">
                          </el-switch>-->
        </template>
      </el-table-column>
      <el-table-column label="操作" width="270" fixed="right" align="center">
        <template slot-scope="scope">
          <div class="f fac fjc">
            <el-button type="text" @click="copyLink(scope.row.product.id)" style="padding: 0 !important;margin-left: 10px !important;">复制链接</el-button>
            <el-button type="text" @click.native="edit( '/layout/lianlianIndex/lianAddGoods',scope.row.product)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
<!--            <el-button type="text" @click="copyProductByid(scope.row.id)">复制</el-button>-->
<!--            <el-button type="text" slot="reference" class="color-red" @click="delGoods(scope.row.product.id)">删除
            </el-button>-->
          </div>
<!--          <div class="checkbox-box">-->
<!--            <el-checkbox v-model="scope.row.product.is_hot" size="mini" @change="handleStatus(scope.row.product,'is_hot')"-->
<!--                         :true-label="1" :false-label="0" label="热卖" border></el-checkbox>-->
<!--            <el-checkbox v-model="scope.row.product.is_promotion" size="mini"-->
<!--                         @change="handleStatus(scope.row.product,'is_promotion')" :true-label="1" :false-label="0"-->
<!--                         label="促销" border></el-checkbox>-->
<!--            <el-checkbox v-model="scope.row.product.is_new" size="mini" @change="handleStatus(scope.row.product,'is_new')"-->
<!--                         :true-label="1" :false-label="0" label="新品" border></el-checkbox>-->
<!--          </div>-->
        </template>
      </el-table-column>
    </el-table>

      <el-pagination
          background
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
<!--    <el-dialog title="批量分类" :visible.sync="categoryVisible">-->
<!--      <el-form :inline="true" :model="batchForm" ref="batchForm" :rules="rules">-->
<!--        <el-row :gutter="10">-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="一级分类:" prop="batch_category1_id">-->
<!--              <el-select-->
<!--                  v-model="batchForm.batch_category1_id"-->
<!--                  placeholder="请选择一级分类"-->
<!--                  class="w100"-->
<!--                  filterable-->
<!--                  clearable-->
<!--                  @change="handleClassiyfChange(2,batchForm.batch_category1_id)"-->
<!--              >-->
<!--                <el-option v-for="item in categoryList1" :key="item.id" :label="item.name" :value="item.id">-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="二级分类:" prop="batch_category2_id">-->
<!--              <el-select-->
<!--                  v-model="batchForm.batch_category2_id"-->
<!--                  placeholder="请选择二级分类"-->
<!--                  filterable-->
<!--                  clearable-->
<!--                  class="w100"-->
<!--                  @change="handleClassiyfChange(3,batchForm.batch_category2_id)"-->
<!--              >-->
<!--                <el-option v-for="item in categoryList2" :key="item.id" :label="item.name" :value="item.id">-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--          <el-col :span="8">-->
<!--            <el-form-item label="三级分类:" prop="batch_category3_id">-->
<!--              <el-select-->
<!--                  v-model="batchForm.batch_category3_id"-->
<!--                  placeholder="请选择三级分类"-->
<!--                  filterable-->
<!--                  clearable-->
<!--                  class="w100"-->
<!--              >-->
<!--                <el-option v-for="item in categoryList3" :key="item.id" :label="item.name" :value="item.id">-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--      </el-form>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button @click="onCloseCategory('batchForm')">取 消</el-button>-->
<!--        <el-button type="primary" @click="onSubmitCategory('batchForm')">确 定</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
  </m-card>
</template>

<script>
import {getProductList,upStatus,copyProduct,deleteProduct,batchDisplays,deleteProductByIds,batchChanges,exportProductList,getLocationList} from "@/api/lianlian";
import {deleteProductInfoList} from "@/api/goods";
// import {getBrandsList} from "@/api/brands";
// import {getClassify} from "@/api/classify";
// import {getSupplyList,getSupplierOptionList} from "@/api/order";
import zhLang from 'element-ui/lib/locale/lang/zh-CN';
import {confirm} from "@/decorators/decorators";
export default {
  name: "lianlianGoodsIndex",
  data(){
    return{
      page: 1,
      pageSize: 10,
      total: 0,
      // arketingChecked: "",//营销属性
      // arketingOptios: [
      //   {label: "全部", value: ""},
      //   {label: "热卖", value: "is_hot"},
      //   {label: "促销", value: "is_promotion"},
      //   {label: "新品", value: "is_new"}
      // ],
      is_hot: 0, // 批量热卖
      is_promotion: 0, // 批量促销
      is_new: 0, // 批量新品
      //品牌属性
      // brandsOptiosData: {
      //   name: "",
      //   brandsOptios: [],
      //   loading: false,
      //   page: 1,
      //   pageSize: 10,
      //   total: 0
      // },
      deleteVisible: false,
      categoryVisible:false,
      //批量分类类目
      batchForm:{
        batch_category1_id:null,
        batch_category2_id:null,
        batch_category3_id:null,
      },
      rules: {
        batch_category1_id: [
          { required: true, message: '请选择活动区域', trigger: 'change' }
        ],
        batch_category2_id: [
          { required: true, message: '请选择活动区域', trigger: 'change' }
        ],
        batch_category3_id: [
          { required: true, message: '请选择活动区域', trigger: 'change' }
        ],
      },
      // 类目1
      // categoryList1: [],
      // categoryList2: [],
      // categoryList3: [],
      tableData:[],
      // 多选选中的数据
      selectTables: [],
      goodsStatus: "0",
      tabGoodsStatus: "0",
      goodsStatusList: [
        {name: "全部", value: "0"},
        {name: "上架", value: "1"},
        {name: "下架", value: "2"},
        {name: "售罄", value: "3"},
      ],
      supplierList:[],//供应商
      supplyOptions:[],//供应链
      goodsCityList:[],//所在城市
      searchInfo:{
        id:null,
        // gather_supply_id: null,//供应链
        // supplier_id: null,   // 供应商
        sortMode:"",//排序方式
        sortType:null,//排序类型
        goodsCity:"",//所在城市
        filter:0,//上下架
        title:"",//商品名称
        category1_id:"",//一级分类
        category2_id:"",//二级分类
        category3_id:"",//三级分类
        maxPrice:"",//最高价
        minPrice:"",//最低价
        barcode:"",//条形码
        // brand_id:"",//品牌
        // status_lock:"",//锁定状态
      },
      // categoryList:[]
    }
  },
  filters:{
    // 预约方式
    formatShipments: function (status) {
      let name = ""
      switch (status) {
        case 0:
          name = "无需预约"
          break;
        case 1:
          name = "网址预约"
          break;
        case 2:
          name = "电话预约"
          break;
      }
      return name;
    },
  },
  mounted() {
    // this.getBrandsOptios()
    this.getProductList()
    // this.getSupply()
    // this.getCategory()
    // // 获取一级类目
    // getCategory(1, 0).then(r => {
    //   this.categoryList1 =JSON.parse(r.data.list)
    // })
    // 获取供应商
    // getSupplierOptionList().then(r => {
    //   this.supplierList = r.data.list
    // })
    // 获取城市列表
    this.getLocationList()

  },
  methods:{
    //城市接口
    getLocationList(){
      getLocationList().then(res =>{
       this.goodsCityList=res.data
      })
    },
    //批量操作验证
    batchHandleVerify() {
      if (this.selectTables.length === 0) {
        this.$message.warning("请选择要处理的数据");
        return false;
      }
      return true;
    },

    //批量上架
    batchPutaway() {
      if (!this.batchHandleVerify()) {
        return;
      }
      let ids = [];
      this.selectTables.forEach(item => {
        ids.push(item.product.id);
      })
      batchDisplays({value: 1, ids: ids}).then(r => {
        if (r.code === 0) {
          this.$message.success(r.msg);
          this.getProductList();
        }
      });
    },

    //批量下架
    batchSoldOut() {
      if (!this.batchHandleVerify()) {
        return;
      }
      let ids = [];
      this.selectTables.forEach(item => {
        ids.push(item.product.id);
      })
      batchDisplays({value: 0, ids: ids}).then(r => {
        if (r.code === 0) {
          this.$message.success(r.msg);
          this.getProductList();
        }
      });
    },

    //批量删除
    batchDeleteDialog() {
      if (!this.batchHandleVerify()) {
        return;
      }
      this.$confirm('商品删除请谨慎操作，确定删除？', '删除商品', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        this.batchDelete();
      }).catch(() => {
      });
    },
    batchDelete() {
      if (!this.batchHandleVerify()) {
        return;
      }
      let ids = [];
      this.selectTables.forEach(item => {
        ids.push(item.product.id);
      })
      deleteProductByIds({ids}).then(r => {
        if (r.code === 0) {
          this.$message.success(r.msg);
          this.getList();
        }
      })
    },
    // 批量分类
    // onSubmitCategory(formName){
    //   this.$refs[formName].validate((valid) => {
    //     if (valid) {
    //       this.categoryVisible = false
    //       this.batchCategory()
    //       this.$refs[formName].resetFields();
    //     } else {
    //       return false;
    //     }
    //   });
    //
    // },
    //关闭批量分类页
    onCloseCategory(formName){
      this.categoryVisible = false
      this.$refs[formName].resetFields();
    },
    // 批量分类 热卖 促销 新品
    batchCategory(){
      let ids = [];
      this.selectTables.forEach(item => {
        ids.push(item.product.id);
      })
      let params = {
        ids: ids,
        category1_id: this.batchForm.batch_category1_id,
        category2_id: this.batchForm.batch_category2_id,
        category3_id: this.batchForm.batch_category3_id,
        is_hot: this.is_hot,
        is_promotion: this.is_promotion,
        is_new: this.is_new,
      }
      batchChanges(params).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg);
          this.getProductList();
        }
      })
    },
   //打开批量分类
   //  onOpenBatch(){
   //    this.categoryVisible = true
   //    this.batchForm.batch_category1_id = null
   //    this.batchForm.batch_category2_id = null
   //    this.batchForm.batch_category3_id = null
   //  },
    //批量热卖
    onCheckedHot(){
      this.is_hot = 1
      this.is_promotion = 0
      this.is_new = 0
      this.batchCategory()
    },
    //批量分销
    onCheckedPromotion(){
      this.is_hot = 0
      this.is_promotion = 1
      this.is_new = 0
      this.batchCategory()
    },
    //批量新品
    onCheckedNew(){
      this.is_hot = 0
      this.is_promotion = 0
      this.is_new = 1
      this.batchCategory()
    },
    /**
     * 多选
     * @param val
     */
    handleSelectionChange(val) {
      this.selectTables = val;
    },
    /**
     * 列表商品状态拼装
     * @param v1 商品状态 1上架 0下架
     * @param v2 锁定该状态 1锁定 0未锁定
     * @returns {string}
     */
    switchStatusStr(v1, v2) {
      let s1, s2 = ""
      switch (v1) {
        case 0:
          s1 = "下架"
          break;
        case 1:
          s1 = "上架"
          break;
      }
      switch (v2) {
        case 0:
          s2 = "未锁定"
          break;
        case 1:
          s2 = "已锁定"
          break;
      }
      return s1 + s2;
    },
    //品牌分页
    // handleBrandPage(val) {
    //   this.brandsOptiosData.page = val
    //   this.getBrandsOptios()
    // },
    // 品牌搜索
    // remoteMethod(query) {
    //   this.brandsOptiosData.name = query
    //   this.brandsOptiosData.page = 1
    //   this.getBrandsOptios()
    // },
    // 获取品牌
    // async getBrandsOptios() {
    //   let params = {
    //     page: this.brandsOptiosData.page,
    //     pageSize: this.brandsOptiosData.pageSize
    //   }
    //   if (this.brandsOptiosData.name) params.name = this.brandsOptiosData.name
    //   this.brandsOptiosData.loading = true
    //   let res = await getBrandsList(params)
    //   this.brandsOptiosData.loading = false
    //   if (res.code === 0) {
    //     this.brandsOptiosData.brandsOptios = res.data.list
    //     this.brandsOptiosData.total = res.data.total
    //   }
    // },
    // 获取供应链
    // getSupply() {
    //   getSupplyList().then(res => {
    //     this.supplyOptions = res.data.list
    //   })
    // },
    // tabs切换
    handleTabsClick() {
      this.goodsStatus = this.tabGoodsStatus;
      switch (this.tabGoodsStatus) {
        case "0":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        case "1":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        case "2":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        case "3":
          this.searchInfo.filter = parseInt(this.goodsStatus);
          break;
        default:
          break;
      }
      this.page = 1;
      this.pageSize = 10;
      this.getProductList();
    },
    // 跳转至前端
    copyLink(id) {
      let link = ""
      if (location.hostname === "localhost") {
        link = location.protocol + "//localhost:9527/goodsDetail?goods_id=" + id
      } else {
        link = location.origin + "/goodsDetail?goods_id=" + id
      }
      this.$fn.copy(link)
    },
    // 编辑
    edit(name,row) {
      this.$router.push({path: name, query: {id:row.id}});
    },
    /**
     * 复制商品
     * @param id
     */
    copyProductByid(id){
      this.$confirm('确定要复制吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        copyProduct({id: id}).then(r => {
          if (r.code === 0) {
            this.$message.success("复制成功");
            this.getProductList();
          }
        })
      }).catch(() => {
      });
    },

    /**
     * 单个删除
     * @param row
     */
    delGoods(id) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(() => {
        deleteProduct({id: id}).then(r => {
          if (r.code === 0) {
            this.$message.success("删除成功");
            this.getProductList();
          }
        })
      }).catch(() => {
      });
    },
    // 热卖 促销 新品
    async handleStatus(row, name) {
      let params = {
        column: name,
        id: row.id,
        status: row[name]
      }
      let res = await upStatus(params)
      if (res.code === 0) {
        this.$message.success(res.msg)
      }
    },
    // 获取类目
    // handleClassiyfChange(level = 1, pid = "") {
    //   switch (level) {
    //     case 1:
    //       this.categoryList1 = []
    //       break;
    //     case 2:
    //       this.categoryList2 = []
    //       this.searchInfo.category2_id = ""
    //       this.categoryList3 = []
    //       this.searchInfo.category3_id = ""
    //       break;
    //     case 3:
    //       this.categoryList3 = []
    //       this.searchInfo.category3_id = ""
    //       break;
    //   }
    //   this.categoryList.forEach(item => {
    //     if (item.level === level) {
    //       this[`categoryList${level}`].push({
    //         ...item
    //       })
    //     }
    //   })
    //
    // },

    getProductList(){
      let data={}
      data.id=this.searchInfo.id
      data.page=this.page;
      data.pageSize=this.pageSize;
      data.filter=this.searchInfo.filter;
      data.title=this.searchInfo.title;
      data.category1_id=this.searchInfo.category1_id;
      data.category2_id=this.searchInfo.category2_id;
      data.category3_id=this.searchInfo.category3_id;
      data.maxPrice=this.searchInfo.maxPrice;
      data.minPrice=this.searchInfo.minPrice;
      data.barcode=this.searchInfo.barcode;
      // data.brand_id=this.searchInfo.brand_id;
      if (this.searchInfo.goodsCity !== "") {
        data.goodsCity = this.searchInfo.goodsCity
      }
      if (this.searchInfo.sortType !== "") {
        data.sortType = this.searchInfo.sortType
      }
      if (this.searchInfo.sortMode !== "") {
        data.sortMode = this.searchInfo.sortMode
      }
      // if (this.searchInfo.gather_supply_id !== "") {
      //   data.gather_supply_id = this.searchInfo.gather_supply_id
      // }
      // if (this.searchInfo.supplier_id !== "") {
      //   data.supplier_id = this.searchInfo.supplier_id
      // }
      // if (this.arketingChecked !== "") {
      //   data[this.arketingChecked] = 1
      // }
      // if (this.searchInfo.status_lock !== "") {
      //   data.status_lock = this.searchInfo.status_lock
      // }
       getProductList(data).then(res=>{
         if(res.code===0){
        this.tableData=res.data.list
           this.total=res.data.total
           zhLang.el.pagination.total = `共 {total} ${this.total / this.pageSize === 100 ? '+' : ''} 条`
        }else {
           this.tableList = [];
           this.total = 0;
         }
      })
    },
    // async getCategory() {
    //   let searchForm = {
    //     gather_supply_id:25,
    //   };
    //   let {data} = await getCategory(searchForm);
    //   this.categoryList =JSON.parse(data)
    //   this.handleClassiyfChange()
    // },
    // 导出
    exTable() {
      this.searchInfo.filter = parseInt(this.goodsStatus);//同步数据
      let data={}
      data.id=this.searchInfo.id
      data.page=this.page;
      data.pageSize=this.pageSize;
      data.filter=this.searchInfo.filter;
      data.title=this.searchInfo.title;
      data.category1_id=this.searchInfo.category1_id;
      data.category2_id=this.searchInfo.category2_id;
      data.category3_id=this.searchInfo.category3_id;
      data.maxPrice=this.searchInfo.maxPrice;
      data.minPrice=this.searchInfo.minPrice;
      data.barcode=this.searchInfo.barcode;
      // data.brand_id=this.searchInfo.brand_id;
      if (this.searchInfo.goodsCity !== "") {
        data.goodsCity = this.searchInfo.goodsCity
      }
      if (this.searchInfo.sortType !== "") {
        data.sortType = this.searchInfo.sortType
      }
      if (this.searchInfo.sortMode !== "") {
        data.sortMode = this.searchInfo.sortMode
      }
      // if (this.searchInfo.gather_supply_id !== "") {
      //   data.gather_supply_id = this.searchInfo.gather_supply_id
      // }
      // if (this.searchInfo.supplier_id !== "") {
      //   data.supplier_id = this.searchInfo.supplier_id
      // }
      // if (this.arketingChecked !== "") {
      //   data[this.arketingChecked] = 1
      // }
      // if (this.searchInfo.status_lock !== "") {
      //   data.status_lock = this.searchInfo.status_lock
      // }
      exportProductList(data).then(res => {
        if (res.code === 0) {
          window.open(this.$path + '/' + res.data.link)
          // window.location.href = this.$path + '/' + res.data.link
        }
      })
    },
    // 根据筛选条件删除所有商品
    @confirm("提示", "确定删除所有筛选出的商品吗?")
    searchDeleteAll() {
      this.searchInfo.filter = parseInt(this.goodsStatus);//同步数据
      let data={}
      data.id=this.searchInfo.id
      data.filter=this.searchInfo.filter;
      data.title=this.searchInfo.title;
      data.category1_id=this.searchInfo.category1_id;
      data.category2_id=this.searchInfo.category2_id;
      data.category3_id=this.searchInfo.category3_id;
      data.maxPrice=this.searchInfo.maxPrice;
      data.minPrice=this.searchInfo.minPrice;
      data.barcode=this.searchInfo.barcode;
      // data.brand_id=this.searchInfo.brand_id;
      // if (this.searchInfo.gather_supply_id !== "") {
      //   data.gather_supply_id = this.searchInfo.gather_supply_id
      // }
      // if (this.searchInfo.supplier_id !== "") {
      //   data.supplier_id = this.searchInfo.supplier_id
      // }
      // if (this.arketingChecked !== "") {
      //   data[this.arketingChecked] = 1
      // }
      // if (this.searchInfo.status_lock !== "") {
      //   data.status_lock = this.searchInfo.status_lock
      // }
      deleteProductInfoList(data).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.search()
        }
      })
    },
    //搜索
    search(){
      this.page=1
      this.tabGoodsStatus = this.goodsStatus;
      this.searchInfo.filter = parseInt(this.goodsStatus);//同步数据
      this.getProductList();
    },
    //重置
    reSearch(){
      this.goodsStatus = "0";
      this.tabGoodsStatus = "0";
      this.page=1;
      this.$refs.from.resetFields();
      this.searchInfo.sortType="";
      // this.arketingChecked = "";
      // this.brandsOptiosData = {
      //   name: "",
      //   brandsOptios: [],
      //   loading: false,
      //   page: 1,
      //   pageSize: 10,
      //   total: 0
      // }
      // this.remoteMethod()
      this.getProductList();
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.getProductList();
    },

    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.getProductList();
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.top-div {
  position: relative;
  width: 100%;

  .add-btn {
    position: absolute;
    right: 0;
    top: 0;
  }
}

.el-row {
  // padding: 0;

  .search-btn {
    background-color: #44d2b9;
    border-color: #44d2b9;
    color: white;
    margin-left: 15px;
    padding-left: 15px;
    padding-right: 15px;
  }

  .zih-span {
    display: inline-block;
    margin: 0 10px;
    color: #606266;
  }
}

.table-top {
  margin-top: 20px;
  margin-bottom: 20px;

  & > .el-checkbox.check-all {
    margin-right: 15px;
  }
}

::v-deep .el-switch {
  .el-switch__label {
    color: #666666;
  }
}

::v-deep .el-checkbox.zdy-checkbox {
  margin-right: 0;
  margin-left: 5px !important;

  &.is-bordered.is-checked {
    border-color: #44d2b9;

    .el-checkbox__input.is-checked {
      .el-checkbox__inner {
        background-color: #44d2b9;
        border-color: #44d2b9;
      }
    }

    .el-checkbox__label {
      color: #44d2b9;
    }
  }
}

/***************************** tabs部分 *******************************/
::v-deep .goods-tabs {
  .el-tabs__header {
    margin-bottom: 0px;

    .el-tabs__item {
      background-color: #f7f8fa;

      &.is-active {
        color: #303133;
        background-color: #ffffff;
      }

      &:hover {
        color: #303133;
      }
    }
  }
}

.checkbox-box {
  .el-checkbox {
    margin-right: 0;
  }
}
::v-deep .el-table .el-table__body { width: 100%; }
/* ::v-deep .el-pager{
  li.number:last-child{
    display: none;
  }
} */

</style>
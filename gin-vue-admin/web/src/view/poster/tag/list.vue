<template>
    <m-card>
        <el-form :model="searchInfo" class="search-term mt25" inline>
            <el-form-item label="" prop="title">
                <el-input
                    v-model="searchInfo.name"
                    placeholder="请输入"
                    clearable
                >
                    <span slot="prepend">标签名称</span>
                </el-input>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" style="width: auto;">
                    <div class="line-box">
                        <span >创建日期</span>
                    </div>
                    <time-card ref="time_card" @timeArr="timeArr"></time-card>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="resetSearch"
                    >重置搜索条件</el-button
                >
                <el-button type="primary" @click="clickTag">新增</el-button>
            </el-form-item>
        </el-form>
        <div class="search-term mt25">总数: {{ total }}</div>
        <el-table :data="tableData" class="mt25">
            <el-table-column
                label="添加日期"
                align="center"
            >
                <template slot-scope="scope">{{scope.row.created_at | formatDate}}</template>
            </el-table-column>
            <el-table-column
                label="标签名称"
                prop="name"
                align="center"
            ></el-table-column>
            <el-table-column
                label="海报数量"
                align="center"
                prop="poster_count"
            ></el-table-column>
            <el-table-column
                label="操作"
                align="center"
                width="200"
                fixed="right"
            >
                <template slot-scope="scope">
                    <div style="display: flex;justify-content: center;">
                        <el-button type="text" @click="edit(scope.row)" style="padding: 0 !important"
                            >编辑</el-button
                        >
                        <el-button type="text" @click="goPosterPage(scope.row.id)" style="padding: 0 !important"
                            >海报</el-button
                        >
                        <el-button
                            type="text"
                            class="color-red"
                            @click="del(scope.row)"
                            style="padding: 0 !important"
                            >删除</el-button
                        >
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>

        <el-dialog
            :title="tag_id == '' ? '添加标签' : '编辑标签'"
            :visible.sync="dialogVisible"
            width="500px"
            :show-close="false"
            center
        >
            <el-form label-width="80px">
                <el-form-item label="标签名称:">
                    <el-input v-model="tag_name" placeholder="请输入" style="width: 300px;"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="clickAddTag">{{tag_id == '' ? '添加' : '编辑'}}</el-button>
                <el-button @click="dialogVisible = false">取 消</el-button>
            </span>
        </el-dialog>
    </m-card>
</template>
<script>
import infoList from '@/mixins/infoList';
import timeCard from '@/view/poster/components/timeCard.vue';
import { formatTimeToStr } from "@/utils/date";
import {
    addTag,
    listTag,
    updateTag,
    deleteTag
} from '@/api/poster';
export default {
    name: 'posterTagList',
    components: { timeCard },
    mixins: [infoList],
    data() {
        return {
            listApi: listTag,
            dialogVisible: false,
            tag_name: "",
            tag_id: "",
        };
    },
    mounted() {
        this.getTableData();
    },
    methods: {
        // 搜索
        search() {
            this.page = 1;
            this.getTableData();
        },
        // 重置搜索条件
        resetSearch() {
            this.searchInfo = {};
            this.$refs.time_card.research();

        },
        timeArr(timeArr) {
            this.searchInfo.start_time =
                timeArr && timeArr.length && timeArr[0]
                    ? timeArr[0]
                    : '';
            this.searchInfo.end_time =
                timeArr && timeArr.length && timeArr[1]
                    ? timeArr[1]
                    : '';
        },
        clickTag() {
            this.dialogVisible = true;
        },
        async clickAddTag() {
            if(this.tag_id == "") {
                if(this.tag_name === "") {
                    this.$message.error("标签名不能为空")
                    return
                }
                let { code, data, msg } = await addTag({ name: this.tag_name})
                if(code == 0){
                    this.$message.success(msg)
                }
            } else {
                let { code, data, msg } = await updateTag({ name: this.tag_name , id: this.tag_id})
                if(code == 0){
                    this.$message.success(msg)
                }
            }
            this.dialogVisible = false;
            this.page = 1;
            this.getTableData();
        },
        edit(item) {
            this.tag_type = 2 
            this.dialogVisible = true;
            this.tag_name = item.name;
            this.tag_id = item.id;
        },
        
        // 删除
        del(item) {
            this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                let { code, data, msg } = await deleteTag({ id: item.id })
                if(code == 0){
                    this.$message.success(msg);
                    this.search();
                }
            }).catch(() => {
                this.$message({
                    type: 'info',
                    message: '已取消删除'
                });          
            });
        },
        goPosterPage(tag_id) {
            this.$router.push({
                path: "/layout/posterIndex/posterManageList",
                query: {
                    tag_id,
                },
            });
            // this.$_blank("/layout/posterIndex/posterManageList", {tag_id});
        }
    },
    filters: {
        formatDate: function (time) {
            if (time != null && time != "") {
                var date = new Date(time);
                return formatTimeToStr(date, "yyyy-MM-dd hh:mm:ss");
            } else {
                return "";
            }
        },
    },
};
</script>
<style scoped lang="scss"></style>

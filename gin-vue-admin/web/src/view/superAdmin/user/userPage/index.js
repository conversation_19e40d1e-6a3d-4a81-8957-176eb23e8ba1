// 获取列表内容封装在mixins内部  getTableData方法 初始化已封装完成
const path = process.env.VUE_APP_BASE_API;
import {
  getUserList,
  setUserAuthority,
  setUserInfo,
  register,
  deleteUser,
} from "@/api/user";
import { getAuthorityList } from "@/api/authority";
import {getSupplyList} from "@/api/order";
import infoList from "@/mixins/infoList";
import { mapGetters } from "vuex";
import CustomPic from "@/components/customPic";
import ChooseImg from "@/components/chooseImg";

export default {
  name: "Lijing",
  mixins: [infoList],
  components: { CustomPic, ChooseImg },
  data() {
    return {
      newTableData: [],
      path: this.$path,
      titleDialog: "新增",
      listApi: getUserList,
      level_id: null,
      searchInfo: {
        username: "",
        nickname: "",
        level_id: null
      },
      path: path,
      authOptions: [],
      addUserDialog: false,
      userInfo: {
        username: "",
        password: "",
        pay_password: "",
        nickName: "",
        headerImg: "",
        authorityId: "",
        pay_status:"1",
        gather_supply_id: null,
      },
      supplyOptions: [],
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
          { min: 6, message: "最低6位字符", trigger: "blur" },
        ],
        // password: [
        //   { required: true, message: "请输入用户密码", trigger: "blur" },
        //   { min: 6, message: "最低6位字符", trigger: "blur" },
        // ],
        nickName: [
          { required: true, message: "请输入用户昵称", trigger: "blur" },
        ],
        authorityId: [
          { required: true, message: "请选择用户角色", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    ...mapGetters("user", ["token"]),
  },
  async created() {
    await this.getTableData()
    await this.getSupply() 
    const res = await getAuthorityList({ page: 1, pageSize: 999 });
    this.setOptions(res.data.list);
  },
  methods: {
    // 获取供应链
    getSupply() {
      getSupplyList().then(res => {
          this.supplyOptions = res.data.list
          this.supplyOptions.unshift(
            {name: '平台', id: ''},
            {name: '平台自营', id: 0},
          )
          console.log('this.supplyOptions',this.supplyOptions);
      })
    },
    // 上传主图
    handleImgSuccess(res) {
      if (res.code === 0) {
        this.userInfo.headerImg = res.data.file.url;
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeAvatarUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
      }
      return isLt10M;
    },
    search() {
      this.page = 1;
      this.searchInfo.level_id = parseInt(this.level_id)
      this.getTableData()
    },
    resetForm(formName) {
      this.level_id = null
      this.$refs[formName].resetFields();
    },
    //赋值表单
    setFrom(val) {
      const keys = Object.keys(val);
      const that = this;
      keys.forEach((element) => {
        that.userInfo[element] = val[element];
      });
    },
    openHeaderChange() {
      this.$refs.chooseImg.open();
    },
    setOptions(authData) {
      this.authOptions = [];
      this.setAuthorityOptions(authData, this.authOptions);
    },
    setAuthorityOptions(AuthorityData, optionsData) {
      AuthorityData &&
        AuthorityData.map((item) => {
          if (item.children && item.children.length) {
            const option = {
              authorityId: item.authorityId,
              authorityName: item.authorityName,
              children: [],
            };
            this.setAuthorityOptions(item.children, option.children);
            optionsData.push(option);
          } else {
            const option = {
              authorityId: item.authorityId,
              authorityName: item.authorityName,
            };
            optionsData.push(option);
          }
        });
    },
    async deleteUser(row) {
      const res = await deleteUser({ id: row.id });
      if (res.code == 0) {
        this.getTableData()
        row.visible = false;
      }
    },
    enterDialog() {
      if (this.userInfo.id) {
        this.enterEditUserDialog()
      } else {
        this.enterAddUserDialog()
      }
    },
    async enterAddUserDialog() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          const res = await register(this.userInfo);
          if (res.code == 0) {
            this.$message({ type: "success", message: res.msg });
          }
          await this.getTableData()
          this.closeAddUserDialog();
        }
      });
    },
    async enterEditUserDialog() {
      this.$refs.userForm.validate(async (valid) => {
        if (valid) {
          if (this.userInfo.password === "") {
            delete this.userInfo.password;
          }
          if (this.userInfo.pay_password === "") {
            delete this.userInfo.pay_password;
          }
          let params = {
            ...this.userInfo
          }
          
          params.supply_id = this.userInfo.gather_supply_id
          delete params.gather_supply_id;
          const res = await setUserInfo(params);
          if (res.code == 0) {
            this.$message({ type: "success", message: res.msg });
            let user =  this.$store.getters["user/userInfo"]
            if(user.id === this.userInfo.id){
                user.pay_status = this.userInfo.pay_status
                this.$store.commit('user/setUserInfo',user)
            }
          }
          await this.getTableData()
          this.closeAddUserDialog();
        }
      });
    },
    closeAddUserDialog() {
      if (this.userInfo.id) {
        delete this.userInfo.id;
      }
      this.$refs.userForm.resetFields();
      this.userInfo = {
        username: "",
        password: "",
        pay_password: "",
        nickName: "",
        headerImg: "",
        authorityId: "",
        pay_status:"1",
      }
      this.addUserDialog = false;
    },
    handleAvatarSuccess(res) {
      this.userInfo.headerImg = res.data.file.url;
    },
    addUser() {
      this.addUserDialog = true;
      this.titleDialog = "新增";
    },
    // 编辑
    editUser(row) {
      this.addUserDialog = true;
      this.titleDialog = "编辑";
      row.gather_supply_id = row.supply_id
      this.userInfo = this.$fn.deepClone(row)
      // this.userInfo.gather_supply_id = row.supply_id
    },
    async changeAuthority(row) {
      const res = await setUserAuthority({
        uuid: row.uuid,
        authorityId: row.authority.authorityId,
      });
      if (res.code == 0) {
        this.$message({ type: "success", message: "角色设置成功" });
      }
    },
  },



};
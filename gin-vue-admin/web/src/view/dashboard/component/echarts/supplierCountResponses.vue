<!-- 供应商echarts -->
<template>
    <div id="supplierCountResponseseEcharts" class="user-count-responses" ref="supplierCountResponseseEcharts"></div>
</template>
<script>
import * as echarts from 'echarts';

export default {
    data() {
        return {
            userList: []
        }
    },
    methods: {
        initChar(date) {
            let chartDom = document.getElementById('supplierCountResponseseEcharts');
            let chartsA = echarts.init(chartDom);
            let option = {
                series: [
                    {
                        name: 'Nightingale Chart',
                        type: 'pie',
                        radius: [20, 100],
                        center: ['50%', '50%'],
                        roseType: 'area',
                        itemStyle: {
                            borderRadius: 8,
                            color: function (params) {
                                var colorlist = ['#155BD4', '#13C267', '#88DEA2', '#DDDFE2', '#BACDFB', '#CDB9FF', '#9F79FE'];
                                return colorlist[params.dataIndex];
                            },
                        },
                        data: date
                    }
                ]
            };
            chartsA.setOption(option);

            window.addEventListener("resize", function () {
                chartsA.resize();
            })
            // window.addEventListener("resize", function () {
            //     myChart.resize();
            // });

        },
    },
};
</script>
<style lang="scss" scoped>
.user-count-responses {
    width: 100%;
    height: 240px;
    margin-top: 50px;
}
</style>
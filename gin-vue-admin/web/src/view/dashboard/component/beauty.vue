<template>
    <div class="beauty">
        <h3 class="beauty-name">{{ title }}</h3>
        <div class="f fw">
            <template v-for="item in mylist">
                <div class="f fac grid-item" @click="goUrl(item.url)">
                    <div v-if="item.icon" class="iconbox f fac">
                        <span v-if="item.size" :class="[item.icon, 'iconfont',item.size === 18?'iconfs-size':'iconfs-size2']" ></span>
                        <span v-else :class="[item.icon, 'iconfont', 'iconfs']"></span>
                    </div>
                    <el-image v-else style="width: 40px; height: 40px" :src="item.imgUrl" fit="fill"></el-image>
                    <p class="name">{{ item.name }}</p>
                </div>
            </template>
        </div>
    </div>
</template>

<script>

export default {
    name: "beauty",
    components: {
    },
    props: {
        // 是否做重复数据不可勾选
        title: {
            type: String,
            default: "zh"
        },
        mylist: Array
    },
    data() {
        return {

        }
    },
    methods: {
        goUrl(url) {
            console.log(url);
            this.$router.push({
                path:url
            })
            this.$bus.emit("beauty", false);
        }
    }
}
</script>

<style scoped>
.beauty {
    padding: 20px;
    background-color: white;
    border-radius: 12px;
    box-sizing: border-box;
    margin-bottom: 16px;
}

.beauty-name{
    font-size: 20px;
    font-weight: bold;
}

.title-h3 {
    font-size: 16px;
    font-weight: bold;
}

.iconbox{
    width: 40px;
    height: 40px;
    justify-content: center;
     background-color: #F5F6F8;
    border-radius: 12px;
}

.iconfs {
    font-size: 16px;
}

.iconfs-size {
    font-size: 18px;
}
.iconfs-size2 {
    font-size: 20px;
}

.name {
    font-size: 12px;
    margin-top: 12px;
}

.grid-item {
    width: 33%;
    flex-direction: column;
    margin:25px 0 0;
    cursor: pointer;
}


@font-face {
    font-family: "iconfont"; /* Project id 432132 */
    src: url('//at.alicdn.com/t/c/font_432132_5i3sq2x1g8m.woff2?t=1718949453384') format('woff2'),
         url('//at.alicdn.com/t/c/font_432132_5i3sq2x1g8m.woff?t=1718949453384') format('woff'),
         url('//at.alicdn.com/t/c/font_432132_5i3sq2x1g8m.ttf?t=1718949453384') format('truetype');
  }
  
  .iconfont {
    font-family: "iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
.icon-fontclass-gantanhao:before {
    content: "\ea2f";
}


.icon-icon_down1:before {
    content: "\eb79";
}
  
.icon-up:before {
    content: "\e713";
}

.icon-down:before {
    content: "\e712";
}

.icon-shuzicangpin:before {
    content: "\ebac";
}

.icon-shouyimingxi:before {
    content: "\ec52";
}

.icon-icon_dingdan_1:before {
    content: "\eb54";
}

.icon-shouhou:before {
    content: "\ebd3";
}

.icon-tixian3:before {
    content: "\ebca";
}

.icon-chengbenmoban:before {
    content: "\ebc1";
}

.icon-a-zu3:before {
    content: "\ebcd";
}

.icon-huiyuanshouru:before {
    content: "\ec59";
}

.icon-caigouduanshenhe:before {
    content: "\ec5f";
}

.icon-yemianshezhi:before {
    content: "\ec5a";
}

.icon-fuzai:before {
    content: "\ec5b";
}

.icon-CPU:before {
    content: "\ec5d";
}

.icon-neicun:before {
    content: "\ec5c";
}
  
.icon-cipan:before {
    content: "\ec5e";
}
</style>

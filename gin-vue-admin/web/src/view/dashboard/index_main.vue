<template>
    <div class="f box">
        <div class="dashboard">
            <!-- 信息卡 -->
            <div class="f fac fjsb top-box">
                <!-- 今日交易额 -->
                <div class="top-item">
                    <p class="title-p">
                        今日交易额
                        <el-tooltip content="今日下单并支付的订单总金额" placement="top">
                            <span class="icon-fontclass-gantanhao iconfont gantanhao"></span>
                        </el-tooltip>
                    </p>
                    <p class="number-p f fac">￥
                        <span ref="tradingVolumeToday">0.00</span>
                    </p>
                    <p class="title-p2 f fac" style="display: flex;">
                        昨日 ￥<span ref="tradingVolumeYesterday">0.00</span> <span style="margin-left: 5px;"
                            :class="[todayYesterdayTradingVolume > 0 ? 'jiantoutop' : 'jiantoubottom']">
                            <span v-if="todayYesterdayTradingVolume >= 0">+</span><span
                                ref="todayYesterdayTradingVolume">0.00</span>% </span> <span
                            v-if="todayYesterdayTradingVolume > 0"
                            class="icon-up iconfont jiantoutop jiantou"></span><span v-else
                            class="icon-down iconfont jiantoubottom jiantou"></span>
                    </p>
                </div>
                <!-- 今日订单数 -->
                <div class="top-item">
                    <p class="title-p">
                        今日订单数 (单)
                        <el-tooltip content="今日下单并支付的订单数量" placement="top">
                            <span class="icon-fontclass-gantanhao iconfont gantanhao"></span>
                        </el-tooltip>
                    </p>
                    <p class="number-p f fac">
                        <span ref="todayOrderCount">0</span>
                    </p>
                    <p class="title-p2 f fac">
                        昨日 <span ref="yesterdayOrderCount">0</span><span style="margin-left: 5px;"
                            :class="[todayYesterdayOrderCount > 0 ? 'jiantoutop' : 'jiantoubottom']">
                            <span v-if="todayYesterdayOrderCount >= 0">+</span><span
                                ref="todayYesterdayOrderCount">0</span>% </span> <span
                            v-if="todayYesterdayOrderCount > 0" class="icon-up iconfont jiantoutop jiantou"></span><span
                            v-else class="icon-down iconfont jiantoubottom jiantou"></span>
                    </p>
                </div>
                <!-- 今日在线商品数 -->
                <div class="top-item">
                    <p class="title-p">
                        今日在线商品数 (sku)
                        <el-tooltip content="访问时在线的商品总sku 数量" placement="top">
                            <span class="icon-fontclass-gantanhao  iconfont gantanhao"></span>
                        </el-tooltip>
                    </p>
                    <p class="number-p f fac">
                        <span ref="nowProductCount">0</span>
                    </p>
                    <p class="title-p2 f fac">
                        昨日 <span ref="oldProductCount">0</span> <span style="margin-left: 5px;"
                            :class="[productCount > 0 ? 'jiantoutop' : 'jiantoubottom']">
                            <span v-if="productCount >= 0">+</span><span ref="productCount">0</span>% </span> <span
                            v-if="productCount > 0" class="icon-up jiantoutop iconfont jiantou"></span><span v-else
                            class="icon-down jiantoubottom iconfont jiantou"></span>
                    </p>
                </div>
                <!-- 交易总额 -->
                <div class="top-item">
                    <p class="title-p">
                        交易总额 (元)
                        <el-tooltip content="累计已支付订单总金额" placement="top">
                            <span class="icon-fontclass-gantanhao iconfont gantanhao"></span>
                        </el-tooltip>
                    </p>
                    <p class="number-p2 f fac">￥<span ref="memberTotal">0</span></p>
                </div>
                <!-- 待发货订单（单） -->
                <div class="top-item">
                    <p class="title-p">
                        待发货订单 (单)
                        <el-tooltip content="当前未发货的订单数量" placement="top">
                            <span class="icon-fontclass-gantanhao iconfont gantanhao"></span>
                        </el-tooltip>
                    </p>
                    <p class="number-p2 f fac">
                        <span ref="waitSendingCount">0</span>
                    </p>
                </div>
            </div>
            <!-- 折线图 -->
            <keyIndicator ref="keyIndicator"></keyIndicator>
            <!-- 统计信息图 -->
            <div class="f fjsb">
                <div class="statistics">
                    <h3 class="title-h3">会员统计</h3>
                    <userCountResponses ref="userCountResponses"></userCountResponses>
                </div>
                <div class="statistics">
                    <div class="f fac fjsb rankingListText">
                        <h3 class="title-h3">商品统计</h3>
                        <div class="f fac">
                            <template v-for="item in goodDate">
                                <div class='btn f fac ' @click="gooddatefun(item.value)"
                                    v-if="gooddateValue !== item.value">{{ item.name }}</div>
                                <div class='btn red-btn f fac ' @click="gooddatefun(item.value)" v-else>{{
                                item.name }}</div>
                            </template>
                        </div>
                    </div>
                    <productCountResponses ref="productCountResponses"></productCountResponses>
                </div>
                <div class="statistics">
                    <h3 class="title-h3">供应商统计</h3>
                    <supplierCountResponses ref="supplierCountResponses"></supplierCountResponses>
                </div>
            </div>
            <!-- 排行榜 -->
            <div class="f fjsb">
                <div class="rankingList">
                    <div class="f fac fjsb rankingListText">
                        <h3 class="title-h3">商品销量排行</h3>
                        <div class="f fac">
                            <template v-for="item in rankingdate">
                                <div class='btn f fac ' @click="productProcurementRanking(item.value)"
                                    v-if="typeProduct !== item.value">{{ item.name }}</div>
                                <div class='btn red-btn f fac ' @click="productProcurementRanking(item.value)" v-else>{{
                                item.name }}</div>
                            </template>
                        </div>
                    </div>
                    <el-table :data="goodsData" :header-cell-style="{ height: '53px' }" :row-style="{ height: '75px' }"
                        style="width: 100%">
                        <el-table-column prop="date" type="index" width="70px" align="center"
                            :index="(index) => index + 1" label="排名">
                        </el-table-column>
                        <el-table-column label="商品" width="320px">
                            <template slot-scope="scope">
                                <div class='f fac'>
                                    <m-image :src="scope.row.image_url"
                                        :style="{ minWidth: '48px', minHeight: '48px', maxWidth: '48px', maxHeight: '48px' }"></m-image>
                                    <div class='vipName'>
                                        {{ scope.row.title }}
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="name" label="来源">
                            <template slot-scope="scope">
                                <div v-if="scope.row.supplier_id === 0 && scope.row.gather_supply_id === 0">自营</div>
                                <div v-else-if="scope.row.supplier_id !== 0">{{ scope.row.supplier.name }}</div>
                                <div v-else-if="scope.row.gather_supply_id !== 0">{{ scope.row.gather_supply.name }}
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="order_sales" label="销量">
                        </el-table-column>
                    </el-table>
                </div>
                <div class="rankingList">
                    <div class="f fac fjsb rankingListText">
                        <h3 class="title-h3">采购排行</h3>
                        <div class="f fac">

                            <template v-for="item in rankingdate">
                                <div class='btn f fac ' @click="userProcurementRanking(item.value)"
                                    v-if="typeUser !== item.value">{{ item.name }}</div>
                                <div class='btn red-btn f fac ' @click="userProcurementRanking(item.value)" v-else>{{
                                item.name }}</div>
                            </template>
                        </div>
                    </div>
                    <el-table :data="purchaseData" :header-cell-style="{ height: '53px' }"
                        :row-style="{ height: '75px' }" style="width: 100%">
                        <el-table-column prop="date" width="70px" type="index" :index="(index) => index + 1" label="排名" align="center">
                        </el-table-column>
                        <el-table-column prop="username" width="200px" label="会员">
                            <template slot-scope="scope">
                                <div class='f fac'>
                                    <m-image :src="scope.row.avatar"
                                        :style="{ minWidth: '48px', minHeight: '48px', maxWidth: '48px', maxHeight: '48px', borderRadius: '24px' }"></m-image>

                                    <div class='vipName'>
                                        <p>{{ scope.row.nickname }}</p>
                                        <p>{{ scope.row.username }}</p>
                                    </div>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column prop="level_name" label="会员等级">
                        </el-table-column>
                        <el-table-column prop="total" label="订单金额(元)">
                            <template slot-scope="scope">
                                {{ scope.row.total | formatF2Y }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="count" label="订单数量(单)">
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
        <div class="shortcut">
            <!-- 快捷入口 -->
            <beauty ref="beauty" title="快捷入口" :mylist="fastList"></beauty>
            <!-- 常用应用 -->
            <beauty ref="beauty" title="常用应用" :mylist="appList"></beauty>

            <!-- 访问前端 -->
            <div class="line-chart mt10">
                <h3 class="title-h3">访问前端</h3>
                <div class="mt25 visitbox">
                    <div class="f fac visit-icon">
                        <el-image style="width: 37px; height: 34px" :src="imgpc" fit="fill"></el-image>
                    </div>
                    <div class="visit-name f fac">PC端</div>
                    <div class="f fac visit-btn">
                        <div @click="gopc" class="visit-btn-bb">打开</div>
                        <div @click="copy">复制链接</div>
                    </div>
                </div>
                <div class="mt25 visitbox">
                    <div class="visit-ewm" ref="qrCodeUrl"></div>
                    <div class="visit-name f fac">H5端</div>
                </div>
                <div class="mt25 visitbox">
                    <div class="visit-ewm">
                        <el-image v-if="XCXUrl" style="width: 90px; height: 90px" :src="XCXUrl" fit="fill"></el-image>
                    </div>
                    <div class="visit-name f fac">小程序</div>
                    <div v-if="!XCXUrl" class="visit-name f fac">请去店铺-基础信息设置！</div>
                </div>
            </div>
            <!-- 系统运行 -->
            <div class="line-chart mt10">
                <h3 class="title-h3">系统运行</h3>
                <template v-for="item in pieTestData">
                    <div class="pie-charts f fac">
                        <div class="pie-charts-icon-box f fac">
                            <div :class="['icon', 'iconfont', item.icon]"></div>
                        </div>
                        <div class="pie-charts-text">
                            <div class="f fac fjsb" style="margin-bottom: 9px;">
                                <div>{{ item.name }}</div>
                                <div :class="[item.num > 90 ? 'jiantoutop' : 'jiantoulan']">{{ item.num }}%</div>
                            </div>
                            <el-progress :show-text="false" :color="item.num > 90 ? 'red' : '#155BD4'"
                                :percentage="item.num"></el-progress>
                        </div>
                    </div>
                </template>
                <div class="f fac fjc mt25 timed—task">
                    <!--              systemOperation-->
                    <div style="text-align: center;" class="f1">
                        <div :class="statusFn(systemOperation.cron.status)" class="systemItem f fac fjc">
                            <i :class="iconFn(systemOperation.cron.status)" style="color: #fff;font-size: 24px"></i>
                        </div>
                        <p class="mt10">定时任务</p>
                    </div>
                    <div style="text-align: center;" class="f1">
                        <div :class="statusFn(systemOperation.mq.status)" class="systemItem f fac fjc">
                            <i :class="iconFn(systemOperation.mq.status) " style="color: #fff;font-size: 24px"></i>
                        </div>
                        <p class="mt10">消息队列</p>
                    </div>
                </div>
                <div class="timed—task mt10">
                    <div class="f fac">
                        <span class="icon-fontclass-gantanhao iconfont gantanhao"></span>
                        <span class="hint">温馨提示</span>
                    </div>
                    <p class="text-timed-task mt10" style="font-weight:bold">
                        如果为红色图标,表示系统运营异常,10分钟左右刷新未恢复正常的,请第一时间联系客服处理!</p>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
import beauty from "./component/beauty.vue";
import keyIndicator from './component/echarts/keyIndicator.vue'
import userCountResponses from "./component/echarts/userCountResponses.vue";
import productCountResponses from './component/echarts/productCountResponses.vue'
import supplierCountResponses from './component/echarts/supplierCountResponses.vue'
import {
    getDashboardIndex,
    getServerStatus,
    getSupplierDashboardIndex,
    getSupplierOrderLineChart,
    getStatus,
    getTotal,
    userProcurementRanking,
    productProcurementRanking,
    productStatisticsData
} from "@/api/dashboard";
import { getPluginLists } from '@/api/plugin'
import { mapGetters } from "vuex";
import QRCode from 'qrcodejs2'

export default {
    name: "dashboard",
    components: { keyIndicator, beauty, userCountResponses, productCountResponses, supplierCountResponses },
    data() {
        return {
            tradingVolumeToday: 0, // 今日交易额
            memberTotal: 0, // 订单总额
            todayOrderCount: 0, // 今日订单数
            waitSendingCount: 0, // 待发货订单
            tradingVolumeYesterday: 0,// 昨日交易额
            yesterdayOrderCount: 0, // 昨日订单数
            todayYesterdayTradingVolume: 0, //交易额对比昨天比例
            todayYesterdayOrderCount: 0, //订单数量对比昨天比例
            nowProductCount: 0, // 今日在线商品数
            oldProductCount: 0, // 昨日在线商品数
            productCount: 0, //  在线商品对比昨天比例

            tradingVolumeTodayValue: 0,

            pieTestData: [],
            systemOperation: {
                cron: { // 定时任务
                    status: 0 // 1正常0读取中-1异常
                },
                mq: { // 消息队列
                    status: 0 // 1正常0读取中-1异常
                }
            },

            // 快捷入口数据
            fastList: [
                {
                    name: '商品审核',
                    url: 'goodsIndex/goodsVerifyList',
                    icon: 'icon-shuzicangpin',
                    size: 18
                },
                {
                    name: '售后维权',
                    url: 'orderIndex/afterSaleIndex',
                    icon: 'icon-shouhou'
                },
                {
                    name: '余额明细',
                    url: 'Finance/purchase',
                    icon: 'icon-chengbenmoban'
                },
                {
                    name: '收入明细',
                    url: 'Finance/financeIncomeIndex/financeIncomeDetail',
                    icon: 'icon-shouyimingxi'
                },
                {
                    name: '提现记录',
                    url: 'Finance/financeWithdrawDepositIndex/financeWithdrawDepositRecord',
                    icon: 'icon-tixian3'
                },
                {
                    name: '会员收入',
                    url: 'Finance/financeIncomeIndex/financeIncomeMemberIncome',
                    icon: 'icon-huiyuanshouru',
                    size: 20
                },
                {
                    name: '文章管理',
                    url: 'shopSettingIndex/article',
                    icon: 'icon-icon_dingdan_1'
                },
                {
                    name: '页面设置',
                    url: 'shopSettingIndex/homeSetting',
                    icon: 'icon-yemianshezhi',
                    size: 20
                },
                {
                    name: '采购端审核',
                    url: 'application/applicationAudit',
                    icon: 'icon-caigouduanshenhe',
                    size: 20
                },
            ],

            // 常用应用数据
            appList: [],
            H5Url: '', // H5二维码
            XCXUrl: '', // 小程序二维码
            userCountResponses: [],// 会员统计
            productCountResponses: [], // 商品统计
            supplierCountResponses: [], // 供应商统计
            goodsData: [], // 商品销量排行
            purchaseData: [], // 采购排行
            typeUser: 0, // 选择的采购排行日期
            typeProduct: 0, // 选择的商品排行日期
            rankingdate: [
                { name: '全部', value: 0 },
                { name: '今日', value: 1 },
                { name: '本周', value: 2 },
                { name: '本月', value: 3 },
                { name: '昨日', value: 4 },
                { name: '上周', value: 5 },
                { name: '上月', value: 6 },
            ],
            goodDate: [
                { name: '全部', value: 0 },
                { name: '今日在线', value: 1 },
                { name: '昨日在线', value: 4 },
            ],
            gooddateValue: 0,
            imgpc: require('@/assets/dashboard_icon/pc.png')
        };
    },
    computed: {
        ...mapGetters("user", ["userInfo"]),
        // 1 = 管理员  2 = 供货商
        status() {
            if (this.userInfo.is_supplier) {
                return 2
            } else {
                return 1
            }
        },

    },
    mounted() {
        this.creatQrCode(); // 二维码
        this.dashboardIndex();
        this.getPluginLists();
        this.userProcurementRanking(0); // 获取采购排行
        this.productProcurementRanking(0); // 获取商品销量排行
        this.getTotal(); // 获取统计和小程序二维码数据
        this.gooddatefun(0); // 获取雷达图
        if (this.status === 1) {
            this.serverStatus();
            this.getSystemOperation()
        }
    },
    methods: {
        toMoney(num) {
            if (num) {
                if (isNaN(num)) {
                    this.$message.error('金额中含有不能识别的字符')
                    return
                }
                num = typeof num == 'string' ? parseFloat(num) : num //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2) //保留两位
                num = parseFloat(num) //转成数字
                num = num.toLocaleString() //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00'
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num
                }
                return num //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return (num = null)
            }
        },
        numberGrow(ele, stater, value) {
            if (!value) {
                if (stater === 1) {
                    ele.innerHTML = "0.00"
                } else {
                    ele.innerHTML = '0'
                }
                return
            }
            let val = value / 10
            let start = 0
            let con = 0
            let t = setInterval(() => {
                start += val
                if (start == value) {
                    clearInterval(t)
                    t = null
                }
                if (start > value) {
                    start = value
                    clearInterval(t)
                    t = null
                }
                if (stater === 1) {
                    con = this.toMoney(start / 100)
                } else {
                    con = parseInt(start)
                }
                ele.innerHTML = con.toString().replace(/(\d)(?=(?:\d{3}[+]?)+$)/g, '$1,')
            }, 100)
        },
        // 二维码
        creatQrCode() {
            var qrcode = new QRCode(this.$refs.qrCodeUrl, {
                text: window.location.origin + '/h5/?menu#/', // 需要转换为二维码的内容
                width: 90,
                height: 90,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.H
            })
        },
        // 获取部分查询权限 概括右侧使用
        async getPluginLists() {
            let arr = [{
                name: '分销',
                url: 'distributorIndex/',
                icon: '',
                imgUrl: ''

            },
            {
                name: '区域分红',
                url: 'areaAgencyindex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '选品专辑',
                url: 'shareAlbumIndex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '小商店',
                url: 'smallShopIndex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '采购账户',
                url: 'purchaseAccountIndex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '发票管理',
                url: 'billIndex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '短视频',
                url: 'videoDistributeIndex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '素材',
                url: 'materialDistributionBaseIndex/',
                icon: '',
                imgUrl: ''
            },
            {
                name: '共享直播',
                url: 'shareLiveIndex/',
                icon: '',
                imgUrl: ''
            }]
            let meun
            let meunList = []
            let res = await getPluginLists()
            if (res.code === 0) {
                meun = [...res.data.pluginList, ...res.data.resourcePluginList, ...res.data.toolsPluginList]
                arr.forEach(element => {
                    let val = meun.find(item => item.name === element.name)
                    if (val) {
                        element.url = element.url + val.path
                        element.imgUrl = require(`@/assets/plugin_icon/${val.logo}`)
                        meunList.push(element)
                    }
                });
                // 共享选品专辑
                let s = meun.find(item => item.name === '共享选品专辑')
                arr[2].url = arr[2].url + s.path
                arr[2].imgUrl = require(`@/assets/plugin_icon/${s.logo}`)
                meunList.push(arr[2])
                // 短视频分发
                let a = meun.find(item => item.name === '短视频分发')
                arr[6].url = arr[6].url + a.path
                arr[6].imgUrl = require(`@/assets/plugin_icon/${a.logo}`)
                meunList.push(arr[6])
                // 素材分发
                let b = meun.find(item => item.name === '素材分发')
                arr[7].url = arr[7].url + b.path
                arr[7].imgUrl = require(`@/assets/plugin_icon/${b.logo}`)
                meunList.push(arr[7])
                this.appList = meunList
            }
        },
        // 打开pc端
        gopc() {
            window.open(window.location.origin, '_blank');
        },
        // 复制pc端地址
        copy() {
            this.$fn.copy(window.location.origin)
        },
        // 获取小程序和H5地址和统计数据
        async getTotal() {
            let res = await getTotal()
            if (res.code === 0) {
                this.XCXUrl = res.data.shop_app_code

                // 会员统计
                res.data.data.userCountResponses.forEach(item => {
                    this.userCountResponses.push({
                        value: item.count,
                        name: item.level_name,
                    })
                })
                this.$refs.userCountResponses.initChar(this.userCountResponses)


                // 供应商统计
                res.data.data.supplierCountResponses.forEach(item => {
                    this.supplierCountResponses.push({
                        value: item.count,
                        name: item.name + ' ' + item.count,
                    })
                })
                this.$refs.supplierCountResponses.initChar(this.supplierCountResponses)
            } else {
                this.$message.error(res.msg);
            }
        },
        // 获取采购排行
        async userProcurementRanking(type) {
            this.typeUser = type
            let res = await userProcurementRanking({ type })
            if (res.code === 0) {
                this.purchaseData = res.data.data
            }
        },
        // 获取商品销量排行
        async productProcurementRanking(type) {
            this.typeProduct = type
            let res = await productProcurementRanking({ type })

            if (res.code === 0) {
                this.goodsData = res.data.data
            }
        },
        // 获取雷达图的数据 商品
        async gooddatefun(type) {
            this.gooddateValue = type
            this.productCountResponses = []
            let res = await productStatisticsData({ type })

            if (res.code === 0) {

                // 商品统计
                if (!res.data.data) {
                    let date = [
                        {name:'暂无数据',max:10},
                        {name:'暂无数据',max:10},
                        {name:'暂无数据',max:10},
                        {name:'暂无数据',max:10},
                        {name:'暂无数据',max:10},
                        {name:'暂无数据',max:10},
                    ]
                    this.$refs.productCountResponses.initChar(date, [])
                    return
                }

                let nums = []

                res.data.data.forEach(item => {
                    nums.push(item.count)
                });
                let max = Math.max(...nums)
                res.data.data.forEach(item => {
                    nums.push(item.count)
                    this.productCountResponses.push({
                        max,
                        name: item.name + "(" + item.count + ")",
                    })
                });
                this.$refs.productCountResponses.initChar(this.productCountResponses, nums)
            }
        },
        iconFn(status) {
            let s = 'el-icon-loading'
            switch (status) {
                case 1:
                    s = 'el-icon-s-opportunity'
                    break;
                case 0:
                    s = 'el-icon-loading'
                    break;
                case -1:
                    s = 'el-icon-warning'
                    break;
            }
            return s;
        },
        statusFn(status) {
            let s = 'info'
            switch (status) {
                case 1:
                    s = 'success'
                    break;
                case 0:
                    s = 'info'
                    break;
                case -1:
                    s = 'danger'
                    break;
            }
            return s = 'system-' + s;
        },
        getSystemOperation() {
            getStatus().then(res => {
                if (res.code === 0) {
                    this.systemOperation.cron.status = res.data.cron.status
                    this.systemOperation.mq.status = res.data.mq.status
                }
            })
        },
        // 获取系统运行状态
        serverStatus() {
            getServerStatus().then((res) => {
                if (res.code === 0) {
                    var server = res.data.server;
                    var cpus = server.cpu.cpus;
                    var cpusTotal = 0;

                    for (var i = 0; i < cpus.length; i++) {
                        cpusTotal += cpus[i];
                    }

                    this.pieTestData = [
                        {
                            name: "负载状态",
                            num: server.load.load15,
                            icon: 'icon-fuzai'
                        },
                        {
                            name: "CPU使用率",
                            num: parseInt(cpusTotal / cpus.length),
                            icon: 'icon-CPU'
                        },
                        {
                            name: "总内存使用率",
                            num: server.ram.usedPercent,
                            icon: 'icon-neicun'
                        },
                        {
                            name: "磁盘使用率",
                            num: server.disk.usedPercent,
                            icon: 'icon-cipan'
                        },
                    ];
                } else {
                    that.$message.error(res.msg);
                }
            });
        },
        // 获取昨日今日的数据
        dashboardIndex() {
            if (this.status === 1) {
                getDashboardIndex().then((res) => {
                    if (res.code === 0) {
                        this.tradingVolumeToday = res.data.data.trading_volume_today; // 今日交易额
                        this.memberTotal = res.data.data.member_total; // 订单总额
                        this.todayOrderCount = res.data.data.today_order_count; // 今日订单数
                        this.waitSendingCount = res.data.data.wait_sending_count; // 待发货订单
                        this.tradingVolumeYesterday = res.data.data.trading_volume_yesterday; // 昨日交易额
                        this.yesterdayOrderCount = res.data.data.yesterday_order_count; // 昨日订单数
                        this.nowProductCount = res.data.data.nowProductCount; // 今日在线商品数
                        this.oldProductCount = res.data.data.oldProductCount; // 昨日在线商品数


                        var cha = this.todayOrderCount - this.yesterdayOrderCount

                        // 交易额对比昨天比例
                        if (this.yesterdayOrderCount === 0) {
                            this.todayYesterdayOrderCount = cha * 100;
                        } else if (cha != 0) {
                            this.todayYesterdayOrderCount = (cha / this.yesterdayOrderCount * 10000).toFixed(2)
                        }


                        var cha1 = this.tradingVolumeToday - this.tradingVolumeYesterday

                        // 订单数量对比昨天比例
                        if (this.tradingVolumeYesterday === 0) {
                            this.todayYesterdayTradingVolume = cha1 * 100;
                        } else if (cha1 != 0) {
                            this.todayYesterdayTradingVolume = (cha1 / this.tradingVolumeYesterday * 10000).toFixed(2)
                        }

                        var cha2 = this.nowProductCount - this.oldProductCount

                        // 订单数量对比昨天比例
                        if (this.oldProductCount === 0) {
                            this.productCount = cha2 * 100;
                        } else if (cha2 != 0) {
                            this.productCount = (cha2 / this.oldProductCount * 10000).toFixed(2)
                        }

                        this.numberGrow(this.$refs.tradingVolumeToday, 1, this.tradingVolumeToday) // 今日交易额
                        this.numberGrow(this.$refs.tradingVolumeYesterday, 1, this.tradingVolumeYesterday) // 昨日交易额
                        this.numberGrow(this.$refs.todayYesterdayTradingVolume, 1, this.todayYesterdayTradingVolume) // 今日交易额增加百分比


                        this.numberGrow(this.$refs.todayOrderCount, 2, this.todayOrderCount) // 今日订单数
                        this.numberGrow(this.$refs.yesterdayOrderCount, 2, this.yesterdayOrderCount) // 昨日订单数
                        this.numberGrow(this.$refs.todayYesterdayOrderCount, 1, this.todayYesterdayOrderCount) // 今日订单数增加百分比


                        this.numberGrow(this.$refs.nowProductCount, 2, this.nowProductCount) // 今日在线商品数
                        this.numberGrow(this.$refs.oldProductCount, 2, this.oldProductCount) // 昨日在线商品数
                        this.numberGrow(this.$refs.productCount, 1, this.productCount) // 今日增加百分比

                        this.numberGrow(this.$refs.memberTotal, 1, this.memberTotal) // 交易总额
                        this.numberGrow(this.$refs.waitSendingCount, 2, this.waitSendingCount) // 代发货订单

                    } else {
                        this.$message.error(res.msg);
                    }
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";

.box {
    background-color: transparent;
    padding: 0;
}

.jiantou {
    font-size: 18px;
    font-weight: bolder;
}

.jiantoubottom {
    color: #13c267;
}

.jiantoutop {
    color: red;
}

.jiantoulan {
    color: #155BD4;
}

.dashboard {
    width: calc(100% - 301px)
}

.shortcut {
    width: 285px;
    margin-left: 16px;
}

.line-chart {
    margin-top: 16px;
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-sizing: border-box;
}

.title-h3 {
    font-size: 20px;
    font-weight: bold;
}

.btn {
    height: 28px;
    margin-left: 10px;
    background-color: #F5F6F8;
    justify-content: center;
    font-size: 12px;
    border-radius: 4px;
    padding:0 14px;
    cursor: pointer
}

.red-btn {
    background-color: rgba(21, 91, 212, 0.2);
    color: #155BD4;
}

// 左边的css
.top-box {
    .top-item {
        background-color: rgba(255, 255, 255, 1);
        width: 19.3%;
        height: 154px;
        border-radius: 12px;
        padding: 20px 24px;
        box-sizing: border-box;

        .number-p {
            margin-top: 22px;
            font-size: 30px;
        }

        .number-p2 {
            margin-top: 22px;
            font-size: 30px;
        }


        .title-p {
            font-size: 14px;
            margin-top: 0 !important;
        }

        .title-p2 {
            margin-top: 22px;
            color: #8D8E8F;
        }

        &:last-child {
            border-right: 0;
        }
    }
}

.statistics {
    width: 32.7%;
    height: 360px;
    margin-top: 16px;
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-sizing: border-box;
}


.rankingList {
    width: 49.5%;
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    box-sizing: border-box;
    margin-top: 18px;

    .rankingListText {
        margin-bottom: 20px;
    }
}

.vipName {
    margin-left: 7px;

    p {
        line-height: 16px;
        font-size: 12px;

    }
}

.goodsName {
    font-size: 12px;
    width: 170px;
    min-width: none;
}

// 右边的css
::v-deep .el-form-item {
    margin-bottom: 15px;
}

.qrcode {
    width: 113px;
    height: 113px;
}


.pie-charts {
    width: 245px;
    margin: 23px auto;


    .pie-charts-icon-box {
        width: 40px;
        height: 40px;
        border-radius: 12px;
        justify-content: center;
        background-color: rgba(21, 91, 212, 0.1);

        .icon {
            font-size: 20px;
            color: #155BD4;
        }
    }

    .pie-charts-text {
        margin-left: 13px;
        width: 192px;
    }
}

.visitbox {
    width: 245px;
    height: 186px;
    background: linear-gradient(180deg, rgba(21, 91, 212, 0.06) 0%, rgba(255, 255, 255, 0) 100%);
    border-radius: 16px 16px 16px 16px;
    border: 1px solid #F5F8FC;

    .visit-icon {
        width: 65px;
        height: 62px;
        margin: 24px auto 15px;
        background-color: white;
        border: 1px solid #F5F8FC;
        border-radius: 12px;
        justify-content: center;
    }

    .visit-name {
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
    }

    .visit-btn {
        width: 100%;
        margin: 25px auto 0;
        color: #155BD4;
        cursor: pointer;

        div {
            width: 50%;
            font-size: 14px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .visit-btn-bb {
            border-right: 1px solid #DBE3EF;
        }
    }

    .visit-ewm {
        width: 104px;
        height: 104px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 24px auto 15px;
        border-radius: 12px;
        background: #FFFFFF;
        box-sizing: border-box;
    }
}

.timed—task {
    width: 245px;
    height: 110px;
    background-color: #F5F6F8;
    border-radius: 12px;
    padding: 12px;
    box-sizing: border-box;

    .text-timed-task {
        font-size: 12px;
        color: #78797A;
        line-height: 18px;
    }

    .hint {
        font-size: 14px;
        font-weight: bold;
        margin-left: 7px;
    }
}

.systemItem {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin: 0 auto;

    &.system-info {
        background: #909399;
    }

    &.system-success {
        background: #13c267;

    }

    &.system-danger {
        background: #f56c6c;

    }
}

.gantanhao {
    color: #D1D4D9;
    font-size: 12px;
}

@font-face {
    font-family: "iconfont";
    /* Project id 432132 */
    src: url('//at.alicdn.com/t/c/font_432132_5i3sq2x1g8m.woff2?t=1718949453384') format('woff2'),
        url('//at.alicdn.com/t/c/font_432132_5i3sq2x1g8m.woff?t=1718949453384') format('woff'),
        url('//at.alicdn.com/t/c/font_432132_5i3sq2x1g8m.ttf?t=1718949453384') format('truetype');
}

.iconfont {
    font-family: "iconfont" !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-fontclass-gantanhao:before {
    content: "\ea2f";
}


.icon-icon_down1:before {
    content: "\eb79";
}

.icon-up:before {
    content: "\e713";
}

.icon-down:before {
    content: "\e712";
}

.icon-shuzicangpin:before {
    content: "\ebac";
}

.icon-shouyimingxi:before {
    content: "\ec52";
}

.icon-icon_dingdan_1:before {
    content: "\eb54";
}

.icon-shouhou:before {
    content: "\ebd3";
}

.icon-tixian3:before {
    content: "\ebca";
}

.icon-chengbenmoban:before {
    content: "\ebc1";
}

.icon-a-zu3:before {
    content: "\ebcd";
}

.icon-huiyuanshouru:before {
    content: "\ec59";
}

.icon-caigouduanshenhe:before {
    content: "\ec5f";
}

.icon-yemianshezhi:before {
    content: "\ec5a";
}

.icon-fuzai:before {
    content: "\ec5b";
}

.icon-CPU:before {
    content: "\ec5d";
}

.icon-neicun:before {
    content: "\ec5c";
}

.icon-cipan:before {
    content: "\ec5e";
}
</style>
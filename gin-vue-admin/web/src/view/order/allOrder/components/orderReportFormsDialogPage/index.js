import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { getOrderReportForms, getOrderReportFormsExport } from '@/api/order';
import { Base64 } from "js-base64";

export default {
    data() {
        return {
            isShow: false,
            fromData: {},
            tableData: [], // 订单数据汇总
            allMemberData: [], // 会员订单数据汇总
            areaOrderData: [], // 区域订单数据汇总
            goodsOrderData: [], // 商品订单数据汇总
            categoryOrderData: [], // 类目订单数据汇总
            shopOrderData: [], // 店铺订单数据汇总
            froms: '', // 统计范围
        };
    },
    filters: {
        toMoney(num) {
            if (num) {
                if (isNaN(num)) {
                    this.$message.error('金额中含有不能识别的字符')
                    return
                }
                num = typeof num == 'string' ? parseFloat(num) : num //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2) //保留两位
                num = parseFloat(num) //转成数字
                num = num.toLocaleString() //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num + '.00'
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num
                }
                return num //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return (num = '0.00')
            }
        },
        toCount(num) {
            if (num) {
                if (isNaN(num)) {
                    this.$message.error('金额中含有不能识别的字符')
                    return
                }
                num = typeof num == 'string' ? parseFloat(num) : num //判断是否是字符串如果是字符串转成数字
                num = num.toFixed(2) //保留两位
                num = parseFloat(num) //转成数字
                num = num.toLocaleString() //转成金额显示模式
                //判断是否有小数
                if (num.indexOf('.') == -1) {
                    num = num
                } else {
                    num = num.split('.')[1].length < 2 ? num + '0' : num
                }
                return num //返回的是具有千分位格式并保留2位小数的字符串
            } else {
                return (num = '0')
            }
        },
    },
    methods: {
        async init(params) {
            this.isShow = true
            this.fromData = params
            let { code, data } = await getOrderReportForms(params)
            if (code === 0) {
                this.froms = data.data.request_title
                this.tableData = [data.data.order_data_summary]
                this.areaOrderData = data.data.regin_order_data // 区域订单数据汇总
                this.allMemberData = data.data.user_order_data // 会员订单数据汇总
                this.goodsOrderData = data.data.product_order_data // 商品订单数据汇总
                this.shopOrderData = data.data.shop_order_data // 店铺订单数据汇总
                this.categoryOrderData = data.data.category_order_data // 类目订单数据汇总
                this.goodsOrderData.forEach(element => {
                    this.getBase64Image(element.image_url,(base64) => {
                        element.image_url = base64
                    })
                });
            }
        },
        handleClose() {
            this.isShow = false;
            this.froms = ''; // 统计范围
            this.fromData = {};
            this.tableData = [];
            this.allMemberData = [];
            this.areaOrderData = [];
            this.goodsOrderData = [];
            this.categoryOrderData = [];
            this.shopOrderData = [];
        },
        // 第一个参数是图片的URL地址，第二个是转换成base64地址后要赋值给的img标签
        getBase64Image(url = "",basefun) {
            if (!url) return;
            let link = url;
            var that = this;
            var image = new Image();
            image.src = link + "?v=" + Math.random(); // 处理缓存
            image.crossOrigin = "*"; // 支持跨域图片
            image.onload = function () {
                var base64 = that.drawBase64Image(image);
                basefun(base64)
            };
        },
        drawBase64Image(img) {
            var canvas = document.createElement("canvas");
            canvas.width = img.width;
            canvas.height = img.height;
            var ctx = canvas.getContext("2d");
            ctx.drawImage(img, 0, 0, img.width, img.height);
            var dataURL = canvas.toDataURL("image/png");
            return dataURL;
        },
        // 下载pdf
        async downloadPDF() {
            const element = document.getElementById('orderReportFormsDialogPage'); // 或者任何你想转换为PDF的DOM元素
            const canvas = await html2canvas(element, {
                useCORS: true
            });
            const imgData = canvas.toDataURL('image/png');
            var contentWidth = canvas.width
            var contentHeight = canvas.height
            var pdfX = (contentWidth + 10) / 2 * 0.75
            var pdfY = (contentHeight + 500) / 2 * 0.75 // 500为底部留白
            var imgX = pdfX;
            var imgY = (contentHeight / 2 * 0.75); //内容图片这里不需要留白的距离
            var pdf = new jsPDF('', 'pt', [pdfX, pdfY])
            pdf.addImage(imgData, 'PNG', 0, 0, imgX, imgY)
            pdf.save('download.pdf')
        },
        // 下载报表（表格）
        async getOrderReportFormsExport() {
            let res = await getOrderReportFormsExport(this.fromData)
            if (res.code === 0) {
                window.open(this.$path + '/' + res.data.link)
            }
        },
    },
};
import {
    createExpressTemplate,
    findExpressTemplate,
    updateExpressTemplate,
} from "@/api/expressTemplate.js";
import SelectRegion from "../selectRegion.vue";

export default {
    components: {SelectRegion},
    data() {
        return {
            isShow: false,
            title: "新增",
            supplierOptions:[],
            supplyOptions:[],
            formData: {
                supplier_id: null,
                gather_supply_id: null,
                name: "",
                type: 0, // 0按重量 1按件数
                items: [],
            },
            typeOptions: [
                {
                    label: "按重量",
                    value: 0,
                },
                {
                    label: "按件数",
                    value: 1,
                },
            ],
            rules: {
                supplier_id:{required: true, message:"请选择供应商",trigger: "change"},
                name: {required: true, message: "请输入模板名称", trigger: "blur"},
                type: {required: true, message: "请选择计算方式", trigger: "change"},
                items: {
                    required: true,
                    validator: (rule, value, callback) => {
                        if (value.length > 0) {
                            callback();
                        } else {
                            return callback(new Error("请添加配送区域"));
                        }
                    },
                },
            },
            amountRules: {
                first_amount: {
                    required: true, message: "请填写", trigger: "blur"
                },
                first_fee_standby: {
                    required: true, message: "请填写", trigger: "blur"
                },
                additional_amount: {
                    required: true, message: "请填写", trigger: "blur"
                },
                additional_fee_standby: {
                    required: true, message: "请填写", trigger: "blur"
                }
            }
        };
    },
    methods: {
        // 获取选中的地市
        getCheckedRegion(ids, regionNameArr, index) {
            if (index >= 0 && index !== null) {
                this.formData.items[index].region_names = regionNameArr;
                this.formData.items[index].regions = ids;
            } else {
                this.formData.items.push({
                    regions: ids,
                    region_names: regionNameArr,
                });
            }
        },
        findData(row) {
            findExpressTemplate({id: row.id}).then((res) => {
                if (res.code === 0) {
                    let data = res.data.reExpressTemplates;
                    this.formData.id = data.id;
                    this.formData.supplier_id = data.supplier_id;
                    this.formData.gather_supply_id = data.gather_supply_id;
                    this.formData.name = data.name;
                    this.formData.type = data.type;
                    data.items.forEach((item) => {
                        item.additional_fee = this.$changeMoneyF2Y(item.additional_fee);
                        item.first_fee = this.$changeMoneyF2Y(item.first_fee);
                        item.first_fee_standby = item.first_fee;
                        item.additional_fee_standby = item.additional_fee
                    });
                    this.formData.items = data.items;
                }
            });
        },
        aredEdit(row, index) {
            this.$refs.selectRegion.isShow = true;
            let formItem = this.$fn.deepClone(this.formData.items)
            formItem.splice(formItem.indexOfJSON('id',row.id),1)
            let disIds = this.regroupItems(formItem)
            this.$refs.selectRegion.disposeData(row.regions, index, disIds);
        },
        regroupItems(formItem) {
            let newArr = []
            let ids = []
            if(!formItem){
                return []
            }
            formItem.forEach(province => {
                newArr.push(province.regions)
            })
            newArr.forEach(item => {
                item.forEach(item1 => {
                    if (item1.regions.length === 0) {
                        ids.push(item1.id)
                    } else {
                        item1.regions.forEach(item2 => {
                            if (item2.regions.length === 0) {
                                ids.push(item2.id)
                            } else {
                                item2.regions.forEach(item3 => {
                                    if (item3.regions.length === 0 || item3.regions === null) {
                                        ids.push(item3.id)
                                    } else {
                                        item3.regions.forEach(item4 => {
                                            ids.push(item4.id)
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            })
            let newIds = ids.filter((item, index, newIds) => newIds.indexOf(item) === index)
            return newIds;
        },
        areaDel(row, index) {
            this.$confirm("你确定要删除该配送区域么?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
            })
                .then(() => {
                    this.formData.items.splice(index, 1);
                })
                .catch(() => {
                });
        },
        openRegion() {
            this.$refs.selectRegion.isShow = true;
            let disIds = this.regroupItems(this.formData.items)
            this.$refs.selectRegion.getProvince(disIds);
        },
        confirm() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.formData.items.forEach((item) => {
                        item.first_fee = this.$changeMoneyY2F(item.first_fee_standby);
                        item.additional_fee = this.$changeMoneyY2F(item.additional_fee_standby);
                        console.log(item);
                    });
                    // 修改
                    if (this.formData.id) {
                        updateExpressTemplate(this.formData).then((res) => {
                            if (res.code === 0) {
                                this.$message.success("操作成功");
                                this.$emit("load");
                                this.handleClose();
                            }
                        });
                    } else {
                        // 新增
                        createExpressTemplate(this.formData).then((res) => {
                            if (res.code === 0) {
                                this.$message.success("操作成功");
                                this.$emit("load");
                                this.handleClose();
                            }
                        });
                    }
                } else {
                    return false;
                }
            });
        },
        handleClose() {
            this.isShow = false;
            try {
                this.$refs.form.resetFields();
            } catch {
            } finally {
                this.formData.items = [];
                if (this.formData.id) {
                    delete this.formData.id;
                }
            }
        },
    },
};
.menu-main {
    height: 100%;

    .shared-first-sidebar {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        height: 100%;
        width: 100px;
        background-color: #444;
        display: -webkit-box;
        display: -webkit-flex;
        display: -moz-box;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -webkit-flex-direction: column;
        -moz-box-orient: vertical;
        -moz-box-direction: normal;
        flex-direction: column;
        overflow: hidden;
        z-index: 2;

        .shared-team-logo {
            position: fixed;
            top: 0;
            bottom: 0;
            height: 60px;
            width: 100px;
            word-break: break-word;
            background: #444;
            z-index: 1002;

            .shared-team-logo-wrap {
                display: block;
                margin: 0 16px;
                height: 60px;
                overflow: hidden;
                text-align: center;

                .logoimg {
                    margin: 12px auto 0;
                    width: 32px;
                    height: 32px;
                    -webkit-border-radius: 50%;
                    border-radius: 50%;
                    background-size: cover;
                    background-position: 50% 50%;
                    background-color: #fff;
                    border: 1px solid #fff;
                }
            }
        }

        .shared-first-sidebar-nav {
            margin-top: 60px;
            padding-right: 20px;
            box-sizing: content-box;
            width: 100%;
            -webkit-box-flex: 1;
            flex: 1 1 100%;
            overflow-x: hidden;
            overflow-y: scroll;

            li {
                width: 100px;
                font-size: 14px;
                height: 40px;
                line-height: 40px;
                cursor: pointer;
                &.active {
                    background: #fff;

                    &:hover {
                        background: rgba(255, 255, 255, 1);

                        span {
                            color: #333;
                        }
                    }

                    span {
                        color: #333;
                    }
                }

                &:hover {
                    background-color: rgba(255, 255, 255, 0.2);

                    span {
                        color: #fff;
                    }
                }

                span {
                    color: #c8c9cc;
                    display: block;
                    padding-left: 18px;

                    i {
                        margin-right: 5px;
                    }
                }
            }
        }

        .shared-corner {
            display: -webkit-box;
            display: -webkit-flex;
            display: -moz-box;
            display: flex;

            -webkit-box-flex: 0;
            -webkit-flex: 0 0 42px;
            -moz-box-flex: 0;
            flex: 0 0 42px;
            background-color: #535353;
            -webkit-box-orient: horizontal;
            -webkit-box-direction: normal;
            -webkit-flex-direction: row;
            -moz-box-orient: horizontal;
            -moz-box-direction: normal;
            flex-direction: row;

            .notice-center {
                -webkit-box-flex: 1;
                -webkit-flex: 1 1 50%;
                -moz-box-flex: 1;
                flex: 1 1 50%;
                padding: 10px;
                cursor: pointer;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
                font-size: 18px;
                position: relative;
                background-image: url(../../../../assets/message_menu.png);
                background-position: 50%;
                background-repeat: no-repeat;
                background-size: 20px;

                .notice-num-box {
                    position: absolute;
                    right: 30px;
                    top: 5px;
                    width: 15px;
                    height: 15px;
                    line-height: 15px;
                    background-color: red;
                    color: #ffffff;
                    text-align: center;
                    border-radius: 50%;
                    font-size: 12px;
                }
                .newMessage-box {
                    position: absolute;
                    right: 35px;
                    top: -3px;
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background-color: red;
                }
            }
        }
    }

    .shared-second-sidebar {
        width: 140px;
        height: 100%;
        margin-left: 100px;
        transition: all 0.2s;
        padding-bottom: 40px;
        background-color: #fff;
        border-right: 1px solid #ebedf0;
        z-index: 1;
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;

        .second-sidebar-title {
            flex: 0 0 60px;
            padding-left: 28px;
            width: 100%;
            height: 60px;
            box-sizing: border-box;
            border-bottom: 1px solid #ebedf0;
            font-size: 14px;
            color: #323233;
            line-height: 60px;
            font-weight: 500;
        }

        .shared-second-sidebar-nav {
            margin-top: 12px;
            padding-left: 12px;

            li.second-nav-item {
                position: relative;
                width: 115px;
                min-height: 30px;
                line-height: 30px;
                padding: 5px 0;
                cursor: pointer;
                font-size: 14px;
                -webkit-box-sizing: border-box;
                -moz-box-sizing: border-box;
                box-sizing: border-box;
                background: #fff;

                a {
                    display: block;
                    color: #323233;
                    text-align: left;
                    padding-left: 16px;
                    -webkit-border-radius: 2px;
                    border-radius: 2px;
                    font-size: 14px;

                    .down-up-i {
                        position: absolute;
                        left: 0;
                        top: 13px;
                    }

                    &:hover {
                        color: #155bd4;
                    }
                }

                &.active {
                    background: #f2f3f5;
                    border-radius: 2px;
                }

                .l3-a {
                    margin-left: 5px;
                }
            }
        }
    }
}
::v-deep .el-badge {
    .el-badge__content {
        top: -12px;
        right: 0px;
    }
}
::v-deep.shouhou {
    .el-badge__content {
        top: -1px !important;
        right: 0px;
    }
}
::v-deep.caiwu {
    .el-badge__content {
        top: -8px !important;
        right: 17px !important;
    }
}
//.shouhou{
//  top:0px !important;
//}
::v-deep .el-badge {
    .el-badge__content {
        top: -5px;
        right: 6px;
    }
}
.systemLog-list-box {
    margin-top: 15px;
    height: 75vh;
    overflow-y: auto;
    padding-right: 10px;
    .systemLog-list-item {
        border-radius: 12px;
        border: 1px solid #dee0e5;
        .title-box {
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            padding: 10px 12px;
            background-color: #fafafa;
            .title {
            }
        }

        .body {
            padding: 10px 12px;
            .new-d {
                margin-left: 20px;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: red;
            }
        }
    }
}

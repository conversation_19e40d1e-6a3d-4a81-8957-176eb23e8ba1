<template>
  <m-card>
    <el-form :inline="true" :model="searchInfo" class="search-term" label-width="80px">
      <el-form-item label="会员:">
        <div class="f">
          <el-select v-model="searchInfo.user_type" clearable>
            <el-option v-for="item in memberSearch" :value="item.value" :label="item.name"></el-option>
          </el-select>
          <el-input class="ml10" clearable placeholder="请输入" v-model="searchInfo.user_key"></el-input>
        </div>
      </el-form-item>
      <el-form-item label="提现编号:">
        <el-input clearable placeholder="请输入" v-model="searchInfo.order_sn"></el-input>
      </el-form-item>
      <el-form-item label="类型选择:">
        <el-select v-model="searchInfo.withdrawal_type" clearable >
          <el-option v-for="item in typeData" :value="item.value" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="提现方式:">
        <el-select v-model="searchInfo.withdrawal_status" clearable>
          <el-option v-for="item in wayData"  :value="item.value" :label="item.name"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="供应商:">
        <el-input clearable placeholder="请输入" v-model="searchInfo.supplier_name"></el-input>
      </el-form-item>
      <el-form-item label="开票状态:">
        <el-select v-model="searchInfo.invoice_status" clearable>
          <el-option :value="0" label="无需开票"></el-option>
          <el-option :value="1" label="待开票"></el-option>
          <el-option :value="2" label="已开票"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请时间:">
        <div class="f">
          <el-date-picker class="w100" placeholder="开始日期" type="datetime" v-model="start_time"></el-date-picker>
          <p class="title-3 ml10 mr10">至</p>
          <el-date-picker class="w100" placeholder="结束日期" type="datetime" v-model="end_time"></el-date-picker>
        </div>
      </el-form-item> 
      <el-form-item>
        <el-button @click="search" type="primary">搜索</el-button>
        <el-button @click="orderExport">导出</el-button>
        <el-button @click="clearSearch" type="text">重置搜索条件</el-button>
      </el-form-item> 
    </el-form>
    <el-tabs class="mt25" v-model="activeName" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="全部" name=""></el-tab-pane>
      <el-tab-pane label="待审核" name="1"></el-tab-pane>
      <el-tab-pane label="待打款" name="2"></el-tab-pane>
      <el-tab-pane label="打款中" name="3"></el-tab-pane>
      <el-tab-pane label="已完成" name="4"></el-tab-pane>
      <el-tab-pane label="已驳回" name="5"></el-tab-pane>
      <el-tab-pane label="已无效" name="6"></el-tab-pane>
    </el-tabs>
    <el-table :data="tableData">
      <el-table-column label="申请时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.created_at | formatDate }}
        </template>
      </el-table-column>
      <el-table-column label="提现编号" align="center">
        <template slot-scope="scope">
          {{ scope.row.order_sn }}
        </template>
      </el-table-column>
      <el-table-column label="手机号" align="center">
        <template slot="header">
          <p>昵称/供应商</p>
          <p>手机号</p>
        </template>
        <template slot-scope="scope">
          <p>{{ scope.row.supplier.name }}</p>
          <p>{{ scope.row.user.username }}</p>
        </template>
      </el-table-column>
      <!-- <el-table-column label="供应商" align="center" prop="supplier.name"></el-table-column> -->
      <el-table-column align="center">
        <template slot="header">
          <p>类型</p>
          <p>提现方式</p>
        </template>
        <template slot-scope="scope">
          <p>{{ scope.row.withdrawal_type | formatWithdrawType }}</p>
          <p>{{ scope.row.withdrawal_mode | formatWithdrawWay }}</p>
        </template>
      </el-table-column>
      <el-table-column label="申请金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.withdrawal_amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header">
          <p>手续费</p>
          <p>劳务税</p>
        </template>
        <template slot-scope="scope">
          <p>{{ scope.row.poundage_amount | formatF2Y }}</p>
          <p>{{ scope.row.service_tax | formatF2Y }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header">
          <p>驳回金额</p>
          <p>无效金额</p>
        </template>
        <template slot-scope="scope">
          <p>
            {{
              scope.row.withdrawal_operation_record.length > 0 ? scope.row.withdrawal_operation_record[0].rejected_amount : 0 | formatF2Y
            }}</p>
          <p>
            {{
              scope.row.withdrawal_operation_record.length ? scope.row.withdrawal_operation_record[0].invalid_amount : 0 | formatF2Y
            }}</p>
          <!--          <p v-for="item in scope.row.withdrawal_operation_record" :key="item.id" v-if="item.withdrawal_status === 1">
                      {{ item.rejected_amount | formatF2Y }}</p>
                    <p v-for="item in scope.row.withdrawal_operation_record" :key="item.id" v-if="item.withdrawal_status === 1">
                      {{ item.invalid_amount | formatF2Y }}</p>-->
        </template>
      </el-table-column>
      <el-table-column label="实际打款金额" align="center">
        <template slot-scope="scope">
          {{ scope.row.income_amount | formatF2Y }}
        </template>
      </el-table-column>
      <el-table-column label="审核状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.withdrawal_status | formatWithdrawalStatus }}
        </template>
      </el-table-column>
      <el-table-column label="打款状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.remit_status | formartRemitStatus }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="openRecordDetail(scope.row)">详情</el-button>
<!--          <el-button type="text" @click="jumpSettlement(scope.row)">结算余额</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{ float: 'right', padding: '20px' }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <record-detail ref="recordDetail"></record-detail>
  </m-card>
</template>
<script>
import {withdrawList, exportWithdrawal} from "@/api/finance";
import RecordDetail from "./components/recordDetail";
import infoList from "@/mixins/infoList";
import {formatTimeToStr} from "@/utils/date";


const path = process.env.VUE_APP_BASE_API;
export default {
  name: "financeWithdrawDepositRecord",
  components: {RecordDetail},
  mixins: [infoList],
  filters: {
    // 提现类型
    formatWithdrawType: function (type) {
      let text = ""
      switch (type) {
        case 1:
          text = "结算余额"
          break;
        case 2:
          text = "收入余额"
          break;
        default:
          text = "-"
          break;
      }
      return text
    },
    // 提现方式
    formatWithdrawWay: function (value) {
      let text = "";
      switch (value) {
        case 1:
          text = "手动提现"
          break;
        case 2:
          text = "汇聚提现"
          break;
        case 4:
          text = "微信-工猫提现"
          break;
        case 5:
          text = "支付宝-工猫提现"
          break;
        case 6:
          text = "银行卡-工猫提现"
          break;
        case 7:
          text = "站内余额"
          break;
        default:
          text = "-"
          break;
      }
      return text;
    },
    // 提现状态
    formatWithdrawalStatus: function (status) {
      let text = "";
      switch (status) {
        case  0:
          text = "待审核"
          break;
        case  1:
          text = "通过"
          break;
        case  2:
          text = "无效"
          break;
        case  3:
          text = "驳回"
          break;
        default:
          text = "-"
          break;
      }
      return text
    },
    // 打款状态
    formartRemitStatus: function (value) {
      let text = "";
      switch (value) {
        case  0:
          text = "待打款"
          break;
        case  1:
          text = "已打款"
          break;
        case  2:
          text = "打款中"
          break;
        case  4:
          text = "无需打款"
          break;
        default:
          text = "-"
          break;
      }
      return text
    }
  },
  data() {
    return {
      path: path,
      activeName: "",
      listApi: withdrawList,
      searchInfo:{
        start_time: '',
        end_time: '',
        user_key: '',
        user_type: '',
        withdrawal_status: '',
        withdrawal_type: '',
        order_sn: ''
      },
      start_time:'',
      end_time:'',
      memberSearch:[
        {value:1,name:'会员ID'},
        {value:2,name:'昵称'},
        {value:3,name:'手机号'},
      ], // 会员下拉框数组
      memberValue:'',// 会员输入框值
      typeData:[
        {value:1,name:'结算余额'},
        {value:2,name:'收入余额'},
      ],// 类型数组
      wayData:[
        {value:1,name:'手动提现'},
        {value:2,name:'汇聚提现'},
        {value:4,name:'微信-工猫提现'},
        {value:5,name:'支付宝-工猫提现'},
        {value:6,name:'银行卡-工猫提现'},
      ], // 提现方式
    };
  },
  mounted() {
    if (this.$route.query.withdrawal_type && this.$route.query.user_id) {
      this.searchInfo.withdrawal_type = parseInt(this.$route.query.withdrawal_type)
      this.searchInfo.user_id = parseInt(this.$route.query.user_id)
    }
    this.getTableData();
  },
  methods: {
    // 筛选
    search(){
      if (this.start_time != "" && this.start_time != undefined) {
        this.searchInfo.start_time = formatTimeToStr(this.start_time.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
      }

      if (this.end_time != "" && this.end_time != undefined) {
        this.searchInfo.end_time = formatTimeToStr(this.end_time.getTime() / 1000, "yyyy-MM-dd hh:mm:ss");
      }
      this.getTableData();
    },
    // 重置筛选条件
    clearSearch(){
      this.start_time = '';
      this.end_time = '';
      this.searchInfo = {
        start_time: '',
        end_time: '',
        user_key: '',
        user_type: '',
        withdrawal_status: '',
        withdrawal_type: '',
        order_sn: ''
      };
      this.getTableData()
    },
    // 导出
    orderExport() {
      /*let params = {
        status: this.activeName ? parseInt(this.activeName) : 0,
        page: this.page,
        pageSize: this.pageSize
      }*/
      exportWithdrawal(this.searchInfo).then(res => {
        if (res.code === 0) {
          window.location.href = this.path + '/' + res.data.link
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    jumpSettlement(row) {
      this.$_blank("/layout/Finance/settlement", {
        withdrawal_id: row.id
      })
    },
    handleTabClick(value) {
      this.searchInfo = {
        status: value.name ? parseInt(value.name) : 0
      }
      this.page = 1
      this.getTableData()
    },
    openRecordDetail(row) {
      this.$refs.recordDetail.isShow = true;
      this.$refs.recordDetail.getDetail(row.id)
    },
  },
};
</script>
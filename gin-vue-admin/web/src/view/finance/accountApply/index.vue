<template>
  <m-card>
    <el-button @click="openDialog" type="primary" >新增</el-button>
    <el-form :model="searchInfo" label-width="100px" class="search-term mt25" inline>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.alt_mch_name" clearable class="line-input-width">
          <el-select
            v-model="searchInfo.name_type"
            clearable
            slot="prepend"
          >
            <el-option :value="1" label="商户全称"></el-option>
            <el-option :value="2" label="商户简称"></el-option>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" v-model="searchInfo.legal_person" clearable class="line-input-width">
          <el-select
            v-model="searchInfo.legal_type"
            clearable
            slot="prepend"
            
          >
            <el-option :value="1" label="法人姓名"></el-option>
            <el-option :value="2" label="法人手机号"></el-option>
            <el-option :value="3" label="法人身份证号"></el-option>
          </el-select>
        </el-input>
      </el-form-item>
      <el-form-item>
          <el-input placeholder="请输入" v-model="searchInfo.login_name" class="line-input" clearable>
              <span slot="prepend">登录名</span>
          </el-input>
      </el-form-item>
      <el-form-item label="">
          <div class="line-input" >
              <div class="line-box ">
                  <span >商户类型</span>
              </div>
              <el-select v-model="searchInfo.alt_merchant_type" class="w100">
                  <el-option :value="10" label="个人"></el-option>
                  <el-option :value="11" label="个体工商户"></el-option>
                  <el-option :value="12" label="企业"></el-option>
                </el-select>
          </div>
      </el-form-item>

      <!-- <el-form-item label="商户全称">
        <el-input
          placeholder="请输入"
          v-model="searchInfo.alt_mch_name"
        ></el-input>
      </el-form-item> -->
      <!-- <el-form-item label="商户简称">
        <el-input
          placeholder="请输入"
          v-model="searchInfo.alt_mch_short_name"
        ></el-input>
      </el-form-item> -->

      <!-- <el-form-item label="法人姓名">
        <el-input
          placeholder="请输入"
          v-model="searchInfo.legal_person"
        ></el-input>
      </el-form-item>
      <el-form-item label="法人手机号">
        <el-input placeholder="请输入" v-model="searchInfo.phone_no"></el-input>
      </el-form-item>
      <el-form-item label="法人身份证号">
        <el-input
          placeholder="请输入"
          v-model="searchInfo.id_card_no"
        ></el-input>
      </el-form-item> -->
      <el-form-item>
        <el-button @click="onSubmit" type="primary" >查询</el-button>
        <el-button @click="ClearSearch" type="text">重置搜索条件</el-button>
      </el-form-item>
      <!-- <el-form-item>
        <el-button @click="openDialog" type="primary">新增开户申请</el-button>
      </el-form-item> -->
    </el-form>
    <el-table
      :data="tableData"
      @selection-change="handleSelectionChange"
      class="mt25"
    >
      <el-table-column label="ID" prop="id" align="center"></el-table-column>
      <el-table-column label="日期" align="center" show-overflow-tooltip>
        <template slot-scope="scope">{{
          scope.row.created_at | formatDate
        }}</template>
      </el-table-column>

      <el-table-column
        label="登录名"
        show-overflow-tooltip
        align="center"
        prop="login_name"
      ></el-table-column>

      <el-table-column label="商户类型" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          {{ scope.row.alt_merchant_type | formatMerchantType }}
        </template>
      </el-table-column>

      <el-table-column
        label="商户全称"
        show-overflow-tooltip
        align="center"
        prop="alt_mch_name"
      ></el-table-column>

      <el-table-column
        label="商户简称"
        show-overflow-tooltip
        align="center"
        prop="alt_mch_short_name"
      ></el-table-column>

      <el-table-column
        label="法人姓名"
        show-overflow-tooltip
        align="center"
        prop="legal_person"
      ></el-table-column>

      <!--      <el-table-column label="法人手机号" prop="phone_no"></el-table-column>-->

      <!--      <el-table-column label="法人身份证号" prop="id_card_no"></el-table-column>-->

      <el-table-column label="经营范围" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          {{
            scope.row.alt_merchant_type === "10" ? "-" : scope.row.manage_scope
          }}
        </template>
      </el-table-column>

      <!--      <el-table-column label="统一社会信用代码" prop="bank_channel_no"></el-table-column>-->

      <el-table-column
        label="联系人姓名"
        show-overflow-tooltip
        align="center"
        prop="busi_contact_name"
      ></el-table-column>

      <el-table-column
        label="联系人电话"
        show-overflow-tooltip
        align="center"
        prop="busi_contact_mobile_no"
      ></el-table-column>
      <el-table-column
        label="商户编号"
        show-overflow-tooltip
        align="center"
        prop="alt_mch_no"
      ></el-table-column>

      <el-table-column label="供应商" show-overflow-tooltip align="center">
        <template slot-scope="scope">
          {{ scope.row.supplier.name }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="180" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button @click="updateAccountApply(scope.row)" type="text"
            >编辑
          </el-button>

          <el-button type="text" @click="deleteRow(scope.row)" class="color-red"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      background
      :current-page="page"
      :page-size="pageSize"
      :page-sizes="[10, 30, 50, 100]"
      :style="{ float: 'right', padding: '20px' }"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
    <el-dialog
      :before-close="closeDialog"
      :visible.sync="dialogFormVisible"
      title="新增开户申请"
    >
      <el-steps :active="active" finish-status="success">
        <el-step title="资料填写"></el-step>
        <el-step title="图片上传"></el-step>
        <el-step title="协议签约"></el-step>
      </el-steps>
      <el-form
        :model="formData"
        label-position="right"
        label-width="15%"
        :rules="rule"
        ref="el-forms"
      >
        <div v-if="active == 0">
          <el-form-item label="登录名:" required="" prop="login_name">
            <el-input
              v-model="formData.login_name"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="商户全称:" required="" prop="alt_mch_name">
            <el-input
              v-model="formData.alt_mch_name"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="商户简称:">
            <el-input
              v-model="formData.alt_mch_short_name"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="商户类型:" required="" prop="alt_merchant_type">
            <el-radio v-model="formData.alt_merchant_type" label="10"
              >个人</el-radio
            >
            <el-radio v-model="formData.alt_merchant_type" label="11"
              >个体工商户</el-radio
            >
            <el-radio v-model="formData.alt_merchant_type" label="12"
              >企业</el-radio
            >
          </el-form-item>

          <el-form-item label="法人姓名:" required="" prop="legal_person">
            <el-input
              v-model="formData.legal_person"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="法人手机号:" required="" prop="phone_no">
            <el-input
              v-model="formData.phone_no"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="法人身份证号:" required="" prop="id_card_no">
            <el-input
              v-model="formData.id_card_no"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="身份证有效期:">
            <el-date-picker
              v-model="formData.id_card_expiry"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item label="经营范围:">
            <el-input
              v-model="formData.manage_scope"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="省市区:">
            <el-cascader
              size="large"
              :options="options"
              v-model="selectedOptions"
              @change="handleChange"
            >
            </el-cascader>
          </el-form-item>

          <el-form-item label="街道:">
            <el-input
              v-model="formData.street"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="营业执照编号:">
            <el-input
              v-model="formData.license_no"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="营业执照有效期:">
            <el-date-picker
              v-model="formData.license_expiry"
              type="date"
              placeholder="选择日期"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item
            label="联系人姓名:"
            required=""
            prop="busi_contact_name"
          >
            <el-input
              v-model="formData.busi_contact_name"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="联系人电话:"
            required=""
            prop="busi_contact_mobile_no"
          >
            <el-input
              v-model="formData.busi_contact_mobile_no"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="结算方式:" required="" prop="sett_mode">
            <el-radio v-model="formData.sett_mode" label="1">自动结算</el-radio>
            <!--            <el-radio v-model="formData.sett_mode" label="2">手工结算</el-radio>-->
            <!--            <el-radio v-model="formData.sett_mode" label="3">手工+D0 结算</el-radio>-->
            <!--            <el-radio v-model="formData.sett_mode" label="4">自动+D0 结算</el-radio>-->
          </el-form-item>

          <el-form-item label="结算周期类型:" required="" prop="sett_date_type">
            <el-radio v-model="formData.sett_date_type" label="1"
              >工作日</el-radio
            >
            <el-radio v-model="formData.sett_date_type" label="2"
              >自然日(D)</el-radio
            >
            <el-radio v-model="formData.sett_date_type" label="3"
              >月结日</el-radio
            >
          </el-form-item>

          <el-form-item label="结算周期:" required="" prop="risk_day">
            <el-input-number
              v-model="formData.risk_day"
              :min="1"
              :max="30"
              label=""
            ></el-input-number>
          </el-form-item>

          <el-form-item
            label="结算账户类型:"
            required=""
            prop="bank_account_type"
          >
            <el-radio v-model="formData.bank_account_type" label="1"
              >个人借记卡</el-radio
            >
            <el-radio v-model="formData.bank_account_type" label="4"
              >对公账户</el-radio
            >
            <!--            <el-input v-model="formData.bank_account_type" clearable placeholder="请输入" ></el-input>-->
          </el-form-item>

          <el-form-item
            label="银行账户名称:"
            required=""
            prop="bank_account_name"
          >
            <el-input
              v-model="formData.bank_account_name"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="银行账户:" required="" prop="bank_account_no">
            <el-input
              v-model="formData.bank_account_no"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="联行号:">
            <el-input
              v-model="formData.bank_channel_no"
              clearable
              placeholder="请输入"
            ></el-input>
          </el-form-item>

          <el-form-item label="会员:">
            <el-autocomplete
              v-model="nickname"
              :fetch-suggestions="querySearchAsync"
              placeholder="请选择会员"
              @select="handleSelect"
            ></el-autocomplete>

            <!--           <div style="margin-top: 15px;">-->
            <!--             <el-input placeholder="" v-model="formData.member_id" class="input-with-select">-->
            <!--               <template slot="prepend">请选择会员</template>-->
            <!--               <el-button @click="OpenMemberDialog" slot="append" icon="el-icon-search"></el-button>-->
            <!--             </el-input>-->
            <!--           </div>-->
            <!--           <el-input v-model.number="formData.member_id" clearable placeholder="请输入">-->
          </el-form-item>
        </div>

        <div v-if="active == 1">
          <el-form-item label="法人身份证照片:">
            <el-row>
              <el-col
                :span="6"
                style="
                  align-content: center;
                  vertical-align: center;
                  align-items: center;
                "
              >
                <el-upload
                  class="avatar-uploader"
                  :action="`${path}/fileUploadAndDownload/upload`"
                  :show-file-list="false"
                  :headers="{ 'x-token': token }"
                  :on-success="handleAvatarzSuccess"
                  :before-upload="$fn.beforeAvatarUpload"
                >
                  <img
                    v-if="formData.Legal_person_imgz_object"
                    :src="formData.Legal_person_imgz_object"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
                身份证正面
              </el-col>
              <el-col :span="6">
                <el-upload
                  class="avatar-uploader"
                  :action="`${path}/fileUploadAndDownload/upload`"
                  :show-file-list="false"
                  :headers="{ 'x-token': token }"
                  :on-success="handleAvatarfSuccess"
                  :before-upload="$fn.beforeAvatarUpload"
                >
                  <img
                    v-if="formData.Legal_person_imgf_object"
                    :src="formData.Legal_person_imgf_object"
                    class="avatar"
                  />
                  <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
                身份证反面
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item label="营业执照:">
            <el-upload
              class="avatar-uploader"
              :action="`${path}/fileUploadAndDownload/upload`"
              :show-file-list="false"
              :headers="{ 'x-token': token }"
              :on-success="handleBusinessSuccess"
              :before-upload="$fn.beforeAvatarUpload"
            >
              <img
                v-if="formData.business_license_img_object"
                :src="formData.business_license_img_object"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <!--            <el-input v-model="formData.business_license_img" clearable placeholder="请输入" ></el-input>-->
          </el-form-item>

          <el-form-item label="开户许可:">
            <el-upload
              class="avatar-uploader"
              :action="`${path}/fileUploadAndDownload/upload`"
              :show-file-list="false"
              :headers="{ 'x-token': token }"
              :on-success="handleOpenSuccess"
              :before-upload="$fn.beforeAvatarUpload"
            >
              <img
                v-if="formData.open_account_licence_object"
                :src="formData.open_account_licence_object"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>

          <el-form-item label="其他图片1:">
            <el-upload
              class="avatar-uploader"
              :action="`${path}/fileUploadAndDownload/upload`"
              :show-file-list="false"
              :headers="{ 'x-token': token }"
              :on-success="handleOtherOneSuccess"
              :before-upload="$fn.beforeAvatarUpload"
            >
              <img
                v-if="formData.other_img1_object"
                :src="formData.other_img1_object"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <!--            <el-input v-model="formData.other_img1" clearable placeholder="请输入" ></el-input>-->
          </el-form-item>

          <el-form-item label="其他图片2:">
            <el-upload
              class="avatar-uploader"
              :action="`${path}/fileUploadAndDownload/upload`"
              :show-file-list="false"
              :headers="{ 'x-token': token }"
              :on-success="handleOtherTwoSuccess"
              :before-upload="$fn.beforeAvatarUpload"
            >
              <img
                v-if="formData.other_img2_object"
                :src="formData.other_img2_object"
                class="avatar"
              />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <!--            <el-input v-model="formData.other_img2" clearable placeholder="请输入" ></el-input>-->
          </el-form-item>
        </div>

        <div v-if="active == 2" v-html="sign_html"></div>
      </el-form>
      <div class="dialog-footer" slot="footer" v-if="active == 0">
        <!--        <el-button @click="next">下一步</el-button>-->
        <el-button
          v-if="type == 'create'"
          @click="createApply_validate"
          type="primary"
          >申请开户</el-button
        >
        <el-button
          v-if="type == 'update'"
          @click="updateApply_validate"
          type="primary"
          >保存修改</el-button
        >

        <el-button @click="skippinga()" type="primary">跳过</el-button>
      </div>
      <div class="dialog-footer" slot="footer" v-if="active == 1">
        <!--        <el-button @click="next">下一步</el-button>-->
        <el-button @click="uploadImages" type="primary">上传</el-button>
        <el-button @click="skippingb()" type="primary">跳过</el-button>
      </div>
      <div class="dialog-footer" slot="footer" v-if="active == 2">
        <el-button @click="signContract" type="primary">确认签约</el-button>
      </div>
    </el-dialog>
  </m-card>
</template>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>
<script>
import { mapGetters } from "vuex";
import {
  createAccountApply,
  deleteAccountApply,
  deleteAccountApplyByIds,
  findAccountApply,
  findMemberList,
  findSignContract,
  signContract,
  getAccountApplyList,
  updateAccountApply,
  postImages,
  saveAccountApply,
} from "@/api/accountApply"; //  此处请自行替换地址
import { formatTimeToStr } from "@/utils/date";
import infoList from "@/mixins/infoList";
import { regionData } from "element-china-area-data";

const path = process.env.VUE_APP_BASE_API;
export default {
  name: "AccountApply",
  mixins: [infoList],
  data() {
    return {
      memberList: [],
      rule: {
        login_name: [
          {
            required: true,
            message: "请填写登录名",
            trigger: "blur",
          },
        ],
        alt_mch_name: [
          {
            required: true,
            message: "请填写商户全称",
            trigger: "blur",
          },
        ],

        legal_person: [
          {
            required: true,
            message: "请填写法人姓名",
            trigger: "blur",
          },
        ],
        phone_no: [
          {
            required: true,
            message: "请填写法人手机号",
            trigger: "blur",
          },
        ],
        id_card_no: [
          {
            required: true,
            message: "请填写法人身份证号",
            trigger: "blur",
          },
        ],

        busi_contact_name: [
          {
            required: true,
            message: "请填写联系人姓名",
            trigger: "blur",
          },
        ],
        busi_contact_mobile_no: [
          {
            required: true,
            message: "请填写联系人电话",
            trigger: "blur",
          },
        ],

        bank_account_name: [
          {
            required: true,
            message: "请填写银行账户名称",
            trigger: "blur",
          },
        ],
        bank_account_no: [
          {
            required: true,
            message: "请填写银行账户",
            trigger: "blur",
          },
        ],
      },

      active: 0,
      options: regionData,
      selectedOptions: [],
      path: path,
      listApi: getAccountApplyList,
      dialogFormVisible: false,
      memberFormVisible: false,
      type: "",
      sign_html: "",
      nickname: "",
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        alt_mch_no: "",
        login_name: "",
        alt_merchant_type: "10",
        alt_mch_name: "",
        alt_mch_short_name: "",
        legal_person: "",
        phone_no: "",
        id_card_no: "",
        Legal_person_imgz: "",
        Legal_person_imgf: "",
        other_img1: "",
        other_img2: "",
        business_license_img: "",
        open_account_licence: "",

        Legal_person_imgz_object: "",
        Legal_person_imgf_object: "",
        other_img1_object: "",
        other_img2_object: "",
        business_license_img_object: "",
        open_account_licence_object: "",
        manage_scope: "",
        province: "",
        city: "",
        area: "",
        street: "",
        license_no: "",
        sett_date_type: "1",
        license_expiry: "",
        bank_channel_no: "",

        busi_contact_name: "",
        busi_contact_mobile_no: "",
        sett_mode: "1",
        risk_day: "",
        bank_account_type: "1",
        bank_account_name: "",
        bank_account_no: "",
        deposit_bank: "",
        member_id: 0,
      },
    };
  },
  filters: {
    formatBoolean: function (bool) {
      if (bool != null) {
        return bool ? "是" : "否";
      } else {
        return "";
      }
    },
    formatMerchantType: function (status) {
      let name = "";
      switch (status) {
        case "10":
          name = "个人";
          break;
        case "11":
          name = "个体户";
          break;
        case "12":
          name = "企业";
          break;
      }
      return name;
    },
  },
  computed: {
    ...mapGetters("user", ["userInfo", "token"]),
  },
  mounted() {
    /* findMemberList({}).then((res) => {
      if (res.code === 0) {
        this.memberList = res.data.list;
      }
    }); */
  },
  methods: {
    skippinga() {
      this.active = 1;
    },
    skippingb() {
      this.active = 2;
    },

    async querySearchAsync(queryString, cb) {
      const res = await findMemberList({ nickName: queryString });

      if (res.code == 0) {
        cb(res.data.list);
      }
    },
    handleSelect(item) {
      this.formData.member_id = item.ID;
    },

    handleChange(value) {
      this.formData.province = value[0].toString();
      this.formData.city = value[1].toString();
      this.formData.area = value[2].toString();
    },
    handleAvatarzSuccess(res, file) {
      this.formData.Legal_person_imgz = res.data.file.url;
      this.formData.Legal_person_imgz_object = URL.createObjectURL(file.raw);
    },
    handleAvatarfSuccess(res, file) {
      this.formData.Legal_person_imgf = res.data.file.url;
      this.formData.Legal_person_imgf_object = URL.createObjectURL(file.raw);
    },

    handleBusinessSuccess(res, file) {
      this.formData.business_license_img = res.data.file.url;
      this.formData.business_license_img_object = URL.createObjectURL(file.raw);
    },
    handleOpenSuccess(res, file) {
      this.formData.open_account_licence = res.data.file.url;
      this.formData.open_account_licence_object = URL.createObjectURL(file.raw);
    },

    handleOtherOneSuccess(res, file) {
      this.formData.other_img1 = res.data.file.url;
      this.formData.other_img1_object = URL.createObjectURL(file.raw);
    },
    handleOtherTwoSuccess(res, file) {
      this.formData.other_img2 = res.data.file.url;
      this.formData.other_img2_object = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg";
      const isLt2M = file.size / 1024 / 1024 < 2;

      return isJPG && isLt2M;
    },

    ClearSearch() {
      this.searchInfo = {};
    },
    //条件搜索前端看此方法
    onSubmit() {
      this.page = 1;
      this.pageSize = 10;
      this.getTableData();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    deleteRow(row) {
      this.$confirm("确定要删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.deleteAccountApply(row);
      });
    },
    async onDelete() {
      const ids = [];
      if (this.multipleSelection.length == 0) {
        this.$message({
          type: "warning",
          message: "请选择要删除的数据",
        });
        return;
      }
      this.multipleSelection &&
        this.multipleSelection.map((item) => {
          ids.push(item.ID);
        });
      const res = await deleteAccountApplyByIds({ ids });
      if (res.code == 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length == ids.length) {
          this.page--;
        }
        this.deleteVisible = false;
        this.getTableData();
      }
    },
    async updateAccountApply(row) {
      this.$router.push({
        path: "/layout/Finance/accountApplyIntro",
        query: { member_id: row.id },
      });
      /* const res = await findAccountApply({ id: row.id });
      this.type = "update";
      if (res.code == 0) {
        this.formData = res.data.reAccount;
        this.selectedOptions = [
          String(this.formData.province),
          String(this.formData.city),
          String(this.formData.area),
        ];

        this.dialogFormVisible = true;
      } */
    },
    closeDialog() {
      this.active = 0;
      this.dialogFormVisible = false;
      this.formData = {
        alt_mch_no: "",
        open_account_licence: "",
        login_name: "",
        alt_merchant_type: "10",
        alt_mch_name: "",
        alt_mch_short_name: "",
        legal_person: "",
        phone_no: "",
        id_card_no: "",
        Legal_person_imgz: "",
        Legal_person_imgf: "",
        manage_scope: "",
        province: "",
        city: "",
        area: "",
        street: "",
        license_no: "",
        sett_date_type: "1",
        license_expiry: "",
        bank_channel_no: "",
        business_license_img: "",
        other_img1: "",
        other_img2: "",
        busi_contact_name: "",
        busi_contact_mobile_no: "",
        sett_mode: "1",
        risk_day: "",
        bank_account_type: "1",
        bank_account_name: "",
        bank_account_no: "",
        deposit_bank: "",
        member_id: 0,
      };
    },
    async deleteAccountApply(row) {
      const res = await deleteAccountApply({ id: row.id });
      if (res.code === 0) {
        this.$message({
          type: "success",
          message: "删除成功",
        });
        if (this.tableData.length === 1) {
          this.page--;
        }
        this.getTableData();
      }
    },

    uploadImages() {
      if (this.formData.Legal_person_imgf_object.length <= 0) {
        this.$message({
          type: "error",
          message: "请上传身份证反面",
        });
        return false;
      }

      if (this.formData.Legal_person_imgz_object.length <= 0) {
        this.$message({
          type: "error",
          message: "请上传身份证正面",
        });
        return false;
      }

      this.post_images();
    },

    async post_images() {
      // 第二部，提交图片资料
      let res;
      res = await postImages(this.formData);

      if (res.code === 0) {
        this.$message({
          type: "success",
          message: res.msg,
        });
        this.active = 2;
        this.findSignContract();
      }
    },

    async findSignContract() {
      let res;
      res = await findSignContract(this.formData);
      if (res.code === 0) {
        this.sign_html = res.data.reAccount.data.sign_content;
      }
    },

    async signContract() {
      let res;
      res = await signContract(this.formData);
      if (res.code == 0) {
        this.active = 3;
        this.$message({
          type: "success",
          message: res.data.reAccount.data.biz_msg,
        });
      } else {
        // this.$message({
        //   type: "error",
        //   message: res.msg
        // });
      }
    },

    createApply_validate() {
      this.$refs["el-forms"].validate((valid) => {
        if (!valid) return;
        this.createApply();
      });
    },

    updateApply_validate() {
      this.$refs["el-forms"].validate((valid) => {
        if (!valid) return;

        this.updateApply();
      });
    },

    async updateApply() {
      let res;
      res = await saveAccountApply(this.formData);
      if (res.code == 0) {
        this.formData.alt_mch_no = res.data.data.alt_mch_no;
        this.$message({
          type: "success",
          message: "修改成功",
        });
        // this.active=1;
      }
    },

    async createApply() {
      let res;
      res = await createAccountApply(this.formData);
      if (res.code == 0) {
        this.formData.alt_mch_no = res.data.data.alt_mch_no;
        this.$message({
          type: "success",
          message: "开户成功，请继续上传图像资料",
        });
        this.active = 1;
      }
    },

    async enterDialog() {
      let res;
      switch (this.type) {
        case "create":
          res = await createAccountApply(this.formData);
          break;

        case "update":
          res = await updateAccountApply(this.formData);
          break;
        default:
          res = await createAccountApply(this.formData);
          break;
      }
      if (res.code == 0) {
        this.formData.alt_mch_no = res.data.data.alt_mch_no;
        this.$message({
          type: "success",
          message: "创建/更改成功",
        });
        this.active = 1;
        this.closeDialog();
        this.getTableData();
      }
    },
    openDialog() {
      /* this.type = "create";
      this.dialogFormVisible = true; */
      this.$router.push("/layout/Finance/accountApplyIntro");
    },
  },
  async created() {
    await this.getTableData();
  },
};
</script>

<style lang="scss" scope>
@import "@/style/base.scss";
</style>

<template>
    <m-card>
        <el-form class="search-term" inline>
            <el-form-item class="mt_5">
                <div class="line-input">
                    <div class="line-box">
                        <span>采购端</span>
                    </div>
                    <el-select
                        class="w100"
                        filterable
                        clearable
                        v-model="orderApplicationConditionsTag"
                    >
                        <el-option
                            v-for="item in orderApplicationConditions"
                            :key="item.id"
                            :label="item.app_name"
                            :value="item.id"
                        >
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="mini" @click="search">
                    搜索
                </el-button>
                <el-button type="text" @click="reset">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table class="mt_20" :data="dataList">
            <el-table-column label="采购端名称" align="center" prop="appName">
            </el-table-column>
            <el-table-column label="累计销售数量" align="center" prop="total">
            </el-table-column>
            <el-table-column
                label="上月销售数量"
                align="center"
                prop="last_month"
            >
            </el-table-column>
            <el-table-column
                label="上周销售数量"
                align="center"
                prop="last_week"
            >
            </el-table-column>
            <el-table-column
                label="昨天销售数量"
                align="center"
                prop="yest_day"
            >
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
                display: 'flex',
                justifyContent: 'flex-end',
                marginRight: '20px',
            }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
    </m-card>
</template>

<script>
import { getApplicationOption } from '@/api/order';
import { getAppCountTotal } from '@/api/guangdian';
export default {
    name: 'dataCount',
    data() {
        return {
            orderApplicationConditions: [{ app_name: '全部', id: 0 }],
            orderApplicationConditionsTag: 0, // 第一条采购端数据
            dataList: [], // 列表数据
            page: 1,
            pageSize: 10,
            total: null,
        };
    },
    mounted() {
        this.getApplication();
        this.getAppCountTotal();
    },
    methods: {
        //应用
        getApplication() {
            getApplicationOption().then((res) => {
                this.orderApplicationConditions = res.data.list;
                this.orderApplicationConditions.unshift({
                    app_name: '全部',
                    id: 0,
                });
            });
        },
        // 获取数据统计
        async getAppCountTotal() {
            const data = {
                app_id: parseInt(this.orderApplicationConditionsTag),
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await getAppCountTotal(data);
            if (res.code === 0) {
                this.dataList = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        search() {
            this.page = 1;
            this.getAppCountTotal();
        },
        // 重置搜索
        reset() {
            this.orderApplicationConditionsTag = 0;
        },
        // 当前页数
        handleCurrentChange(page) {
            this.page = page;
            this.getAppCountTotal();
        },
        // 每页条数
        handleSizeChange(size) {
            this.pageSize = size;
            this.search();
        },
    },
};
</script>

<style></style>

<!-- 规格介绍 -->
<template>
    <el-drawer title="订单报表" :visible="isShow" :close-on-press-escape="false" :append-to-body="true"
        :wrapperClosable="false" :before-close="handleClose" size="calc(100% - 220px)" class="detail-ct">
        <div slot="title" style='width: 280px;'>
            <el-button @click="save" type="primary">保存规格介绍</el-button>
        </div>
        <m-card>
            <el-form label-width="130px">
                <el-tabs type="card" v-model="activeName">
                    <el-tab-pane label="基本信息" name="1">
                        <el-row>
                            <el-col :span="16">
                                <el-form-item>
                                    <span slot="label"><span class="color-red">*</span>商品名称:</span>
                                    <el-input v-model="title"></el-input>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label">商品描述:</span>
                                    <el-input v-model="desc" :maxlength="60" show-word-limit></el-input>
                                    <p class="color-grap">商品描述最多可输入60个字符</p>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"><span class="color-red">*</span>商品主图:</span>
                                    <el-upload class="avatar-uploader" :show-file-list="false"
                                        :action="path + '/fileUploadAndDownload/upload'" :headers="{ 'x-token': token }"
                                        :on-success="handleMainImgSuccess" :before-upload="$fn.beforeAvatarUpload"
                                        accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                                        <img v-if="image_url" :src="image_url" class="avatar" />
                                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                    </el-upload>
                                    <p class="color-grap">建议尺寸：400*400像素,图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
                                </el-form-item>
                                <el-form-item>
                                    <span slot="label"><span class="color-red">*</span>商品图片:</span>
                                    <div class="f fac">
                                        <p>({{ goodsGallery.length }}/9)</p>
                                        <el-button type="text" class="ml10" @click="openDialogSort(1)">点击进行排序
                                        </el-button>
                                    </div>
                                    <div class="f fw">
                                        <div class="productImgBox" v-for="(item, index) in goodsGalleryFileList"
                                            :key="item.id">
                                            <m-image v-if="item.type === 1" :src="item.url"
                                                style="width:180px;height:180px" />
                                            <div class="del-box">
                                                <el-button type="text" icon="el-icon-delete"
                                                    @click="removeGoodsGalleryList(index)"></el-button>
                                            </div>
                                        </div>
                                        <el-upload v-if="goodsGalleryFileList.length < 9" class="avatar-uploader"
                                            multiple list-type="picture-card"
                                            :action="path + '/fileUploadAndDownload/upload'"
                                            :headers="{ 'x-token': token }" :on-success="handleGoodsGallerySuccess"
                                            :before-upload="$fn.beforeAvatarUpload" :show-file-list="false"
                                            accept=".jpg,.jpeg,.png,.gif,.bmp,.pdf,.JPG,.JPEG,.PBG,.GIF,.BMP,.PDF">
                                            <i class="el-icon-plus avatar-uploader-icon"></i>
                                        </el-upload>
                                    </div>
                                    <p class="color-grap">建议尺寸：400*400像素,最多上传9个素材,单张图片需限制在{{ $store.state.uploadLimitSize }}M以内</p>
                                </el-form-item>
                                <el-form-item label="首图视频:">
                                    <div class="f fac">
                                        <el-progress v-if="progressIsShow" type="circle" :width="178"
                                            :percentage="progressNum" style="margin-right: 8px;"></el-progress>
                                        <div v-if="videoUrl && !progressIsShow" class="video-box">
                                            <video controls="controls" :src="videoUrl"
                                                style="width: 100%;height: 100%"></video>
                                        </div>
                                        <el-upload class="avatar-uploader" :show-file-list="false"
                                            :action="path + '/fileUploadAndDownload/upload'"
                                            :headers="{ 'x-token': token }" :on-success="handleMainVideoSuccess"
                                            :on-progress="videoPrigress" :on-error="videoError"
                                            :before-upload="$fn.beforeVideoUpload" accept=".mp4,.3gp,.avi">
                                            <i v-if="videoUrl" class="el-icon-edit-outline avatar-uploader-icon"></i>
                                            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                                        </el-upload>

                                    </div>
                                    <el-button v-if="videoUrl" type="text" @click="removeVideo">移除视频</el-button>
                                    <p class="color-grap">设置后商品详情首图默认显示视频,建议时长9-30秒</p>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="商品详情" name="2">
                        <m-editor v-model="describe"></m-editor>
                    </el-tab-pane>
                    <el-tab-pane label="商品参数" name="3">
                        <el-table :data="tableData" border>
                            <el-table-column label="属性名称">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.name"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="属性值">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.value"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100" align="center">
                                <template slot-scope="scope">
                                    <el-button type="text" slot="reference" class="color-red"
                                               @click="del(scope.$index)">删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                        <div class="mt25">
                            <el-button @click="add" type="primary">添加属性</el-button>
                            <el-button @click="allDel">清空</el-button>
                        </div>
                    </el-tab-pane>
                </el-tabs>
            </el-form>
        </m-card>
        <el-dialog title="商品详情图片" :close-on-click-modal="false" :close-on-press-escape="false"
            :visible="dialogSortIsShow" width="50%" :before-close="dialogSortClose" :append-to-body="true">
            <draggable v-model="goodsGallery" chosenClass="chosen" forceFallback="true" group="people" animation="500"
                @start="onStart" @end="onEnd(1)" class="draggable-box">
                <transition-group>
                    <div class="item" v-for="element in goodsGallery" :key="element.src">
                        <img :src="element.url">
                    </div>
                </transition-group>
            </draggable>
        </el-dialog>
    </el-drawer>
</template>

<script>
import { mapGetters } from "vuex";
//导入draggable组件
import draggable from "vuedraggable";

export default {
    components: { draggable },
    data() {
        return {
            isShow: false,
            path: this.$path,
            drag: false,

            activeName: "1",
            title: '', // 商品名称
            image_url: '', // 主图
            desc: '', // 商品描述

            goodsGallery: [],//产品图册  - type 类型（1图片2视频） - src 资源链接
            goodsGalleryFileList: [], //

            progressIsShow: false,
            progressNum: 0,
            videoUrl: "", // 首图视频
            describe: "", // 商品详情
            index: null, // 规格下标

            tableData: [], // 属性
            formData: {}, // 添加属性

            dialogSortIsShow: false,

        }
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),
    },
    methods: {
        handleClose() {
            this.isShow = false;
            this.index = null;
            this.describe = '';
            this.title = '';
            this.desc = '';
            this.goodsGalleryFileList = [];
            this.goodsGallery = []
            this.videoUrl = '';
            this.image_url = '';
            this.activeName = '1'
            this.tableData = []
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error('上传图片大小不能超过 10MB!');
            }
            return isLt10M;
        },
        // 上传主图
        handleMainImgSuccess(res) {
            if (res.code === 0) {
                this.image_url = res.data.file.url;
            } else {
                this.$message.error(res.msg)
            }
        },
        // 打开排序dialog
        openDialogSort(type) {
            if (type === 1) {
                if (this.goodsGallery.length > 0) {
                    this.dialogSortIsShow = true
                } else {
                    this.$message.error("请上传图片")
                }
            }
        },
        // 商品相册图片
        handleGoodsGallerySuccess(res) {
            if (res.code === 0) {
                this.goodsGallery.push({
                    type: 1,
                    src: res.data.file.url,
                    url: res.data.file.url
                })
                this.goodsGalleryFileList = [...this.goodsGallery];

            } else {
                this.$message.error(res.msg)
            }
        },
        // 上传首图视频
        handleMainVideoSuccess(res) {
            if (res.code === 0) {

                this.progressIsShow = false;
                this.videoUrl = res.data.file.url;

            } else {
                this.$message.error(res.msg)
            }
        },
        videoPrigress(event, file, fileList) {
            this.progressNum = 0;
            this.progressIsShow = true
            if (Math.floor(event.percent) >= 100) {
                setTimeout(() => {
                    this.progressNum = 99;
                }, 500)
            } else {
                setTimeout(() => {
                    this.progressNum = Math.floor(event.percent);
                }, 500)
            }

        },
        removeVideo() {
            this.videoUrl = ""
        },
        videoError(err, file, fileList) {
            this.progressNum = 0;
            this.progressIsShow = true
            this.$message.error("上传失败")
        },
        beforeVideoUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 20;
            if (!isLt10M) {
                this.$message.error('上传视频大小不能超过 20MB!');
            }
            return isLt10M;
        },
        // 删除商品相册图片
        removeGoodsGalleryList(index) {
            this.goodsGallery.splice(index, 1)
            this.goodsGalleryFileList.splice(index, 1)
        },

        // 添加属性
        add() {
            this.tableData.push({name: "", value: ""});
        },
        // 删除添加属性
        del(i) {
            this.tableData = this.tableData.filter((item, index) => index !== i)
        },
        // 清空添加属性
        allDel() {
            this.tableData = []
        },
        // 关闭排序dialog
        dialogSortClose() {
            this.dialogSortIsShow = false
        },
        //开始拖拽事件
        onStart() {
            this.drag = true;
        },
        //拖拽结束事件
        onEnd(type) {
            this.drag = false;
            this.$message.success("排序成功!")

            switch (type) {
                case 1:
                    this.goodsGalleryFileList = [...this.goodsGallery];
                    break;
            }
        },
        // 保存规格
        save() {
            if (!this.title) {
                this.$message.warning("请先选择商品名称");
                return
            }
            if (!this.image_url) {
                this.$message.warning("请先选择商品主图");
                return
            }
            if (this.goodsGalleryFileList.length === 0) {
                this.$message.warning("请先选择商品图片");
                return
            }
            let data = {
                index: this.index,
                describe: this.describe, // 商品描述
                title: this.title, // 商品名称
                desc: this.desc, // 商品描述
                video_url: this.videoUrl, // 首图视频
                image_url: this.image_url, // 商品主图
                attrs: this.tableData // 属性
            }
            data.gallery = this.goodsGalleryFileList.map(element => {
                return { type: parseInt(element.type), src: element.src }
            });
            this.$emit("getDescribe", data)
            this.handleClose()
        },
        // 开启规格弹窗
        setData(item, index) {
            this.describe = item.describe
            this.index = index;
            this.title = item.title;
            this.desc = item.desc ? item.desc : '';
            this.videoUrl = item.video_url ? item.video_url : '';
            this.image_url = item.image_url ? item.image_url : '';
            if (item.attrs && item.attrs.length > 0) {
                this.tableData = item.attrs
            } else {
                this.tableData = []
            }

            if (item.gallery) {
                item.gallery.forEach(element => {
                    this.goodsGalleryFileList.push({
                        type: parseInt(element.type),
                        src: element.src,
                        url: element.src
                    })
                });
                this.goodsGallery = [...this.goodsGalleryFileList]
            } else {
                this.goodsGalleryFileList = []
            }

        }
    }
}
</script>

<style lang="scss" scoped>
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;

    line-height: 178px;
    text-align: center;
}


::v-deep .avatar-uploader {
    .el-upload-list {
        li {
            width: 178px;
            height: 178px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 178px;
        height: 178px;

        img {
            width: 178px;
            height: 178px;
        }
    }
}

.draggable-box {
    span {
        display: flex;
        flex-wrap: wrap;
    }

    /*被拖拽对象的样式*/
    .item {
        width: 180px;
        height: 180px;
        padding: 6px;
        margin-left: 10px;
        margin-bottom: 10px;
        cursor: move;

        &:nth-child(1) {
            margin-left: 0;
        }
    }

    .item img {
        width: 100%;
        height: 100%;
    }

    /*选中样式*/
    .chosen {
        border: solid 1px #269d88 !important;
    }
}

::v-deep .avatar-uploader {
    .el-upload-list {
        li {
            width: 178px;
            height: 178px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 178px;
        height: 178px;

        img {
            width: 178px;
            height: 178px;
        }
    }
}

.video-box {
    width: 178px;
    height: 178px;
    border: 1px dashed #d9d9d9;
    margin-right: 8px;
}
</style>
<template>
  <m-card>
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
          <el-tab-pane label="基础设置" name="baseSetting">
              <BaseSetting ref="baseSetting" />
          </el-tab-pane>
      </el-tabs>
  </m-card>
</template>
<script>
import BaseSetting from "./components/baseSetting.vue"
export default {
    name: "distributionSynchronizationBaseIndex",
    components:{
        BaseSetting
    },
    data(){
        return {
            activeName: "baseSetting"
        }
    },
    mounted() {
      this.handleClick('baseSetting')
    },
    methods:{
        handleClick(v){
            this.$refs[this.activeName].init()
        }
    }
}
</script>
<style scoped lang="scss"></style>
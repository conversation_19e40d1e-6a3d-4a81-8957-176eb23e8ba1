<template>
  <!-- <div>自动同步</div> -->
  <el-dialog title="自动同步" :visible.sync="showDialog" width="35%">
    <el-form :model="autoForm" label-width="120px">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-form-item label="选择供应链:">
            <el-checkbox-group v-model="autoForm.checkList">
              <el-checkbox v-for="item in gatherList" :key="item.id" :label="item.id">{{ item.name }}</el-checkbox>
            </el-checkbox-group>
            <p class="tip">每天自动同步勾选的供应链中共享的商品专辑，只同步未导入的，不会更新已导入的！</p>
            <p class="tip">自动同步如果专辑中商品未同步的，会自动同步商品，自动创建/匹配分类导入！</p>
          </el-form-item>
        </el-col>
        <el-col :span="14">
          <el-form-item label="选择会员:">
            <el-select class="w100" v-model="autoForm.uid" filterable clearable placeholder="请输入手机号搜索">
              <el-option v-for="item in userList" :key="item.id" :label="item.nickname" :value="item.id"> </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="同步时间:">
            <el-time-picker v-model="autoForm.time" format="HH:mm" placeholder="请选择自动同步时间"> </el-time-picker>
          </el-form-item>
        </el-col>
        <el-col :offset="16">
          <el-button type="primary" @click="autoOk">确定</el-button>
          <el-button @click="cancelAuto">取消</el-button>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import { getUserList } from '@/api/member'
import { getGatherList, getAutoSynchroSetting, storeAutoSynchroSetting } from '@/api/shareAlbum'

export default {
  name: 'autoSyncIndex',

  data() {
    return {
      showDialog: false,
      autoForm: {
        checkList: [], // 复选框的数据
        uid: null, // 会员ID
        time: null, // 同步时间
      },
      userList: [], // 会员列表
      gatherList: [], // 供应链列表
      id: null, // 返回的id
      key: '',
      created_at: null,
      updated_at: null,
    }
  },

  mounted() {},

  methods: {
    // 获取会员
    async getUserList() {
      const params = {
        status: 1,
        page: 1,
        pageSize: 999999,
      }
      const res = await getUserList(params)
      if (res.code === 0) {
        this.userList = res.data.list
      }
    },
    // 获取供应链列表
    async getGatherList() {
      const data = {
        name: '',
      }
      const res = await getGatherList(data)
      if (res.code === 0) {
        this.gatherList = res.data.list
      }
    },
    // 自动同步设置查询
    async getAutoSynchroSetting() {
      const res = await getAutoSynchroSetting()
      if (res.code === 0) {
        const newValue = JSON.parse(res.data.setting.value)
        this.autoForm.checkList = newValue.gather_ids
        this.autoForm.uid = newValue.uid
        this.autoForm.time = new Date(2016, 9, 10, newValue.sync_hour, newValue.sync_minute)
        this.id = res.data.setting.id
        this.key = res.data.setting.key
        this.created_at = res.data.setting.created_at
        this.updated_at = res.data.setting.updated_at
      }
    },
    // 确定同步
    async autoOk() {
      // 将时间转换格式
      const dateStr = this.autoForm.time
      const date = new Date(dateStr)
      const hours = String(date.getHours()).padStart(2, '0') // 获取小时，并补齐两位
      const minutes = String(date.getMinutes()).padStart(2, '0') // 获取分钟，并补齐两位
      const seconds = String(date.getSeconds()).padStart(2, '0') // 获取秒，并补齐两位
      const timeStr = `${hours}:${minutes}:${seconds}`
      console.log(timeStr)
      // 存储选中的信息
      const val = {
        uid: this.autoForm.uid,
        gather_ids: this.autoForm.checkList,
        sync_hour: parseInt(hours),
        sync_minute: parseInt(minutes),
      }
      let data = {}
      if (this.id) {
        data = {
          key: this.key,
          value: val,
          id: this.id,
          created_at: this.created_at,
          updated_at: this.updated_at,
        }
      } else {
        data = {
          key: '',
          value: val,
          id: 0,
          created_at: '',
          updated_at: '',
        }
      }
      // if (this.autoForm.checkList.length === 0) {
      //   this.$message.error('请选择同步的供应链')
      //   return
      // }
      if (!this.autoForm.uid) {
        this.$message.error('请选择同步的会员')
        return
      }
      if (!this.autoForm.time) {
        this.$message.error('请选择同步的时间')
        return
      }
      const res = await storeAutoSynchroSetting(data)
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.showDialog = false
      }
    },
    // 取消同步
    cancelAuto() {
      this.showDialog = false
    },
  },
}
</script>

<style lang="scss" scoped>
.tip {
  font-size: 12px;
  color: rgba(16, 16, 16, 1);
}
</style>

<!-- 导入商品列表 -->
<template>
    <el-drawer
        title="详情"
        :visible="isShow"
        :close-on-press-escape="false"
        :wrapperClosable="false"
        :before-close="handleClose"
        size="calc(100% - 220px)"
        class="detail-ct"
    >
        <m-card>
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
                <el-tab-pane label="成功商品列表" name="success">
                    <el-table :data="tableData">
                        <el-table-column label="商品标题" align="center">
                            <template slot-scope scope="scop"
                                >{{ scop.row.title }}
                            </template>
                        </el-table-column>
                        <el-table-column label="商品分类" align="center">
                            <template slot-scope="scope">
                                {{ scope.row.CategoryOne.name }}/
                                {{ scope.row.CategoryTwo.name }}/
                                {{ scope.row.CategoryThree.name }}
                            </template>
                        </el-table-column>
                        <el-table-column label="图片" align="center">
                            <template slot-scope="scope">
                                <img
                                    :src="scope.row.image_url"
                                    width="100"
                                    height="100"
                                />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button type="text" @click="edit(scope.row)"
                                    >编辑
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        :current-page="page"
                        :page-size="pageSize"
                        :page-sizes="[10, 30, 50, 100]"
                        :style="{ display: 'flex', justifyContent: 'flex-end' }"
                        :total="total"
                        @current-change="handleCurrentChange"
                        @size-change="handleSizeChange"
                        layout="total, sizes, prev, pager, next, jumper"
                    ></el-pagination>
                </el-tab-pane>
                <el-tab-pane label="失败商品列表" name="error">
                    <el-table :data="errData.tableData">
                        <el-table-column label="商品标题" align="center">
                            <template slot-scope scope="scop"
                                >{{ scop.row.title }}
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="商品分类" align="center">
                            <template slot-scope="scope">
                                {{ scope.row.CategoryOne.name }}/
                                {{ scope.row.CategoryTwo.name }}/
                                {{ scope.row.CategoryThree.name }}
                            </template>
                        </el-table-column>
                        <el-table-column label="图片" align="center">
                            <template slot-scope="scope">
                                <img
                                    :src="scope.row.image_url"
                                    width="100"
                                    height="100"
                                />
                            </template>
                        </el-table-column> -->
                        <el-table-column label="操作" align="center">
                            <template slot-scope="scope">
                                <el-button type="text" @click="edit(scope.row)"
                                    >编辑
                                </el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination
                        background
                        :current-page="errData.page"
                        :page-size="errData.pageSize"
                        :page-sizes="[10, 30, 50, 100]"
                        :style="{ display: 'flex', justifyContent: 'flex-end' }"
                        :total="errData.total"
                        @current-change="handleErrCurrentChange"
                        @size-change="handleErrSizeChange"
                        layout="total, sizes, prev, pager, next, jumper"
                    ></el-pagination>
                </el-tab-pane>
            </el-tabs>
        </m-card>
    </el-drawer>
</template>
<script>
import {
    getSupplyGoodsList,
    getImportRecordErrorList,
} from '@/api/gatherSupply';

export default {
    data() {
        return {
            activeName: 'success',
            isShow: false,
            importId: 0,
            batch:'',
            is_plugin: '',
            page: 1,
            pageSize: 10,
            total: 0,
            tableData: [],
            source: '127',
            errData: {
                tableData: [],
                page: 1,
                pageSize: 10,
                total: 0,
            },
        };
    },

    created() {
        console.log('接到id', this.importId);
    },
    mounted() {
        console.log('接到id1', this.importId);
    },
    methods: {
        handleClick(v) {
            switch (this.activeName) {
                case 'success':
                    this.getSuccessGoodsList();
                    break;
                case 'error':
                    this.getErrorGoodsList();
                    break;
            }
        },
        // 获取失败商品列表
        getErrorGoodsList() {
            let params = {
                batch: this.batch,
                page: this.errData.page,
                pageSize: this.errData.pageSize,
                source: this.source,
                is_plugin: parseInt(this.is_plugin),
            };
            getImportRecordErrorList(params).then((res) => {
                if (res.code === 0) {
                    this.errData.tableData = res.data.list;
                    this.errData.total = res.data.total;
                } else {
                    this.errData.tableData = [];
                    this.errData.total = 0;
                    this.$message.error(res.msg);
                }
            });
        },
        // 获取成功商品列表
        getSuccessGoodsList() {
            let params = {
                id: this.importId,
                page: this.page,
                pageSize: this.pageSize,
                source: this.source,
                is_plugin: parseInt(this.is_plugin),
            };
            getSupplyGoodsList(params).then((res) => {
                if (res.code === 0) {
                    this.tableData = res.data.list;
                    this.total = res.data.total;
                } else {
                    this.tableData = [];
                    this.total = 0;
                    this.$message.error(res.msg);
                }
            });
        },
        handleClose() {
            this.isShow = false;
            this.activeName = 'success';
            this.isShow = false;
            this.importId = 0;
            this.page = 1;
            this.pageSize = 10;
            this.total = 0;
            this.tableData = [];
            this.source = '127';
            this.errData = {
                tableData: [],
                page: 1,
                pageSize: 10,
                total: 0,
            };
        },
        edit(row) {
            this.$_blank('/layout/goodsIndex/addGoods', { id: row.id });
            /* this.$router.push({
              path: "/layout/goodsIndex/addGoods",
              query: {
                id: row.id,
              },
            }); */
        },
        handleErrCurrentChange(page) {
            this.errData.page = page;
            this.getErrorGoodsList();
        },
        handleErrSizeChange(size) {
            this.errData.pageSize = size;
            this.getErrorGoodsList();
        },
        handleCurrentChange(page) {
            this.page = page;
            this.getSuccessGoodsList();
        },
        handleSizeChange(size) {
            this.pageSize = size;
            this.getSuccessGoodsList();
        },
    },
};
</script>
<style lang="scss" scoped></style>

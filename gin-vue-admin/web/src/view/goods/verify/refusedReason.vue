<template>
    <el-dialog
        width="800px"
        title="驳回理由"
        :visible="visible"
        @open="onOpen"
        @close="onClose"
        @closed="onClosed"
    >
        <el-row :gutter="10">
            <el-form :model="formData">
                <el-col>
                    <m-editor v-model="formData.remark"></m-editor>
                </el-col>
                <el-col class="mt50" :span="6" :offset="18" style="display: flex;">
                    <el-button size="mini" @click="onClose">取消</el-button>
                    <el-button type="primary" size="mini" @click="onSubmit">确定</el-button>
                </el-col>
            </el-form>
        </el-row>
    </el-dialog>
</template>
<script>
import {productVerifyList, supplierProductVerify, VerifyByIds} from "@/api/product";
export default ({
    name:'refusedReason',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      isBatch: {
        type: Boolean,
        default: false
      },
      isSingle: {
        type: Boolean,
        default: false
      },
      propRow: {/* 单条 */
        type: Object,
        default: () => { return {} }
      },
      batchParams: {/* 批量 */
        type: Object,
        default: () => { return {} }
      },
    },
    data() {
        return {
            formData: {
              remark:'',
            } 
        }
    },
    methods: {
      onOpen () {       
        if(this.isSingle){
          this.formData = {...this.propRow}
        }
      },
      onClose () {
        this.$emit('update:visible', false)
        this.formData = {}
      },
      onClosed () {
        this.formData = {}
      },
      onSubmit(){
        // console.log('this.isBatch',this.isBatch);
        // console.log('this.isSingle',this.isSingle);
        // console.log('this.batchParams',this.batchParams);
        // console.log('this.formData',this.formData);
        if(this.isBatch){ 
          this.batchParams.remark = this.formData.remark
          VerifyByIds(this.batchParams).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.onClose()
              this.$emit('refresh')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
        if(this.isSingle){
          supplierProductVerify(this.formData).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.onClose()
               this.$emit('refresh')
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      }
    },
})
</script>
<style lang='scss' scoped>
.mt50 {
  margin-top: 50px;
}
</style>
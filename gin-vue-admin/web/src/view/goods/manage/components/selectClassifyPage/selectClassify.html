<div class="big">
  <div class="selectClassify-box f">
    <div class="selectClassify-item f1">
      <el-input
        v-model="searchData.name1"
        prefix-icon="el-icon-search"
        placeholder="一级类目"
        @change="search(1, searchData.name1)"
        clearable
      ></el-input>
      <div class="select-list">
        <div
          class="f fac fjsb"
          :class="index === oneIndex ? 'active' : ''"
          v-for="(item, index) in list"
          @click="handleCheck(1, index, item)"
          :key="item.id"
        >
          <p>{{ item.name }}</p>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>
    <div class="selectClassify-item f1">
      <el-input
        v-model="searchData.name2"
        prefix-icon="el-icon-search"
        placeholder="二级类目"
        @change="search(2, searchData.name2)"
        clearable
      ></el-input>
      <div class="select-list">
        <div
          class="f fac fjsb"
          :class="index === twoIndex ? 'active' : ''"
          v-for="(item, index) in list2"
          @click="handleCheck(2, index, item)"
          :key="item.id"
        >
          <p>{{ item.name }}</p>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>
    <div class="selectClassify-item f1">
      <el-input
        v-model="searchData.name3"
        prefix-icon="el-icon-search"
        placeholder="三级类目"
        @change="search(3, searchData.name3)"
        clearable
      ></el-input>
      <div class="select-list">
        <div
          class="f fac fjsb"
          :class="index === threeIndex ? 'active' : ''"
          v-for="(item, index) in list3"
          @click="handleCheck(3, index, item)"
          :key="item.id"
        >
          <p>{{ item.name }}</p>
          <i class="el-icon-arrow-right"></i>
        </div>
      </div>
    </div>
    <!-- <div class="selectClassify-item f1">
              <el-input prefix-icon="el-icon-search" placeholder="品牌类目"></el-input>
              <div class="select-list">
                  <div class="f fac fjsb" :class="index === brandIndex ?'active':''" v-for="(item,index) in list4"
                       @click="handleCheck(4,index,item)"
                       :key="item.id">
                      <p>{{item.name}}</p>
                      <i class="el-icon-arrow-right"></i>
                  </div>
              </div>
          </div> -->
  </div>
  <div class="mt25 title-3">
    <el-button class="btn1" @click="handleNext">下一步</el-button>
  </div>
</div>
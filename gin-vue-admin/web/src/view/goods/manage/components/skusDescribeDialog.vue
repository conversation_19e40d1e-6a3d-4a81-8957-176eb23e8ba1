<template>
  <el-dialog
      title="描述"
      :visible="isShow"
      width="50%"
      :before-close="handleClose">
    <m-editor v-model="describe"></m-editor>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "skusDescribeDialog",
  data() {
    return {
      isShow: false,
      describe: "",
      index: null
    }
  },
  methods: {
    setData(describe, index) {
      this.describe = describe
      this.index = index
    },
    handleClose() {
      this.isShow = false
      this.describe = ""
      this.index = null
    },
    confirm() {
      let data = {
        describe: this.describe,
        index: this.index
      }
      this.$emit("getDescribe", data)
      this.handleClose()
    }
  }
}
</script>

<style scoped>

</style>
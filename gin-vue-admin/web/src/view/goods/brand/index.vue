<template>
  <m-card>
    <el-button type="primary" @click="openBrandDialog()" >新增</el-button>
    <el-form class="search-term mt25" :model="searchInfo" label-width="90px" inline>
      <el-form-item>
          <el-input v-model="searchInfo.id" class="line-input"  placeholder="请输入" clearable>
              <span slot="prepend">品牌ID</span>
          </el-input>
      </el-form-item>
      <el-form-item>
          <el-input v-model="searchInfo.name" class="line-input"  placeholder="请输入" clearable>
              <span slot="prepend">品牌名称</span>
          </el-input>
      </el-form-item>
      <br>

      <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button type="text" @click="reSearch">重置搜索条件</el-button>
      </el-form-item> 
    </el-form>
    <el-table class="mt25" :data="tableData">
      <el-table-column label="ID" align="center" prop="id"></el-table-column>
      <el-table-column label="图片" align="center">
        <template slot-scope="scope">
          <m-image style="width: 60px;height:60px" :src="scope.row.logo"></m-image>
        </template>
      </el-table-column>
      <el-table-column label="品牌名称" align="center" prop="name"></el-table-column>
      <el-table-column label="仓库编号" align="center" prop="consumer_data"></el-table-column>
      <el-table-column label="操作" align="center" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="openBrandDialog(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">编辑</el-button>
          <el-button type="text" class="color-red" @click="del(scope.row)" style="padding: 0 !important;margin-left: 10px !important;">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100,200]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes,prev, pager, next, jumper"></el-pagination>
    <brand-dialog ref="brandDialog" @onload="getTableData"></brand-dialog>
  </m-card>
</template>
<script>
import infoList from "../../../mixins/infoList";
import {getBrandsList,deleteBrands} from "@/api/brands";
import BrandDialog from "./components/brandDialog";

export default {
  name: "brandIndex",
  components: {BrandDialog},
  mixins: [infoList],
  data() {
    return {
      listApi: getBrandsList,
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    openBrandDialog(row = {}) {
      this.$refs.brandDialog.isShow = true
      if (row) {
        this.$nextTick(() => {
          this.$refs.brandDialog.formData.id = row.id
          this.$refs.brandDialog.formData.name = row.name
          this.$refs.brandDialog.formData.alias = row.alias
          this.$refs.brandDialog.formData.logo = row.logo
          this.$refs.brandDialog.formData.isRecommend = row.isRecommend
          this.$refs.brandDialog.formData.desc = row.desc
        })
      }
    },
    del(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let res = await deleteBrands({id: row.id})
        if(res.code === 0){
          this.$message.success(res.msg)
          this.getTableData()
        }
      }).catch(() => {
      })
    },
    reSearch() {
      this.page = 1
      this.searchInfo = {}
    },
    search() {
      this.page = 1
      this.getTableData()
    }
  }

}
</script>
<style lang="scss" scoped>
@import "@/style/base.scss";
</style>
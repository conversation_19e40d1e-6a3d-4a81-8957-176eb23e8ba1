<template>
    <!-- <div>短视频抽屉</div> -->
    <el-drawer
        title="查看详情"
        size="70%"
        :visible.sync="drawer"
        :direction="direction"
    >
        <el-form :model="formDetail" label-width="120px">
            <el-row :gutter="10">
                <el-col :span="14">
                    <el-form-item label="短视频名称:">
                        <p>{{ formDetail.title }}</p>
                    </el-form-item>
                </el-col>
                <el-col :span="14">
                    <el-form-item label="短视频分组:">
                        <p>{{ formDetail.group_title }}</p>
                    </el-form-item>
                </el-col>
                <el-col :span="14">
                    <el-form-item label="短视频封面:">
                        <m-image
                            :src="formDetail.img_url"
                            :style="{ width: '100px', height: '100px' }"
                        ></m-image>
                    </el-form-item>
                </el-col>
                <el-col :span="14">
                    <el-form-item label="短视频内容:">
                        <video
                            controls="controls"
                            :src="formDetail.video_url"
                            style="width: 40%; height: 40%"
                        ></video>
                    </el-form-item>
                </el-col>
                <el-col :span="14">
                    <el-form-item label="关联商品:">
                        <m-image
                            :src="formDetail.product_img"
                            :style="{ width: '100px', height: '100px' }"
                        ></m-image>
                        <p>{{ formDetail.product_title }}</p>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </el-drawer>
</template>

<script>
import { getGatherVideoDetail } from '@/api/video';

export default {
    name: 'videoDrawerIndex',

    data() {
        return {
            drawer: false, // 抽屉显示或隐藏
            direction: 'rtl', // 抽屉弹出的方向
            formDetail: {
                title: '', // 短视频名称
                group_title: '', // 短视频分组
                img_url: '', // 短视频封面
                video_url: '', // 短视频内容
                product_img: '', // 关联商品图片
                product_title: '', // 关联商品名称
            },
        };
    },

    mounted() {},

    methods: {
        async getGatherVideoDetail(id, supply_id) {
            const data = {
                video_id: id,
                gather_id: supply_id,
            };
            const res = await getGatherVideoDetail(data);
            if (res.code === 0) {
                this.formDetail.title = res.data.video.title;
                this.formDetail.group_title = res.data.video.group.title;
                this.formDetail.img_url = res.data.video.cover_url;
                this.formDetail.video_url = res.data.video.video_url;
                this.formDetail.product_img = res.data.video.product.image_url;
                this.formDetail.product_title = res.data.video.product.title;
            }
        },
    },
};
</script>

<style lang="scss" scoped></style>

<template>
    <m-card>
        <el-form :model="formData" label-width="120px">
            <el-row>
                <el-col :span="13">
                    <el-form-item label="插件开关:" prop="is_open">
                        <el-radio-group v-model="formData.is_open">
                            <el-radio :label="1">开启</el-radio>
                            <el-radio :label="0">关闭</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :span="13">
                    <el-form-item label="插件自定义名称:" prop="plugin_name">
                        <el-input v-model="formData.plugin_name"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="13">
                    <el-form-item label="商户号:" prop="merchant_id">
                        <el-input v-model="formData.merchant_id"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="13">
                    <el-form-item label="key:" prop="key">
                        <el-input v-model="formData.key"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :span="13">
                    <el-form-item label="密钥:" prop="secret">
                        <el-input v-model="formData.secret"></el-input>
                    </el-form-item>
                </el-col>
                <el-col>
                    <el-form-item>
                        <el-button type="primary" @click="save">保存设置</el-button>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
    </m-card>
</template>
<script>
import {getSetting, setSetting} from "@/api/hbzf"

export default {
    name: "hbzfBaseSetting",
    data() {
        return {
            formData: {
                key: "", // key
                secret: "", // 密钥
                plugin_name: "", // 自定义名称
                merchant_id: "", // 商户号
                is_open: 0, // 是否开启 1开启 0关闭
            }
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        async save() {
            const {code,msg} = await setSetting(this.formData)
            if(code === 0){
                this.$message.success(msg)
            }
        },
        async init(){
            const {code,data} = await getSetting()
            if(code === 0){
                this.formData = {...JSON.parse(data.value)}
            }
        }
    }
}
</script>
<style scoped lang="scss"></style>
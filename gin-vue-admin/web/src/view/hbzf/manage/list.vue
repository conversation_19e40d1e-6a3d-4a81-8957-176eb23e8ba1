<template>
    <m-card>
        <el-tabs v-model="activeName" type="card" @tab-click="handleTabsClick">
            <el-tab-pane label="个人开户" name="1">
                <PersonalList ref="personalList"/>
            </el-tab-pane>
            <el-tab-pane label="企业开户" name="2">
                <EnterpriseList ref="enterpriseList"/>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>
<script>
import PersonalList from "./page/personalList.vue";
import EnterpriseList from "./page/enterpriseList.vue";
import {getSupplierOptionList} from "@/api/goods";

export default {
    name: "bhzfManageList",
    components: {PersonalList, EnterpriseList},
    provide() {
        return {
            supplierOption: () => this.supplierList,
            filteredState: (status)=>this.filteredState(status),
            statusOption: [
                {title:"待处理",value:"pending"},
                {title:"已过审",value:"accept"},
                {title:"已拒绝",value:"reject"},
                {title:"审核中",value:"review"},
            ]
        }
    },
    data() {
        return {
            activeName: '1', // 1=个人 2=企业
            supplierList: [],
        }
    },
    created() {
        this.getSupplierOption()
    },
    methods: {
        filteredState(status){
            let s = ""
            switch (status){
                case "accept":
                    s = "已过审"
                    break;
                case "pending":
                    s = "待处理"
                    break;
                case "reject":
                    s = "已拒绝"
                    break;
                case "review":
                    s = "审核中"
                    break;
            }
            return s;
        },
        handleTabsClick() {

        },
        // 获取供应商
        async getSupplierOption() {
            const {code, data} = await getSupplierOptionList()
            if (code === 0) {
                this.supplierList = data.list
            }
        },
    }
}
</script>
<style scoped lang="scss"></style>
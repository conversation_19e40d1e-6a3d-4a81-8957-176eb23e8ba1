<template>
    <m-card>
        <el-button type="primary" @click="openEnterpriseDialog(null)">新增</el-button>
        <el-form :model="searchInfo" label-width="90px" class="mt25 search-term" inline>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >供应商</span>
                    </div>
                    <el-select v-model="searchInfo.supplier_id" class="w100">
                        <el-option v-for="item in supplierOption()" :key="item.id" :label="item.name" :value="item.id">
                        </el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="line-input" >
                    <div class="line-box ">
                        <span >状态</span>
                    </div>
                    <el-select v-model="searchInfo.state" class="w100">
                        <el-option v-for="item in statusOption" :key="item.id" :label="item.title" :value="item.value"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item label="">
                <m-daterange v-model="date" valueFormat="timestamp"></m-daterange>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="tableData" class="mt25">
            <el-table-column label="ID" align="center" prop="id"></el-table-column>
            <el-table-column label="时间" align="center">
                <template slot-scope="scope">
                    {{ scope.row.created_at | formatDate }}
                </template>
            </el-table-column>
            <el-table-column label="供应商名称" align="center">
                <template slot-scope="scope">
                    {{scope.row.supplier.name}}
                </template>
            </el-table-column>
            <el-table-column label="分账方" align="center">
                <template slot-scope="scope">
                    企业
                </template>
            </el-table-column>
            <el-table-column label="企业名称" align="center" prop="name"></el-table-column>
            <el-table-column label="法人姓名" align="center" prop="legal_name"></el-table-column>
            <el-table-column label="法人手机号" align="center" prop="legal_mobile"></el-table-column>
            <el-table-column label="状态" align="center">
                <template slot-scope="scope">
                    {{filteredState(scope.row.state)}}
                    <el-tooltip :content="scope.row.reason" placement="top">
                        <i v-if="scope.row.state === 'reject'" class="el-icon-warning" style="margin-left: 20px;"></i>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button type="text" @click="openEnterpriseDialog(scope.row)">编辑</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100,200]"
            :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes,prev, pager, next, jumper"
        >
        </el-pagination>
        <EnterpriseDialog ref="enterpriseDialog" @reLoad="getTableData" />
    </m-card>
</template>
<script>
import infoList from "@/mixins/infoList";
import {enterpriseHolderList} from "@/api/hbzf";
import MDaterange from "@/components/mDate/daterange.vue";
import EnterpriseDialog from "../components/enterpriseDialog.vue";
export default {
    components: {MDaterange,EnterpriseDialog},
    mixins: [infoList],
    inject:['supplierOption','filteredState','statusOption'],
    data() {
        return {
            listApi: enterpriseHolderList,
            searchInfo:{
                type: 'company',
                supplier_id: null,
                begin_time:"",
                end_time:"",
                /**
                 * accept  已过审
                 * pending  待处理
                 * reject  已拒绝
                 * review  审核中
                 */
                state:""
            },
            date:[]
        }
    },
    mounted() {
        this.getTableData()
    },
    methods:{
        openEnterpriseDialog(item = {}){
            this.$refs.enterpriseDialog.init(item)
        },
        search(){
            if(this.date && this.date.length){
                this.searchInfo.begin_time = parseInt(this.date[0] / 1000)
                this.searchInfo.end_time = parseInt(this.date[1] / 1000)
            }
            this.getTableData(1)
        },
        reSearch(){
            this.date = []
            this.searchInfo = {
                type: 'company',
                supplier_id: null,
                begin_time:"",
                end_time:"",
                /**
                 * accept  已过审
                 * pending  待处理
                 * reject  已拒绝
                 * review  审核中
                 */
                state:""
            }
        }
    }
}
</script>
<style scoped lang="scss"></style>
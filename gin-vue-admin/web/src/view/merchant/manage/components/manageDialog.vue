<template>
  <el-dialog :title="title" :visible="isShow" width="50%" :before-close="handleClose">
    <el-form :model="formData" :rules="rules" ref="form" label-width="100px">
      <el-form-item prop="create_type" label-width="0" class="text-center" v-if="!formData.id">
        <el-radio-group v-model="formData.create_type">
          <el-radio :label="1">单个新增/修改</el-radio>
          <el-radio :label="2">批量新增/修改</el-radio>
        </el-radio-group>
      </el-form-item>
      <div v-if="formData.create_type==1">
        <el-form-item label="选择会员:" prop="uid">
          <el-select v-model="formData.uid" clearable filterable remote :remote-method="searchMember" class="w100">
            <el-option :value="item.id" v-for="item in memberOptions" :key="item.id" :label="item.username">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择等级:" prop="level_id">
          <el-select v-model="formData.level_id" clearable filterable class="w100">
            <el-option :value="item.id" v-for="item in attractOptions" :key="item.id" :label="item.name">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <div v-if="formData.create_type==2">
        <el-form-item label="选择会员:" prop="source_type">
          <el-radio-group v-model="formData.source_type" style="padding: 10px">
            <el-radio :label="1">全部会员</el-radio>
            <br>
            <el-radio style="margin-top: 10px" :label="2">已消费会员 (有已支付订单)</el-radio>
            <br>
            <el-radio style="margin-top: 10px" :label="3">供应商</el-radio>
            <br>
            <el-radio style="margin-top: 10px" :label="4">采购端会员</el-radio>
            <br>
            <el-radio style="margin-top: 10px" :label="5">会员的等级
              <el-select v-model="formData.user_level_id" clearable filterable class="w100" size="mini">
                <el-option :value="item.id" v-for="item in memberLevelOptions" :key="item.id" :label="item.name">
                </el-option>
              </el-select>
            </el-radio>
            <br>
            <el-radio :label="6" style="margin-top: 4px">分销商等级
              <el-select v-model="formData.distributor_level_id" clearable filterable class="w100" size="mini">
                <el-option :value="item.id" v-for="item in levelOptions" :key="item.id" :label="item.name">
                </el-option>
              </el-select>
            </el-radio>
            <br>
            <el-radio :label="7" style="margin-top: 4px">招商员等级
              <el-select v-model="formData.merchant_level_id" clearable filterable class="w100" size="mini">
                <el-option :value="item.id" v-for="item in attractOptions" :key="item.id" :label="item.name">
                </el-option>
              </el-select>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="选择等级:" prop="level_id">
          <el-select v-model="formData.level_id" clearable filterable class="w100">
            <el-option :value="item.id" v-for="item in attractOptions" :key="item.id" :label="item.name">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <div slot="footer">
      <el-button type="primary" @click="confirm">确 定</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getUserList, getUserLevelOptionList, getUserListBySearch} from "@/api/member";
import {getDistributorAllLevel} from "@/api/distributor";
import {getAllLevel, addOrEditMerchant,updateMerchant} from "@/api/merchant";

export default {
  name: "manageDialog",
  data() {
    return {
      isShow: false,
      title: "新增",
      levelOptions: [],//等级option
      memberOptions: [], //会员option
      memberLevelOptions: [],//会员的等级
      attractOptions: [],//招商员等级
      formData: {
        create_type: 1, // 1单个 2批量
        level_id: null, // 等级id
        uid: null, // 会员
        source_type: 1, // 会员来源 1:全部会员 2:已消费会员(有已支付订单) 3:供应商 4:采购端会员 5:会员等级 6:分销商等级 7:招商等级
        distributor_level_id: null,//分销商等级id
        user_level_id: null,//会员等级id
        merchant_level_id: null,//招商等级id

      },
      rules: {}
    }

  },
  methods: {
    setData(row) {
      this.formData.id = row.id
      this.formData.uid = row.uid
      this.formData.level_id = row.level_id
      this.title = "编辑"
      this.searchMember(row.user_info.username)
    },
    searchMember(query) {
      getUserListBySearch({keyword: query}).then(res => {
        if (res.code === 0) {
          this.memberOptions = res.data.list
        }
      })
    },
    async initOptions() {
      //获取会员
      /*let memberOptionsRes = await getUserList({page: 1, pageSize: 9999});
      if (memberOptionsRes.code === 0) {
        this.memberOptions = memberOptionsRes.data.list
      }*/
      //会员等级
      let memberLevelOptionsRes = await getUserLevelOptionList();
      if (memberLevelOptionsRes.code === 0) {
        this.memberLevelOptions = memberLevelOptionsRes.data.list
      }
      //分销商等级
      let levelOptionsRes = await getDistributorAllLevel();
      if (levelOptionsRes.code === 0) {
        this.levelOptions = levelOptionsRes.data.levels
      }
      //招商员等级
      let attractOptionsRes = await getAllLevel();
      if (attractOptionsRes.code === 0) {
        this.attractOptions = attractOptionsRes.data.levels
      }
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return false;
        let data = {}
        if (this.formData.id) {
          data.id = this.formData.id
          data.uid = this.formData.uid
          data.level_id = this.formData.level_id
          updateMerchant(data).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.handleClose(true)
            }
          })
          return;
        }
        data.create_type = this.formData.create_type
        switch (this.formData.create_type) {
          case 1: // 单个
            data.uid = this.formData.uid
            data.level_id = this.formData.level_id
            break;
          case 2: // 批量
            data.source_type = this.formData.source_type
            data.user_level_id = this.formData.user_level_id
            data.distributor_level_id = this.formData.distributor_level_id
            data.merchant_level_id = this.formData.merchant_level_id
            data.level_id = this.formData.level_id
            break;
        }
        addOrEditMerchant(data).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.handleClose(true)
          }
        })
      })
    },

    handleClose(reload = false) {
      try {
        this.$refs.form.validate();
      } catch {
      } finally {
        this.isShow = false
        this.formData = {
          create_type: 1, // 1单个 2批量
          uid: null, // 会员
          level_id: null,// 等级id
          source_type: 1, // 会员来源 1:全部会员 2:已消费会员(有已支付订单) 3:供应商 4:采购端会员 5:会员等级 6:分销商等级
          user_level_id: null, //  会员等级id
          distributor_level_id: null, // 分销等级id
          merchant_level_id: "",//招商等级id
        }
        this.title = "新增"
        if (reload) this.$emit("reload")
      }
    },
  }
}
</script>

<style scoped>

</style>
import SlideshowDialog from "../slideshowDialog.vue"
import {getAdList, deleteAd, updateAd, changeStatus} from "@/api/ad";
import infoList from "@/mixins/infoList";
import CustomPic from "@/components/customPic";

export default {
    components: {SlideshowDialog, CustomPic},
    mixins: [infoList],
    filters: {
        formatStatus: function (status) {
            return status === 1 ? "启用" : "关闭";
        },
        formatBtnStatus: function (status) {
            return status === 0 ? "启用" : "关闭";
        },
        formatJumpType: function (type) {
            let linkTypeName = "";
            switch (type) {
                case 1:
                    linkTypeName = "链接";
                    break;
                case 2:
                    linkTypeName = "分类";
                    break;
                case 3:
                    linkTypeName = "专辑";
                    break;

                default:
                    break;
            }
            return linkTypeName;
        },
        formatAdType: function (type) {
            let adType = "";
            switch (type) {
                case 1:
                    adType = "首页-轮播";
                    break;
                case 2:
                    adType = "首页-主频道";
                    break;
                case 3:
                    adType = "首页-副频道";
                    break;
                case 4:
                    adType = "首页-热销商品";
                    break;
                case 5:
                    adType = "首页-频道";
                    break;
                case 6:
                    adType = "首页-产品专辑";
                    break;
                case 7:
                    adType = "首页-推荐商品";
                    break;
                default:
                    break;
            }
            return adType;
        }
    },
    data() {
        return {
            listApi: getAdList,
            searchInfo: {cid: 1}
        }
    },
    mounted() {
        this.getTableData();
    },
    methods: {
        // 根据广告类型返回相应预览尺寸
        retImgSize(type) {
            let width, height = "160px";
            switch (type) {
                case 1:
                    width = "714px";
                    height = "420px";
                    break;
                case 2:
                    width = "290px";
                    height = "375px";
                    break;
                case 3:
                    width = "110px";
                    height = "110px";
                    break;
                case 4:
                    width = "160px";
                    height = "160px";
                    break;
                case 5:
                    width = "160px";
                    height = "160px";
                    break;
                case 6:
                    width = "160px";
                    height = "160px";
                    break;
                case 7:
                    width = "160px";
                    height = "160px";
                    break;
                default:
                    break;
            }

            return {width, height}
        },
        //编辑
        editAdItem(item) {
            this.$refs.slideshowDialog.dialogVisible = true;
            this.$refs.slideshowDialog.title = "编辑";
            this.$refs.slideshowDialog.getInfo(item.id);
        },

        //新建广告
        newAd() {
            this.$refs.slideshowDialog.title = "新建";
            this.$refs.slideshowDialog.dialogVisible = true;
            this.$refs.slideshowDialog.getInfo();
        },

        //删除广告
        deleteAdItemDialog(item) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                this.deleteAdItem(item);
            }).catch(() => {
            });
        },

        deleteAdItem(item) {
            let para = {
                "id": item.id
            }
            deleteAd(para).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.getTableData();
                }
            });
        },

        //更新状态
        handleStateChange(item) {
            let para = {
                "id": item.id,
                "status": item.status === 0 ? 1 : 0
            }
            changeStatus(para).then(res => {
                if (res.code === 0) {
                    this.$message.success(res.msg);
                    this.getTableData();
                }
            });
        }

    }
};

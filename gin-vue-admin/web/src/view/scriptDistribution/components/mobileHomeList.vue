<template>
  <div>
    <el-button type="primary" @click="handleOpen(null)">新增列表</el-button>
    <el-table class="mt25" :data="tableData">
      <el-table-column label="排序" align="center" prop="sort"></el-table-column>
      <el-table-column label="显示名称" align="center" prop="title"></el-table-column>
      <el-table-column label="内容" align="center">
        <template slot-scope="scope">
          {{ scope.row.type | formatType }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleOpen(scope.row)">编辑</el-button>
          <el-button type="text" class="color-red" @click="del(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                   :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                   @current-change="handleCurrentChange" @size-change="handleSizeChange"
                   layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    <mobile-home-list-drawer ref="mobileHomeListDrawer" @reload="getTableData(1,10)"></mobile-home-list-drawer>
  </div>
</template>

<script>
import {getWapHomeSettingList, deleteWapHomeSetting} from "@/api/shopSetting";
import infoList from "@/mixins/infoList";
import MobileHomeListDrawer from "./mobileHomeListDrawer";
import {isNull} from "@antv/util";
import {confirm} from "@/decorators/decorators";

export default {
  name: "mobileHomeList",
  mixins: [infoList],
  components: {MobileHomeListDrawer},
  data() {
    return {
      listApi: getWapHomeSettingList
    }
  },
  filters: {
    formatType(value) {
      let s = ""
      switch (value) {
        case 0:
          s = "全部商品"
          break;
        case 1:
          s = "指定分类"
          break;
        case 2:
          s = "营销属性"
          break;
        case 3:
          s = "指定专辑"
          break;
      }
      return s;
    }
  },
  methods: {
    @confirm("提示", "确定删除?")
    async del(id) {
      let {code, msg} = await deleteWapHomeSetting({id})
      if (code === 0) {
        this.$message.error(msg)
        this.getTableData()
      }
    },
    // 打开抽屉
    handleOpen(row) {
      this.$refs.mobileHomeListDrawer.isShow = true
      this.$refs.mobileHomeListDrawer.getAlbum()
      if (isNull(row)) { // 新增
        this.$refs.mobileHomeListDrawer.getCategory()
      } else { // 编辑
        this.$refs.mobileHomeListDrawer.init(row)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/base.scss";
</style>
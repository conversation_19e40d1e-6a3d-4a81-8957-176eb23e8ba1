import {mapGetters} from "vuex";
import {createAd, updateAd, findAd} from "@/api/ad";
import {getCollectionList} from "@/api/collection";
import {getAdChannelList} from "@/api/adChannel";
import {getCategoryListWithParentId, getClassify} from "@/api/classify"
import AddClassify from "@/view/goods/classify/components/add"
import AddAlbum from "@/view/goods/album/components/addAlbum";
import AddChannel from "../channelDIalog"

export default {
    components: {AddClassify, AddAlbum, AddChannel},
    data() {
        return {
            // 广告类型
            advertisingTypeList: [
                {name: "首页-轮播", value: 1},
                {name: "首页-主频道", value: 2},
                {name: "首页-副频道", value: 3},
                {name: "首页-热销商品", value: 4},
                {name: "首页-产品专辑", value: 6},
                {name: "首页-推荐商品", value: 7},
            ],
            //跳转类型
            adLinkTypeList: [
                {name: "链接", value: 1},
                {name: "分类", value: 2},
                {name: "专辑", value: 3}
            ],
            adLinkTypeDisabled: false,
            //频道组
            channelList: [],
            collectionList: [],
            classifyList: [],
            classifyList2: [],
            classifyList3: [],
            dialogVisible: false,
            formData: {
                // 跳转方式
                jump_type: "",
                sort: 0,
                type: "",
                title: "",
                url: "",
                src: "",
                mini_url:"",//小程序访问链接
                h5_url:"",//H5访问链接
                app_url:"",//APP访问链接
                status: 0,
                cid: 1,
                //频道组id
                channel_id: 0,
                collection_id: 0,
                // category_id: 0,
                category1_id: null,
                category2_id: null,
                category3_id: null,
            },
            title: "新增",
            path: this.$path,
        };
    },
    filters: {
        imgHint: function (n) {
            let s = ""
            switch (n) {
                case 1:
                    s = "轮播建议尺寸：714*420像素,图片需限制在10M以内"
                    break;
                case 2:
                    s = "主频道建议尺寸：290*375像素,图片需限制在10M以内"
                    break;
                case 3:
                    s = "副频道建议尺寸：110*110像素,图片需限制在10M以内"
                    break;
                case 4:
                    s = "热销商品建议尺寸：160*160像素,图片需限制在10M以内"
                    break;
                case 6:
                    s = "产品专辑建议尺寸：160*160像素,图片需限制在10M以内"
                    break;
                case 7:
                    s = "推荐商品建议尺寸：160*160像素,图片需限制在10M以内"
                    break;
            }
            return s
        }
    },
    computed: {
        ...mapGetters("user", ["userInfo", "token"]),

        collectionShow() {
            if (this.formData.cid == 7) {
                return false;
            } else {
                if (this.formData.jump_type == 3) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    },
    mounted() {

    },
    methods: {
        // 切换类型
        handleTypeChange(val) {
            switch (val) {
                case 1:
                    this.getChanneId();
                    this.adLinkTypeDisabled = false;
                    break;
                case 2:
                    this.adLinkTypeDisabled = false;
                    break;
                case 3:
                    this.initChannelList();
                    this.adLinkTypeDisabled = false;
                    break;
                case 4:
                    this.getChanneId();
                    this.formData.jump_type = "";
                    this.adLinkTypeDisabled = true;
                    this.formData.jump_type = this.adLinkTypeList[2].value;
                    break;
                case 6:
                    this.getChanneId();
                    this.adLinkTypeDisabled = true;
                    this.formData.jump_type = this.adLinkTypeList[2].value;
                    break;
                case 7:
                    this.getChanneId();
                    this.formData.jump_type = "";
                    this.adLinkTypeDisabled = true;
                    this.formData.jump_type = this.adLinkTypeList[2].value;
                    break;
            }
        },

        //赋值表单
        setFrom(val) {
            if (!val) {
                this.formData.jump_type = "";
                this.formData.type = "";
                this.formData.title = "";
                this.formData.url = "";
                this.formData.src = "";
                this.formData.status = 0;
                this.formData.cid = 1;
                this.formData.channel_id = 0;
                this.formData.collection_id = 0;
                this.formData.category1_id = null;
                this.formData.category2_id = null;
                this.formData.category3_id = null;
                return;
            }
            const keys = Object.keys(val);
            const that = this;
            keys.forEach((element) => {
                that.formData[element] = val[element];
            });
        },

        //获得信息
        getInfo(id) {
            if (!id) {
                this.setFrom();
                this.initBaseInfo();
                return;
            }
            let para = {
                "id": id
            }
            findAd(para).then((res) => {
                if (res.code === 0) {
                    let data = res.data.read;
                    // 回显二级三级分类下拉问题 * 未改完
                    if (data.category2_id) {
                        this.getClassify2(data.category1_id)
                        this.getClassify3(data.category2_id)
                    } else {
                        data.category2_id = null
                        this.formData.category3_id = null
                        this.classifyList3 = []
                    }
                    if (data.category3_id) {
                        this.getClassify3(data.category2_id)
                    } else {
                        data.category3_id = null
                    }

                    this.setFrom(data);
                    this.initBaseInfo();

                }
            });
        },
        // 获取二级分类
        getClassify2(id) {
            this.formData.category2_id = null
            this.formData.category3_id = null
            this.classifyList2 = []
            this.classifyList3 = []
            getClassify(2, id).then(res => {
                if (res.code === 0) {
                    this.classifyList2 = res.data.list
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        // 获取三级分类
        getClassify3(id) {
            this.formData.category3_id = null
            this.classifyList3 = []
            getClassify(3, id).then(res => {
                if (res.code === 0) {
                    this.classifyList3 = res.data.list
                } else {
                    this.$message.error(res.msg)
                }
            })
        },
        //初始化基础数据
        initBaseInfo() {
            //初始化频道组数据
            this.getChannelList();
            //初始化专辑数据
            this.getCollectionList();
            //初始化分类数据
            this.getClassifyList();
        },

        //提交
        save() {
            if (!this.formData.title) {
                this.$message.error("请填写广告名称");
                return;
            }
            switch (this.formData.cid) {
                case 3:
                    if (!this.formData.channel_id) {
                        this.$message.error("请选择频道组");
                        return;
                    }
                    break;
                default:
                    break;
            }

            if (this.formData.cid !== 7) {
                if (!this.formData.jump_type) {
                    this.$message.error("请选择展示方式");
                    return;
                }
            }
            switch (this.formData.linkType) {
                case 1:
                    if (!this.formData.url) {
                        this.$message.error("请填写跳转链接");
                        return;
                    }
                    break;
                case 2:
                    if (!this.formData.url) {
                        this.$message.error("请选择跳转分类");
                        return;
                    }
                    break;
                case 3:
                    if (!this.formData.channel_id) {
                        this.$message.error("请选择跳转专辑");
                        return;
                    }
                    break;
                default:
                    break;
            }
            if (this.formData.jump_type === 2) {
                if (!this.formData.category1_id) {
                    this.$message.error("请选择分类");
                    return;
                }
            }
            if (!this.formData.src) {
                this.$message.error("请上传图片");
                return;
            }

            // 编辑
            if (this.formData.id) {
                this.editAdInfo();
            } else {
                this.newAdInfo();
            }
        },

        //编辑广告信息
        editAdInfo() {
            if (!this.formData.category1_id) {
                this.formData.category1_id = 0;
            }
            if (!this.formData.category2_id) {
                this.formData.category2_id = 0;
            }
            if (!this.formData.category3_id) {
                this.formData.category3_id = 0;
            }
            updateAd(this.formData).then((res) => {
                if (res.code === 0) {
                    this.$message.success("编辑成功");
                    this.$emit("onload");
                    this.handleClose();
                } else {
                    this.$message.error(res.msg);
                }
            });
        },

        //新件广告
        newAdInfo() {

            if (!this.formData.category1_id) {
                this.formData.category1_id = 0;
            }
            if (!this.formData.category2_id) {
                this.formData.category2_id = 0;
            }
            if (!this.formData.category3_id) {
                this.formData.category3_id = 0;
            }

            createAd(this.formData).then((res) => {
                if (res.code === 0) {
                    this.$message.success("新增成功");
                    this.$emit("onload");
                    this.handleClose();
                } else {
                    this.$message.error(res.msg);
                }
            });
        },


        handleClose() {
            this.dialogVisible = false;
            this.$refs.form.resetFields();
            this.formData = {
                // 跳转方式
                jump_type: "",
                sort: null,
                type: "",
                title: "",
                url: "",
                src: "",
                status: 0,
                cid: 1,
                //频道组id
                channel_id: 0,
                collection_id: 0,
                // category_id: 0,
                category1_id: null,
                category2_id: null,
                category3_id: null,
            }
            if (this.formData.id) {
                delete this.formData.id;
            }
        },
        handleBannerImgSuccess(res) {
            this.formData.src = res.data.file.url;
        },
        beforeAvatarUpload(file) {
            const isLt10M = file.size / 1024 / 1024 < 10;
            if (!isLt10M) {
                this.$message.error("上传头像图片大小不能超过 10MB!");
            }
            return isLt10M;
        },
        //新建频道
        goChannel() {
            // this.$router.push({ path: "/layout/shopSettingIndex/channelSetting", query: { addChannel: 1 } })
            this.$refs.addChannel.isShow = true
        },

        //初始化频道组
        initChannelList(isRefresh) {
            if (isRefresh) {
                this.getChannelList();
                return;
            }
            if (this.channelList.length == 0) {
                this.getChannelList();
            }
        },

        //获取频道组数据
        getChannelList() {
            this.channelList = [];
            let para = {
                page: this.page,
                pageSize: this.pageSize
            }
            getAdChannelList(para).then(
                (res) => {
                    if (res.code === 0) {
                        this.channelList = res.data.list;
                    }
                    this.getChanneId();
                }
            );
        },

        //获取频道组id
        getChanneId() {
            if (this.channelList.length > 0) {
                if (!this.formData.channel_id) {
                    this.formData.channel_id = this.channelList[0].id;
                }
            } else {
                this.formData.channel_id = 0;
            }
        },
        // 添加专辑
        addAlbum() {
            this.$refs.addAlbum.isShow = true
        },
        //获取专辑列表
        getCollectionList() {
            this.collectionList = [];
            let para = {
                page: this.page,
                pageSize: this.pageSize
            }
            getCollectionList(para).then(
                (res) => {
                    if (res.code === 0) {
                        this.collectionList = res.data.list;
                    }
                    this.getCollectionId();
                }
            );
        },

        //获取专辑id
        getCollectionId() {
            if (this.collectionList.length > 0) {
                if (!this.formData.collection_id) {
                    this.formData.collection_id = this.collectionList[0].id;
                }
            } else {
                this.formData.collection_id = 0;
            }
        },
        // 新建分类
        addClassify() {
            this.$refs.addClassify.isShow = true
        },
        //获取分类列表
        getClassifyList() {
            this.classifyList = [];
            let para = {
                "is_display": 1,
                "page": this.page,
                "pageSize": this.pageSize
            }
            getCategoryListWithParentId(para).then(res => {
                if (res.code === 0) {
                    this.classifyList = res.data.list;
                }
                this.getClassifyId();
            });
        },

        //获取分类id
        getClassifyId() {
            if (this.classifyList.length > 0) {
                if (!this.formData.category1_id) {
                    this.formData.category1_id = this.classifyList[0].id;
                    this.getClassify2(this.formData.category1_id)
                }
            } else {
                this.formData.category1_id = null;
            }
        },

    }


};
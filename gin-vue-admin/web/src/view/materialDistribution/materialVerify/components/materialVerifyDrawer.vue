<template>
    <div>
        <el-drawer
            title="详情"
            :visible="isShow"
            :close-on-press-escape="false"
            :wrapperClosable="false"
            :before-close="handleClose"
            size="calc(100% - 220px)"
            class="detail-ct"
        >
            <m-card>
                <el-form :model="formData" label-width="200px" ref="form">
                    <el-form-item label="ID:" class="w500">
                        <div>{{ formData.id }}</div>
                    </el-form-item>
                    <el-form-item label="素材标题:">
                        <div>{{ formData.title }}</div>
                    </el-form-item>
                    <el-form-item label="素材分组:">
                        <div>{{ formData.group.title }}</div>
                    </el-form-item>
                    <el-form-item label="素材文案:">
                        <div>{{ formData.content }}</div>
                    </el-form-item>
                    <el-form-item label="图片:">
                        <div class="f fac fw" style="width: 650px">
                            <m-image
                                class="ml_10 mb_10"
                                v-for="(item, index) in img_list"
                                :key="index"
                                :src="item"
                                style="width: 150px; height: 150px"
                            ></m-image>
                        </div>
                    </el-form-item>
                    <el-form-item label="视频:">
                        <video
                            controls="controls"
                            style="width: 150px; height: 150px"
                            :src="formData.video_url"
                        ></video>
                    </el-form-item>
                    <el-form-item label="商品:">
                        <div>
                            <m-image
                                :src="formData.product.image_url"
                                style="width: 150px; height: 150px"
                            ></m-image>
                            <div style="line-height: 20px">
                                <p>{{ formData.product.title }}</p>
                                <p>[ID:{{ formData.product.id }}]</p>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item>
                        <div v-if="formData.status === 1">
                            <el-button type="primary" @click="pass"
                                >通 过</el-button
                            >
                            <el-button @click="rejection">驳 回</el-button>
                        </div>
                        <div v-else>
                            <el-button type="primary" @click="handleClose"
                                >返 回</el-button
                            >
                        </div>
                    </el-form-item>
                </el-form>
            </m-card>
            <el-dialog
                width="800px"
                title="驳回理由"
                :visible="visible"
                @close="onClose"
                :append-to-body="true"
            >
                <el-form :model="formDataRemark">
                    <m-editor v-model="formDataRemark.remark"></m-editor>
                    <div
                        style="
                            margin-top: 30px;
                            display: flex;
                            justify-content: end;
                        "
                    >
                        <el-button size="mini" @click="onClose">取消</el-button>
                        <el-button type="primary" size="mini" @click="onSubmit"
                            >确定</el-button
                        >
                    </div>
                </el-form>
            </el-dialog>
        </el-drawer>
    </div>
</template>
<script>
import { getAuditOperate } from '@/api/material';
import { confirm } from '@/decorators/decorators';
export default {
    data() {
        return {
            isShow: false,
            img_list: [],
            formData: {
                group: {},
                product: {},
            },
            // 驳回
            visible: false,
            formDataRemark: {
                remark: '',
            },
        };
    },
    methods: {
        info(item) {
            this.formData = item;
            this.img_list = this.formData.img_url.split(',');
            this.isShow = true;
        },
        // 关闭抽屉
        handleClose() {
            this.formData = {
                group: {},
                product: {},
            };
            this.isShow = false;
            this.$emit('reSearch');
        },
        // 通过
        @confirm('提示', '确定通过?')
        async pass() {
            let data = {
                ids: [this.formData.id],
                status: 2,
            };
            let res = await getAuditOperate(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.formData.status = 2;
            }
        },
        // 驳回
        rejection() {
            this.visible = true;
        },
        // 关闭驳回
        onClose() {
            this.visible = false;
            this.formData.remark = '';
        },
        // 确认驳回
        async onSubmit() {
            let data = {
                ids: [this.formData.id],
                status: 3,
                rejected_reason: this.formDataRemark.remark,
            };
            let res = await getAuditOperate(data);
            if (res.code === 0) {
                this.onClose();
                this.$message.success(res.msg);
                this.formData.status = 2;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.w500 {
    width: 500px;
}

.upload-box {
    width: 150px;
    height: 150px;
    border: 1px dotted #cecece;
    line-height: 150px;
    text-align: center;

    i {
        font-size: 30px;
        color: #cecece;
    }
}
</style>

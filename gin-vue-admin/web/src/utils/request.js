import axios from 'axios'; // 引入axios
import {Message} from 'element-ui';
import {store} from '@/store/index'
import context from '@/main.js'

const service = axios.create({
    baseURL: process.env.VUE_APP_BASE_API,
    timeout: 99999
})
// 请求失败重试次数
service.defaults.retry = 1;
// 请求重试延迟
service.defaults.retryDelay = 1000;
let acitveAxios = 0
let timer
const showLoading = () => {
    acitveAxios++
    if (timer) {
        clearTimeout(timer)
    }
    timer = setTimeout(() => {
        if (acitveAxios > 0) {
            context.$bus.emit("showLoading")
        }
    }, 400);
}

const closeLoading = () => {
    acitveAxios--
    if (acitveAxios <= 0) {
        clearTimeout(timer)
        context.$bus.emit("closeLoading")
    }
}
//http request 拦截器
service.interceptors.request.use(
    config => {
        if (!config.donNotShowLoading) {
            showLoading()
        }
        const token = store.getters['user/token']
        const user = store.getters['user/userInfo']
        config.data = JSON.stringify(config.data);
        config.headers = {
            'Content-Type': 'application/json',
            'x-token': token,
            'x-user-id': user.ID
        }
        return config;
    },
    error => {
        closeLoading()
        Message({
            showClose: true,
            message: error,
            type: 'error'
        })
        return error;
    }
);


//http response 拦截器
service.interceptors.response.use(
    response => {
        closeLoading()
        if (response.headers["new-token"]) {
            store.commit('user/setToken', response.headers["new-token"])
        }
        if (response.data.code == 0 || (response.data.msg=="失败" && response.data.code==7)) {
            return response.data
        } else {
            Message({
                showClose: true,
                message: response.data.msg || decodeURI(response.headers.msg),
                type: response.headers.msgtype || 'error',
            })
            if (response.data.data && response.data.data.reload) {
                store.commit('user/LoginOut')
            }
            return response.data.msg ? response.data : response
        }
    },
    error => {
        if (error.response.status && error.response.status === 502) {
            let config = error.config;
            if (!config || !config.retry) return Promise.reject(error);
            config.__retryCount = config.__retryCount || 0;
            if (config.__retryCount >= config.retry) {
                return Promise.reject(error);
            }
            config.__retryCount += 1;
            // 创建新的异步对象来处理重新请求
            var backoff = new Promise((resolve) => {
                setTimeout(() => {
                    resolve()
                }, config.retryDelay || 1)
            })
            // 返回调用axios来重试请求
            return backoff.then(function () {
                return service(config)
            })
        } else {
            let {message} = error;
            if (message == "Network Error") {
                message = "后端接口连接异常";
            } else if (message.includes("timeout")) {
                message = "系统接口请求超时";
            } else if (message.includes("Request failed with status code")) {
                message = "系统接口" + message.substr(message.length - 3) + "异常";
            }
            closeLoading()
            Message({
                message: message,
                type: 'error',
                duration: 5 * 1000
            })
            return Promise.reject(error)
        }
    }
)

export default service

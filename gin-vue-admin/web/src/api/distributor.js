import service from '@/utils/request'

export const findSetting = (params) => {
    return service({
        url: "/distributor/findSetting",
        method: 'get',
        params
    })
}

export const updateSetting = (data) => {
    return service({
        url: "/distributor/updateSetting",
        method: 'put',
        data
    })
}
/*
 *@Summary 创建分销商等级
 *@Router  /distributor/createLevel
 *@Method  post
 *@Date    2021-12-27
*/
export const createLevel = (data) => {
    return service({
        url: "/distributor/createLevel",
        method: "post",
        data
    })
}
/*
 *@Summary 更新分销商等级高
 *@Router  /distributor/updateLevel
 *@Method  put
 *@Date    2021-12-27
*/
export const updateLevel = (data) => {
    return service({
        url: "/distributor/updateLevel",
        method: "put",
        data
    })
}
/*
 *@Summary 删除分销商等级
 *@Router  /distributor/deleteLevel
 *@Method  delete
 *@Date    2021-12-27
*/
export const deleteLevel = (data) => {
    return service({
        url: "/distributor/deleteLevel",
        method: "delete",
        data
    })
}
/*
 *@Summary 通过id查询分销商等级高
 *@Router  /distributor/findLevel
 *@Method  get
 *@Date    2021-12-27
*/
export const findLevel = (params) => {
    return service({
        url: "/distributor/findLevel",
        method: "get",
        params
    })
}
/*
 *@Summary 分页获取分销等级列表
 *@Router  /distributor/getLevelList
 *@Method  get
 *@Date    2021-12-27
*/
export const getLevelList = (params) => {
    return service({
        url: "/distributor/getLevelList",
        method: "get",
        params
    })
}
/*
 *@Summary 获取全部分销等级无分页
 *@Router  /distributor/getAllLevel
 *@Method  get
 *@Date    2021-12-27
*/
export const getDistributorAllLevel = (params) => {
    return service({
        url: "/distributor/getAllLevel",
        method: "get",
        params
    })
}

/*
 *@Summary 创建/修改分销商
 *@Router  /distributor/addOrEditDistributor
 *@Method  post
 *@Date    2021-12-27
*/
export const addOrEditDistributor = (data) => {
    return service({
        url: "/distributor/addOrEditDistributor",
        method: "post",
        data
    })
}
/*
 *@Summary 分销商列表
 *@Router  /distributor/getDistributorList
 *@Method  get
 *@Date    2021-12-27
*/
export const getDistributorList = (params) => {
    return service({
        url: "/distributor/getDistributorList",
        method: "get",
        params
    })
}
/*
 *@Summary 导出分销商列表
 *@Router  /distributor/exportDistributorList
 *@Method  get
 *@Date    2021-12-28
*/
export const exportDistributorList = (params) => {
    return service({
        url: "/distributor/exportDistributorList",
        method: "get",
        params
    })
}

/*
 * 获取分成记录列表页
 * @param params
 * @returns {*}
 */
export const getAwardList = (params) => {
    return service({
        url: "/distributor/getAwardList",
        method: "get",
        params
    })
}
/*
 *@Summary 查询分销商
 *@Router  /distributor/findDistributor
 *@Method  get
 *@Date    2021-12-28
*/
export const findDistributor = (params) => {
    return service({
        url: "/distributor/findDistributor",
        method: "get",
        params
    })
}
/*
 *@Summary 更新分销商
 *@Router  /distributor/updateDistributor
 *@Method  put
 *@Date    2021-12-28
*/
export const updateDistributor = (data) => {
    return service({
        url: "/distributor/updateDistributor",
        method: "put",
        data
    })
}

/*
 *@Summary 导出分成记录
 *@Router  /distributor/exportAwardList
 *@Method  get
 *@Date    2021-12-28
*/
export const exportAwardList = (params) => {
    return service({
        url: "/distributor/exportAwardList",
        method: "get",
        params
    })
}

// 开通记录
export const getPurchaseRecordList = (params) => {
    return service({
        url: "/distributor/getPurchaseRecordList",
        method: "get",
        params
    })
}

// 黑名单列表
export const getBlacklist = (data) => {
    return service({
        url: "/distributor/blacklist",
        method: "post",
        data
    })
}


import service from '@/utils/request'

// 更新设置
export const updateSetting = (data) => {
    return service({
        url: "/areaAgency/updateSetting",
        method: 'put',
        data
    })
}

// 获取设置
export const findSetting = (params) => {
    return service({
        url: "/areaAgency/findSetting",
        method: 'get',
        params
    })
}
/*
 *@Summary 创建区域代理
 *@Router  /areaAgency/createAgency
 *@Method  post
 *@Date    2021-11-08
*/
export const createAgency = (data) => {
    return service({
        url: "/areaAgency/createAgency",
        method: "post",
        data
    })
}
/*
 *@Summary 获取区域代理列表
 *@Router  /areaAgency/getAgenciesList
 *@Method  get
 *@Date    2021-11-09
*/
export const getAgenciesList = (params) => {
    return service({
        url: "/areaAgency/getAgenciesList",
        method: "get",
        params
    })
}
/*
 *@Summary 删除区域代理
 *@Router  /areaAgency/deleteAgency
 *@Method  delete
 *@Date    2021-11-09
*/
export const deleteAgency = (data) => {
    return service({
        url: "/areaAgency/deleteAgency",
        method: "delete",
        data
    })
}
/*
 *@Summary 根据id查询区域代理
 *@Router  /areaAgency/findAgency
 *@Method  get
 *@Date    2021-11-09
*/
export const findAgency = (params) => {
    return service({
        url: "/areaAgency/findAgency",
        method: "get",
        params
    })
}
/*
 *@Summary 更新区域代理
 *@Router  /areaAgency/updateAgency
 *@Method  put
 *@Date    2021-11-09
*/
export const updateAgency = (data) => {
    return service({
        url: "/areaAgency/updateAgency",
        method: "put",
        data
    })
}
/*
 *@Summary 区域代理管理导出
 *@Router  /areaAgency/exportAgenciesList
 *@Method  get
 *@Date    2021-11-10
*/
export const exportAgenciesList = (params) => {
    return service({
        url: "/areaAgency/exportAgenciesList",
        method: "get",
        params
    })
}
/*
 *@Summary 区域代理申请列表
 *@Router  /areaAgency/getAgencyAppliesList
 *@Method  get
 *@Date    2021-11-10
*/
export const getAgencyAppliesList = (params) => {
    return service({
        url: "/areaAgency/getAgencyAppliesList",
        method: "get",
        params
    })
}
/*
 *@Summary 区域代理审核
 *@Router  /areaAgency/checkAgencyApply
 *@Method  put
 *@Date    2021-11-10
*/
export const checkAgencyApply = (data) => {
    return service({
        url: "/areaAgency/checkAgencyApply",
        method: "put",
        data
    })
}
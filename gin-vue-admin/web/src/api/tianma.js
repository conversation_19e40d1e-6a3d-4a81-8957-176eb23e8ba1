import service from "@/utils/request";

// 获取发货仓
export const getWareHouseNameInfo = (data) => {
  return service({
    url: "/tianma/getWareHouseNameInfo",
    method: "post",
    data
  })
}

// 根据货号获取商品仓库库存列表
export const getStockListByGoodsNo = (data) => {
  return service({
    url: "/tianma/getStockListByGoodsNo",
    method: "post",
    data
  })
}

// 获取查询单号数据
export const getCommodityList = (data) => {
  return service({
    url: "/tianma/getCommodityList",
    method: "post",
    data
  })
}

// 获取快递物流运费
export const getDeliveryInfo = (data) => {
  return service({
    url: "/tianma/getDeliveryInfo",
    method: "post",
    data
  })
}
/*
 *@Summary 根据货源返回品牌
 *@Router  /tianma/getWarehouseDetail
 *@Method  post
 *@Date    2024-03-18
*/
export const getWarehouseDetail = (data)=>{
  return service({
    url:"/tianma/getWarehouseDetail",
    method: "post",
    data
  })
}
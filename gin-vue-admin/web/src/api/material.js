import service from '@/utils/request';

/*
 *@Summary: 保存设置
 *@Router:  /material/savaBaseSetting
 *@Method:  post
 *@Date: 2023-05-10
 */
export const savaBaseSetting = data => {
    return service({
        url: '/material/savaBaseSetting',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 获取设置
 *@Router:  /material/getBaseSetting
 *@Method:  post
 *@Date: 2023-05-10
 */
export const getBaseSetting = () => {
    return service({
        url: '/material/getBaseSetting',
        method: 'post',
    });
};

/*
 *@Summary: 素材列表
 *@Router:  /material/list
 *@Method:  post
 *@Date: 2023-05-10
 */
export const materialList = data => {
    return service({
        url: '/material/list',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 素材新增
 *@Router:  /material/create
 *@Method:  post
 *@Date: 2023-05-10
 */
export const materialCreate = data => {
    return service({
        url: '/material/create',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 素材修改
 *@Router:  /material/update
 *@Method:  post
 *@Date: 2023-05-10
 */
export const materialUpdate = data => {
    return service({
        url: '/material/update',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 素材删除
 *@Router:  /material/delete
 *@Method:  post
 *@Date: 2023-05-10
 */
export const materialDelete = data => {
    return service({
        url: '/material/delete',
        method: 'post',
        data,
    });
};

/*
 *@Summary: 创建分组
 *@Router:  /material/createGroup
 *@Method:  post
 *@Date: 2023-07-28
 */
 export const createGroup = data => {
    return service({
        url: '/material/createGroup',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 更新分组
 *@Router:  /material/updateGroup
 *@Method:  post
 *@Date: 2023-07-28
 */
 export const updateGroup = data => {
    return service({
        url: '/material/updateGroup',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 删除分组
 *@Router:  /material/deleteGroup
 *@Method:  post
 *@Date: 2023-07-28
 */
 export const deleteGroup = data => {
    return service({
        url: '/material/deleteGroup',
        method: 'post',
        data,
    });
};
/*
 *@Summary: 获取分组
 *@Router:  /material/getGroup
 *@Method:  post
 *@Date: 2023-07-28
 */
 export const getGroup = data => {
    return service({
        url: '/material/getGroup',
        method: 'post',
        data,
    });
};

// 获取供应链列表
export const getSupplyList = params => {
    return service({
        url: '/material/supply/list',
        method: 'get',
        params,
    });
};

// 获取素材列表
export const getMaterialList = params => {
    return service({
        url: '/material/sync/getMaterialList',
        method: 'get',
        params,
    });
};

// 查看素材
export const getMaterialDetail = params => {
    return service({
        url: '/material/sync/getMaterialDetail',
        method: 'get',
        params,
    });
};

// 更新素材
export const updateMaterial = data => {
    return service({
        url: '/material/sync/updateMaterial',
        method: 'post',
        data,
    });
};

// 导入素材
export const importMaterial = data => {
    return service({
        url: '/material/sync/importMaterial',
        method: 'post',
        data,
    });
};

// 获取自动同步设置
export const getFindSetting = params => {
    return service({
        url: '/material/sync/findSetting',
        method: 'get',
        params,
    });
};

// 更新自动同步设置
export const updateSetting = data => {
    return service({
        url: '/material/sync/updateSetting',
        method: 'put',
        data,
    });
};

// 素材审核列表
export const getAuditList = data => {
    return service({
        url: '/material/audit/list',
        method: 'post',
        data,
    });
};

// 素材审核请求
export const getAuditOperate = data => {
    return service({
        url: '/material/audit/operate',
        method: 'post',
        data,
    });
};

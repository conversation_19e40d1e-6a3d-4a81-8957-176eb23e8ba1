import service from "@/utils/request";
/*
 *@Summary 获取设置
 *@Router  /surfaceSingle/findSetting
 *@Method  get
 *@Date    2022-08-24
*/
export const findSetting = (params) => {
    return service({
        url: "/surfaceSingle/findSetting",
        method: "get",
        params
    })
}
/*
 *@Summary 更新设置
 *@Router  /surfaceSingle/updateSetting
 *@Method  put
 *@Date    2022-08-24
*/
export const updateSetting = (data) => {
    return service({
        url: "/surfaceSingle/updateSetting",
        method: "put",
        data
    })
}
/*
 *@Summary 邮编查询
 *@Router  /surfaceSingle/getPostCode
 *@Method  post
 *@Date    2022-08-24
*/
export const getPostCode = (data) => {
    return service({
        url: "/surfaceSingle/getPostCode",
        method: "post",
        data
    })
}
/*
 *@Summary 电子面单列表
 *@Router  /surfaceSingle/getSurfaceSinglesList
 *@Method  get
 *@Date    2022-08-24
*/
export const getSurfaceSinglesList = (params) => {
    return service({
        url: "/surfaceSingle/getSurfaceSinglesList",
        method: "get",
        params
    })
}
/*
 *@Summary 新建电子面单
 *@Router  /surfaceSingle/createSurfaceSingle
 *@Method  post
 *@Date    2022-08-24
*/
export const createSurfaceSingle = (data) => {
    return service({
        url: "/surfaceSingle/createSurfaceSingle",
        method: "post",
        data
    })
}
/*
 *@Summary 获取快递列表
 *@Router  /surfaceSingle/company/list
 *@Method  post
 *@Date    2022-08-24
*/
export const getCourierList = (data) => {
    return service({
        url: "/surfaceSingle/company/list",
        method: "post",
        data
    })
}
/*
 *@Summary 修改电子面单默认模板
 *@Router  /surfaceSingle/changeDefault
 *@Method  post
 *@Date    2022-08-25
*/
export const changeDefault = (data) => {
    return service({
        url: "/surfaceSingle/changeDefault",
        method: "post",
        data
    })
}
/*
 *@Summary 删除电子面单
 *@Router  /surfaceSingle/deleteSurfaceSingle
 *@Method  delete
 *@Date    2022-08-25
*/
export const deleteSurfaceSingle = (data) => {
    return service({
        url: "/surfaceSingle/deleteSurfaceSingle",
        method: "delete",
        data
    })
}
/*
 *@Summary 更新电子面单
 *@Router  /surfaceSingle/updateSurfaceSingle
 *@Method  put
 *@Date    2022-08-25
*/
export const updateSurfaceSingle = (data) => {
    return service({
        url: "/surfaceSingle/updateSurfaceSingle",
        method: "put",
        data
    })
}

/*
 *@Summary 新建发件人
 *@Router  /surfaceSingle/createAddresser
 *@Method  post
 *@Date  2022-08-24
*/
export const createAddresser = (data) => {
    return service({
        url: "/surfaceSingle/createAddresser",
        method: "post",
        data
    })
}

/*
 *@Summary 更新发件人
 *@Router  /surfaceSingle/updateAddresser
 *@Method  put
 *@Date  2022-08-24
*/
export const updateAddresser = (data) => {
    return service({
        url: "/surfaceSingle/updateAddresser",
        method: "put",
        data
    })
}

/*
 *@Summary 删除发件人
 *@Router  /surfaceSingle/deleteAddresser
 *@Method  delete
 *@Date  2022-08-24
*/
export const deleteAddresser = (data) => {
    return service({
        url: "/surfaceSingle/deleteAddresser",
        method: "delete",
        data
    })
}

/*
 *@Summary 分页获取发件人列表
 *@Router  /surfaceSingle/getAddressersList
 *@Method  get
 *@Date  2022-08-24
*/
export const getAddressersList = (params) => {
    return service({
        url: "/surfaceSingle/getAddressersList",
        method: "get",
        params
    })
}

/*
 *@Summary 根据ID获取发件人
 *@Router  /surfaceSingle/findAddresser
 *@Method  get
 *@Date  2022-08-24
*/
export const findAddresser = (params) => {
    return service({
        url: "/surfaceSingle/findAddresser",
        method: "get",
        params
    })
}

/*
 *@Summary 修改发件人默认模板
 *@Router  /surfaceSingle/changeAddresserDefault
 *@Method  post
 *@Date  2022-08-24
*/
export const changeAddresserDefault = (data) => {
    return service({
        url: "/surfaceSingle/changeAddresserDefault",
        method: "post",
        data
    })
}
/*
 *@Summary 查询未非配包裹的订单商品
 *@Router  /surfaceSingle/getUnpackedProducts
 *@Method  get
 *@Date    2022-08-29
*/
export const getUnpackedProducts = (params) => {
    return service({
        url: "/surfaceSingle/getUnpackedProducts",
        method: "get",
        params
    })
}


/*
 *@Summary 创建包裹
 *@Router  /surfaceSingle/createPackage
 *@Method  post
 *@Date    2022-08-30
*/
export const createPackage = (data) => {
    return service({
        url: "/surfaceSingle/createPackage",
        method: "post",
        data
    })
}
/*
 *@Summary 删除包裹详情item
 *@Router  /surfaceSingle/deleteItem
 *@Method  delete
 *@Date    2022-08-30
*/
export const deleteItem = (data) => {
    return service({
        url: "/surfaceSingle/deleteItem",
        method: "delete",
        data
    })
}
/*
 *@Summary 删除包裹
 *@Router  /surfaceSingle/deletePackage
 *@Method  delete
 *@Date    2022-08-30
*/
export const deletePackage = (data) => {
    return service({
        url: "/surfaceSingle/deletePackage",
        method: "delete",
        data
    })
}
/*
 *@Summary 更新包裹详情item
 *@Router  /surfaceSingle/updateItem
 *@Method  put
 *@Date    2022-08-30
*/
export const updateItem = (data) => {
    return service({
        url: "/surfaceSingle/updateItem",
        method: "put",
        data
    })
}
/*
 *@Summary 获取包裹列表
 *@Router  /surfaceSingle/getOrderPackages
 *@Method  get
 *@Date    2022-09-15
*/
export const getOrderPackages = (params) => {
    return service({
        url: "/surfaceSingle/getOrderPackages",
        method: "get",
        params
    })
}
/*
 *@Summary 新增包裹item
 *@Router  /surfaceSingle/addPackageItem
 *@Method  post
 *@Date    2022-09-15
*/
export const addPackageItem = (data) => {
    return service({
        url: "/surfaceSingle/addPackageItem",
        method: "post",
        data
    })
}
/*
 *@Summary 多包裹打印
 *@Router  /surfaceSingle/morePackagePrint
 *@Method  post
 *@Date    2022-09-16
*/
export const morePackagePrint = (data) => {
    return service({
        url: "/surfaceSingle/morePackagePrint",
        method: "post",
        data
    })
}
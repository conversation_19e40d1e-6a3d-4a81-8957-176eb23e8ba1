import service from '@/utils/request'

// @Tags SupplierClerk
// @Summary 创建SupplierClerk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "创建SupplierClerk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/createSupplierClerk [post]
export const createSupplierClerk = (data) => {
    return service({
        url: "/adminSupplier/createSupplierClerk",
        method: 'post',
        data
    })
}


// @Tags SupplierClerk
// @Summary 删除SupplierClerk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "删除SupplierClerk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adminSupplier/deleteSupplierClerk [delete]
export const deleteSupplierClerk = (data) => {
    return service({
        url: "/adminSupplier/deleteSupplierClerk",
        method: 'delete',
        data
    })
}

// @Tags SupplierClerk
// @Summary 删除SupplierClerk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除SupplierClerk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adminSupplier/deleteSupplierClerk [delete]
export const deleteSupplierClerkByIds = (data) => {
    return service({
        url: "/adminSupplier/deleteSupplierClerkByIds",
        method: 'delete',
        data
    })
}

// @Tags SupplierClerk
// @Summary 更新SupplierClerk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "更新SupplierClerk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adminSupplier/updateSupplierClerk [put]
export const updateSupplierClerk = (data) => {
    return service({
        url: "/adminSupplier/updateSupplierClerk",
        method: 'put',
        data
    })
}


// @Tags SupplierClerk
// @Summary 用id查询SupplierClerk
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SupplierClerk true "用id查询SupplierClerk"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adminSupplier/findSupplierClerk [get]
export const findSupplierClerk = (params) => {
    return service({
        url: "/adminSupplier/findSupplierClerk",
        method: 'get',
        params
    })
}


// @Tags SupplierClerk
// @Summary 分页获取SupplierClerk列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.PageInfo true "分页获取SupplierClerk列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adminSupplier/getSupplierClerkList [get]
export const getSupplierClerkList = (params) => {
    return service({
        url: "/adminSupplier/getSupplierClerkList",
        method: 'get',
        params
    })
}

export const getUserList = (data) => {
    return service({
        url: "/sysUser/getUserList",
        method: 'post',
        data
    })
}
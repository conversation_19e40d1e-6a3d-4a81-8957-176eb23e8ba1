import service from '@/utils/request'

// @Tags SysTop
// @Summary 更新SysTop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysTop true "更新SysTop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /sysTop/updateSysTop [put]
 export const updateSysSetting = (data) => {
     return service({
         url: "/sysSetting/updateSysSetting",
         method: 'put',
         data
     })
 }


// @Tags SysTop
// @Summary 用id查询SysTop
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SysTop true "用id查询SysTop"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /sysTop/findSysTop [get]
 export const findSysSetting = (params) => {
     return service({
         url: "/sysSetting/findSysSetting",
         method: 'get',
         params
     })
 }


import service from '@/utils/request'
/*
 *@Summary 营销类应用
 *@Router  /plugin/getPluginList
 *@Method  get
 *@Date    2021-11-22
*/
export const getPluginList = (params) => {
    return service({
        url: "/plugin/getPluginList",
        method: "get",
        params
    })
}
/*
 *@Summary 资源类应用
 *@Router  /plugin/getResourcePluginList
 *@Method  get
 *@Date  2022-02-09
*/
export const getResourcePluginList = (params) => {
    return service({
        url: "/plugin/getResourcePluginList",
        method: "get",
        params
    })
}
/*
 *@Summary 工具类应用
 *@Router  /plugin/getToolsPluginList
 *@Method  get
 *@Date  2022-03-08
*/
export const getToolsPluginList = (params) => {
    return service({
        url: "/plugin/getToolsPluginList",
        method: "get",
        params
    })
}
/*
 *@Summary 渠道类应用
 *@Router  /plugin/getChannelPluginList
 *@Method   get
 *@Date    2023-05-11
*/
export const getChannelPluginList = (params) => {
    return service({
        url: "/plugin/getChannelPluginList",
        method: "get",
        params
    })
}

/*
 *@Summary 获取部分查询权限 概括右侧使用
 *@Router  /plugin/getPluginLists
 *@Method   get
 *@Date    2024-06-18
*/
export const getPluginLists = (params) => {
    return service({
        url: "/plugin/getPluginLists",
        method: "get",
        params
    })
}
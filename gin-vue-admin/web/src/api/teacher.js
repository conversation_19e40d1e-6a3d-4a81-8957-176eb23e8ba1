import service from "@/utils/request";
/*
 *@Summary 添加平台讲师
 *@Router  /lecturer/createLecturer
 *@Method  post
 *@Date  2023-02-23
*/
export const createLecturer = (data) => {
    return service({
        url: "/lecturer/createLecturer",
        method: "POST",
        data
    })
}

/*
 *@Summary 删除平台讲师
 *@Router  /lecturer/deleteLecturer
 *@Method  post
 *@Date  2023-02-23
*/
export const deleteLecturer = (data) => {
    return service({
        url: "/lecturer/deleteLecturer",
        method: "POST",
        data
    })
}

/*
 *@Summary 编辑平台讲师
 *@Router  /lecturer/updateLecturer
 *@Method  post
 *@Date  2023-02-23
*/
export const updateLecturer = (data) => {
    return service({
        url: "/lecturer/updateLecturer",
        method: "POST",
        data
    })
}

/*
 *@Summary 更改状态
 *@Router  /lecturer/updateLecturer
 *@Method  post
 *@Date  2023-02-23
*/
export const updateChapterStatus = (data) => {
    return service({
        url: "/curriculum/updateChapterStatus",
        method: "POST",
        data
    })
}

/*
 *@Summary 查询平台讲师
 *@Router  /lecturer/findLecturer
 *@Method  post
 *@Date  2023-02-23
*/
export const findLecturer = (data) => {
    return service({
        url: "/lecturer/findLecturer",
        method: "POST",
        data
    })
}

/*
 *@Summary 查询第三方讲师
 *@Router  /lecturer/findThirdLecturer
 *@Method  post
 *@Date  2023-02-23
*/
export const findThirdLecturer = (data) => {
    return service({
        url: "/lecturer/findThirdLecturer",
        method: "POST",
        data
    })
}

/*
 *@Summary 保存基础设置
 *@Router  /curriculum/savaBaseSetting
 *@Method  post
 *@Date  2023-02-23
*/
export const savaBaseSetting = (data) => {
    return service({
        url: "/curriculum/savaBaseSetting",
        method: "POST",
        data
    })
}

/*
 *@Summary 查询基础设置
 *@Router  /curriculum/getBaseSetting
 *@Method  post
 *@Date  2023-02-23
*/
export const getBaseSetting = (data) => {
    return service({
        url: "/curriculum/getBaseSetting",
        method: "POST",
        data
    })
}

/*
 *@Summary “发布课程”保存
 *@Router  /curriculum/createChapter
 *@Method  post
 *@Date  2023-02-23
*/
export const createChapter = (data) => {
    return service({
        url: "/curriculum/createChapter",
        method: "POST",
        data
    })
}

/*
 *@Summary 课程列表编辑
 *@Router  /curriculum/updateChapter
 *@Method  post
 *@Date  2023-02-23
*/
export const updateChapter = (data) => {
    return service({
        url: "/curriculum/updateChapter",
        method: "POST",
        data
    })
}

/*
 *@Summary 课程列表删除
 *@Router  /curriculum/deleteChapter
 *@Method  post
 *@Date  2023-02-23
*/
export const deleteChapter = (data) => {
    return service({
        url: "/curriculum/deleteChapter",
        method: "POST",
        data
    })
}

/*
 *@Summary 课程列表查询
 *@Router  /curriculum/findChapter
 *@Method  post
 *@Date  2023-02-23
*/
export const findChapter = (data) => {
    return service({
        url: "/curriculum/findChapter",
        method: "POST",
        data
    })
}

/*
 *@Summary 查询课程数量
 *@Router  /curriculum/selectCurriculumCount
 *@Method  post
 *@Date  2023-02-23
*/
export const selectCurriculumCount = (data) => {
    return service({
        url: "/curriculum/selectCurriculumCount",
        method: "POST",
        data
    })
}

/*
 *@Summary 导入未导入课程
 *@Router  /curriculum/importCurriculum
 *@Method  post
 *@Date  2023-02-23
*/
export const importCurriculum = (data) => {
    return service({
        url: "/curriculum/importCurriculum",
        method: "POST",
        data
    })
}

/*
 *@Summary 更新已导入课程
 *@Router  /curriculum/updateCurriculum
 *@Method  post
 *@Date  2023-02-23
*/
export const updateCurriculum = (data) => {
    return service({
        url: "/curriculum/updateCurriculum",
        method: "POST",
        data
    })
}

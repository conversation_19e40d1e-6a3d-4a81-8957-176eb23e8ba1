import service from "@/utils/request";
/*
 *@Summary 保存设置
 *@Router  /holder/setSetting
 *@Method  post
 *@Date    2023-08-14
*/
export const setSetting = (data) => {
    return service({
        url: "/holder/setSetting",
        method: "post",
        data
    })
}
/*
 *@Summary 获取设置
 *@Router  /holder/getSetting
 *@Method  get
 *@Date    2023-08-14
*/
export const getSetting = (params) => {
    return service({
        url: "/holder/getSetting",
        method: "get",
        params
    })
}
/*
 *@Summary 获取支付记录列表
 *@Router  /holder/getPurchaseBalanceList
 *@Method  post
 *@Date    2023-08-14
*/
export const getPurchaseBalanceList = (data) => {
    return service({
        url: "/holder/getPurchaseBalanceList",
        method: "post",
        data
    })
}
/*
 *@Summary 获取分账列表
 *@Router  /holder/getDivideAccounts
 *@Method  post
 *@Date    2023-08-14
*/
export const getDivideAccounts = (data) => {
    return service({
        url: "/holder/getDivideAccounts",
        method: "post",
        data
    })
}

/*
 *@Summary 获取银行列表
 *@Router  /holder/getBank
 *@Method  get
 *@Date    2023-08-21
*/
export const getBank = (params) => {
    return service({
        url: "/holder/getBank",
        method: "get",
        params
    })
}
/*
 *@Summary 获取支行
 *@Router  /holder/getBranch
 *@Method  post
 *@Date    2023-08-21
*/
export const getBranch = (data) => {
    return service({
        url: "/holder/getBranch",
        method: "post",
        data
    })
}
/*
 *@Summary 获取地区
 *@Router  /holder/getRegion
 *@Method  get
 *@Date    2023-08-21
*/
export const getRegion = (params) => {
    return service({
        url: "/holder/getRegion",
        method: "get",
        params
    })
}
export const uploadFile = (data) => {
    return service({
        url: "/holder/uploadFile",
        method: "post",
        data: data
    })
}

/*
 *@Summary 创建个人分账方开户
 *@Router  /holder/createPersonHolder
 *@Method  post
 *@Date    2023-08-21
*/
export const createPersonHolder = (data) => {
    return service({
        url: "/holder/createPersonHolder",
        method: "post",
        data
    })
}
/*
 *@Summary 创建个人分账方开户
 *@Router  /holder/updatePersonHolder
 *@Method  post
 *@Date    2023-08-21
*/
export const updatePersonHolder = (data) => {
    return service({
        url: "/holder/updatePersonHolder",
        method: "post",
        data
    })
}
/*
 *@Summary 个人账户列表
 *@Router  /holder/personHolderList
 *@Method  post
 *@Date    2023-08-21
*/
export const personHolderList = (data) => {
    return service({
        url: "/holder/personHolderList",
        method: "post",
        data
    })
}
/*
 *@Summary 查询开户信息详情
 *@Router  /holder/shareHolderQuery
 *@Method  post
 *@Date    2023-08-21
*/
export const shareHolderQuery = (data) => {
    return service({
        url: "/holder/shareHolderQuery",
        method: "post",
        data
    })
}

/*
 *@Summary 查询企业开户列表
 *@Router  /holder/enterpriseHolderList
 *@Method  post
 *@Date    2023-08-24
*/
export const enterpriseHolderList = (data) => {
    return service({
        url: "/holder/enterpriseHolderList",
        method: "post",
        data
    })
}
/*
 *@Summary 创建企业分账方开户
 *@Router  /holder/enterpriseHolderList
 *@Method  post
 *@Date    2023-08-24
*/
export const enterpriseHolder = (data) => {
    return service({
        url: "/holder/enterpriseHolder",
        method: "post",
        data
    })
}
/*
 *@Summary 修改企业分账方开户
 *@Router  /holder/updateShareEnterpriseHolder
 *@Method  post
 *@Date    2023-08-24
*/
export const updateShareEnterpriseHolder = (data) => {
    return service({
        url: "/holder/updateShareEnterpriseHolder",
        method: "post",
        data
    })
}

/*
 *@Summary 供应商获取开户code
 *@Router  /holder/updateShareEnterpriseHolder
 *@Method  post
 *@Date    2023-08-24
*/
export const supplierDetail = (data) => {
    return service({
        url: "/holder/supplierDetail",
        method: "post",
        data
    })
}
/**
 * 发送验证码
 */
export const smsVerify = (data) => {
    return service({
        url: "/holder/smsVerify",
        method: "post",
        data
    })
}
//企业手动开户
export const manualEnterpriseHolder = (data) => {
    return service({
        url: "/holder/manualEnterpriseHolder",
        method: "post",
        data
    })
}
// 个人手动开户
export const manualPersonHolder = (data) => {
    return service({
        url: "/holder/manualPersonHolder",
        method: "post",
        data
    })
}
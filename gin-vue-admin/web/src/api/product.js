import service from '@/utils/request'

// @Tags Product
// @Summary 创建Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "创建Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/createProduct [post]
export const createProduct = (data) => {
    return service({
        url: "/product/createProduct",
        method: 'post',
        data
    })
}


// @Tags Product
// @Summary 删除Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "删除Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /product/deleteProduct [delete]
export const deleteProduct = (data) => {
    return service({
        url: "/product/deleteProduct",
        method: 'delete',
        data
    })
}

// @Tags Product
// @Summary 删除Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /product/deleteProduct [delete]
export const deleteProductByIds = (data) => {
    return service({
        url: "/product/deleteProductByIds",
        method: 'delete',
        data
    })
}

// @Tags Product
// @Summary 更新Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "更新Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /product/updateProduct [put]
export const updateProduct = (data) => {
    return service({
        url: "/product/updateProduct",
        method: 'put',
        data
    })
}


// @Tags Product
// @Summary 用id查询Product
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Product true "用id查询Product"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /product/findProduct [get]
export const findProduct = (params) => {
    return service({
        url: "/product/findProduct",
        method: 'get',
        params
    })
}
export const adminSupplierFindProduct = (params) => {
    return service({
        url: "/adminSupplier/product/findProduct",
        method: 'get',
        params
    })
}


// @Tags Product
// @Summary 分页获取Product列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.PageInfo true "分页获取Product列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
export const getProductList = (params) => {
    return service({
        url: "/product/getProductList",
        method: 'get',
        params
    })
}
export const getAliProductList = (params) => {
    return service({
        url: "/product/getAliProductList",
        method: 'get',
        params
    })
}
/*
 *@Summary 供应商商品审核列表
 *@Router  /product/productVerifyList
 *@Method  get
 *@Date    2021-11-11
*/
export const productVerifyList = (params) => {
    return service({
        url: "/product/productVerifyList",
        method: "get",
        params
    })
}
/*
 *@Summary 供货商商品审核操作
 *@Router  /product/verify
 *@Method  post
 *@Date    2021-11-12
*/
export const supplierProductVerify = (data) => {
    return service({
        url: "/product/verify",
        method: "post",
        data
    })
}

/*
 *@Summary 供应商商品审核-批量操作
 *@Router  /product/VerifyByIds
 *@Method  post
 *@Date    2022-05-09
*/
export const VerifyByIds = (data) => {
    return service({
        url: "/product/VerifyByIds",
        method: "post",
        data
    })
}

/*
 *@Summary 取消绑定
 *@Router  /aliOpen/cancelBindProduct
 *@Method  post
 *@Date    2022-08-16
*/
export const cancelBindProduct = (data) => {
    return service({
        url: "/aliOpen/cancelBindProduct",
        method: "post",
        data
    })
}

/*
 *@Summary 获取商品设置
 *@Router  /product/findProductSetting
 *@Method  get
 *@Date    2023-01-30
*/
export const findProductSetting = (params) => {
    return service({
        url: "/product/findProductSetting",
        method: 'get',
        params
    })
}
/*
 *@Summary 更新商品设置
 *@Router  product/updateProductSetting
 *@Method  post
 *@Date    2023-01-30
*/
export const updateProductSetting = (data) => {
    return service({
        url: "/product/updateProductSetting",
        method: 'post',
        data
    })
}
// 租赁商品列表
export const leaseSupplygetProductList = (params) => {
    return service({
        url: "/leaseSupply/getProductList",
        method: 'get',
        params
    })
}
// 租赁商品审核列表
export const leasegetLeaseAuditProductList = (params) => {
    return service({
        url: "/lease/getLeaseAuditProductList",
        method: 'get',
        params
    })
}
/*
 *@Summary 租赁商品审核
 *@Router  /lease/auditLeaseAuditProduct
 *@Method  post
 *@Date    2023-01-30
*/
export const leaseAuditLeaseAuditProduct = (data) => {
    return service({
        url: "/lease/auditLeaseAuditProduct",
        method: 'post',
        data
    })
}
// 获取商品详情
export const leasefindProduct = (params) => {
    return service({
        url: "/lease/findProduct",
        method: 'get',
        params
    })
}


// 商品合并
export const productMerge = (data) => {
    return service({
        url: "/product/productMerge",
        method: 'post',
        data
    })
}

// 商品合并
export const productMergeRecord = (params) => {
    return service({
        url: "/product/productMergeRecord",
        method: 'get',
        params
    })
}
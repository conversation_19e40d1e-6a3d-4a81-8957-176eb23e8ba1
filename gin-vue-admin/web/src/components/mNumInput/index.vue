<template>
  <div class="f fac m-number-input">
    <div :class="startText ? 'input-start-div slot-div' :''" :style="`${inputHeight ? 'height:' + inputHeight + 'px;' + 'line-height:' + inputHeight + 'px;' : ''}`" v-if="startText">{{ startText }}</div>
    <el-input-number ref="inputRef" :value="value" @input="inputChange($event)" :min="min" class="f1 input-number-text-left"
                     :controls="false" :precision="precision" :placeholder="pla"></el-input-number>
    <div :class="endText ? 'input-end-div slot-div' :''" :style="`${inputHeight ? 'height:' + inputHeight + 'px;' + 'line-height:' + inputHeight + 'px;' : ''}`" v-if="endText">{{ endText }}</div>
  </div>
</template>

<script>
export default {
  name: "mNumInput",
  props: {
    value: Number,
    pla: {
      type: String,
      default: () => {
        return "请输入"
      }
    },
    startText: {
      type: String,
      default: () => {
        return ""
      }
    }, endText: {
      type: String,
      default: () => {
        return ""
      }
    },
    min: {
      type: Number,
      default: () => {
        return 0
      }
    },
    precision: {
      type: Number,
      default: () => {
        return 0
      }
    }
  },
    data(){
      return {
          inputHeight: null
      }
    },
    mounted() {
      this.inputHeight = this.$refs.inputRef.$el.offsetHeight
    },
    methods: {
    inputChange(val) {
      this.$emit("input", val);
    },
  },
}
</script>

<style lang="scss" scoped>
.m-number-input {
  width: 100%;

  .slot-div {
    background-color: #f5f7fa;
    color: #909399;
    vertical-align: middle;
    display: table-cell;
    position: relative;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 0 20px;
    white-space: nowrap;
  }

  .input-start-div {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: 0;
  }

  .input-end-div {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 0;
  }

  ::v-deep .el-input {
    .el-input__inner {
      height: 38px;
      line-height: 38px;
    }
  }
}
</style>
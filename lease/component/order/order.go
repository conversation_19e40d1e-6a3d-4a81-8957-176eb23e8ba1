package order

import (
	"github.com/writethesky/stbz-sdk-golang"
	callback2 "public-supply/callback"
	"public-supply/request"
	"public-supply/response"
	//self "self-supply/component/order"
	//szbao "szbao-supply/component/order"
)

//@author: [ccfish86](https://github.com/ccfish86)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@interface_name: OSS
//@description: OSS接口

type Order interface {
	InitSetting(gatherSupplyID uint) (err error)
	OrderBeforeCheck(request request.RequestSaleBeforeCheck) (err error, data response.BeforeCheck)
	SaleBeforeCheck(request request.RequestSaleBeforeCheck) (err error, resData response.ResSaleBeforeCheck)
	ConfirmOrder(request request.RequestConfirmOrder) (err error, info *stbz.APIResult)
	ExpressQuery(request request.RequestExpress) (err error, info interface{})
	AfterSalesBeforeCheck(request request.RequestAfterSale) (err error, info interface{})
	AfterSalesPicture(request request.RequestAfterSalePicture) (err error, info interface{})
	AfterSale(request request.AfterSale) (err error, info interface{})
	OrderDelivery(OrderData callback2.OrderCallBack) (err error)
	SyncOrderExpNo(unionIdList []string) (err error, data []response.SyncOrderExpNoResponse)
}

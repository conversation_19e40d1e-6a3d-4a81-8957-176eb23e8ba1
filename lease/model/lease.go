package model

import (
	"database/sql/driver"
	"encoding/json"
	"gorm.io/gorm"
	"region/model"
	"yz-go/source"
)

type OrderStatus int8

const (
	WaitPay OrderStatus = iota
	WaitSend
	WaitReceive
	Completed
	Returned
	AuditReturned
	ReturnedindSend
	WaitConfirmed
	Closed         OrderStatus = -1
	RejectReturned OrderStatus = -2
)

func GetStatusName(status OrderStatus) string {
	statusNameMap := map[OrderStatus]string{
		WaitPay:         "待付款",
		WaitSend:        "待发货",
		WaitReceive:     "待收货",
		Completed:       "已完成",
		Returned:        "待归还",
		AuditReturned:   "审核中",
		ReturnedindSend: "归还中", //需要物流
		WaitConfirmed:   "待确认",
		Closed:          "已关闭",
		RejectReturned:  "驳回申请",
	}
	return statusNameMap[status]

}
func (receiver *OrderLease) AfterFind(tx *gorm.DB) (err error) {
	receiver.StatusName = GetStatusName(receiver.Status)
	return
}

// 归还地址
type LeaseReturnAddress struct {
	source.Model
	Username    string `json:"username" form:"username" gorm:"column:username;comment:联系人;"`                            // 联系人
	Tel         string `json:"tel" form:"tel" gorm:"column:tel;comment:联系电话;"`                                          // 联系电话
	ProvinceId  int    `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省id;type:int(11);"`      // 省id
	CityId      int    `json:"city_id" form:"city_id" gorm:"column:city_id;comment:市id;type:int(11);"`                  // 市id
	CountyId    int    `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区id;type:int(11);"`            // 区id
	TownId      int    `json:"town_id" form:"town_id" gorm:"column:town_id;comment:街id;type:int(11);"`                  // 街id
	Province    string `json:"province" form:"province" gorm:"column:province;comment:省;" `                             // 省
	City        string `json:"city" form:"city" gorm:"column:city;comment:市;"`                                          // 市
	County      string `json:"county" form:"county" gorm:"column:county;comment:区;"`                                    //区
	Town        string `json:"town" form:"town" gorm:"column:town;comment:街;"`                                          //街
	Detail      string `json:"detail" form:"detail" gorm:"column:detail;comment:详细地址;"`                                 // 详细地址
	IsDefault   int    `json:"is_default" form:"is_default" gorm:"column:is_default;comment:是否默认1是0否;default:0"`        // 省id
	SuppliersId int    `json:"suppliers_id" form:"suppliers_id" gorm:"column:suppliers_id;comment:供应商id;type:int(11);"` // 市id
}

// 租赁订单信息
type OrderLeaseMigration struct {
	source.Model
	OrderId           uint        `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index"`                                                              // 订单id
	NumDays           int         `json:"num_days" form:"num_days" gorm:"column:num_days;comment:天数;type:int(11);default:0"`                                               // 天数
	PreferentialRatio int         `json:"preferential_ratio" form:"preferential_ratio" gorm:"column:preferential_ratio;comment:preferential_ratio;type:int(11);default:0"` //优惠比例 存时*100 使用时需要/100
	Status            OrderStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`                                            // 订单状态
	StatusName        string      `json:"status_name" gorm:"-"`

	StartTime *source.LocalTime `json:"start_at"` //租赁开始时间
	EndTime   *source.LocalTime `json:"end_at"`   //租赁结束时间

	SentAt               *source.LocalTime `json:"sent_at" gorm:"index;"`   //发货时间
	ReceivedAt           *source.LocalTime `json:"received_at"`             //收货时间
	ReturnApplyAt        *source.LocalTime `json:"return_apply_at"`         //申请归还时间
	ReturnApplySuccessAt *source.LocalTime `json:"return_apply_success_at"` //申请归还审核通过时间
	AuditAt              *source.LocalTime `json:"audit_at"`                //申请归还审核通过时间

	ReturnAt *source.LocalTime `json:"return_at"` //归还时间

	OrderLeaseReturnAddressId uint                    `json:"order_lease_return_address_id" form:"order_lease_return_address_id" gorm:"column:order_lease_return_address_id;comment:户选择的当前订单的归还地址;"` // 户选择的当前订单的归还地址
	CompanyCode               string                  `json:"company_code" form:"company_code" gorm:"column:company_code;comment:归还快递公司码;type:varchar(30);size:30;"`
	ExpressNo                 string                  `json:"express_no" form:"express_no" gorm:"column:express_no;comment:归还快递单号;type:varchar(30);size:30;"`
	CompanyName               string                  `json:"company_name" form:"company_name" gorm:"column:company_name;comment:归还快递公司名称;type:varchar(30);size:30;"`
	OrderLeaseReturnAddress   OrderLeaseReturnAddress `json:"order_lease_return_address" gorm:"foreignkey:OrderLeaseReturnAddressId;references:ID"`
}

func (OrderLeaseMigration) TableName() string {
	return "order_leases"
}

type OrderLease struct {
	source.Model
	OrderId           uint        `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index"`                                                              // 订单id
	NumDays           int         `json:"num_days" form:"num_days" gorm:"column:num_days;comment:天数;type:int(11);default:0"`                                               // 天数
	PreferentialRatio int         `json:"preferential_ratio" form:"preferential_ratio" gorm:"column:preferential_ratio;comment:preferential_ratio;type:int(11);default:0"` //优惠比例 存时*100 使用时需要/100
	Status            OrderStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`                                            // 订单状态
	StatusName        string      `json:"status_name" gorm:"-"`

	StartTime *source.LocalTime `json:"start_at"` //租赁开始时间
	EndTime   *source.LocalTime `json:"end_at"`   //租赁结束时间

	SentAt               *source.LocalTime `json:"sent_at" gorm:"index;"`   //发货时间
	ReceivedAt           *source.LocalTime `json:"received_at"`             //收货时间
	ReturnApplyAt        *source.LocalTime `json:"return_apply_at"`         //申请归还时间
	ReturnApplySuccessAt *source.LocalTime `json:"return_apply_success_at"` //申请归还审核通过时间
	AuditAt              *source.LocalTime `json:"audit_at"`                //申请归还审核通过时间

	ReturnAt *source.LocalTime `json:"return_at"` //归还时间

	OrderLeaseReturnAddressId uint                    `json:"order_lease_return_address_id" form:"order_lease_return_address_id" gorm:"column:order_lease_return_address_id;comment:户选择的当前订单的归还地址;"` // 户选择的当前订单的归还地址
	CompanyCode               string                  `json:"company_code" form:"company_code" gorm:"column:company_code;comment:归还快递公司码;type:varchar(30);size:30;"`
	ExpressNo                 string                  `json:"express_no" form:"express_no" gorm:"column:express_no;comment:归还快递单号;type:varchar(30);size:30;"`
	CompanyName               string                  `json:"company_name" form:"company_name" gorm:"column:company_name;comment:归还快递公司名称;type:varchar(30);size:30;"`
	OrderLeaseReturnAddress   OrderLeaseReturnAddress `json:"order_lease_return_address" gorm:"foreignkey:OrderLeaseReturnAddressId;references:ID"`
}

// 归还地址 -- 用户选择的当前订单的归还地址
type OrderLeaseReturnAddress struct {
	source.Model
	Username    string `json:"username" form:"username" gorm:"column:username;comment:联系人;"`                            // 联系人
	Tel         string `json:"tel" form:"tel" gorm:"column:tel;comment:联系电话;"`                                          // 联系电话
	ProvinceId  int    `json:"province_id" form:"province_id" gorm:"column:province_id;comment:省id;type:int(11);"`      // 省id
	CityId      int    `json:"city_id" form:"city_id" gorm:"column:city_id;comment:市id;type:int(11);"`                  // 市id
	CountyId    int    `json:"county_id" form:"county_id" gorm:"column:county_id;comment:区id;type:int(11);"`            // 区id
	TownId      int    `json:"town_id" form:"town_id" gorm:"column:town_id;comment:街id;type:int(11);"`                  // 街id
	Province    string `json:"province" form:"province" gorm:"column:province;comment:省;" `                             // 省
	City        string `json:"city" form:"city" gorm:"column:city;comment:市;"`                                          // 市
	County      string `json:"county" form:"county" gorm:"column:county;comment:区;"`                                    //区
	Town        string `json:"town" form:"town" gorm:"column:town;comment:街;"`                                          //街
	Detail      string `json:"detail" form:"detail" gorm:"column:detail;comment:详细地址;"`                                 // 详细地址
	IsDefault   int    `json:"is_default" form:"is_default" gorm:"column:is_default;comment:是否默认1是0否;default:0"`        // 省id
	SuppliersId int    `json:"suppliers_id" form:"suppliers_id" gorm:"column:suppliers_id;comment:供应商id;type:int(11);"` // 市id
}

type OrderLeasePushMessage struct { // 消息推送记录表
	source.Model
	OrderLeaseId          uint   `json:"order_lease_id" form:"order_lease_id" gorm:"column:order_lease_id;comment:售后id;type:varchar(20);size:20;"` // 售后id
	ApplicationID         uint   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;index;"`                    // 应用id
	OrderID               uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单idid;index;"`                                    // 应用id
	ThirdOrderSN          string `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`                            // 采购端单号
	LeaseOrderMessageType string `json:"lease_order_message_type" form:"lease_order_message_type" gorm:"column:lease_order_message_type;comment:消息类型;"`
	Status                int    `json:"status" form:"status" gorm:"column:status;default:1;comment:状态;type:smallint;size:3;"` // 状态 1待推送 2待商城确认 3完成 -1推送失败
	ErrorMsg              string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;"`                     //错误内容

}
type OrderItemLease struct {
	source.Model
	OrderLeaseId uint `json:"order_lease_id" form:"order_lease_id" gorm:"column:order_lease_id;comment:租赁订单id;"` // 订单id
	OrderId      uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id;"`                   // 订单id
	OrderItemId  uint `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:中台子订单id;"`   // 订单id
	ProductId    uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;"`               // 商品id
	SkuId        uint `json:sku_id" form:"sku_id" gorm:"column:sku_id;comment:商品规格id;"`                          // 商品id
	Price        uint `json:price" form:"price" gorm:"column:price;comment:商品购买价格;"`                             // 商品id
}

// 租期
type LeaseTenancyTerm struct {
	source.Model
	Title             string `json:"title" form:"title" gorm:"column:title;comment:名称;"`                                                                              // 名称
	Sort              int    `json:"sort" form:"sort" gorm:"column:sort;comment:排序;type:int(11);default:0"`                                                           // 排序
	NumDays           int    `json:"num_days" form:"num_days" gorm:"column:num_days;comment:天数;type:int(11);default:0"`                                               // 天数
	PreferentialRatio int    `json:"preferential_ratio" form:"preferential_ratio" gorm:"column:preferential_ratio;comment:preferential_ratio;type:int(11);default:0"` //优惠比例 存时*100 使用时需要/100
	SuppliersId       int    `json:"suppliers_id" form:"suppliers_id" gorm:"column:suppliers_id;comment:供应商id;type:int(11);"`                                         // 供应商id
}

// 租赁商品设置
type LeaseCoverage struct {
	source.Model
	LeaseCoverageRegions     LeaseCoverageRegions       `json:"lease_coverage_regions"`
	ProductID                uint                       `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;index;"` // 商品id
	LeaseCoverageRegionsData []LeaseCoverageRegionsData `json:"lease_coverage_regions_data"  gorm:"-"`                                     //所有区的名称

}
type LeaseCoverageRegionsData struct {
	ID   int    `json:"id" gorm:"primarykey"`
	Name string `json:"name" gorm:"column:name;comment:地区名;type:varchar(32);size:32;"` //地名
}

func (receiver *LeaseCoverage) AfterFind(tx *gorm.DB) (err error) {
	var ids []int
	for _, item := range receiver.LeaseCoverageRegions {
		for _, itemItem := range item.LeaseCoverageRegions {
			for _, itemItemItem := range itemItem.LeaseCoverageRegions {
				ids = append(ids, itemItemItem.ID)
			}
		}
	}
	//var region []model.Region
	if len(ids) > 0 {
		source.DB().Model(&model.Region{}).Where("id in ?", ids).Find(&receiver.LeaseCoverageRegionsData)
	}
	return
}

// 租赁商品审核
type LeaseAuditProduct struct {
	source.Model
	ProductID   uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:商品id;index;"`                            // 商品id
	SuppliersId uint `json:"suppliers_id" form:"suppliers_id" gorm:"column:suppliers_id;comment:供应商id;type:int(11);index"`         // 供应商id
	Status      int  `json:"status" form:"status" gorm:"column:status;comment:审核状态0待审核 1审核通过 -1驳回 -2取消审核;default:0;type:int(11);"` // 审核状态0待审核 1审核通过 -1驳回
	IsDisplay   int  `json:"is_display" form:"is_display" gorm:"column:is_display;comment:同商品的is_display;default:0"`               //同商品的is_display
}

func (value LeaseCoverageRegions) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *LeaseCoverageRegions) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

type LeaseCoverageRegion struct {
	ID                   int                  `json:"id"`                     // 区域id
	LeaseCoverageRegions LeaseCoverageRegions `json:"lease_coverage_regions"` // 子区域
}
type LeaseCoverageRegions []LeaseCoverageRegion

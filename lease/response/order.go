package response

import (
	"gorm.io/gorm"
	"lease/model"
	orderModel "order/model"
	"order/response"
	"payment/service"
	"yz-go/source"
)

type Order struct {
	response.Order
	OrderLease         model.OrderLease `json:"order_leases" gorm:"foreignkey:order_id"`
	ShippingMethodName string           `json:"shipping_method_name" gorm:"-"`
}

type ShippingMethod struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}

var shippingMethods []ShippingMethod

func GetList() (error, []ShippingMethod) {
	var err error
	if len(shippingMethods) == 0 {
		// 排除门店自提
		err = source.DB().Where("status = ? and id != ?", 1, 2).Order("sort").Find(&shippingMethods).Error
	}
	return err, shippingMethods
}

func GetShippingMethodName(shippingMethodId uint) string {
	_, list := GetList()
	for _, i := range list {
		if i.ID == shippingMethodId {
			return i.Name
		}
	}
	return ""
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {
	b.Button = GetButton(b.OrderLease.Status)
	if b.SupplierID != 0 {
		b.ShopName = "供应商:" + b.Supplier.Name
	} else if b.GatherSupplyID != 0 {
		b.ShopName = "供应链:" + b.GatherSupply.Name
	} else {
		b.ShopName = "平台自营"
	}
	b.ShippingMethodName = GetShippingMethodName(uint(b.ShippingMethodID))
	_, payType := service.GetPayType()
	for _, v := range payType {
		if b.PayTypeID == v.Code {
			b.PayType = v.Name
		}
	}
	if b.PayTypeID == -1 {
		b.PayType = "后台支付"
	}

	if b.PayType == "" {
		b.PayType = "未知"
	}
	return
}
func GetButton(status model.OrderStatus) interface{} {
	buttonMap := map[model.OrderStatus][]orderModel.Button{
		model.WaitPay:       {{Title: "确认付款", Url: "order/pay"}, {Title: "关闭订单", Url: "order/close"}},
		model.WaitSend:      {{Title: "确认发货", Url: "lease/orderLease/send"}, {Title: "全额退款", Url: "order/orderRefund"}},
		model.WaitReceive:   {{Title: "确认收货", Url: "lease/orderLease/receive"}, {Title: "全额退款", Url: "order/orderRefund"}},
		model.Completed:     {{Title: "已完成", Url: ""}, {Title: "全额退款", Url: "order/orderRefund"}},
		model.Closed:        {{Title: "已关闭", Url: ""}},
		model.AuditReturned: {}, //{Title: "归还申请", Url: "lease/orderLease/audit"}, {Title: "全额退款", Url: "order/orderRefund"} -- 前端要求不返回-自己增加按钮
		model.WaitConfirmed: {{Title: "确认归还", Url: "lease/orderLease/confirmedCompleted"}, {Title: "全额退款", Url: "order/orderRefund"}},
	}
	return buttonMap[status]
}
